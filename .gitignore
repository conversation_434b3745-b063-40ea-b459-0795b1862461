# editor temporary / backup files
*~

genfiles/
locales/*.UTF-8
!locales/merge-en.sh
py-compile
__pycache__
*.la
*.o
.deps/
.dirstamp
*.lo
.libs/
libvalhalla.la
date_time_zonespec.csv
mason/
mason_packages/

# scripts
scripts/gdal-2.0.0/
scripts/gdal-2.0.0.tar.gz

# tools
.cproject
.project
.settings
CMakeLists.txt.user
/.vs*/
/*build*/
/CMakeSettings.json
.idea
/.tidytmp
vcpkg*/
*.log

# python
.venv

# clangd
.cache/

# documentation
site/
*.gph

.DS_Store

# python bindings
src/bindings/python/valhalla/__version__.py
**/*.egg-info
dist
wheelhouse

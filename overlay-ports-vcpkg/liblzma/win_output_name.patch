diff --git a/CMakeLists.txt b/CMakeLists.txt
index 0c6d4b7..62a824a 100644
--- a/CMakeLists.txt
+++ b/CMakeLists.txt
@@ -868,8 +868,11 @@ set_target_properties(liblzma PROPERTIES
 
     # It's liblzma.so or liblzma.dll, not libliblzma.so or lzma.dll.
     # Avoid the name lzma.dll because it would conflict with LZMA SDK.
-    PREFIX ""
+    OUTPUT_NAME lzma
 )
+if(WIN32 AND NOT MINGW)
+    set_target_properties(liblzma PROPERTIES RUNTIME_OUTPUT_NAME liblzma)
+endif()
 
 # Create liblzma-config-version.cmake.
 #

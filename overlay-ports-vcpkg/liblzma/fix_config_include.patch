diff --git a/CMakeLists.txt b/CMakeLists.txt
index 34c6aca00..7b3708ab2 100644
--- a/CMakeLists.txt
+++ b/CMakeLists.txt
@@ -413,6 +413,7 @@ if(WIN32)
     if(BUILD_SHARED_LIBS)
         # Add the Windows resource file for liblzma.dll.
         target_sources(liblzma PRIVATE src/liblzma/liblzma_w32res.rc)
+        target_include_directories(liblzma PRIVATE windows/vs2019)
 
         set_target_properties(liblzma PROPERTIES
             LINK_DEPENDS "${CMAKE_CURRENT_SOURCE_DIR}/src/common/common_w32res.rc"

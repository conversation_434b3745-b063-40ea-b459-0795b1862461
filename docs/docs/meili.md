<PERSON><PERSON>, provides a set of algorithms and datastructures for map matching. It matches a sequence of locations (usually noisy e.g. GPS trajectory) to the underlying road network. In keeping with the Norse mythological theme, the name [<PERSON><PERSON>](https://en.wikipedia.org/wiki/<PERSON><PERSON>), <PERSON>'s brother, was chosen. Since map matching is closely related to routing and since <PERSON> is the Valhalla routing library, meili seemed most appropriate. Additionally the main author of this software, @ptpt from team [Mapillary](https://github.com/mapillary)  noted that, mĕilì (美丽) means beautiful in Chinese. This is indeed a beautiful collaboration between team Mapillary and team Valhalla! Open source FTW!

Documentation
-------------

1. [Overview](meili/overview.md)
2. [Library API](meili/library_api.md)
3. [Configuration](meili/configuration.md)
4. [The Algorithms](meili/algorithms.md)

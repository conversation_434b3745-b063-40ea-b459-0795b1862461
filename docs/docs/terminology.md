# Mobility glossary

* __break location__ - the start or end point of a route.
* __cost__ - fixed costs in seconds that are added to both the path cost and the estimated time.
* __costing model__ - set of costs for particular methods of travel, such as automobile or pedestrian.
* __edge__ - a line connected between nodes
* __factor__ - multiply the cost along an edge or road section in a way that influences the path to favor or avoid a particular attribute
* __graph__ - a set of edges connected by nodes used for building a route
* __location__ - a latitude, longitude coordinate pair, specified in decimal degrees that determines the routing and order of navigation.
* __maneuver__ - an operation to be performed during navigation, such as a turn, and the expected duration of the movement.
* __narration__ - textual guidance describing the maneuver to be performed, such as a turn, distance to travel, and expected time.
* __path__ - the sequence of edges forming a route
* __penalty__ - fixed costs in seconds that are only added to the path cost. Penalties can influence the route path determination but do not add to the estimated time along the path.
* __route__ - sequence of edges and maneuvers forming the best travel path between locations given the available road network, costs, influence factors, and other inputs.
* __short path__ - a route that attempts to minimize distance traveled over the constituent edges, but may not be the shortest distance.
* __through location__ - an optional location to influence the route to travel through that location.
* __tiled routing__ - method of building a path on graph data that has been split into square cells.
* __time__ - the number of seconds estimated to complete a maneuver or trip, including any additional costs.
* __trip__ - results of an entire route, including locations, legs, and maneuvers.
* __height__ - with respect to elevation, the height above or below sea level at a specific location (lat,lng).
* __height with range__ - computing the range (cumulative distance) and height for a series of lat,lng pairs of a line or shape.  This is very useful for charting/graphing.

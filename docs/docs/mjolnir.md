# Mjolnir

The mjolnir is essentially a set of applications, data structures and algorithms which deal with things like: parsing OSM data extracts, cutting routable "graph" tiles, generating tile hierarchies and testing for data deficiencies.  This tiled routing data is used in routing and searching under the valhalla organization.  In keeping with the Norse mythological theme, the name [<PERSON><PERSON><PERSON><PERSON><PERSON>](https://en.wikipedia.org/wiki/Mj%C3%B6lnir) was chosen as it represents a weapon of mass destruction.  This seemed fitting since the main application deals mostly with pounding planet sized OSM data into tiny routable tile fragments.

## Components ##

What follows are some notable components of the mjolnir.

# Skadi

Skadi can be used to access digital elevation model data which is useful in computing steepness of edges in the route graph or generating an elevation profile along a computed route. In keeping with the Norse mythological theme, the jotunn/goddess [<PERSON><PERSON><PERSON><PERSON>](https://en.wikipedia.org/wiki/<PERSON><PERSON><PERSON><PERSON>) was chosen as she is associated, among other things, with the mountains. Since skadi deals mostly with extracting elevation data from various datasets, this seemed like a fitting name!

Skadi is essentially a set of various data structures and algorithms which deal with things like: sampling elevation data in an adhoc or evenly sampled manner as well as serving elevation data in structured (json) or raw (geotiff) formats.

## Components ##

What follows are some notable components of Skadi.

### Sample ###

TODO:

### Service ###

TODO:

# Tyr

Tyr is a service layer taking locations and options as input and returning a route and maneuvers as output essentially linking together all other projects under the valhalla organization. In keeping with the Norse mythological theme, the name <PERSON><PERSON> was chosen as backcronym standing for: Take Your Route. Since this software deals mostly with providing routes based on http requests, this seemed like a fitting name! Tyr is essentially a set of various data structures and algorithms which deal with things like: data marshalling, http, request parsing, response serializing, and  interprocess communication.

## Components ##

What follows are some notable components of tyr.


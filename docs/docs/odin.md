# Odin

Odin serves as a directions engine for annotating a path as input from the [routing engine](https://github.com/valhalla/thor) for use in navigation. In keeping with the Norse mythological theme, the name [<PERSON><PERSON>](https://en.wikipedia.org/wiki/<PERSON><PERSON>) was chosen as he has often been noted as being very wise. Since the library deals mostly with providing, hopefully wise, guidance along a path to be used for navigation, this seemed like a fitting name! We've also managed to create a backronym out of Odin which stands for: Open Directions and Improved Narrative.

Odin contains a set of various data structures and algorithms which deal with things like: maneuver generation, streetname matching and narrative generation.

## Components ##

What follows are some notable components of the odin.

### Maneuver ###

TODO:

### Util ###

TODO:

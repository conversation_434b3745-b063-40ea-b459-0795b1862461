# Baldr

Baldr serves as a set of routing-specific data structures for use within other pieces of the valhalla library. In keeping with the Norse mythological theme, the name [<PERSON><PERSON><PERSON>](https://en.wikipedia.org/wiki/Baldr) was chosen as a backronym standing for: Base ALgorithms and Data Resource. Since baldr deals mostly with accessing routing data and algorithms related to routing subproblems.


Baldr is essentially a set of various data structures and algorithms which deal with things like: route data tiles, tile caching, hierarchical tile layout and tile data members such as nodes, edgeds and exits.

## Components

What follows are some notable components of baldr.

### GraphId

TODO:

### GraphTileReader

TODO:

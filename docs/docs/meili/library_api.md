# Library API

*The API is still in testing and will be changed any time. Any
 suggestions are welcome to share at
 [GitHub Issues](https://github.com/valhalla/valhalla/issues).*

*`TODO` The API listed here is not complete but ready for simple use.*

## Measurement

A `Measurement` object is a measured point read from GPS devices and
it is usually inaccurate and noisy therefore needed to be
matched. Extra attributes such as accuracy and search radius can be
optionally attached to this object to help improve matching
performance.

```C++
#include <valhalla/meili/measurement.h>
using namespace valhalla;

// Constructor
const midgard::PointLL lnglat(13.44, 53.67); // the noisy location read from GPS device
float gps_accuracy = 4.07,                   // GPS accuracy in meters
      search_radius = 30;                    // in the area specified by the radius in meters search road candidates
meili::Measurement(lnglat, gps_accuracy, search_radius);
```

See `valhalla/meili/measurement.h` for more information.

## Map Matcher Factory

A `MapMatcherFactory` object produces [`MapMatcher`](#map-matcher)
objects for a specific transport mode. Other than that, it also
manages in-memory data structures (e.g. tiles) shared among its
matchers for you. It is recommended to instantiate it only once; but
you have to keep it until all its matchers get destroyed.

Pass it a valid configuration object, otherwise it throws
`std::invalid_argument`.

```C++
#include <valhalla/meili/map_matcher_factory.h>

boost::property_tree::ptree config;
boost::property_tree::json_parser read(config, "conf/valhalla.json");

// Constructor
meili::MapMatcherFactory(const boost::property_tree::ptree& config);
```

To create a `MapMatcher` object of a specific transport mode:

```C++
// Possibly throw std::invalid_argument if invalid parameters are
// found in this mode's configuration
meili::MapMatcher*
meili::MapMatcherFactory::Create(const std::string& mode_name);
```

You should take care of the raw `MapMatcher` pointer returned by the
factory.

## Map Matcher

`MapMatcher` object is responsible for matching sequences to the road
network. It is created by
[`MapMatcherFactory`](#map-matcher-factory).

To match a sequence offline:
```C++
std::vector<MatchResult>
meili::MapMatcher::OfflineMatch(const std::vector<Measurement>& sequence);
```

It returns a sequence of [`MatchResult`](#match-result) objects
corresponding to the sequence of [`Measurement`](#measurement) objects.

## Match Result

A `MatchResult` object contains information about which road and
where the corresponding measurement is matched, and how to construct
the route from previous result. It is usually generated by a
[`MapMatcher`](#map-matcher) object as one result of a sequential
matching procedure.

Here are some attributes:
```C++
// Matched coordinate
const valhalla::midgard::PointLL&
meili::MatchResult::lnglat();

// Distance from measurement to the matched coordinate
float meili::MatchResult::distance();

// GraphId identify edges and nodes internally in Valhalla tiled data
valhalla::baldr::GraphId&
meili::MatchResult::edgeid();
```

See the header file `valhalla/meili/match_result.h` for more
information.

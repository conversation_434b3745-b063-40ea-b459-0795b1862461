name: Deploy Docs

on:
  push:
    branches:
      - master

jobs:
  build-deploy:
    name: Build and deploy docs

    runs-on: ubuntu-latest
    if: ${{ github.repository_owner == 'valhalla' }}

    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Set up Python 3
        uses: actions/setup-python@v4

      - name: Install dependencies 🔧
        working-directory: docs
        run: |
          python -m pip install --upgrade pip
          python -m pip install -r requirements.txt

      - name: Build and deploy to GitHub Pages 🏗️ 🚀
        working-directory: docs
        run: |
          mkdocs gh-deploy --force --strict

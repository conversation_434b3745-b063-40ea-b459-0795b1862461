name: Publish Docker image
on:
  push:
    branches:
      - "master"
    tags:
      - "*"
    paths-ignore:
      - ".gitignore"
      - "**.md"
      - "test/"
jobs:
  build_and_publish:
    strategy:
      matrix:
        include:
          - os: ubuntu-latest
            platform: linux/amd64
            tagsuffix: amd64
          - os: ubuntu-24.04-arm
            platform: linux/arm64
            tagsuffix: arm64
    outputs:
      tag: ${{ steps.extract_tag.outputs.tag }}

    if: ${{ github.repository_owner == 'valhalla' }}
    runs-on: ${{ matrix.os }}

    steps:
      - name: Check out the repo
        uses: actions/checkout@v3
        with:
          submodules: "recursive"
          fetch-depth: 0

      - name: Extract tag name
        if: startsWith(github.ref, 'refs/tags/')
        run: echo "##[set-output name=tag;]${GITHUB_REF#refs/tags/}"
        id: extract_tag

      - name: Get version modifier
        if: github.ref == 'refs/heads/master'
        run: echo "version_modifier=$(git rev-parse --short HEAD)" >> $GITHUB_OUTPUT

      - name: Log in to GitHub Docker Registry
        uses: docker/login-action@v2
        with:
          registry: ghcr.io
          username: ${{ github.actor }}
          password: ${{ secrets.GITHUB_TOKEN }}

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: Build and push latest
        if: github.ref == 'refs/heads/master'
        uses: docker/build-push-action@v6
        with:
          context: .
          provenance: false # needed to be able to converge into one image
          platforms: ${{ matrix.platform }}
          push: true
          tags: ghcr.io/valhalla/valhalla:latest-${{ matrix.tagsuffix }}
          cache-from: type=gha,scope=${{ matrix.platform }}
          cache-to: type=gha,mode=max,scope=${{ matrix.platform }}
          build-args: |
            VERSION_MODIFIER=${{ steps.vars.outputs.version_modifier || "" }}

      - name: Build and push tag
        if: startsWith(github.ref, 'refs/tags/')
        uses: docker/build-push-action@v6
        with:
          context: .
          provenance: false # needed to be able to converge into one image
          platforms: ${{ matrix.platform }}
          push: true
          tags: ghcr.io/valhalla/valhalla:${{ steps.extract_tag.outputs.tag }}-${{ matrix.tagsuffix }}
          cache-from: type=gha,scope=${{ matrix.platform }}
          cache-to: type=gha,mode=max,scope=${{ matrix.platform }}

  create-manifests:
    runs-on: ubuntu-latest
    needs: [build_and_publish]
    steps:
      - name: Log in to GitHub Docker Registry
        uses: docker/login-action@v2
        with:
          registry: ghcr.io
          username: ${{ github.actor }}
          password: ${{ secrets.GITHUB_TOKEN }}

      - name: Create and push tag manifest
        if: startsWith(github.ref, 'refs/tags/')
        run: |
          docker manifest create \
            ghcr.io/valhalla/valhalla:${{ needs.build_and_publish.outputs.tag }} \
            --amend ghcr.io/valhalla/valhalla:${{ needs.build_and_publish.outputs.tag }}-amd64 \
            --amend ghcr.io/valhalla/valhalla:${{ needs.build_and_publish.outputs.tag }}-arm64 
          docker manifest push ghcr.io/valhalla/valhalla:${{ needs.build_and_publish.outputs.tag }}

      - name: Create and push latest manifest
        if: github.ref == 'refs/heads/master'
        run: |
          docker manifest create \
            ghcr.io/valhalla/valhalla:latest \
            --amend ghcr.io/valhalla/valhalla:latest-amd64 \
            --amend ghcr.io/valhalla/valhalla:latest-arm64 
          docker manifest push ghcr.io/valhalla/valhalla:latest

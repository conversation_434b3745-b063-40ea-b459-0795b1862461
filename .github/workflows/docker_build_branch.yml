name: Manually build Docker image and tag with branch name

on:
  workflow_dispatch:

jobs:
  build_and_publish:
    strategy:
      matrix:
        include:
          - os: ubuntu-latest
            platform: linux/amd64
            tagsuffix: amd64
          - os: ubuntu-24.04-arm
            platform: linux/arm64
            tagsuffix: arm64

    if: ${{ github.repository_owner == 'valhalla' }}
    runs-on: ${{ matrix.os }}

    steps:
      - name: Check out the repo
        uses: actions/checkout@v3
        with:
          submodules: "recursive"
          fetch-depth: 0

      - name: Log in to GitHub Docker Registry
        uses: docker/login-action@v2
        with:
          registry: ghcr.io
          username: ${{ github.actor }}
          password: ${{ secrets.GITHUB_TOKEN }}

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: Build and push branch
        uses: docker/build-push-action@v6
        with:
          context: .
          provenance: false # needed to be able to converge into one image
          platforms: ${{ matrix.platform }}
          push: true
          tags: ghcr.io/valhalla/valhalla:${{ github.ref_name }}-${{ matrix.tagsuffix }}
          cache-from: type=gha,scope=${{ matrix.platform }}
          cache-to: type=gha,mode=max,scope=${{ matrix.platform }}

  create-manifests:
    runs-on: ubuntu-latest
    needs: [build_and_publish]
    steps:
      - name: Log in to GitHub Docker Registry
        uses: docker/login-action@v2
        with:
          registry: ghcr.io
          username: ${{ github.actor }}
          password: ${{ secrets.GITHUB_TOKEN }}

      - name: Create and push branch manifest
        run: |
          docker manifest create \
            ghcr.io/valhalla/valhalla:${{ github.ref_name }} \
            --amend ghcr.io/valhalla/valhalla:${{ github.ref_name }}-amd64 \
            --amend ghcr.io/valhalla/valhalla:${{ github.ref_name }}-arm64 
          docker manifest push ghcr.io/valhalla/valhalla:${{ github.ref_name }}

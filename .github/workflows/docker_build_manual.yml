name: Build Manual Amd64 Docker Image

on:
  workflow_dispatch:

jobs:
  build_image:
    name: Manual docker image build
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v3
      with:
        submodules: 'recursive'
        fetch-depth: 0

    - name: Log in to GitHub Docker Registry
      uses: docker/login-action@v2
      with:
        registry: ghcr.io
        username: ${{ github.actor }}
        password: ${{ secrets.GITHUB_TOKEN }}

    - name: Build latest image
      run: |
        docker buildx build --push --platform linux/amd64 --tag ghcr.io/valhalla/valhalla:manually_triggered_build_amd64 .

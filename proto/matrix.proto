syntax = "proto3";
option optimize_for = LITE_RUNTIME;
package valhalla;
import public "common.proto";

message Matrix {
  enum Algorithm {
    TimeDistanceMatrix = 0;
    CostMatrix = 1;
    TimeDistanceBSSMatrix = 2;
  }

  repeated uint32 distances = 2;
  repeated float times = 3;
  repeated uint32 from_indices = 4;
  repeated uint32 to_indices = 5;
  repeated string date_times = 6;
  Algorithm algorithm = 7;
  repeated string shapes = 8;
  repeated string time_zone_offsets = 9;
  repeated string time_zone_names = 10;
  repeated bool second_pass = 11;
  repeated float begin_heading = 12;
  repeated float end_heading = 13;
  repeated double begin_lat = 14;
  repeated double begin_lon = 15;
  repeated double end_lat = 16;
  repeated double end_lon = 17;
}

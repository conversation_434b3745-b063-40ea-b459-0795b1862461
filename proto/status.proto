syntax = "proto3";
option optimize_for = LITE_RUNTIME;
package valhalla;

message Status {
  // oneof's are only returned on verbose=true
  oneof has_has_tiles {
    bool has_tiles = 1;
  }
  oneof has_has_admins {
    bool has_admins = 2;
  }
  oneof has_has_timezones {
    bool has_timezones = 3;
  }
  oneof has_has_live_traffic {
    bool has_live_traffic = 4;
  }
  oneof has_bbox {
    string bbox = 5;
  }
  string version = 6;
  uint32 tileset_last_modified = 7;
  repeated string available_actions = 8;
  oneof has_has_transit_tiles {
    bool has_transit_tiles = 9;
  }
  oneof has_osm_changeset {
    uint64 osm_changeset = 10;
  }
}

syntax = "proto3";
option optimize_for = LITE_RUNTIME;
package valhalla;
import public "common.proto";

message Contour {
  oneof has_time {
    float time = 1;     // minutes
  }
  oneof has_color {
    string color = 2;   // hex color with out # - for example: "ff0000" for red
  }
  oneof has_distance {
    float distance = 3; // kilometers
  }
}

message Ring {
  repeated LatLng coords = 1;
}

enum ShapeMatch {
  walk_or_snap = 0;
  edge_walk = 1;
  map_snap = 2;
}

enum FilterAction {
  no_action = 0;
  exclude = 1;
  include = 2;
}

enum DirectionsType {
  instructions = 0;
  none = 1;
  maneuvers = 2;
}

enum ShapeFormat {
  polyline6 = 0;
  polyline5 = 1;
  geojson = 2;
  no_shape = 3;  // to omit returning the geometry, e.g. default in matrix
}

// use this to select which top level fields should be present in the pbf output
// the actions below are marked for each field that would provide a minimal response
message PbfFieldSelector {
  bool options = 1;
  bool trip = 2;       // /trace_attributes
  bool directions = 3; // /route /trace_route /optimized_route /centroid
  bool status = 4;     // /status
  bool matrix = 5;     // sources_to_targets
  bool isochrone = 6;
  bool expansion = 9;
  // TODO: enable these once we have objects for them
  // bool locate = 7;
  // bool height = 8;
}

message AvoidEdge {
  oneof has_id {
    uint64 id = 1;
  }
  oneof has_percent_along {
    float percent_along = 2;
  }
}

/**
 * Hierarchy limits controls expansion and transitions between hierarchy
 * levels. It allows limiting expansion on hierarchy levels once
 * some number of "upward" transitions have been made (e.g. from the local
 * level to the arterial level). Expansion on a level is allowed within some
 * distance from the destination location. This also allows control of where
 * upward and downward transitions are allowed based on distance from the
 * destination.
 */
message HierarchyLimits {
 uint32 up_transition_count = 1;   // Keep track of # of upward transitions
                                   // Internal use only, remember to clear on incoming requests
 uint32 max_up_transitions = 2;    // Maximum number of upward transitions before 
                                   // expansion is stopped on a level.
 float expand_within_dist = 3; // Distance (m) to destination within which
                                   // expansion of a hierarchy level is
                                   // always allowed. Used for A*.
}

message Costing {
  enum Type {
    none_ = 0;
    bicycle = 1;
    bus = 2;
    motor_scooter = 3;
    multimodal = 4;     // turns into pedestrian + transit
    pedestrian = 5;
    transit = 6;
    truck = 7;
    motorcycle = 8;
    taxi = 9;
    auto_ = 10;
    bikeshare = 11;    // turns into pedestrian + bike
  }

  message Options {
    oneof has_maneuver_penalty {
      float maneuver_penalty = 1;
    }
    oneof has_destination_only_penalty {
      float destination_only_penalty = 2;
    }
    oneof has_gate_cost {
      float gate_cost = 3;
    }
    oneof has_gate_penalty {
      float gate_penalty = 4;
    }
    oneof has_toll_booth_cost {
      float toll_booth_cost = 5;
    }
    oneof has_toll_booth_penalty {
      float toll_booth_penalty = 6;
    }
    oneof has_alley_penalty {
      float alley_penalty = 7;
    }
    oneof has_country_crossing_cost {
      float country_crossing_cost = 8;
    }
    oneof has_country_crossing_penalty {
      float country_crossing_penalty = 9;
    }
    oneof has_ferry_cost {
      float ferry_cost = 10;
    }
    oneof has_avoid_bad_surfaces {
      float avoid_bad_surfaces = 11;
    }
    oneof has_use_ferry {
      float use_ferry = 12;
    }
    oneof has_use_highways {
      float use_highways = 13;
    }
    oneof has_use_tolls {
      float use_tolls = 14;
    }
    oneof has_use_roads {
      float use_roads = 15;
    }
    oneof has_max_distance {
      uint32 max_distance = 16;
    }
    oneof has_walking_speed {
      float walking_speed = 17;
    }
    oneof has_step_penalty {
      float step_penalty = 18;
    }
    oneof has_max_grade {
      uint32 max_grade = 19;
    }
    oneof has_max_hiking_difficulty {
      uint32 max_hiking_difficulty = 20;
    }
    oneof has_mode_factor {
      float mode_factor = 21;
    }
    oneof has_walkway_factor {
      float walkway_factor = 22;
    }
    oneof has_sidewalk_factor {
      float sidewalk_factor = 23;
    }
    oneof has_alley_factor {
      float alley_factor = 24;
    }
    oneof has_driveway_factor {
      float driveway_factor = 25;
    }
    oneof has_driveway_penalty {
      float driveway_penalty = 26;
    }
    oneof has_transit_start_end_max_distance {
      uint32 transit_start_end_max_distance = 27;
    }
    oneof has_transit_transfer_max_distance {
      uint32 transit_transfer_max_distance = 28;
    }
    oneof has_transport_type {
      string transport_type = 29;
    }
    oneof has_top_speed {
      float top_speed = 30;
    }
    oneof has_use_hills {
      float use_hills = 31;
    }
    oneof has_use_primary {
      float use_primary = 32;
    }
    oneof has_use_trails {
      float use_trails = 33;
    }
    oneof has_low_class_penalty {
      float low_class_penalty = 34;
    }
    oneof has_hazmat {
      bool hazmat = 35;
    }
    oneof has_weight {
      float weight = 36;
    }
    oneof has_axle_load {
      float axle_load = 37;
    }
    oneof has_height {
      float height = 38;
    }
    oneof has_width {
      float width = 39;
    }
    oneof has_length {
      float length = 40;
    }
    oneof has_cycling_speed {
      float cycling_speed = 41;
    }
    oneof has_wheelchair {
      bool wheelchair = 42;
    }
    oneof has_bicycle {
      bool bicycle = 43;
    }
    oneof has_use_bus {
      float use_bus = 44;
    }
    oneof has_use_rail {
      float use_rail = 45;
    }
    oneof has_use_transfers {
      float use_transfers = 46;
    }
    oneof has_transfer_cost {
      float transfer_cost = 47;
    }
    oneof has_transfer_penalty {
      float transfer_penalty = 48;
    }
    FilterAction filter_stop_action = 49;
    repeated string filter_stop_ids = 50;
    FilterAction filter_operator_action = 51;
    repeated string filter_operator_ids = 52;
    FilterAction filter_route_action = 53;
    repeated string filter_route_ids = 54;
    oneof has_flow_mask {
      uint32 flow_mask = 55;
    }
    oneof has_bike_share_cost {
      float bike_share_cost = 56;
    }
    oneof has_bike_share_penalty {
      float bike_share_penalty = 57;
    }
    oneof has_rail_ferry_cost {
      float rail_ferry_cost = 58;
    }
    oneof has_use_rail_ferry {
      float use_rail_ferry = 59;
    }
    oneof has_ignore_restrictions {
      bool ignore_restrictions = 60;
    }
    oneof has_ignore_oneways {
      bool ignore_oneways = 61;
    }
    oneof has_ignore_access {
      bool ignore_access = 62;
    }
    oneof has_ignore_closures {
      bool ignore_closures = 63;
    }
    oneof has_shortest {
      bool shortest = 64;
    }
    oneof has_service_penalty {
      float service_penalty = 65;
    }
    oneof has_use_tracks {
      float use_tracks = 66;
    }
    oneof has_use_distance {
      float use_distance = 67;
    }
    oneof has_use_living_streets {
      float use_living_streets = 68;
    }
    oneof has_service_factor {
      float service_factor = 69;
    }
    oneof has_closure_factor {
      float closure_factor = 70;
    }
    oneof has_private_access_penalty {
      float private_access_penalty = 71;
    }
    oneof has_exclude_unpaved {
      bool exclude_unpaved = 72;
    }
    oneof has_include_hot {
      bool include_hot = 73;
    }
    oneof has_include_hov2 {
      bool include_hov2 = 74;
    }
    oneof has_include_hov3 {
      bool include_hov3 = 75;
    }
    oneof has_exclude_cash_only_tolls {
      bool exclude_cash_only_tolls = 76;
    }
    oneof has_restriction_probability {
      uint32 restriction_probability = 77;
    }
    repeated AvoidEdge exclude_edges = 78;
    oneof has_elevator_penalty {
      float elevator_penalty = 79;
    }
    uint32 fixed_speed = 80;
    uint32 axle_count = 81;
    float use_lit = 82;
    bool disable_hierarchy_pruning = 83;
    bool ignore_non_vehicular_restrictions = 84;
    oneof has_hgv_no_access_penalty {
      float hgv_no_access_penalty = 85;
    }
    float use_truck_route = 86;
    bool exclude_bridges = 87;
    bool exclude_tunnels = 88;
    bool exclude_tolls = 89;
    bool exclude_highways = 90;
    bool exclude_ferries = 91;
    map<uint32, HierarchyLimits> hierarchy_limits = 92;
    bool ignore_construction = 93;
  }

  oneof has_options {
    Options options = 1;
  }
  Type type = 2;
  oneof has_name {
    string name = 3;
  }

  // this is used internally only, setting it in your request will have no effect
  oneof has_filter_closures {
    bool filter_closures = 4;
  }
}

message Options {

  enum Units {
    kilometers = 0;
    miles = 1;
  }

  enum Format {
    json = 0;
    gpx = 1;
    osrm = 2;
    pbf = 3;
    geotiff = 4;
  }

  enum Action {
    no_action = 0;
    route = 1;
    locate = 2;
    sources_to_targets = 3;
    optimized_route = 4;
    isochrone = 5;
    trace_route = 6;
    trace_attributes = 7;
    height = 8;
    transit_available = 9;
    expansion = 10;
    centroid = 11;
    status = 12;
  }

  enum DateTimeType {
    no_time  = 0;
    current = 1;
    depart_at = 2;
    arrive_by = 3;
    invariant = 4;
  }

  enum ExpansionProperties {
    cost = 0;
    duration = 1;
    distance = 2;
    edge_status = 3;
    edge_id = 4;
    pred_edge_id = 5;
    expansion_type = 6;
  }

  Units units = 1;                                                 // kilometers or miles
  oneof has_language {
    string language = 2;                                           // Based on IETF BCP 47 language tag string [default = "en-US"]
  }
  DirectionsType directions_type = 3;                              // Enable/disable narrative production [default = instructions]
  Format format = 4;                                               // What the response format should be [default = json]
  oneof has_id {
    string id = 5;                                                 // id for the request
  }
  oneof has_jsonp {
    string jsonp = 6;                                              // javascript callback for the request
  }
  oneof has_encoded_polyline {
    string encoded_polyline = 7;                                   // polyline 6 encoded shape used in /height /trace_*
  }
  Action action = 8;                                               // Action signifying the request type
  //deprecated = 9;
  oneof has_range {
    bool range = 10;                                               // Used in /height if the range between points should be serialized  [default = false]
  }
  // verbose needs to stay oneof, so that matrix serializer can default to true
  oneof has_verbose {
    bool verbose = 11;                                             // Used in /locate & /status request to give back extensive information [default = false]
  }
  Costing.Type costing_type = 12;                                  // The main costing to use with the action, in multimodal this is the first costing to use
  map<int32, Costing> costings = 13;                               // A map of Costing.Type enum to its Costing object
  repeated Location locations = 14;                                // Locations for /route /optimized /locate /isochrone
  repeated Location exclude_locations = 15;                        // Avoids for any costing
  repeated Location sources = 16;                                  // Sources for /sources_to_targets
  repeated Location targets = 17;                                  // Targets for /sources_to_targets
  DateTimeType date_time_type = 18;                                // Are you leaving now or then or arriving then
  oneof has_date_time {
    string date_time = 19;                                         // And what day and time
  }
  repeated Location shape = 20;                                    // Raw shape for map matching
  oneof has_resample_distance {
    double resample_distance = 21;                                 // Resampling shape at regular intervals
  }
  repeated Contour contours = 22;                                  // List of isochrone contours
  oneof has_polygons {
    bool polygons = 23;                                            // Boolean value to determine whether to return geojson polygons or linestrings as the contours
  }
  oneof has_denoise {
    float denoise = 24;                                            // A floating point value from 0 to 1 which can be used to remove smaller contours (default 1.0)
  }
  oneof has_generalize {
    float generalize = 25;                                         // Meters used as the tolerance for Douglas-Peucker generalization
  }
  oneof has_show_locations {
    bool show_locations = 26;                                      // Add original locations to the isochrone geojson response
  }
  repeated Location trace = 27;                                    // Trace points for map matching
  ShapeMatch shape_match = 28;                                     // The matching algorithm based on the type of input [default = walk_or_snap]
  //deprecated = 29;
  oneof has_gps_accuracy {
    float gps_accuracy = 30;                                       // The gps accuracy associated with the supplied trace points
  }
  oneof has_search_radius {
    float search_radius = 31;                                      // The search radius associated with the supplied trace points
  }
  oneof has_turn_penalty_factor {
    float turn_penalty_factor = 32;                                // The turn penalty factor associated with the supplied trace points
  }
  FilterAction filter_action = 33;                                 // The trace filter action - either exclude or include
  repeated string filter_attributes = 34;                          // The filter list for trace attributes
  oneof has_breakage_distance {
    float breakage_distance = 36;                                  // Map-matching breaking distance (distance between GPS trace points)
  }
  oneof has_use_timestamps {
    bool use_timestamps = 37;                                      // Use timestamps to compute elapsed time for trace_route and trace_attributes [default = false]
  }
  oneof has_shape_format {
    ShapeFormat shape_format = 38;                                 // Shape format, defaults to polyline6 encoding for OSRM/height, and no_shape for matrix
  }
  oneof has_alternates {
    uint32 alternates = 39;                                        // Maximum number of alternate routes that can be returned
  }
  oneof has_interpolation_distance {
    float interpolation_distance = 40;                             // Map-matching interpolation distance beyond which trace points are merged
  }
  oneof has_guidance_views {
    bool guidance_views = 41;                                      // Whether to return guidance_views in the response
  }
  // 42 is reserved
  oneof has_height_precision {
    uint32 height_precision = 43;                                  // Number of digits precision for heights returned [default = 0]
  }
  oneof has_roundabout_exits {
    bool roundabout_exits = 44;                                    // Whether to announce roundabout exit maneuvers [default = true]
  }
  oneof has_linear_references {
    bool linear_references = 45;                                   // Include linear references for graph edges returned in certain responses.
  }
  repeated Costing recostings = 46;                                // CostingType options to use to recost a path after it has been found
  repeated Ring exclude_polygons = 47;                                    // Rings/polygons to exclude entire areas during path finding
  oneof has_prioritize_bidirectional {
    bool prioritize_bidirectional = 48;                            // Prioritize bidirectional a*/matrix when depart_at date_time.type is specified [default = false]
  }
  oneof has_expansion_action {
    Action expansion_action = 49;                                  // Meta action for /expansion endpoint
  }
  oneof has_skip_opposites {
    bool skip_opposites = 50;                                      // Whether to return opposite edges encountered during expansion
  }
  repeated ExpansionProperties expansion_properties = 51;          // The array keys (ExpansionTypes enum) to return in the /expansions's GeoJSON "properties"
  PbfFieldSelector pbf_field_selector = 52;                        // Which pbf fields to include in the pbf format response
  bool reverse = 53;                                               // should the isochrone expansion be done in the reverse direction, ignored for multimodal isochrones
  oneof has_matrix_locations {                                     // Number of matrix locations found that will trigger an early exit from
    uint32 matrix_locations = 54;                                  // a one to many or many to one time distance matrix. Does not affect
  }                                                                // sources_to_targets when either sources or targets has more than 1 location
                                                                   // or when CostMatrix is the selected matrix mode.
  bool banner_instructions = 55;                                   // Whether to return bannerInstructions in the OSRM serializer response
  float elevation_interval = 56;                                   // Interval for sampling elevation along the route path.  [default = 0.0];
  bool voice_instructions = 57;                                    // Whether to return voiceInstructions in the OSRM serializer response
  bool dedupe = 58;                                                // Keep track of edges and override their properties during expansion,
                                                                   // ensuring that each edge appears in the output only once. [default = false]
  bool admin_crossings = 59;                                       // Include administrative boundary crossings
  bool turn_lanes = 60;                                            // Include turn lane information into Valhalla serializer response.
}

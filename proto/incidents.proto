// File contains definitions for side-loaded data-structures
//
// Because of the more distributed nature of this data
// compared to the previous protobuf structures used in Valhalla
// it is important to follow the protobuf schema evolution rules
// when modifying this file

syntax = "proto3";
option optimize_for = LITE_RUNTIME;
package valhalla;

message IncidentsTile {
  // Sorted list of edge_ids describing what incidents are attached to an edge_id
  repeated Location locations = 1;
  // Look at `incident_locations` to find how to index this array
  repeated Metadata metadata = 2;

  // Links a portion of an edge to incident metadata
  message Location {
    uint32 edge_index = 1;
    float start_offset = 2;
    float end_offset = 3;
    uint32 metadata_index = 4;
  }

  // A single incident is described by this
  // TODO This is not yet finalized
  message Metadata {
    enum Type {
      ACCIDENT = 0;
      CONGESTION = 1;
      CONSTRUCTION = 2;
      DISABLED_VEHICLE = 3;
      LANE_RESTRICTION = 4;
      MASS_TRANSIT = 5;
      MISCELLANEOUS = 6;
      OTHER_NEWS = 7;
      PLANNED_EVENT = 8;
      ROAD_CLOSURE = 9;
      ROAD_HAZARD = 10;
      WEATHER = 11;
    }
    Type type = 1;
    repeated uint32 alertc_codes = 2;
    string description = 3;
    string sub_type = 4;
    string sub_type_description = 5;
    uint64 start_time = 6;
    uint64 end_time = 7;
    enum Impact {
      UNKNOWN = 0;
      CRITICAL = 1;
      MAJOR = 2;
      MINOR = 3;
      LOW = 4;
    }
    Impact impact = 9;
    bool road_closed = 10;
    message Congestion {
      uint32 value = 1;
    }
    Congestion congestion = 11;
    repeated string lanes_blocked = 12;
    uint64 creation_time = 13;
    string long_description = 14;
    string clear_lanes = 15;
    uint64 num_lanes_blocked = 16;
    uint32 length = 17;  // Length of incident as matched to road graph

    // IncidentMetadata id
    uint64 id = 128;
    // Country code (2 & 3 char codes)
    string iso_3166_1_alpha2 = 129;
    string iso_3166_1_alpha3 = 130;
  }
}

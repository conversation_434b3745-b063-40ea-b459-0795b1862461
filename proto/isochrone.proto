syntax = "proto3";

option optimize_for = LITE_RUNTIME;
package valhalla;

message Isochrone {

  enum metric_type {
    time = 0;
    distance = 1;
  }

  message Geometry {
    repeated sint32 coords = 1 [packed=true];
  }

  message Contour {
    repeated Geometry geometries = 2; // if polygon first one is outer rest are inners, though this is a problem when we allow multi location isochrones
  }

  message Interval {
    metric_type metric = 1; // time or distance enum
    float metric_value = 2; // the target metric, eg 15min
    repeated Contour contours = 3;
  }

  repeated Interval intervals = 1;
}
syntax = "proto3";
option optimize_for = LITE_RUNTIME;
package valhalla;
import public "common.proto";
import public "sign.proto";

message DirectionsLeg {


  message GuidanceView {
    enum Type{
      kJunction = 0;
      kSapa = 1;
      kTollbranch = 2;
      kAftertoll = 3;
      kEnt = 4;
      kExit = 5;
      kCityreal = 6;
      kDirectionboard = 7;
      kSignboard = 8;
    }
    string data_id = 1;        // TODO future enum as data id?
    Type type = 2;             // The type of guidance view
    string base_id = 3;        // Image base id
    repeated string overlay_ids = 4;    // List of overlay ids
  }

  message Maneuver {
    enum CardinalDirection {
      kNorth = 0;
      kNorthEast = 1;
      kEast = 2;
      kSouthEast = 3;
      kSouth = 4;
      kSouthWest = 5;
      kWest = 6;
      kNorthWest = 7;
    }

    // TODO - add others later
    enum Type {
      kNone = 0;
      kStart = 1;
      kStartRight = 2;
      kStartLeft = 3;
      kDestination = 4;
      kDestinationRight = 5;
      kDestinationLeft = 6;
      kBecomes = 7;
      kContinue = 8;
      kSlightRight = 9;
      kRight = 10;
      kSharpRight = 11;
      kUturnRight = 12;
      kUturnLeft = 13;
      kSharpLeft = 14;
      kLeft = 15;
      kSlightLeft = 16;
      kRampStraight = 17;
      kRampRight = 18;
      kRampLeft = 19;
      kExitRight = 20;
      kExitLeft = 21;
      kStayStraight = 22;
      kStayRight = 23;
      kStayLeft = 24;
      kMerge = 25;
      kRoundaboutEnter = 26;
      kRoundaboutExit = 27;
      kFerryEnter = 28;
      kFerryExit = 29;
      kTransit = 30;
      kTransitTransfer = 31;
      kTransitRemainOn = 32;
      kTransitConnectionStart = 33;
      kTransitConnectionTransfer = 34;
      kTransitConnectionDestination = 35;
      kPostTransitConnectionDestination = 36;
      kMergeRight = 37;
      kMergeLeft = 38;
      kElevatorEnter = 39;
      kStepsEnter = 40;
      kEscalatorEnter = 41;
      kBuildingEnter = 42;
      kBuildingExit = 43;
    }

    enum BssManeuverType{
      kNoneAction = 0;
      kRentBikeAtBikeShare = 1;
      kReturnBikeAtBikeShare = 2;
    }

    Type type = 1;                                        // Maneuver type
    string text_instruction = 2;                          // text instruction
    repeated StreetName street_name = 3;                  // street names
    float length = 4;                                     // kilometers or miles based on units
    double time = 5;                                      // seconds
    CardinalDirection begin_cardinal_direction = 6;       // CardinalDirection
    uint32 begin_heading = 7;                             // 0-360
    uint32 begin_shape_index = 8;                         // inclusive index
    uint32 end_shape_index = 9;                           // inclusive index
    bool portions_toll = 10;                              // has portions toll
    bool portions_unpaved = 11;                           // has portions unpaved
    string verbal_transition_alert_instruction = 12;      // verbal transition alert instruction
    string verbal_pre_transition_instruction = 13;        // verbal pre-transition instruction
    string verbal_post_transition_instruction = 14;       // verbal post-transition instruction
    repeated StreetName begin_street_name = 15;           // begin street names
    TripSign sign = 16;                                   // associated sign information, for example: exit number
    uint32 roundabout_exit_count = 17;                    // which spoke to exit roundabout after entering
    string depart_instruction = 18;                       // depart instruction - used with transit
    string verbal_depart_instruction = 19;                // verbal depart instruction - used with transit
    string arrive_instruction = 20;                       // arrive instruction - used with transit
    string verbal_arrive_instruction = 21;                // verbal arrive instruction - used with transit
    TransitRouteInfo transit_info = 22;                   // transit attributes including a list of transit stops
    bool verbal_multi_cue = 23;                           // verbal multi-cue flag
    TravelMode travel_mode = 24;                          // travel mode
    VehicleType vehicle_type = 25;
    PedestrianType pedestrian_type = 26;
    BicycleType bicycle_type = 27;
    TransitType transit_type = 28;
    uint32 begin_path_index = 29;                         // Index in TripPath for first node of maneuver
    uint32 end_path_index = 30;                           // Index in TripPath for last node of maneuver
    bool to_stay_on = 31;                                 // True if same name as previous maneuver
    repeated StreetName roundabout_exit_street_names = 32;// Outbound street names from roundabout
    uint32 turn_degree = 33;                              // Turn degree of maneuver
    bool has_time_restrictions = 34;                      // Whether edge has any time restrictions or not
    repeated GuidanceView guidance_views = 35;            // List of guidance views
    BssManeuverType bss_maneuver_type = 36;
    string verbal_succinct_transition_instruction = 37;   // verbal succinct transition instruction
    BikeShareStationInfo bss_info = 38;                   // Bike Share Station Info
    bool portions_highway = 39;                           // has portions highway
    bool portions_ferry = 40;                             // has portions ferry
    repeated RouteLandmark landmarks = 41;                // landmarks correlated with the maneuver
  }

  uint64 trip_id = 1;
  uint32 leg_id = 2;
  uint32 leg_count = 3;
  repeated Location location = 4;
  Summary summary = 5;
  repeated Maneuver maneuver = 6;
  string shape = 7;
  repeated LevelChange level_changes = 8;
}

message DirectionsRoute {
  repeated DirectionsLeg legs = 1;
}

message Directions {
  repeated DirectionsRoute routes = 1;
}

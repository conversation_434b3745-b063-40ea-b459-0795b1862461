{"_comment": ["Sample CMakeSettings.json for building with CMake integration in Visual Studio 2017.", "See https://go.microsoft.com//fwlink//?linkid=834763 for more information.", "Usage:", "Copy cmake/CMkeSettings.json to top-level directory of Valhalla source tree", "Then, launch Visual Studio 2017 > File > Open > CMake > select top-level CMakeLists.txt", "See Valhalla top-level CMakeLists.txt for complete list of options available to be passed via -D to cmake."], "environments": [{"BuildDir": "${workspaceRoot}\\_build"}, {"InstallDir": "${workspaceRoot}\\_install"}, {"VALHALLA_D_DEFAULT_ENABLE_CCACHE": "OFF"}, {"VALHALLA_D_DEFAULT_ENABLE_COVERAGE": "OFF"}, {"VALHALLA_D_DEFAULT_ENABLE_SANITIZERS": "OFF"}, {"VALHALLA_D_DEFAULT_ENABLE_ADDRESS_SANITIZER": "OFF"}, {"VALHALLA_D_DEFAULT_ENABLE_UNDEFINED_SANITIZER": "OFF"}, {"VALHALLA_D_DEFAULT_ENABLE_DATA_TOOLS": "OFF"}, {"VALHALLA_D_DEFAULT_ENABLE_HTTP": "OFF"}, {"VALHALLA_D_DEFAULT_ENABLE_PYTHON_BINDINGS": "OFF"}, {"VALHALLA_D_DEFAULT_ENABLE_SERVICES": "OFF"}, {"VALHALLA_D_DEFAULT_ENABLE_TESTS": "OFF"}, {"VALHALLA_D_DEFAULT_ENABLE_TOOLS": "OFF"}, {"VALHALLA_D_DEFAULT_LOGGING_LEVEL": "INFO"}], "configurations": [{"name": "x64-Debug-Ninja", "generator": "Ninja", "configurationType": "Debug", "inheritEnvironments": ["msvc_x64_x64"], "buildRoot": "${env.BuildDir}\\${name}", "installRoot": "${env.InstallDir}\\${name}", "cmakeCommandArgs": "", "buildCommandArgs": "-v", "ctestCommandArgs": "", "variables": [{"name": "ENABLE_CCACHE", "value": "${env.VALHALLA_D_DEFAULT_ENABLE_CCACHE}"}, {"name": "ENABLE_COVERAGE", "value": "${env.VALHALLA_D_DEFAULT_ENABLE_COVERAGE}"}, {"name": "ENABLE_SANITIZERS", "value": "${env.VALHALLA_D_DEFAULT_ENABLE_SANITIZERS}"}, {"name": "ENABLE_ADDRESS_SANITIZER", "value": "${env.VALHALLA_D_DEFAULT_ENABLE_ADDRESS_SANITIZER}"}, {"name": "ENABLE_UNDEFINED_SANITIZER", "value": "${env.VALHALLA_D_DEFAULT_ENABLE_UNDEFINED_SANITIZER}"}, {"name": "ENABLE_DATA_TOOLS", "value": "${env.VALHALLA_D_DEFAULT_ENABLE_DATA_TOOLS}"}, {"name": "ENABLE_HTTP", "value": "${env.VALHALLA_D_DEFAULT_ENABLE_HTTP}"}, {"name": "ENABLE_PYTHON_BINDINGS", "value": "${env.VALHALLA_D_DEFAULT_ENABLE_PYTHON_BINDINGS}"}, {"name": "ENABLE_SERVICES", "value": "${env.VALHALLA_D_DEFAULT_ENABLE_SERVICES}"}, {"name": "ENABLE_TESTS", "value": "${env.VALHALLA_D_DEFAULT_ENABLE_TESTS}"}, {"name": "ENABLE_TOOLS", "value": "${env.VALHALLA_D_DEFAULT_ENABLE_TOOLS}"}, {"name": "LOGGING_LEVEL", "value": "${env.VALHALLA_D_DEFAULT_LOGGING_LEVEL}"}]}, {"name": "x64-Release-<PERSON>", "generator": "Ninja", "configurationType": "RelWithDebInfo", "inheritEnvironments": ["msvc_x64_x64"], "buildRoot": "${env.BuildDir}\\${name}", "installRoot": "${env.InstallDir}\\${name}", "cmakeCommandArgs": "", "buildCommandArgs": "-v", "ctestCommandArgs": "", "variables": [{"name": "ENABLE_CCACHE", "value": "${env.VALHALLA_D_DEFAULT_ENABLE_CCACHE}"}, {"name": "ENABLE_COVERAGE", "value": "${env.VALHALLA_D_DEFAULT_ENABLE_COVERAGE}"}, {"name": "ENABLE_SANITIZERS", "value": "${env.VALHALLA_D_DEFAULT_ENABLE_SANITIZERS}"}, {"name": "ENABLE_ADDRESS_SANITIZER", "value": "${env.VALHALLA_D_DEFAULT_ENABLE_ADDRESS_SANITIZER}"}, {"name": "ENABLE_UNDEFINED_SANITIZER", "value": "${env.VALHALLA_D_DEFAULT_ENABLE_UNDEFINED_SANITIZER}"}, {"name": "ENABLE_DATA_TOOLS", "value": "${env.VALHALLA_D_DEFAULT_ENABLE_DATA_TOOLS}"}, {"name": "ENABLE_HTTP", "value": "${env.VALHALLA_D_DEFAULT_ENABLE_HTTP}"}, {"name": "ENABLE_PYTHON_BINDINGS", "value": "${env.VALHALLA_D_DEFAULT_ENABLE_PYTHON_BINDINGS}"}, {"name": "ENABLE_SERVICES", "value": "${env.VALHALLA_D_DEFAULT_ENABLE_SERVICES}"}, {"name": "ENABLE_TESTS", "value": "${env.VALHALLA_D_DEFAULT_ENABLE_TESTS}"}, {"name": "ENABLE_TOOLS", "value": "${env.VALHALLA_D_DEFAULT_ENABLE_TOOLS}"}, {"name": "LOGGING_LEVEL", "value": "${env.VALHALLA_D_DEFAULT_LOGGING_LEVEL}"}]}, {"name": "x86-Debug-Ninja", "generator": "Ninja", "configurationType": "Debug", "inheritEnvironments": ["msvc_x86"], "buildRoot": "${env.BuildDir}\\${name}", "installRoot": "${env.InstallDir}\\${name}", "cmakeCommandArgs": "", "buildCommandArgs": "-v", "ctestCommandArgs": "", "variables": [{"name": "ENABLE_CCACHE", "value": "${env.VALHALLA_D_DEFAULT_ENABLE_CCACHE}"}, {"name": "ENABLE_COVERAGE", "value": "${env.VALHALLA_D_DEFAULT_ENABLE_COVERAGE}"}, {"name": "ENABLE_SANITIZERS", "value": "${env.VALHALLA_D_DEFAULT_ENABLE_SANITIZERS}"}, {"name": "ENABLE_ADDRESS_SANITIZER", "value": "${env.VALHALLA_D_DEFAULT_ENABLE_ADDRESS_SANITIZER}"}, {"name": "ENABLE_UNDEFINED_SANITIZER", "value": "${env.VALHALLA_D_DEFAULT_ENABLE_UNDEFINED_SANITIZER}"}, {"name": "ENABLE_DATA_TOOLS", "value": "${env.VALHALLA_D_DEFAULT_ENABLE_DATA_TOOLS}"}, {"name": "ENABLE_HTTP", "value": "${env.VALHALLA_D_DEFAULT_ENABLE_HTTP}"}, {"name": "ENABLE_PYTHON_BINDINGS", "value": "${env.VALHALLA_D_DEFAULT_ENABLE_PYTHON_BINDINGS}"}, {"name": "ENABLE_SERVICES", "value": "${env.VALHALLA_D_DEFAULT_ENABLE_SERVICES}"}, {"name": "ENABLE_TESTS", "value": "${env.VALHALLA_D_DEFAULT_ENABLE_TESTS}"}, {"name": "ENABLE_TOOLS", "value": "${env.VALHALLA_D_DEFAULT_ENABLE_TOOLS}"}, {"name": "LOGGING_LEVEL", "value": "${env.VALHALLA_D_DEFAULT_LOGGING_LEVEL}"}]}, {"name": "x86-Release-<PERSON>", "generator": "Ninja", "configurationType": "RelWithDebInfo", "inheritEnvironments": ["msvc_x86"], "buildRoot": "${env.BuildDir}\\${name}", "installRoot": "${env.InstallDir}\\${name}", "cmakeCommandArgs": "", "buildCommandArgs": "-v", "ctestCommandArgs": "", "variables": [{"name": "ENABLE_CCACHE", "value": "${env.VALHALLA_D_DEFAULT_ENABLE_CCACHE}"}, {"name": "ENABLE_COVERAGE", "value": "${env.VALHALLA_D_DEFAULT_ENABLE_COVERAGE}"}, {"name": "ENABLE_SANITIZERS", "value": "${env.VALHALLA_D_DEFAULT_ENABLE_SANITIZERS}"}, {"name": "ENABLE_ADDRESS_SANITIZER", "value": "${env.VALHALLA_D_DEFAULT_ENABLE_ADDRESS_SANITIZER}"}, {"name": "ENABLE_UNDEFINED_SANITIZER", "value": "${env.VALHALLA_D_DEFAULT_ENABLE_UNDEFINED_SANITIZER}"}, {"name": "ENABLE_DATA_TOOLS", "value": "${env.VALHALLA_D_DEFAULT_ENABLE_DATA_TOOLS}"}, {"name": "ENABLE_PYTHON_BINDINGS", "value": "${env.VALHALLA_D_DEFAULT_ENABLE_PYTHON_BINDINGS}"}, {"name": "ENABLE_SERVICES", "value": "${env.VALHALLA_D_DEFAULT_ENABLE_SERVICES}"}, {"name": "ENABLE_TESTS", "value": "${env.VALHALLA_D_DEFAULT_ENABLE_TESTS}"}, {"name": "ENABLE_TOOLS", "value": "${env.VALHALLA_D_DEFAULT_ENABLE_TOOLS}"}, {"name": "LOGGING_LEVEL", "value": "${env.VALHALLA_D_DEFAULT_LOGGING_LEVEL}"}]}, {"name": "x64-Debug-VS2017", "generator": "Visual Studio 15 2017 Win64", "configurationType": "Debug", "buildRoot": "${env.BuildDir}\\${name}", "installRoot": "${env.InstallDir}\\${name}", "buildCommandArgs": "-m", "cmakeCommandArgs": "", "ctestCommandArgs": "", "variables": [{"name": "ENABLE_CCACHE", "value": "${env.VALHALLA_D_DEFAULT_ENABLE_CCACHE}"}, {"name": "ENABLE_COVERAGE", "value": "${env.VALHALLA_D_DEFAULT_ENABLE_COVERAGE}"}, {"name": "ENABLE_SANITIZERS", "value": "${env.VALHALLA_D_DEFAULT_ENABLE_SANITIZERS}"}, {"name": "ENABLE_ADDRESS_SANITIZER", "value": "${env.VALHALLA_D_DEFAULT_ENABLE_ADDRESS_SANITIZER}"}, {"name": "ENABLE_UNDEFINED_SANITIZER", "value": "${env.VALHALLA_D_DEFAULT_ENABLE_UNDEFINED_SANITIZER}"}, {"name": "ENABLE_DATA_TOOLS", "value": "${env.VALHALLA_D_DEFAULT_ENABLE_DATA_TOOLS}"}, {"name": "ENABLE_HTTP", "value": "${env.VALHALLA_D_DEFAULT_ENABLE_HTTP}"}, {"name": "ENABLE_PYTHON_BINDINGS", "value": "${env.VALHALLA_D_DEFAULT_ENABLE_PYTHON_BINDINGS}"}, {"name": "ENABLE_SERVICES", "value": "${env.VALHALLA_D_DEFAULT_ENABLE_SERVICES}"}, {"name": "ENABLE_TESTS", "value": "${env.VALHALLA_D_DEFAULT_ENABLE_TESTS}"}, {"name": "ENABLE_TOOLS", "value": "${env.VALHALLA_D_DEFAULT_ENABLE_TOOLS}"}, {"name": "LOGGING_LEVEL", "value": "${env.VALHALLA_D_DEFAULT_LOGGING_LEVEL}"}]}, {"name": "x64-Release-VS2017", "generator": "Visual Studio 15 2017 Win64", "configurationType": "Release", "buildRoot": "${env.BuildDir}\\${name}", "installRoot": "${env.InstallDir}\\${name}", "buildCommandArgs": "-m", "cmakeCommandArgs": "", "ctestCommandArgs": "", "variables": [{"name": "ENABLE_CCACHE", "value": "${env.VALHALLA_D_DEFAULT_ENABLE_CCACHE}"}, {"name": "ENABLE_COVERAGE", "value": "${env.VALHALLA_D_DEFAULT_ENABLE_COVERAGE}"}, {"name": "ENABLE_SANITIZERS", "value": "${env.VALHALLA_D_DEFAULT_ENABLE_SANITIZERS}"}, {"name": "ENABLE_ADDRESS_SANITIZER", "value": "${env.VALHALLA_D_DEFAULT_ENABLE_ADDRESS_SANITIZER}"}, {"name": "ENABLE_UNDEFINED_SANITIZER", "value": "${env.VALHALLA_D_DEFAULT_ENABLE_UNDEFINED_SANITIZER}"}, {"name": "ENABLE_DATA_TOOLS", "value": "${env.VALHALLA_D_DEFAULT_ENABLE_DATA_TOOLS}"}, {"name": "ENABLE_HTTP", "value": "${env.VALHALLA_D_DEFAULT_ENABLE_HTTP}"}, {"name": "ENABLE_PYTHON_BINDINGS", "value": "${env.VALHALLA_D_DEFAULT_ENABLE_PYTHON_BINDINGS}"}, {"name": "ENABLE_SERVICES", "value": "${env.VALHALLA_D_DEFAULT_ENABLE_SERVICES}"}, {"name": "ENABLE_TESTS", "value": "${env.VALHALLA_D_DEFAULT_ENABLE_TESTS}"}, {"name": "ENABLE_TOOLS", "value": "${env.VALHALLA_D_DEFAULT_ENABLE_TOOLS}"}, {"name": "LOGGING_LEVEL", "value": "${env.VALHALLA_D_DEFAULT_LOGGING_LEVEL}"}]}, {"name": "x86-Debug-VS2017", "generator": "Visual Studio 15 2017", "configurationType": "Debug", "buildRoot": "${env.BuildDir}\\${name}", "installRoot": "${env.InstallDir}\\${name}", "buildCommandArgs": "-m", "cmakeCommandArgs": "", "ctestCommandArgs": "", "variables": [{"name": "ENABLE_CCACHE", "value": "${env.VALHALLA_D_DEFAULT_ENABLE_CCACHE}"}, {"name": "ENABLE_COVERAGE", "value": "${env.VALHALLA_D_DEFAULT_ENABLE_COVERAGE}"}, {"name": "ENABLE_SANITIZERS", "value": "${env.VALHALLA_D_DEFAULT_ENABLE_SANITIZERS}"}, {"name": "ENABLE_ADDRESS_SANITIZER", "value": "${env.VALHALLA_D_DEFAULT_ENABLE_ADDRESS_SANITIZER}"}, {"name": "ENABLE_UNDEFINED_SANITIZER", "value": "${env.VALHALLA_D_DEFAULT_ENABLE_UNDEFINED_SANITIZER}"}, {"name": "ENABLE_DATA_TOOLS", "value": "${env.VALHALLA_D_DEFAULT_ENABLE_DATA_TOOLS}"}, {"name": "ENABLE_HTTP", "value": "${env.VALHALLA_D_DEFAULT_ENABLE_HTTP}"}, {"name": "ENABLE_PYTHON_BINDINGS", "value": "${env.VALHALLA_D_DEFAULT_ENABLE_PYTHON_BINDINGS}"}, {"name": "ENABLE_SERVICES", "value": "${env.VALHALLA_D_DEFAULT_ENABLE_SERVICES}"}, {"name": "ENABLE_TESTS", "value": "${env.VALHALLA_D_DEFAULT_ENABLE_TESTS}"}, {"name": "ENABLE_TOOLS", "value": "${env.VALHALLA_D_DEFAULT_ENABLE_TOOLS}"}, {"name": "LOGGING_LEVEL", "value": "${env.VALHALLA_D_DEFAULT_LOGGING_LEVEL}"}]}, {"name": "x86-Release-VS2017", "generator": "Visual Studio 15 2017", "configurationType": "Release", "buildRoot": "${env.BuildDir}\\${name}", "installRoot": "${env.InstallDir}\\${name}", "buildCommandArgs": "-m", "cmakeCommandArgs": "", "ctestCommandArgs": "", "variables": [{"name": "ENABLE_CCACHE", "value": "${env.VALHALLA_D_DEFAULT_ENABLE_CCACHE}"}, {"name": "ENABLE_COVERAGE", "value": "${env.VALHALLA_D_DEFAULT_ENABLE_COVERAGE}"}, {"name": "ENABLE_SANITIZERS", "value": "${env.VALHALLA_D_DEFAULT_ENABLE_SANITIZERS}"}, {"name": "ENABLE_ADDRESS_SANITIZER", "value": "${env.VALHALLA_D_DEFAULT_ENABLE_ADDRESS_SANITIZER}"}, {"name": "ENABLE_UNDEFINED_SANITIZER", "value": "${env.VALHALLA_D_DEFAULT_ENABLE_UNDEFINED_SANITIZER}"}, {"name": "ENABLE_DATA_TOOLS", "value": "${env.VALHALLA_D_DEFAULT_ENABLE_DATA_TOOLS}"}, {"name": "ENABLE_HTTP", "value": "${env.VALHALLA_D_DEFAULT_ENABLE_HTTP}"}, {"name": "ENABLE_PYTHON_BINDINGS", "value": "${env.VALHALLA_D_DEFAULT_ENABLE_PYTHON_BINDINGS}"}, {"name": "ENABLE_SERVICES", "value": "${env.VALHALLA_D_DEFAULT_ENABLE_SERVICES}"}, {"name": "ENABLE_TESTS", "value": "${env.VALHALLA_D_DEFAULT_ENABLE_TESTS}"}, {"name": "ENABLE_TOOLS", "value": "${env.VALHALLA_D_DEFAULT_ENABLE_TOOLS}"}, {"name": "LOGGING_LEVEL", "value": "${env.VALHALLA_D_DEFAULT_LOGGING_LEVEL}"}]}, {"name": "x64-Debug-VS2019", "generator": "Visual Studio 16 2019 Win64", "configurationType": "Debug", "buildRoot": "${env.BuildDir}\\${name}", "installRoot": "${env.InstallDir}\\${name}", "buildCommandArgs": "-m", "cmakeCommandArgs": "", "ctestCommandArgs": "", "variables": [{"name": "ENABLE_CCACHE", "value": "${env.VALHALLA_D_DEFAULT_ENABLE_CCACHE}"}, {"name": "ENABLE_COVERAGE", "value": "${env.VALHALLA_D_DEFAULT_ENABLE_COVERAGE}"}, {"name": "ENABLE_SANITIZERS", "value": "${env.VALHALLA_D_DEFAULT_ENABLE_SANITIZERS}"}, {"name": "ENABLE_ADDRESS_SANITIZER", "value": "${env.VALHALLA_D_DEFAULT_ENABLE_ADDRESS_SANITIZER}"}, {"name": "ENABLE_UNDEFINED_SANITIZER", "value": "${env.VALHALLA_D_DEFAULT_ENABLE_UNDEFINED_SANITIZER}"}, {"name": "ENABLE_DATA_TOOLS", "value": "${env.VALHALLA_D_DEFAULT_ENABLE_DATA_TOOLS}"}, {"name": "ENABLE_HTTP", "value": "${env.VALHALLA_D_DEFAULT_ENABLE_HTTP}"}, {"name": "ENABLE_PYTHON_BINDINGS", "value": "${env.VALHALLA_D_DEFAULT_ENABLE_PYTHON_BINDINGS}"}, {"name": "ENABLE_SERVICES", "value": "${env.VALHALLA_D_DEFAULT_ENABLE_SERVICES}"}, {"name": "ENABLE_TESTS", "value": "${env.VALHALLA_D_DEFAULT_ENABLE_TESTS}"}, {"name": "ENABLE_TOOLS", "value": "${env.VALHALLA_D_DEFAULT_ENABLE_TOOLS}"}, {"name": "LOGGING_LEVEL", "value": "${env.VALHALLA_D_DEFAULT_LOGGING_LEVEL}"}]}, {"name": "x64-Release-VS2019", "generator": "Visual Studio 16 2019 Win64", "configurationType": "Release", "buildRoot": "${env.BuildDir}\\${name}", "installRoot": "${env.InstallDir}\\${name}", "buildCommandArgs": "-m", "cmakeCommandArgs": "", "ctestCommandArgs": "", "variables": [{"name": "ENABLE_CCACHE", "value": "${env.VALHALLA_D_DEFAULT_ENABLE_CCACHE}"}, {"name": "ENABLE_COVERAGE", "value": "${env.VALHALLA_D_DEFAULT_ENABLE_COVERAGE}"}, {"name": "ENABLE_SANITIZERS", "value": "${env.VALHALLA_D_DEFAULT_ENABLE_SANITIZERS}"}, {"name": "ENABLE_ADDRESS_SANITIZER", "value": "${env.VALHALLA_D_DEFAULT_ENABLE_ADDRESS_SANITIZER}"}, {"name": "ENABLE_UNDEFINED_SANITIZER", "value": "${env.VALHALLA_D_DEFAULT_ENABLE_UNDEFINED_SANITIZER}"}, {"name": "ENABLE_DATA_TOOLS", "value": "${env.VALHALLA_D_DEFAULT_ENABLE_DATA_TOOLS}"}, {"name": "ENABLE_HTTP", "value": "${env.VALHALLA_D_DEFAULT_ENABLE_HTTP}"}, {"name": "ENABLE_PYTHON_BINDINGS", "value": "${env.VALHALLA_D_DEFAULT_ENABLE_PYTHON_BINDINGS}"}, {"name": "ENABLE_SERVICES", "value": "${env.VALHALLA_D_DEFAULT_ENABLE_SERVICES}"}, {"name": "ENABLE_TESTS", "value": "${env.VALHALLA_D_DEFAULT_ENABLE_TESTS}"}, {"name": "ENABLE_TOOLS", "value": "${env.VALHALLA_D_DEFAULT_ENABLE_TOOLS}"}, {"name": "LOGGING_LEVEL", "value": "${env.VALHALLA_D_DEFAULT_LOGGING_LEVEL}"}]}, {"name": "x86-Debug-VS2019", "generator": "Visual Studio 16 2019", "configurationType": "Debug", "buildRoot": "${env.BuildDir}\\${name}", "installRoot": "${env.InstallDir}\\${name}", "buildCommandArgs": "-m", "cmakeCommandArgs": "", "ctestCommandArgs": "", "variables": [{"name": "ENABLE_CCACHE", "value": "${env.VALHALLA_D_DEFAULT_ENABLE_CCACHE}"}, {"name": "ENABLE_COVERAGE", "value": "${env.VALHALLA_D_DEFAULT_ENABLE_COVERAGE}"}, {"name": "ENABLE_SANITIZERS", "value": "${env.VALHALLA_D_DEFAULT_ENABLE_SANITIZERS}"}, {"name": "ENABLE_ADDRESS_SANITIZER", "value": "${env.VALHALLA_D_DEFAULT_ENABLE_ADDRESS_SANITIZER}"}, {"name": "ENABLE_UNDEFINED_SANITIZER", "value": "${env.VALHALLA_D_DEFAULT_ENABLE_UNDEFINED_SANITIZER}"}, {"name": "ENABLE_DATA_TOOLS", "value": "${env.VALHALLA_D_DEFAULT_ENABLE_DATA_TOOLS}"}, {"name": "ENABLE_HTTP", "value": "${env.VALHALLA_D_DEFAULT_ENABLE_HTTP}"}, {"name": "ENABLE_PYTHON_BINDINGS", "value": "${env.VALHALLA_D_DEFAULT_ENABLE_PYTHON_BINDINGS}"}, {"name": "ENABLE_SERVICES", "value": "${env.VALHALLA_D_DEFAULT_ENABLE_SERVICES}"}, {"name": "ENABLE_TESTS", "value": "${env.VALHALLA_D_DEFAULT_ENABLE_TESTS}"}, {"name": "ENABLE_TOOLS", "value": "${env.VALHALLA_D_DEFAULT_ENABLE_TOOLS}"}, {"name": "LOGGING_LEVEL", "value": "${env.VALHALLA_D_DEFAULT_LOGGING_LEVEL}"}]}, {"name": "x86-Release-VS2019", "generator": "Visual Studio 16 2019", "configurationType": "Release", "buildRoot": "${env.BuildDir}\\${name}", "installRoot": "${env.InstallDir}\\${name}", "buildCommandArgs": "-m", "cmakeCommandArgs": "", "ctestCommandArgs": "", "variables": [{"name": "ENABLE_CCACHE", "value": "${env.VALHALLA_D_DEFAULT_ENABLE_CCACHE}"}, {"name": "ENABLE_COVERAGE", "value": "${env.VALHALLA_D_DEFAULT_ENABLE_COVERAGE}"}, {"name": "ENABLE_SANITIZERS", "value": "${env.VALHALLA_D_DEFAULT_ENABLE_SANITIZERS}"}, {"name": "ENABLE_ADDRESS_SANITIZER", "value": "${env.VALHALLA_D_DEFAULT_ENABLE_ADDRESS_SANITIZER}"}, {"name": "ENABLE_UNDEFINED_SANITIZER", "value": "${env.VALHALLA_D_DEFAULT_ENABLE_UNDEFINED_SANITIZER}"}, {"name": "ENABLE_DATA_TOOLS", "value": "${env.VALHALLA_D_DEFAULT_ENABLE_DATA_TOOLS}"}, {"name": "ENABLE_HTTP", "value": "${env.VALHALLA_D_DEFAULT_ENABLE_HTTP}"}, {"name": "ENABLE_PYTHON_BINDINGS", "value": "${env.VALHALLA_D_DEFAULT_ENABLE_PYTHON_BINDINGS}"}, {"name": "ENABLE_SERVICES", "value": "${env.VALHALLA_D_DEFAULT_ENABLE_SERVICES}"}, {"name": "ENABLE_TESTS", "value": "${env.VALHALLA_D_DEFAULT_ENABLE_TESTS}"}, {"name": "ENABLE_TOOLS", "value": "${env.VALHALLA_D_DEFAULT_ENABLE_TOOLS}"}, {"name": "LOGGING_LEVEL", "value": "${env.VALHALLA_D_DEFAULT_LOGGING_LEVEL}"}]}]}
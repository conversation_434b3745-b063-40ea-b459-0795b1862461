#
# Activate component packaging
#
if(<PERSON><PERSON><PERSON>_GENERATOR MATCHES "ZIP")
   set(CPACK_ARCHIVE_COMPONENT_INSTALL "ON")
endif()

if(CPACK_GENERATOR MATCHES "RPM")
   set(CPACK_RPM_COMPONENT_INSTALL "ON")
   set(CPACK_RPM_DEVELOPMENT_PACKAGE_REQUIRES "mylib-Runtime")
endif()

if(CPACK_GENERATOR MATCHES "DEB")
   set(CPACK_DEB_COMPONENT_INSTALL "ON")
endif()

if(CPACK_GENERATOR MATCHES "DragNDrop")
   set(CPACK_COMPONENTS_GROUPING "ONE_PER_GROUP")
endif()

if(CPACK_GENERATOR MATCHES "NuGet")
   set(CPACK_NUGET_COMPONENT_INSTALL "ON")
endif()

#
# Choose grouping way
#
#set(CPACK_COMPONENTS_ALL_GROUPS_IN_ONE_PACKAGE)
#set(CPAC<PERSON>_COMPONENTS_GROUPING)
#set(CP<PERSON><PERSON>_COMPONENTS_IGNORE_GROUPS)
#set(CP<PERSON>K_COMPONENTS_ALL_IN_ONE_PACKAGE)

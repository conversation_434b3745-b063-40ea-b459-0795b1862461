#!/bin/bash
set -e

#This script gets elevation from AWS Public Datasets for the tiles/files listed in
#the specified file.
function usage() {
	echo "Usage: $0 tile_file [target_dir=elevation] [parallelism=$(nproc)] [decompress=true]"
	exit 1
}

#make sure land tile file is present
if [ -z $1 ]; then
	usage
else
	tile_name="$1"
fi

if [ -z $2 ]; then
	dest=elevation
else
	dest="$2"
fi

if [ -z $3 ]; then
	para=$(nproc)
else
	para="$3"
fi
if [[ -z $4 ]] || [[ $4 != "false" ]]; then
	decomp=true
	export decomp
else
	unset decomp
fi

#iterate through the file and make directories based on latitude
echo "Make directories"
input=$tile_name
while IFS= read -r line
do
        dir=${line:0:3}
        echo "${dest}/${dir}"
done < "$input" | while read d; do mkdir -p $d; done

#get expected count
expected=$(wc -l < "$tile_name")
echo "extracting $expected elevation tiles"

#iterate through the file list and get the elevation tiles
ext=$( [[ ${decomp} ]] && echo "" || echo ".gz" )

input=$tile_name
while IFS= read -r line
do
	file=$line".hgt"
        dir=$(echo ${file} | sed "s/^\([NS][0-9]\{2\}\).*/\1/g")
        #if we dont already have it or its the wrong size
        if [ ! -e ${dest}/${dir}/${file} ] || [ $(stat -c %s ${dest}/${dir}/${file}) -ne $((3601*3601*2)) ]; then
        	echo "https://elevation-tiles-prod.s3.us-east-1.amazonaws.com/skadi/${dir}/${file}.gz ${dest}/${dir}/${file}${ext}"
        fi
done < "$input" | parallel --env decomp --progress -C ' ' -P ${para} 'curl --retry 3 --retry-delay 0 --max-time 100 -s -o - {1} | ( [[ ! ${decomp} ]] && cat || gunzip ) > {2}'

#check they are all there
echo "checking all files exist"
total=$(find ${dest} | grep -cF hgt)
if [ ${total} -ne ${expected} ]; then
        echo "try again, $((${expected} - ${total})) files are missing"
        exit 1
fi

#check their sizes
echo "checking file sizes"
if [ ${decomp} ]; then
        found=$(find ${dest} -type f -size $((3601*3601*2))c | wc -l)
else
        found=$(for f in $(find ${dest} -type f); do gzip -l $f | tail -n 1; done | grep -cvF $((3601*3601*2)))
fi
if [ ${found} -ne ${expected} ]; then
        echo "try again, $((${expected} - ${found})) files are corrupt"
        exit 1
fi



#!/bin/sh

error_exit() {
  echo "error: $1" 1>&2
  # geos 3.9* has a double free bug which, although it crashes the programs
  # that use it when they go to exit, works fine otherwise in terms of the
  # output. so here if we detect that we ignore the failure and keep going
  if [ $(pkg-config geos --modversion | grep -cvF "3.9") -eq 0 ]; then
    exit 1
  fi
}

if  ! which spatialite >/dev/null; then
    error_exit "spatialite not found which is required.  Please install via:  sudo apt-get install spatialite-bin"
fi

if  ! which unzip >/dev/null; then
    error_exit "unzip not found which is required.  Please install via:  sudo apt-get install unzip"
fi

rm -rf dist
rm -f ./timezones-with-oceans-1970.shapefile.zip

url="https://github.com/evansiroky/timezone-boundary-builder/releases/download/2025b/timezones-with-oceans-1970.shapefile.zip"

echo "downloading timezone polygon file." 1>&2
curl -L -s -o ./timezones-with-oceans-1970.shapefile.zip ${url} || error_exit "curl failed for ${url}"
unzip ./timezones-with-oceans-1970.shapefile.zip 1>&2 || error_exit "unzip failed"

tz_file=$(mktemp)
spatialite_tool -i -shp "./combined-shapefile-with-oceans-1970" -d "${tz_file}" -t tz_world -s 4326 -g geom -c UTF-8 1>&2 || error_exit "spatialite_tool import failed"
spatialite ${tz_file} "SELECT CreateSpatialIndex('tz_world', 'geom');" 1>&2 || error_exit "SpatialIndex failed"
spatialite ${tz_file} "VACUUM;" 1>&2 || error_exit "VACUUM failed"
spatialite ${tz_file} "ANALYZE;" 1>&2 || error_exit "ANALYZE failed"

rm -rf ./combined-shapefile-with-oceans-1970*
rm -f ./timezones-with-oceans-1970.shapefile.zip alias_tz.csv
cat ${tz_file}

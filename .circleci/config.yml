version: 2.1

commands:
  linux_deps:
    steps:
      - run: ./scripts/install-linux-deps.sh
  linux_lint_deps:
    steps:
      - run: |
          apt-get update --assume-yes
          env DEBIAN_FRONTEND=noninteractive apt-get install --yes --quiet sudo python3-pip python3-requests git curl file
  check_ci_lint:
    steps:
      - run: ./scripts/format.sh && ./scripts/error_on_dirty.sh

jobs:
  lint-build-debug:
    docker:
      - image: ubuntu:24.04
    resource_class: xlarge
    steps:
      - checkout
      - linux_lint_deps
      - check_ci_lint
      - linux_deps
      - run: git submodule sync && git submodule update --init
      - restore_cache:
          keys:
            - ccache-debug-linux-x86_64-v3-{{ .Branch }}
            - ccache-debug-linux-x86_64-v3-master
            - ccache-debug-linux-x86_64-v3-
      - run: |
          mkdir build
          # NOTE: -Werror disabled in CI, as we currently have >4k warnings.
          cd build
          cmake .. -DCMAKE_BUILD_TYPE=Debug -DENABLE_COVERAGE=On -DCPACK_GENERATOR=DEB \
            -DENABLE_COMPILER_WARNINGS=On -DENABLE_WERROR=Off -DCMAKE_EXPORT_COMPILE_COMMANDS=On \
            -DCMAKE_CXX_FLAGS="-fuse-ld=lld" -DLOGGING_LEVEL=INFO -DENABLE_PYTHON_BINDINGS=On -DENABLE_GDAL=On
      - run: python3 ./scripts/valhalla_build_config
      - run: make -C build -j8
      - run: make -C build utrecht_tiles
      - run: make -C build -j8 tests
      # Note: we save the cache here before doing linting so that if linting fails, we can rebuild quickly
      # for follow-up fixes
      - save_cache:
          key: ccache-debug-linux-x86_64-v3-{{ .Branch }}-{{ epoch }}
          paths:
            - ~/.cache/ccache
      # clang-tidy is quite slow and might take much longer than default 10m limit
      - run:
          name: Run clang-tidy checks
          no_output_timeout: 30m
          # Limit concurrency to 6 to avoid OOMKills on CircleCI runners.
          command: scripts/clang-tidy-only-diff.sh 6
      - run: sudo make -C build install
      - run: make -C build package
      - run: |
          # Note: run (compile) tests and make code coverage report.
          make -C build -j8 coverage
      - run: .circleci/vendored-codecov.sh || echo "Codecov did not collect coverage reports"

  build-release:
    docker:
      - image: ubuntu:24.04
    resource_class: xlarge
    steps:
      - checkout
      - linux_lint_deps
      - check_ci_lint
      - linux_deps
      - run: git submodule sync && git submodule update --init
      - restore_cache:
          keys:
            - ccache-release-linux-x86_64-v3-{{ .Branch }}
            - ccache-release-linux-x86_64-v3-master
            - ccache-release-linux-x86_64-v3
      - run: |
          mkdir build
          cd build
          cmake .. -DCMAKE_BUILD_TYPE=Release -DBUILD_SHARED_LIBS=On -DENABLE_PYTHON_BINDINGS=On -DLOGGING_LEVEL=TRACE \
            -DCPACK_GENERATOR=DEB -DCPACK_PACKAGE_VERSION_SUFFIX="-0ubuntu1-$(lsb_release -sc)" -DENABLE_SANITIZERS=ON \
            -DENABLE_SINGLE_FILES_WERROR=Off -DENABLE_GDAL=On
      - run: make -C build -j8
      - run: make -C build utrecht_tiles
      - run: make -C build -j8 tests
      # leaks in glibc we cant control for
      - run: export ASAN_OPTIONS=detect_leaks=0 && make -C build -j8 check
      - save_cache:
          key: ccache-release-linux-x86_64-v3-{{ .Branch }}-{{ epoch }}
          paths:
            - ~/.cache/ccache
      - run: sudo make -C build install
      - run: make -C build package

  build-arm-release:
    docker:
      - image: arm64v8/ubuntu:24.04
    resource_class: arm.xlarge
    steps:
      - checkout
      - linux_lint_deps
      - check_ci_lint
      - linux_deps
      - run: git submodule sync && git submodule update --init
      - restore_cache:
          keys:
            - ccache-release-linux-arm_64-v3-{{ .Branch }}
            - ccache-release-linux-arm_64-v3-master
            - ccache-release-linux-arm_64-v3
      - run: |
          mkdir build
          cd build
          cmake .. -DCMAKE_BUILD_TYPE=Release -DBUILD_SHARED_LIBS=On -DENABLE_PYTHON_BINDINGS=On -DCPACK_GENERATOR=DEB \
            -DCPACK_PACKAGE_VERSION_SUFFIX="-0ubuntu1-$(lsb_release -sc)" -DENABLE_SINGLE_FILES_WERROR=Off -DENABLE_GDAL=On
      - run: make -C build -j8
      - run: make -C build utrecht_tiles
      - run: make -C build -j8 tests
      # leaks in glibc we cant control for
      - run: export ASAN_OPTIONS=detect_leaks=0 && make -C build -j8 check
      - save_cache:
          key: ccache-release-linux-arm_64-v3-{{ .Branch }}
          paths:
            - ~/.cache/ccache
      - run: sudo make -C build install
      - run: make -C build package

workflows:
  version: 2
  build_test_publish:
    jobs:
      - lint-build-debug:
          filters:
            tags:
              ignore: /.*/
      - build-release:
          filters:
            tags:
              ignore: /.*/
      - build-arm-release:
          filters:
            tags:
              ignore: /.*/

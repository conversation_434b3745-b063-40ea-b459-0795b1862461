{"data_format": 1, "data_url": "", "project": {"name": "Valhalla", "description": "Open Source Tile-Based Routing Applications and APIs", "project_url": "https://github.com/valhalla", "doc_url": "", "icon_url": "https://avatars0.githubusercontent.com/u/********", "contact_name": "<PERSON>", "contact_email": "<EMAIL>"}, "tags": [{"key": "layer", "object_types": ["way"], "description": "Relative Z-level"}, {"key": "ISO3166-1", "object_types": ["relation"], "description": "Country code"}, {"key": "ISO3166-1:alpha2", "object_types": ["relation"], "description": "2 letter country code"}, {"key": "ISO3166-2", "object_types": ["relation"], "description": "Principal subdivision codes (state or province)"}, {"key": "access", "value": "no", "object_types": ["node", "way"], "description": "Sets access flag to false"}, {"key": "access", "value": "agricultural", "object_types": ["node", "way"], "description": "Sets access flag to false"}, {"key": "access", "value": "discouraged", "object_types": ["node", "way"], "description": "Sets access flag to false"}, {"key": "access", "value": "forestry", "object_types": ["node", "way"], "description": "Sets access flag to false"}, {"key": "access", "value": "official", "object_types": ["node", "way"], "description": "Sets access flag to false"}, {"key": "access", "value": "restricted", "object_types": ["node", "way"], "description": "Sets access flag to true"}, {"key": "access", "value": "yes", "object_types": ["node", "way"], "description": "Sets access flag to true"}, {"key": "access", "value": "private", "object_types": ["node", "way"], "description": "Sets access flag to true; however, if access = private and (emergency = yes or service = emergency_access) then access flag is false."}, {"key": "access", "value": "permit", "object_types": ["node", "way"], "description": "Sets access flag to true"}, {"key": "access", "value": "residents", "object_types": ["node", "way"], "description": "Sets access flag to true"}, {"key": "access", "value": "permissive", "object_types": ["node", "way"], "description": "Sets access flag to true"}, {"key": "access", "value": "use_sidepath", "object_types": ["node", "way"], "description": "Sets access flag to true"}, {"key": "access", "value": "delivery", "object_types": ["node", "way"], "description": "Sets access flag to true"}, {"key": "access", "value": "designated", "object_types": ["node", "way"], "description": "Sets access flag to true"}, {"key": "access", "value": "dismount", "object_types": ["node", "way"], "description": "Sets access flag to true"}, {"key": "access", "value": "destination", "object_types": ["node", "way"], "description": "Sets access flag to true"}, {"key": "access", "value": "customers", "object_types": ["node", "way"], "description": "Sets access flag to true"}, {"key": "access", "value": "public", "object_types": ["node", "way"], "description": "Sets access flag to true"}, {"key": "access", "value": "allowed", "object_types": ["node", "way"], "description": "Sets access flag to true"}, {"key": "access", "value": "emergency", "object_types": ["node", "way"], "description": "Overrides access flags for emergency vehicles.  Emergency Routing is allowed."}, {"key": "access", "value": "psv", "object_types": ["node", "way"], "description": "Sets access to false for all modes except for taxis and buses"}, {"key": "admin_level", "value": "2", "object_types": ["relation"], "description": "Country level boundary"}, {"key": "admin_level", "value": "3", "object_types": ["relation"], "description": "Special cases of state/provinces in eg France and the Philippines"}, {"key": "admin_level", "value": "4", "object_types": ["relation"], "description": "State/province level boundary"}, {"key": "admin_level", "value": "6", "object_types": ["relation"], "description": "Government territories such as Washington DC"}, {"key": "alt_name", "object_types": ["way"], "description": "Alternative name for the way, frequently used when a street has another official or locally preferred name"}, {"key": "alt_name:<lg>", "object_types": ["way"], "description": "Alternative name in a given language for the way, frequently used when a street has another official or locally preferred name"}, {"key": "alt_name:pronunciation", "object_types": ["way"], "description": "This tag contains a phonetic guide(IPA) to pronouncing the alternative name for the way, frequently used when a street has another official or locally preferred name"}, {"key": "alt_name:pronunciation:<alphabet>", "object_types": ["way"], "description": "This tag contains a phonetic guide to pronouncing the alternative name for the way, frequently used when a street has another official or locally preferred name.  Phonetic Alphabet: jeita|katakana|nt-sampa."}, {"key": "alt_name:<lg>pronunciation", "object_types": ["way"], "description": "This tag contains a phonetic guide(IPA) to pronouncing the alternative name in a given language for the way, frequently used when a street has another official or locally preferred name"}, {"key": "alt_name:<lg>pronunciation:<alphabet>", "object_types": ["way"], "description": "This tag contains a phonetic guide to pronouncing the alternative name in a given language for the way, frequently used when a street has another official or locally preferred name.  Phonetic Alphabet: jeita|katakana|nt-sampa."}, {"key": "alt_name:left", "object_types": ["way"], "description": "Alternative name for the way for the left side of the way, frequently used when a street has another official or locally preferred name"}, {"key": "alt_name:left:<lg>", "object_types": ["way"], "description": "Alternative name in a given language for the left side of the way, frequently used when a street has another official or locally preferred name"}, {"key": "alt_name:left:pronunciation", "object_types": ["way"], "description": "This tag contains a phonetic guide(IPA) to pronouncing the alternative name for the left side of the way, frequently used when a street has another official or locally preferred name"}, {"key": "alt_name:left:pronunciation:<alphabet>", "object_types": ["way"], "description": "This tag contains a phonetic guide to pronouncing the alternative name for the left side of the way, frequently used when a street has another official or locally preferred name.  Phonetic Alphabet: jeita|katakana|nt-sampa."}, {"key": "alt_name:left:<lg>pronunciation", "object_types": ["way"], "description": "This tag contains a phonetic guide(IPA) to pronouncing the alternative name in a given language for the left side of the way, frequently used when a street has another official or locally preferred name"}, {"key": "alt_name:left:<lg>pronunciation:<alphabet>", "object_types": ["way"], "description": "This tag contains a phonetic guide to pronouncing the alternative name in a given language for the left side of the way, frequently used when a street has another official or locally preferred name.  Phonetic Alphabet: jeita|katakana|nt-sampa."}, {"key": "alt_name:right", "object_types": ["way"], "description": "Alternative name for the way for the right side of the way, frequently used when a street has another official or locally preferred name"}, {"key": "alt_name:right:<lg>", "object_types": ["way"], "description": "Alternative name in a given language for the right side of the way, frequently used when a street has another official or locally preferred name"}, {"key": "alt_name:right:pronunciation", "object_types": ["way"], "description": "This tag contains a phonetic guide(IPA) to pronouncing the alternative name for the right side of the way, frequently used when a street has another official or locally preferred name"}, {"key": "alt_name:right:pronunciation:<alphabet>", "object_types": ["way"], "description": "This tag contains a phonetic guide to pronouncing the alternative name for the right side of the way, frequently used when a street has another official or locally preferred name.  Phonetic Alphabet: jeita|katakana|nt-sampa."}, {"key": "alt_name:right:<lg>pronunciation", "object_types": ["way"], "description": "This tag contains a phonetic guide(IPA) to pronouncing the alternative name in a given language for the right side of the way, frequently used when a street has another official or locally preferred name"}, {"key": "alt_name:right:<lg>pronunciation:<alphabet>", "object_types": ["way"], "description": "This tag contains a phonetic guide to pronouncing the alternative name in a given language for the right side of the way, frequently used when a street has another official or locally preferred name.  Phonetic Alphabet: jeita|katakana|nt-sampa."}, {"key": "area", "value": "yes", "object_types": ["way"], "description": "Way will not be used in routing graph."}, {"key": "barrier", "value": "gate", "object_types": ["node"], "description": "Set gate flag to true."}, {"key": "barrier", "value": "lift_gate", "object_types": ["node"], "description": "Set gate flag to true."}, {"key": "barrier", "value": "yes", "object_types": ["node"], "description": "Set gate flag to true."}, {"key": "barrier", "value": "swing_gate", "object_types": ["node"], "description": "Set gate flag to true."}, {"key": "barrier", "value": "sliding_beam", "object_types": ["node"], "description": "Set gate flag to true."}, {"key": "barrier", "value": "bollard", "object_types": ["node"], "description": "Set bollard flag to true. Access for all motor vehicles is turned off, unless the override is set."}, {"key": "barrier", "value": "block", "object_types": ["node"], "description": "Set bollard flag to true. Access for all motor vehicles is turned off, unless the override is set."}, {"key": "barrier", "value": "removable", "object_types": ["node"], "description": "Set bollard flag to true. Access for all motor vehicles is turned off, unless the override is set."}, {"key": "barrier", "value": "kissing_gate", "object_types": ["node"], "description": "Set bollard flag to true. Access for all motor vehicles is turned off, unless the override is set."}, {"key": "barrier", "value": "motorcycle_barrier", "object_types": ["node"], "description": "Set bollard flag to true. Access for all motor vehicles is turned off, unless the override is set."}, {"key": "barrier", "value": "cycle_barrier", "object_types": ["node"], "description": "Set bollard flag to true. Access for all motor vehicles is turned off, unless the override is set."}, {"key": "barrier", "value": "chain", "object_types": ["node"], "description": "Set bollard flag to true. Access for all motor vehicles is turned off, unless the override is set."}, {"key": "barrier", "value": "bar", "object_types": ["node"], "description": "Set bollard flag to true. Access for all motor vehicles is turned off, unless the override is set."}, {"key": "barrier", "value": "jersey_barrier", "object_types": ["node"], "description": "Access for all modes is turned off, unless the override is set."}, {"key": "barrier", "value": "barrier_board", "object_types": ["node"], "description": "Access for all modes is turned off, unless the override is set."}, {"key": "barrier", "value": "fence", "object_types": ["node"], "description": "Access for all modes is turned off, unless the override is set."}, {"key": "barrier", "value": "wall", "object_types": ["node"], "description": "Access for all modes is turned off, unless the override is set."}, {"key": "barrier", "value": "debris", "object_types": ["node"], "description": "Access for all modes is turned off, unless the override is set."}, {"key": "barrier", "value": "sump_buster", "object_types": ["node"], "description": "Set sump_buster flag to true. Access for auto, hov and taxi is turned off, unless the override is set."}, {"key": "barrier", "value": "border_control", "object_types": ["node"], "description": "Sets the border control flag to true"}, {"key": "barrier", "value": "toll_booth", "object_types": ["node"], "description": "Sets the toll booth flag to true"}, {"key": "highway", "value": "toll_gantry", "object_types": ["node"], "description": "Sets the toll gantry flag to true"}, {"key": "public_transport", "value": "", "object_types": ["node"], "description": "Used to determine if a node is a named intersection."}, {"key": "reference_point", "value": "yes", "object_types": ["node"], "description": "Used to determine if a node is a named intersection."}, {"key": "junction", "object_types": ["node"], "description": "Used to determine if a node is a named intersection."}, {"key": "bicycle", "value": "yes", "object_types": ["node", "way"], "description": "Overrides access flags for bicycle.  Bicycle Routing allowed."}, {"key": "bicycle", "value": "designated", "object_types": ["node", "way"], "description": "Overrides access flags for bicycle.  Bicycle Routing allowed."}, {"key": "bicycle", "value": "use_sidepath", "object_types": ["node", "way"], "description": "Overrides access flags for bicycle.  Bicycle Routing allowed."}, {"key": "bicycle", "value": "no", "object_types": ["node", "way"], "description": "Overrides access flags for bicycle.  Bicycle Routing not allowed."}, {"key": "bicycle", "value": "permissive", "object_types": ["node", "way"], "description": "Overrides access flags for bicycle.  Bicycle Routing allowed."}, {"key": "bicycle", "value": "destination", "object_types": ["node", "way"], "description": "Overrides access flags for bicycle.  Bicycle Routing allowed."}, {"key": "bicycle", "value": "dismount", "object_types": ["node", "way"], "description": "Overrides access flags for bicycle.  Bicycle Routing allowed."}, {"key": "bicycle", "value": "lane", "object_types": ["node", "way"], "description": "Overrides access flags for bicycle.  Bicycle Routing allowed."}, {"key": "bicycle", "value": "track", "object_types": ["node", "way"], "description": "Overrides access flags for bicycle.  Bicycle Routing allowed."}, {"key": "bicycle", "value": "shared", "object_types": ["node", "way"], "description": "Overrides access flags for bicycle.  Bicycle Routing allowed."}, {"key": "bicycle", "value": "shared_lane", "object_types": ["node", "way"], "description": "Overrides access flags for bicycle.  Bicycle Routing allowed."}, {"key": "bicycle", "value": "sidepath", "object_types": ["node", "way"], "description": "Overrides access flags for bicycle.  Bicycle Routing allowed."}, {"key": "bicycle", "value": "share_busway", "object_types": ["node", "way"], "description": "Overrides access flags for bicycle.  Bicycle Routing allowed."}, {"key": "bicycle", "value": "none", "object_types": ["node", "way"], "description": "Overrides access flags for bicycle.  Bicycle Routing not allowed."}, {"key": "bicycle", "value": "allowed", "object_types": ["node", "way"], "description": "Overrides access flags for bicycle.  Bicycle Routing allowed."}, {"key": "bicycle", "value": "private", "object_types": ["node", "way"], "description": "Overrides access flags for bicycle.  Bicycle Routing allowed."}, {"key": "bicycle", "value": "permit", "object_types": ["node", "way"], "description": "Overrides access flags for bicycle.  Bicycle Routing allowed."}, {"key": "bicycle", "value": "residents", "object_types": ["node", "way"], "description": "Overrides access flags for bicycle.  Bicycle Routing allowed."}, {"key": "bicycle", "value": "official", "object_types": ["node", "way"], "description": "Overrides access flags for bicycle.  Bicycle Routing allowed."}, {"key": "bicycle", "value": "crossing", "object_types": ["node"], "description": "Allows access for all modes unless the mode override is set."}, {"key": "bicycle:backward", "value": "no", "object_types": ["way"], "description": "Sets the bike reverse flag to true.  Note from wiki: (when a road has a oneway cycleway next to it that must be used, and a cyclelane in the other direction)"}, {"key": "bicycle:backward", "value": "yes", "object_types": ["way"], "description": "Sets the bike reverse flag to true.  Note from wiki: (when cyclists are allowed to travel in both directions on a oneway street (but no lane is present))"}, {"description": "Adds conditional access to an edge with a time dependency in opening_hours format, see: https://wiki.openstreetmap.org/wiki/Key:opening_hours", "value": "no @ (opening_hours format)", "key": "bicycle:conditional", "object_types": ["way"]}, {"description": "Adds conditional access to an edge with a time dependency in opening_hours format, see: https://wiki.openstreetmap.org/wiki/Key:opening_hours", "value": "yes @ (opening_hours format)", "key": "bicycle:conditional", "object_types": ["way"]}, {"description": "Adds conditional access to an edge with a time dependency in opening_hours format, see: https://wiki.openstreetmap.org/wiki/Key:opening_hours", "value": "private @ (opening_hours format)", "key": "bicycle:conditional", "object_types": ["way"]}, {"description": "Adds conditional access to an edge with a time dependency in opening_hours format, see: https://wiki.openstreetmap.org/wiki/Key:opening_hours", "value": "delivery @ (opening_hours format)", "key": "bicycle:conditional", "object_types": ["way"]}, {"description": "Adds conditional access to an edge with a time dependency in opening_hours format, see: https://wiki.openstreetmap.org/wiki/Key:opening_hours", "value": "designated @ (opening_hours format)", "key": "bicycle:conditional", "object_types": ["way"]}, {"key": "bicycle_road", "value": "yes", "object_types": ["node", "way"], "description": "Overrides access flags for bicycle.  Bicycle Routing allowed."}, {"key": "bicycle_road", "value": "designated", "object_types": ["node", "way"], "description": "Overrides access flags for bicycle.  Bicycle Routing allowed."}, {"key": "bicycle_road", "value": "use_sidepath", "object_types": ["node", "way"], "description": "Overrides access flags for bicycle.  Bicycle Routing allowed."}, {"key": "bicycle_road", "value": "no", "object_types": ["node", "way"], "description": "Overrides access flags for bicycle.  Bicycle Routing not allowed."}, {"key": "bicycle_road", "value": "permissive", "object_types": ["node", "way"], "description": "Overrides access flags for bicycle.  Bicycle Routing allowed."}, {"key": "bicycle_road", "value": "destination", "object_types": ["node", "way"], "description": "Overrides access flags for bicycle.  Bicycle Routing allowed."}, {"key": "bicycle_road", "value": "dismount", "object_types": ["node", "way"], "description": "Overrides access flags for bicycle.  Bicycle Routing allowed."}, {"key": "bicycle_road", "value": "lane", "object_types": ["node", "way"], "description": "Overrides access flags for bicycle.  Bicycle Routing allowed."}, {"key": "bicycle_road", "value": "track", "object_types": ["node", "way"], "description": "Overrides access flags for bicycle.  Bicycle Routing allowed."}, {"key": "bicycle_road", "value": "shared", "object_types": ["node", "way"], "description": "Overrides access flags for bicycle.  Bicycle Routing allowed."}, {"key": "bicycle_road", "value": "shared_lane", "object_types": ["node", "way"], "description": "Overrides access flags for bicycle.  Bicycle Routing allowed."}, {"key": "bicycle_road", "value": "sidepath", "object_types": ["node", "way"], "description": "Overrides access flags for bicycle.  Bicycle Routing allowed."}, {"key": "bicycle_road", "value": "share_busway", "object_types": ["node", "way"], "description": "Overrides access flags for bicycle.  Bicycle Routing allowed."}, {"key": "bicycle_road", "value": "none", "object_types": ["node", "way"], "description": "Overrides access flags for bicycle.  Bicycle Routing not allowed."}, {"key": "bicycle_road", "value": "allowed", "object_types": ["node", "way"], "description": "Overrides access flags for bicycle.  Bicycle Routing allowed."}, {"key": "bicycle_road", "value": "private", "object_types": ["node", "way"], "description": "Overrides access flags for bicycle.  Bicycle Routing allowed."}, {"key": "bicycle_road", "value": "official", "object_types": ["node", "way"], "description": "Overrides access flags for bicycle.  Bicycle Routing allowed."}, {"key": "bollard", "value": "rising", "object_types": ["node"], "description": "If bollard flag equals true, then set bollard flag to false and gate tor true."}, {"key": "boundary", "value": "administrative", "object_types": ["relation"], "description": "Denotes a boarder between administrative territories"}, {"key": "bridge", "value": "yes", "object_types": ["way"]}, {"key": "bridge", "value": "true", "object_types": ["way"]}, {"key": "bridge", "value": "1", "object_types": ["way"]}, {"key": "bus", "value": "no", "object_types": ["node", "way"], "description": "Overrides access flags for bus.  Bus Routing not allowed."}, {"key": "bus", "value": "delivery", "object_types": ["node", "way"], "description": "Overrides access flags for bus.  Bus Routing not allowed."}, {"key": "bus", "value": "yes", "object_types": ["node", "way"], "description": "Overrides access flags for bus.  Bus Routing allowed."}, {"key": "bus", "value": "designated", "object_types": ["node", "way"], "description": "Overrides access flags for bus.  Bus Routing allowed."}, {"key": "bus", "value": "urban", "object_types": ["node", "way"], "description": "Overrides access flags for bus.  Bus Routing allowed."}, {"key": "bus", "value": "permissive", "object_types": ["node", "way"], "description": "Overrides access flags for bus.  Bus Routing allowed."}, {"key": "bus", "value": "restricted", "object_types": ["node", "way"], "description": "Overrides access flags for bus.  Bus Routing allowed."}, {"key": "bus", "value": "destination", "object_types": ["node", "way"], "description": "Overrides access flags for bus.  Bus Routing allowed."}, {"key": "bus:backward", "value": "designated", "object_types": ["way"], "description": "Sets the bus reverse flag to true."}, {"key": "bus:backward", "value": "yes", "object_types": ["way"], "description": "Sets the bus reverse flag to true."}, {"description": "Adds conditional access to an edge with a time dependency in opening_hours format, see: https://wiki.openstreetmap.org/wiki/Key:opening_hours", "value": "no @ (opening_hours format)", "key": "bus:conditional", "object_types": ["way"]}, {"description": "Adds conditional access to an edge with a time dependency in opening_hours format, see: https://wiki.openstreetmap.org/wiki/Key:opening_hours", "value": "yes @ (opening_hours format)", "key": "bus:conditional", "object_types": ["way"]}, {"description": "Adds conditional access to an edge with a time dependency in opening_hours format, see: https://wiki.openstreetmap.org/wiki/Key:opening_hours", "value": "private @ (opening_hours format)", "key": "bus:conditional", "object_types": ["way"]}, {"description": "Adds conditional access to an edge with a time dependency in opening_hours format, see: https://wiki.openstreetmap.org/wiki/Key:opening_hours", "value": "delivery @ (opening_hours format)", "key": "bus:conditional", "object_types": ["way"]}, {"description": "Adds conditional access to an edge with a time dependency in opening_hours format, see: https://wiki.openstreetmap.org/wiki/Key:opening_hours", "value": "designated @ (opening_hours format)", "key": "bus:conditional", "object_types": ["way"]}, {"description": "Adds conditional access to an edge with a time dependency in opening_hours format, see: https://wiki.openstreetmap.org/wiki/Key:opening_hours", "value": "destination @ (opening_hours format)", "key": "bus:conditional", "object_types": ["way"]}, {"key": "busway", "valuedd": "lane", "object_types": ["way"], "description": "Overrides access flags for bus.  Bus Routing allowed."}, {"key": "busway", "value": "opposite", "object_types": ["way"], "description": "Overrides oneway flag.  Sets bus reverse flag to true."}, {"key": "busway", "value": "opposite_lane", "object_types": ["way"], "description": "Overrides oneway flag.  Sets bus reverse flag to true."}, {"key": "busway:left", "value": "lane", "object_types": ["way"], "description": "Overrides access flags for bus.  Bus Routing allowed.  busway:right must exist as well."}, {"key": "busway:right", "value": "lane", "object_types": ["way"], "description": "Overrides access flags for bus.  Bus Routing allowed.  busway:left must exist as well."}, {"key": "conveying", "object_types": ["way"], "description": "Used for escalator and moving walkway."}, {"key": "crossing", "object_types": ["node"], "description": "Allows access for all modes unless the mode override is set."}, {"key": "crossing", "value": "traffic_signals", "object_types": ["node"]}, {"key": "cyclestreet", "value": "yes", "object_types": ["node", "way"], "description": "Overrides access flags for bicycle.  Bicycle Routing allowed."}, {"key": "cyclestreet", "value": "designated", "object_types": ["node", "way"], "description": "Overrides access flags for bicycle.  Bicycle Routing allowed."}, {"key": "cyclestreet", "value": "use_sidepath", "object_types": ["node", "way"], "description": "Overrides access flags for bicycle.  Bicycle Routing allowed."}, {"key": "cyclestreet", "value": "no", "object_types": ["node", "way"], "description": "Overrides access flags for bicycle.  Bicycle Routing not allowed."}, {"key": "cyclestreet", "value": "permissive", "object_types": ["node", "way"], "description": "Overrides access flags for bicycle.  Bicycle Routing allowed."}, {"key": "cyclestreet", "value": "destination", "object_types": ["node", "way"], "description": "Overrides access flags for bicycle.  Bicycle Routing allowed."}, {"key": "cyclestreet", "value": "dismount", "object_types": ["node", "way"], "description": "Overrides access flags for bicycle.  Bicycle Routing allowed."}, {"key": "cyclestreet", "value": "lane", "object_types": ["node", "way"], "description": "Overrides access flags for bicycle.  Bicycle Routing allowed."}, {"key": "cyclestreet", "value": "track", "object_types": ["node", "way"], "description": "Overrides access flags for bicycle.  Bicycle Routing allowed."}, {"key": "cyclestreet", "value": "shared", "object_types": ["node", "way"], "description": "Overrides access flags for bicycle.  Bicycle Routing allowed."}, {"key": "cyclestreet", "value": "shared_lane", "object_types": ["node", "way"], "description": "Overrides access flags for bicycle.  Bicycle Routing allowed."}, {"key": "cyclestreet", "value": "sidepath", "object_types": ["node", "way"], "description": "Overrides access flags for bicycle.  Bicycle Routing allowed."}, {"key": "cyclestreet", "value": "share_busway", "object_types": ["node", "way"], "description": "Overrides access flags for bicycle.  Bicycle Routing allowed."}, {"key": "cyclestreet", "value": "none", "object_types": ["node", "way"], "description": "Overrides access flags for bicycle.  Bicycle Routing not allowed."}, {"key": "cyclestreet", "value": "allowed", "object_types": ["node", "way"], "description": "Overrides access flags for bicycle.  Bicycle Routing allowed."}, {"key": "cyclestreet", "value": "private", "object_types": ["node", "way"], "description": "Overrides access flags for bicycle.  Bicycle Routing allowed."}, {"key": "cyclestreet", "value": "official", "object_types": ["node", "way"], "description": "Overrides access flags for bicycle.  Bicycle Routing allowed."}, {"key": "cycleway", "value": "yes", "object_types": ["way"], "description": "Overrides access flags for bicycle.  Bicycle Routing allowed."}, {"key": "cycleway", "value": "designated", "object_types": ["way"], "description": "Overrides access flags for bicycle.  Bicycle Routing allowed."}, {"key": "cycleway", "value": "use_sidepath", "object_types": ["way"], "description": "Overrides access flags for bicycle.  Bicycle Routing allowed."}, {"key": "cycleway", "value": "permissive", "object_types": ["way"], "description": "Overrides access flags for bicycle.  Bicycle Routing allowed."}, {"key": "cycleway", "value": "destination", "object_types": ["way"], "description": "Overrides access flags for bicycle.  Bicycle Routing allowed."}, {"key": "cycleway", "value": "dismount", "object_types": ["way"], "description": "Overrides access flags for bicycle.  Bicycle Routing allowed."}, {"key": "cycleway", "value": "lane", "object_types": ["way"], "description": "Overrides access flags for bicycle.  Bicycle Routing allowed.  Used to determine if the bike lane is shared, dedicated, or separated."}, {"key": "cycleway", "value": "buffered_lane", "object_types": ["way"], "description": "Overrides access flags for bicycle.  Bicycle Routing allowed.  Used to determine if the bike lane is shared, dedicated, or separated."}, {"key": "cycleway", "value": "track", "object_types": ["way"], "description": "Overrides access flags for bicycle.  Bicycle Routing allowed.  Used to determine if the bike lane is shared, dedicated, or separated."}, {"key": "cycleway", "value": "shared", "object_types": ["way"], "description": "Overrides access flags for bicycle.  Bicycle Routing allowed.  Used to determine if the bike lane is shared, dedicated, or separated."}, {"key": "cycleway", "value": "shared_lane", "object_types": ["way"], "description": "Overrides access flags for bicycle.  Bicycle Routing allowed.  Used to determine if the bike lane is shared, dedicated, or separated."}, {"key": "cycleway", "value": "sidepath", "object_types": ["way"], "description": "Overrides access flags for bicycle.  Bicycle Routing allowed."}, {"key": "cycleway", "value": "share_busway", "object_types": ["way"], "description": "Overrides access flags for bicycle.  Bicycle Routing allowed.  Used to determine if the bike lane is shared, dedicated, or separated."}, {"key": "cycleway", "value": "allowed", "object_types": ["way"], "description": "Overrides access flags for bicycle.  Bicycle Routing allowed."}, {"key": "cycleway", "value": "private", "object_types": ["way"], "description": "Overrides access flags for bicycle.  Bicycle Routing allowed."}, {"key": "cycleway", "value": "cyclestreet", "object_types": ["way"], "description": "Overrides access flags for bicycle.  Bicycle Routing allowed."}, {"key": "cycleway", "value": "crossing", "object_types": ["node", "way"], "description": "Allows access for all modes unless the mode override is set."}, {"key": "cycleway", "value": "opposite", "object_types": ["way"], "description": "Overrides oneway flag.  Sets bike reverse flag to true."}, {"key": "cycleway", "value": "opposite_lane", "object_types": ["way"], "description": "Overrides oneway flag.  Sets bike reverse flag to true.  Used to determine if the bike lane is shared, dedicated, or separated."}, {"key": "cycleway", "value": "opposite_track", "object_types": ["way"], "description": "Overrides oneway flag.  Sets bike reverse flag to true.  Used to determine if the bike lane is shared, dedicated, or separated."}, {"key": "cycleway:both", "value": "shared_lane", "object_types": ["way"], "description": "Bike forward and backward overrides.  Sets bike forward and backward to true."}, {"key": "cycleway:both", "value": "share_busway", "object_types": ["way"], "description": "Bike forward and backward overrides.  Sets bike forward and backward to true."}, {"key": "cycleway:both", "value": "shared", "object_types": ["way"], "description": "Bike forward and backward overrides.  Sets bike forward and backward to true."}, {"key": "cycleway:both", "value": "opposite_track", "object_types": ["way"], "description": "Bike forward and backward overrides.  Sets bike forward and backward to true."}, {"key": "cycleway:both", "value": "track", "object_types": ["way"], "description": "Bike forward and backward overrides.  Sets bike forward and backward to true."}, {"key": "cycleway:both", "value": "opposite_lane", "object_types": ["way"], "description": "Bike forward and backward overrides.  Sets bike forward and backward to true."}, {"key": "cycleway:both", "value": "lane", "object_types": ["way"], "description": "Bike forward and backward overrides.  Sets bike forward and backward to true."}, {"key": "cycleway:both:buffer", "value": "yes", "object_types": ["way"], "description": "Bike forward and backward overrides.  Sets bike forward and backward to true.  Used to determine if the bike lane is shared, dedicated, or separated."}, {"key": "cycleway:both:buffer", "value": "no", "object_types": ["way"], "description": "Bike forward and backward overrides.  Sets bike forward and backward to true.  Used to determine if the bike lane is shared, dedicated, or separated."}, {"key": "cycleway:left", "value": "opposite", "object_types": ["way"], "description": "Overrides oneway flag.  Sets bike reverse flag to true."}, {"key": "cycleway:left", "value": "opposite_lane", "object_types": ["way"], "description": "Overrides oneway flag.  Sets bike reverse flag to true.  Used to determine if the bike lane is shared, dedicated, or separated."}, {"key": "cycleway:left", "value": "opposite_track", "object_types": ["way"], "description": "Overrides oneway flag.  Sets bike reverse flag to true.  Used to determine if the bike lane is shared, dedicated, or separated."}, {"key": "cycleway:left", "value": "shared_lane", "object_types": ["way"], "description": "Bike forward and backward overrides.  Sets bike forward and backward to true.  Used to determine if the bike lane is shared, dedicated, or separated."}, {"key": "cycleway:left", "value": "share_busway", "object_types": ["way"], "description": "Bike forward and backward overrides.  Sets bike forward and backward to true.  Used to determine if the bike lane is shared, dedicated, or separated."}, {"key": "cycleway:left", "value": "shared", "object_types": ["way"], "description": "Bike forward and backward overrides.  Sets bike forward and backward to true.  Used to determine if the bike lane is shared, dedicated, or separated."}, {"key": "cycleway:left", "value": "track", "object_types": ["way"], "description": "Bike forward and backward overrides.  Sets bike forward and backward to true.  Used to determine if the bike lane is shared, dedicated, or separated."}, {"key": "cycleway:left", "value": "lane", "object_types": ["way"], "description": "Bike forward and backward overrides.  Sets bike forward and backward to true.  Used to determine if the bike lane is shared, dedicated, or separated."}, {"key": "cycleway:left:buffer", "value": "yes", "object_types": ["way"], "description": "Bike forward and backward overrides.  Sets bike forward and backward to true.  Used to determine if the bike lane is shared, dedicated, or separated."}, {"key": "cycleway:left:buffer", "value": "no", "object_types": ["way"], "description": "Bike forward and backward overrides.  Sets bike forward and backward to true.  Used to determine if the bike lane is shared, dedicated, or separated."}, {"key": "cycleway:right", "value": "opposite", "object_types": ["way"], "description": "Overrides oneway flag.  Sets bike reverse flag to true."}, {"key": "cycleway:right", "value": "opposite_lane", "object_types": ["way"], "description": "Overrides oneway flag.  Sets bike reverse flag to true.  Used to determine if the bike lane is shared, dedicated, or separated."}, {"key": "cycleway:right", "value": "opposite_track", "object_types": ["way"], "description": "Overrides oneway flag.  Sets bike reverse flag to true.  Used to determine if the bike lane is shared, dedicated, or separated."}, {"key": "cycleway:right", "value": "shared_lane", "object_types": ["way"], "description": "Bike forward and backward overrides.  Sets bike forward and backward to true.  Used to determine if the bike lane is shared, dedicated, or separated."}, {"key": "cycleway:right", "value": "share_busway", "object_types": ["way"], "description": "Bike forward and backward overrides.  Sets bike forward and backward to true.  Used to determine if the bike lane is shared, dedicated, or separated."}, {"key": "cycleway:right", "value": "shared", "object_types": ["way"], "description": "Bike forward and backward overrides.  Sets bike forward and backward to true.  Used to determine if the bike lane is shared, dedicated, or separated."}, {"key": "cycleway:right", "value": "track", "object_types": ["way"], "description": "Bike forward and backward overrides.  Sets bike forward and backward to true.  Used to determine if the bike lane is shared, dedicated, or separated."}, {"key": "cycleway:right", "value": "lane", "object_types": ["way"], "description": "Bike forward and backward overrides.  Sets bike forward and backward to true.  Used to determine if the bike lane is shared, dedicated, or separated."}, {"key": "cycleway:right:buffer", "value": "yes", "object_types": ["way"], "description": "Bike forward and backward overrides.  Sets bike forward and backward to true.  Used to determine if the bike lane is shared, dedicated, or separated."}, {"key": "cycleway:right:buffer", "value": "no", "object_types": ["way"], "description": "Bike forward and backward overrides.  Sets bike forward and backward to true.  Used to determine if the bike lane is shared, dedicated, or separated."}, {"key": "day_off", "object_types": ["relation"]}, {"key": "day_on", "object_types": ["relation"]}, {"key": "destination", "description": "Toward location. Used for exit information, see: https://wiki.openstreetmap.org/wiki/Exit_Info", "object_types": ["way"]}, {"key": "destination:lang:<lg>", "description": "Toward location in a given language. Used for exit information, see: https://wiki.openstreetmap.org/wiki/Exit_Info", "object_types": ["way"]}, {"key": "destination:pronunciation", "description": "This tag contains a phonetic guide(IPA) to pronouncing the toward location. Used for exit information, see: https://wiki.openstreetmap.org/wiki/Exit_Info", "object_types": ["way"]}, {"key": "destination:lang:<lg>:pronunciation", "description": "This tag contains a phonetic guide(IPA) to pronouncing the toward location in a given language. Used for exit information, see: https://wiki.openstreetmap.org/wiki/Exit_Info", "object_types": ["way"]}, {"key": "destination:pronunciation:<alphabet>", "description": "This tag contains a phonetic guide to pronouncing the toward location. Phonetic Alphabet: jeita|katakana|nt-sampa. Used for exit information, see: https://wiki.openstreetmap.org/wiki/Exit_Info", "object_types": ["way"]}, {"key": "destination:lang:<lg>:pronunciation:<alphabet>", "description": "This tag contains a phonetic guide to pronouncing the toward location in a given language. Phonetic Alphabet: jeita|katakana|nt-sampa. Used for exit information, see: https://wiki.openstreetmap.org/wiki/Exit_Info", "object_types": ["way"]}, {"description": "Adds sinage information to the given edge in the graph which is helpful to know to where a given road leads. Forward and backward allow for distinct values based on the direction the way is drawn specifically useful for single carriageways.", "key": "destination:backward", "object_types": ["way"]}, {"description": "This tag contains a phonetic guide(IPA) to pronouncing the sinage information to the given edge in the graph which is helpful to know to where a given road leads. Forward and backward allow for distinct values based on the direction the way is drawn specifically useful for single carriageways.", "key": "destination:backward:pronunciation", "object_types": ["way"]}, {"description": "This tag contains a phonetic guide to pronouncing the sinage information to the given edge in the graph which is helpful to know to where a given road leads. Phonetic Alphabet: jeita|katakana|nt-sampa. Forward and backward allow for distinct values based on the direction the way is drawn specifically useful for single carriageways.", "key": "destination:backward:pronunciation:<alphabet>", "object_types": ["way"]}, {"description": "Adds sinage information in a given language to the given edge in the graph which is helpful to know to where a given road leads. Forward and backward allow for distinct values based on the direction the way is drawn specifically useful for single carriageways.", "key": "destination:backward:lang:<lg>", "object_types": ["way"]}, {"description": "This tag contains a phonetic guide(IPA) in a given language to pronouncing the sinage information to the given edge in the graph which is helpful to know to where a given road leads. Forward and backward allow for distinct values based on the direction the way is drawn specifically useful for single carriageways.", "key": "destination:backward:lang:<lg>:pronunciation", "object_types": ["way"]}, {"description": "This tag contains a phonetic guide in a given language to pronouncing the sinage information to the given edge in the graph which is helpful to know to where a given road leads. Phonetic Alphabet: jeita|katakana|nt-sampa. Forward and backward allow for distinct values based on the direction the way is drawn specifically useful for single carriageways.", "key": "destination:backward:lang:<lg>:pronunciation:<alphabet>", "object_types": ["way"]}, {"description": "Adds sinage information to the given edge in the graph which is helpful to know to where a given road leads. Forward and backward allow for distinct values based on the direction the way is drawn specifically useful for single carriageways.", "key": "destination:forward", "object_types": ["way"]}, {"description": "This tag contains a phonetic guide(IPA) to pronouncing the sinage information to the given edge in the graph which is helpful to know to where a given road leads. Forward and backward allow for distinct values based on the direction the way is drawn specifically useful for single carriageways.", "key": "destination:forward:pronunciation", "object_types": ["way"]}, {"description": "This tag contains a phonetic guide to pronouncing the sinage information to the given edge in the graph which is helpful to know to where a given road leads. Phonetic Alphabet: jeita|katakana|nt-sampa.  Forward and backward allow for distinct values based on the direction the way is drawn specifically useful for single carriageways.", "key": "destination:forward:pronunciation:<alphabet>", "object_types": ["way"]}, {"description": "Adds sinage information in a given language to the given edge in the graph which is helpful to know to where a given road leads. Forward and backward allow for distinct values based on the direction the way is drawn specifically useful for single carriageways.", "key": "destination:forward:lang:<lg>", "object_types": ["way"]}, {"description": "This tag contains a phonetic guide(IPA) in a given language to pronouncing the sinage information to the given edge in the graph which is helpful to know to where a given road leads. Forward and backward allow for distinct values based on the direction the way is drawn specifically useful for single carriageways.", "key": "destination:forward:lang:<lg>:pronunciation", "object_types": ["way"]}, {"description": "This tag contains a phonetic guide in a given language to pronouncing the sinage information to the given edge in the graph which is helpful to know to where a given road leads. Phonetic Alphabet: jeita|katakana|nt-sampa.  Forward and backward allow for distinct values based on the direction the way is drawn specifically useful for single carriageways.", "key": "destination:forward:lang:<lg>:pronunciation:<alphabet>", "object_types": ["way"]}, {"key": "destination:ref", "description": "Branch route number for sinage ie. exit information, see: https://wiki.openstreetmap.org/wiki/Exit_Info", "object_types": ["way"]}, {"key": "destination:ref:pronunciation", "description": "This tag contains a phonetic guide(IPA) to pronouncing the branch route number for sinage ie. exit information, see: https://wiki.openstreetmap.org/wiki/Exit_Info", "object_types": ["way"]}, {"key": "destination:ref:pronunciation:<alphabet>", "description": "This tag contains a phonetic guide to pronouncing the branch route number for sinage ie. exit information, see: https://wiki.openstreetmap.org/wiki/Exit_Info  Phonetic Alphabet: jeita|katakana|nt-sampa.", "object_types": ["way"]}, {"key": "destination:ref:lang:<lg>", "description": "Branch route number in a given language for sinage ie. exit information, see: https://wiki.openstreetmap.org/wiki/Exit_Info", "object_types": ["way"]}, {"key": "destination:ref:lang:<lg>:pronunciation", "description": "This tag contains a phonetic guide(IPA) to pronouncing the branch route number in a given language for sinage ie. exit information, see: https://wiki.openstreetmap.org/wiki/Exit_Info", "object_types": ["way"]}, {"key": "destination:ref:lang:<lg>:pronunciation:<alphabet>", "description": "This tag contains a phonetic guide to pronouncing the branch route number in a given language for sinage ie. exit information, see: https://wiki.openstreetmap.org/wiki/Exit_Info  Phonetic Alphabet: jeita|katakana|nt-sampa.", "object_types": ["way"]}, {"key": "destination:ref:to", "description": "Toward route number for sinage ie. exit information, see: https://wiki.openstreetmap.org/wiki/Exit_Info", "object_types": ["way"]}, {"key": "destination:ref:to:pronunciation", "description": "This tag contains a phonetic guide(IPA) to pronouncing the toward route number for sinage ie. exit information, see: https://wiki.openstreetmap.org/wiki/Exit_Info", "object_types": ["way"]}, {"key": "destination:ref:to:pronunciation:<alphabet>", "description": "This tag contains a phonetic guide to pronouncing the toward route number for sinage ie. exit information, see: https://wiki.openstreetmap.org/wiki/Exit_Info  Phonetic Alphabet: jeita|katakana|nt-sampa.", "object_types": ["way"]}, {"key": "destination:ref:to:lang:<lg>", "description": "Toward route number in a given language for sinage ie. exit information, see: https://wiki.openstreetmap.org/wiki/Exit_Info", "object_types": ["way"]}, {"key": "destination:ref:to:lang:<lg>:pronunciation", "description": "This tag contains a phonetic guide(IPA) to pronouncing the toward route number in a given language for sinage ie. exit information, see: https://wiki.openstreetmap.org/wiki/Exit_Info", "object_types": ["way"]}, {"key": "destination:ref:to:lang:<lg>:pronunciation:<alphabet>", "description": "This tag contains a phonetic guide to pronouncing the toward route number in a given language for sinage ie. exit information, see: https://wiki.openstreetmap.org/wiki/Exit_Info  Phonetic Alphabet: jeita|katakana|nt-sampa.", "object_types": ["way"]}, {"key": "destination:street", "description": "Branch road name for sinage ie. exit information, see: https://wiki.openstreetmap.org/wiki/Exit_Info", "object_types": ["way"]}, {"key": "destination:street:pronunciation", "description": "This tag contains a phonetic guide(IPA) to pronouncing the branch road name for sinage ie. exit information, see: https://wiki.openstreetmap.org/wiki/Exit_Info", "object_types": ["way"]}, {"key": "destination:street:pronunciation:<alphabet>", "description": "This tag contains a phonetic guide to pronouncing the branch road name for sinage ie. exit information, see: https://wiki.openstreetmap.org/wiki/Exit_Info  Phonetic Alphabet: jeita|katakana|nt-sampa.", "object_types": ["way"]}, {"key": "destination:street:lang:<lg>", "description": "Branch road name in a given language for sinage ie. exit information, see: https://wiki.openstreetmap.org/wiki/Exit_Info", "object_types": ["way"]}, {"key": "destination:street:lang:<lg>:pronunciation", "description": "This tag contains a phonetic guide(IPA) to pronouncing the branch road name in a given language for sinage ie. exit information, see: https://wiki.openstreetmap.org/wiki/Exit_Info", "object_types": ["way"]}, {"key": "destination:street:lang:<lg>:pronunciation:<alphabet>", "description": "This tag contains a phonetic guide to pronouncing the branch road name in a given language for sinage ie. exit information, see: https://wiki.openstreetmap.org/wiki/Exit_Info  Phonetic Alphabet: jeita|katakana|nt-sampa.", "object_types": ["way"]}, {"key": "destination:street:to", "description": "Toward road name for sinage ie. exit information, see: https://wiki.openstreetmap.org/wiki/Exit_Info", "object_types": ["way"]}, {"key": "destination:street:to:pronunciation", "description": "This tag contains a phonetic guide(IPA) to pronouncing the toward road name for sinage ie. exit information, see: https://wiki.openstreetmap.org/wiki/Exit_Info", "object_types": ["way"]}, {"key": "destination:street:to:pronunciation:<alphabet>", "description": "This tag contains a phonetic guide to pronouncing the toward road name for sinage ie. exit information, see: https://wiki.openstreetmap.org/wiki/Exit_Info  Phonetic Alphabet: jeita|katakana|nt-sampa.", "object_types": ["way"]}, {"key": "destination:street:to:lang:<lg>", "description": "Toward road name in a given language for sinage ie. exit information, see: https://wiki.openstreetmap.org/wiki/Exit_Info", "object_types": ["way"]}, {"key": "destination:street:to:lang:<lg>:pronunciation", "description": "This tag contains a phonetic guide(IPA) to pronouncing the toward road name in a given language for sinage ie. exit information, see: https://wiki.openstreetmap.org/wiki/Exit_Info", "object_types": ["way"]}, {"key": "destination:street:to:lang:<lg>:pronunciation:<alphabet>", "description": "This tag contains a phonetic guide to pronouncing the toward road name in a given language for sinage ie. exit information, see: https://wiki.openstreetmap.org/wiki/Exit_Info  Phonetic Alphabet: jeita|katakana|nt-sampa.", "object_types": ["way"]}, {"key": "emergency", "value": "yes", "object_types": ["node", "way"], "description": "Overrides access flags for emergency vehicles.  Emergency Routing is allowed."}, {"description": "Adds conditional access to an edge with a time dependency in opening_hours format, see: https://wiki.openstreetmap.org/wiki/Key:opening_hours", "value": "no @ (opening_hours format)", "key": "emergency:conditional", "object_types": ["way"]}, {"description": "Adds conditional access to an edge with a time dependency in opening_hours format, see: https://wiki.openstreetmap.org/wiki/Key:opening_hours", "value": "yes @ (opening_hours format)", "key": "emergency:conditional", "object_types": ["way"]}, {"description": "Adds conditional access to an edge with a time dependency in opening_hours format, see: https://wiki.openstreetmap.org/wiki/Key:opening_hours", "value": "private @ (opening_hours format)", "key": "emergency:conditional", "object_types": ["way"]}, {"description": "Adds conditional access to an edge with a time dependency in opening_hours format, see: https://wiki.openstreetmap.org/wiki/Key:opening_hours", "value": "delivery @ (opening_hours format)", "key": "emergency:conditional", "object_types": ["way"]}, {"description": "Adds conditional access to an edge with a time dependency in opening_hours format, see: https://wiki.openstreetmap.org/wiki/Key:opening_hours", "value": "designated @ (opening_hours format)", "key": "emergency:conditional", "object_types": ["way"]}, {"description": "Adds conditional access to an edge with a time dependency in opening_hours format, see: https://wiki.openstreetmap.org/wiki/Key:opening_hours", "value": "destination @ (opening_hours format)", "key": "emergency:conditional", "object_types": ["way"]}, {"key": "entrance", "value": "yes", "object_types": ["node"], "description": "The point where you can go into a building or enclosed area."}, {"description": "Exceptions for a specific mode of transport on a turn restriction where all other modes are restricted", "value": "psv", "key": "except", "object_types": ["relation"]}, {"description": "Exceptions for a specific mode of transport on a turn restriction where all other modes are restricted", "value": "bicycle", "key": "except", "object_types": ["relation"]}, {"description": "Exceptions for a specific mode of transport on a turn restriction where all other modes are restricted", "value": "hgv", "key": "except", "object_types": ["relation"]}, {"description": "Exceptions for a specific mode of transport on a turn restriction where all other modes are restricted", "value": "motorcar", "key": "except", "object_types": ["relation"]}, {"description": "Exceptions for a specific mode of transport on a turn restriction where all other modes are restricted", "value": "emergency", "key": "except", "object_types": ["relation"]}, {"key": "exit_to", "object_types": ["node"], "description": "Branch route number/road name.  And/or...Toward route number/road name.  And/or...Toward location. Form more information see: https://wiki.openstreetmap.org/wiki/Exit_Info"}, {"key": "foot", "value": "no", "object_types": ["node", "way"], "description": "Overrides access flags for pedestrian.  Pedestrian Routing not allowed."}, {"key": "foot", "value": "agricultural", "object_types": ["node", "way"], "description": "Overrides access flags for pedestrian.  Pedestrian Routing not allowed."}, {"key": "foot", "value": "discouraged", "object_types": ["node", "way"], "description": "Overrides access flags for pedestrian.  Pedestrian Routing not allowed."}, {"key": "foot", "value": "forestry", "object_types": ["node", "way"], "description": "Overrides access flags for pedestrian.  Pedestrian Routing not allowed."}, {"key": "foot", "value": "official", "object_types": ["node", "way"], "description": "Overrides access flags for pedestrian.  Pedestrian Routing allowed."}, {"key": "foot", "value": "restricted", "object_types": ["node", "way"], "description": "Overrides access flags for pedestrian.  Pedestrian Routing allowed."}, {"key": "foot", "value": "yes", "object_types": ["node", "way"], "description": "Overrides access flags for pedestrian.  Pedestrian Routing allowed."}, {"key": "foot", "value": "private", "object_types": ["node", "way"], "description": "Overrides access flags for pedestrian.  Pedestrian Routing allowed."}, {"key": "foot", "value": "permit", "object_types": ["node", "way"], "description": "Overrides access flags for pedestrian.  Pedestrian Routing allowed."}, {"key": "foot", "value": "residents", "object_types": ["node", "way"], "description": "Overrides access flags for pedestrian.  Pedestrian Routing allowed."}, {"key": "foot", "value": "permissive", "object_types": ["node", "way"], "description": "Overrides access flags for pedestrian.  Pedestrian Routing allowed."}, {"key": "foot", "value": "use_sidepath", "object_types": ["node", "way"], "description": "Overrides access flags for pedestrian.  Pedestrian Routing allowed."}, {"key": "foot", "value": "delivery", "object_types": ["node", "way"], "description": "Overrides access flags for pedestrian.  Pedestrian Routing allowed."}, {"key": "foot", "value": "designated", "object_types": ["node", "way"], "description": "Overrides access flags for pedestrian.  Pedestrian Routing allowed."}, {"key": "foot", "value": "destination", "object_types": ["node", "way"], "description": "Overrides access flags for pedestrian.  Pedestrian Routing allowed."}, {"key": "foot", "value": "customers", "object_types": ["node", "way"], "description": "Overrides access flags for pedestrian.  Pedestrian Routing allowed."}, {"key": "foot", "value": "public", "object_types": ["node", "way"], "description": "Overrides access flags for pedestrian.  Pedestrian Routing allowed."}, {"key": "foot", "value": "crossing", "object_types": ["node", "way"], "description": "Overrides access flags for pedestrian.  Pedestrian Routing allowed.  For nodes: Allows access for all modes unless the mode override is set."}, {"key": "foot", "value": "sidewalk", "object_types": ["node", "way"], "description": "Overrides access flags for pedestrian.  Pedestrian Routing allowed."}, {"key": "foot", "value": "allowed", "object_types": ["node", "way"], "description": "Overrides access flags for pedestrian.  Pedestrian Routing allowed."}, {"key": "foot", "value": "passable", "object_types": ["node", "way"], "description": "Overrides access flags for pedestrian.  Pedestrian Routing allowed."}, {"key": "foot", "value": "footway", "object_types": ["node", "way"], "description": "Overrides access flags for pedestrian.  Pedestrian Routing allowed."}, {"key": "foot:backward", "value": "yes", "object_types": ["way"], "description": "Sets the foot reverse flag to true."}, {"description": "Adds conditional access to an edge with a time dependency in opening_hours format, see: https://wiki.openstreetmap.org/wiki/Key:opening_hours", "value": "no @ (opening_hours format)", "key": "foot:conditional", "object_types": ["way"]}, {"description": "Adds conditional access to an edge with a time dependency in opening_hours format, see: https://wiki.openstreetmap.org/wiki/Key:opening_hours", "value": "yes @ (opening_hours format)", "key": "foot:conditional", "object_types": ["way"]}, {"description": "Adds conditional access to an edge with a time dependency in opening_hours format, see: https://wiki.openstreetmap.org/wiki/Key:opening_hours", "value": "private @ (opening_hours format)", "key": "foot:conditional", "object_types": ["way"]}, {"description": "Adds conditional access to an edge with a time dependency in opening_hours format, see: https://wiki.openstreetmap.org/wiki/Key:opening_hours", "value": "delivery @ (opening_hours format)", "key": "foot:conditional", "object_types": ["way"]}, {"description": "Adds conditional access to an edge with a time dependency in opening_hours format, see: https://wiki.openstreetmap.org/wiki/Key:opening_hours", "value": "designated @ (opening_hours format)", "key": "foot:conditional", "object_types": ["way"]}, {"description": "Adds conditional access to an edge with a time dependency in opening_hours format, see: https://wiki.openstreetmap.org/wiki/Key:opening_hours", "value": "destination @ (opening_hours format)", "key": "foot:conditional", "object_types": ["way"]}, {"key": "footway", "value": "crossing", "object_types": ["node", "way"], "description": "For nodes: allows access for all modes unless the mode override is set.  For ways: sets the use to Pedestrian Crossing "}, {"key": "hazmat", "value": "designated", "object_types": ["way"], "description": "Sets the hazmat flag to true."}, {"key": "hazmat", "value": "yes", "object_types": ["way"], "description": "Sets the hazmat flag to true."}, {"key": "hazmat", "value": "no", "object_types": ["way"], "description": "Sets the hazmat flag to false."}, {"key": "hazmat", "value": "destination", "object_types": ["way"], "description": "Sets the hazmat flag to true."}, {"key": "hazmat", "value": "delivery", "object_types": ["way"], "description": "Sets the hazmat flag to true."}, {"key": "hazmat:A", "value": "designated", "object_types": ["way"], "description": "Sets the hazmat flag to true."}, {"key": "hazmat:A", "value": "yes", "object_types": ["way"], "description": "Sets the hazmat flag to true."}, {"key": "hazmat:A", "value": "no", "object_types": ["way"], "description": "Sets the hazmat flag to false."}, {"key": "hazmat:A", "value": "destination", "object_types": ["way"], "description": "Sets the hazmat flag to true."}, {"key": "hazmat:A", "value": "delivery", "object_types": ["way"], "description": "Sets the hazmat flag to true."}, {"key": "hazmat:B", "value": "designated", "object_types": ["way"], "description": "Sets the hazmat flag to true."}, {"key": "hazmat:B", "value": "yes", "object_types": ["way"], "description": "Sets the hazmat flag to true."}, {"key": "hazmat:B", "value": "no", "object_types": ["way"], "description": "Sets the hazmat flag to false."}, {"key": "hazmat:B", "value": "destination", "object_types": ["way"], "description": "Sets the hazmat flag to true."}, {"key": "hazmat:B", "value": "delivery", "object_types": ["way"], "description": "Sets the hazmat flag to true."}, {"key": "hazmat:C", "value": "designated", "object_types": ["way"], "description": "Sets the hazmat flag to true."}, {"key": "hazmat:C", "value": "yes", "object_types": ["way"], "description": "Sets the hazmat flag to true."}, {"key": "hazmat:C", "value": "no", "object_types": ["way"], "description": "Sets the hazmat flag to false."}, {"key": "hazmat:C", "value": "destination", "object_types": ["way"], "description": "Sets the hazmat flag to true."}, {"key": "hazmat:C", "value": "delivery", "object_types": ["way"], "description": "Sets the hazmat flag to true."}, {"key": "hazmat:D", "value": "designated", "object_types": ["way"], "description": "Sets the hazmat flag to true."}, {"key": "hazmat:D", "value": "yes", "object_types": ["way"], "description": "Sets the hazmat flag to true."}, {"key": "hazmat:D", "value": "no", "object_types": ["way"], "description": "Sets the hazmat flag to false."}, {"key": "hazmat:D", "value": "destination", "object_types": ["way"], "description": "Sets the hazmat flag to true."}, {"key": "hazmat:D", "value": "delivery", "object_types": ["way"], "description": "Sets the hazmat flag to true."}, {"key": "hazmat:E", "value": "designated", "object_types": ["way"], "description": "Sets the hazmat flag to true."}, {"key": "hazmat:E", "value": "yes", "object_types": ["way"], "description": "Sets the hazmat flag to true."}, {"key": "hazmat:E", "value": "no", "object_types": ["way"], "description": "Sets the hazmat flag to false."}, {"key": "hazmat:E", "value": "destination", "object_types": ["way"], "description": "Sets the hazmat flag to true."}, {"key": "hazmat:E", "value": "delivery", "object_types": ["way"], "description": "Sets the hazmat flag to true."}, {"key": "hazmat:water", "value": "designated", "object_types": ["way"], "description": "Sets the hazmat flag to true."}, {"key": "hazmat:water", "value": "yes", "object_types": ["way"], "description": "Sets the hazmat flag to true."}, {"key": "hazmat:water", "value": "no", "object_types": ["way"], "description": "Sets the hazmat flag to false."}, {"key": "hazmat:water", "value": "destination", "object_types": ["way"], "description": "Sets the hazmat flag to true."}, {"key": "hazmat:water", "value": "delivery", "object_types": ["way"], "description": "Sets the hazmat flag to true."}, {"key": "hgv", "value": "designated", "object_types": ["node", "way"], "description": "Overrides access flags for hgv.  Hgv Routing is allowed. Sets the truck_route flag to true."}, {"key": "hgv", "value": "yes", "object_types": ["node", "way"], "description": "Overrides access flags for hgv.  Hgv Routing is allowed."}, {"key": "hgv", "value": "no", "object_types": ["node", "way"], "description": "Overrides access flags for hgv.  Hgv Routing is not allowed."}, {"key": "hgv", "value": "destination", "object_types": ["node", "way"], "description": "Overrides access flags for hgv.  Hgv Routing is allowed."}, {"key": "hgv", "value": "delivery", "object_types": ["node", "way"], "description": "Overrides access flags for hgv.  Hgv Routing is allowed."}, {"key": "hgv", "value": "local", "object_types": ["node", "way"], "description": "Overrides access flags for hgv.  Hgv Routing is allowed. Sets the truck_route flag to true."}, {"key": "hgv", "value": "agricultural", "object_types": ["node", "way"], "description": "Overrides access flags for hgv.  Hgv Routing is not allowed."}, {"key": "hgv", "value": "private", "object_types": ["node", "way"], "description": "Overrides access flags for hgv.  Hgv Routing is allowed."}, {"key": "hgv", "value": "permit", "object_types": ["node", "way"], "description": "Overrides access flags for hgv.  Hgv Routing is allowed."}, {"key": "hgv", "value": "residents", "object_types": ["node", "way"], "description": "Overrides access flags for hgv.  Hgv Routing is allowed."}, {"key": "hgv", "value": "discouraged", "object_types": ["node", "way"], "description": "Overrides access flags for hgv.  Hgv Routing is not allowed."}, {"key": "hgv", "value": "permissive", "object_types": ["node", "way"], "description": "Overrides access flags for hgv.  Hgv Routing is not allowed."}, {"key": "hgv", "value": "unsuitable", "object_types": ["node", "way"], "description": "Overrides access flags for hgv.  Hgv Routing is not allowed."}, {"key": "hgv", "value": "agricultural;forestry", "object_types": ["node", "way"], "description": "Overrides access flags for hgv.  Hgv Routing is not allowed."}, {"key": "hgv", "value": "official", "object_types": ["node", "way"], "description": "Overrides access flags for hgv.  Hgv Routing is not allowed."}, {"key": "hgv", "value": "forestry", "object_types": ["node", "way"], "description": "Overrides access flags for hgv.  Hgv Routing is not allowed."}, {"key": "hgv", "value": "destination;delivery", "object_types": ["node", "way"], "description": "Overrides access flags for hgv.  Hgv Routing is not allowed."}, {"description": "Adds conditional access to an edge with a time dependency in opening_hours format, see: https://wiki.openstreetmap.org/wiki/Key:opening_hours", "value": "no @ (opening_hours format)", "key": "hgv:conditional", "object_types": ["way"]}, {"description": "Adds conditional access to an edge with a time dependency in opening_hours format, see: https://wiki.openstreetmap.org/wiki/Key:opening_hours", "value": "yes @ (opening_hours format)", "key": "hgv:conditional", "object_types": ["way"]}, {"description": "Adds conditional access to an edge with a time dependency in opening_hours format, see: https://wiki.openstreetmap.org/wiki/Key:opening_hours", "value": "private @ (opening_hours format)", "key": "hgv:conditional", "object_types": ["way"]}, {"description": "Adds conditional access to an edge with a time dependency in opening_hours format, see: https://wiki.openstreetmap.org/wiki/Key:opening_hours", "value": "delivery @ (opening_hours format)", "key": "hgv:conditional", "object_types": ["way"]}, {"description": "Adds conditional access to an edge with a time dependency in opening_hours format, see: https://wiki.openstreetmap.org/wiki/Key:opening_hours", "value": "designated @ (opening_hours format)", "key": "hgv:conditional", "object_types": ["way"]}, {"description": "Adds conditional access to an edge with a time dependency in opening_hours format, see: https://wiki.openstreetmap.org/wiki/Key:opening_hours", "value": "destination @ (opening_hours format)", "key": "hgv:conditional", "object_types": ["way"]}, {"key": "hgv:national_network", "object_types": ["way"], "description": "Sets the truck_route flag to true."}, {"key": "hgv:state_network", "object_types": ["way"], "description": "Sets the truck_route flag to true."}, {"key": "highway", "value": "motorway", "object_types": ["way"]}, {"key": "highway", "value": "motorway_link", "object_types": ["way"]}, {"key": "highway", "value": "motorway_junction", "object_types": ["node"]}, {"key": "highway", "value": "traffic_signals", "object_types": ["node"]}, {"key": "highway", "value": "stop", "object_types": ["node"]}, {"key": "highway", "value": "give_way", "object_types": ["node"]}, {"key": "highway", "value": "trunk", "object_types": ["way"]}, {"key": "highway", "value": "trunk_link", "object_types": ["way"]}, {"key": "highway", "value": "primary", "object_types": ["way"]}, {"key": "highway", "value": "primary_link", "object_types": ["way"]}, {"key": "highway", "value": "secondary", "object_types": ["way"]}, {"key": "highway", "value": "secondary_link", "object_types": ["way"]}, {"key": "highway", "value": "residential", "object_types": ["way"]}, {"key": "highway", "value": "residential_link", "object_types": ["way"]}, {"key": "highway", "value": "service", "object_types": ["way"]}, {"key": "highway", "value": "tertiary", "object_types": ["way"]}, {"key": "highway", "value": "tertiary_link", "object_types": ["way"]}, {"key": "highway", "value": "road", "object_types": ["way"]}, {"key": "highway", "value": "track", "object_types": ["way"]}, {"key": "highway", "value": "unclassified", "object_types": ["way"]}, {"key": "highway", "value": "undefined", "object_types": ["way"]}, {"key": "highway", "value": "unknown", "object_types": ["way"]}, {"key": "highway", "value": "living_street", "object_types": ["way"]}, {"key": "highway", "value": "footway", "object_types": ["way"]}, {"key": "highway", "value": "pedestrian", "object_types": ["way"]}, {"key": "highway", "value": "corridor", "object_types": ["way"]}, {"key": "highway", "value": "elevator", "object_types": ["node", "way"]}, {"key": "highway", "value": "steps", "object_types": ["way"]}, {"key": "highway", "value": "bridleway", "object_types": ["way"]}, {"key": "highway", "value": "construction", "object_types": ["way"]}, {"key": "highway", "value": "proposed", "object_types": ["way"]}, {"key": "highway", "value": "cycleway", "object_types": ["way"]}, {"key": "highway", "value": "path", "object_types": ["way"]}, {"key": "highway", "value": "bus_guideway", "object_types": ["way"]}, {"key": "highway", "value": "busway", "object_types": ["way"]}, {"key": "highway", "value": "crossing", "object_types": ["node"], "description": "Allows access for all modes unless the mode override is set except for bicycles."}, {"key": "construction", "value": "motorway", "object_types": ["way"], "description": "Motorway road under construction."}, {"key": "construction", "value": "motorway_link", "object_types": ["way"], "description": "Motorway road link under construction."}, {"key": "construction", "value": "trunk", "object_types": ["way"], "description": "Trunk road under construction."}, {"key": "construction", "value": "trunk_link", "object_types": ["way"], "description": "Trunk road link under construction."}, {"key": "construction", "value": "primary", "object_types": ["way"], "description": "Primary road under construction."}, {"key": "construction", "value": "primary_link", "object_types": ["way"], "description": "Primary road link under construction."}, {"key": "construction", "value": "secondary", "object_types": ["way"], "description": "Secondary road under construction."}, {"key": "construction", "value": "secondary_link", "object_types": ["way"], "description": "Secondary road link under construction."}, {"key": "construction", "value": "tertiary", "object_types": ["way"], "description": "Tertiary road under construction."}, {"key": "construction", "value": "tertiary_link", "object_types": ["way"], "description": "Tertiary road link under construction."}, {"key": "construction", "value": "unclassified", "object_types": ["way"], "description": "Minor public road under construction."}, {"key": "construction", "value": "undefined", "object_types": ["way"], "description": "Road with unknown classification under construction."}, {"key": "construction", "value": "unknown", "object_types": ["way"], "description": "Road with unknown classification under construction."}, {"key": "construction", "value": "road", "object_types": ["way"], "description": "Road with unknown classification under construction."}, {"key": "construction", "value": "residential", "object_types": ["way"], "description": "Residential road under construction."}, {"key": "construction", "value": "residential_link", "object_types": ["way"], "description": "Residential road link under construction."}, {"key": "construction", "value": "living_street", "object_types": ["way"], "description": "Living street under construction."}, {"key": "construction", "value": "pedestrian", "object_types": ["way"], "description": "Pedestrian road under construction."}, {"key": "construction", "value": "service", "object_types": ["way"], "description": "Service road under construction."}, {"key": "construction", "value": "track", "object_types": ["way"], "description": "Track road under construction."}, {"key": "construction", "value": "bridleway", "object_types": ["way"], "description": "Bridleway under construction."}, {"key": "construction", "value": "cycleway", "object_types": ["way"], "description": "Cycleway under construction."}, {"key": "construction", "value": "footway", "object_types": ["way"], "description": "Foot path under construction."}, {"key": "construction", "value": "path", "object_types": ["way"], "description": "Path under construction."}, {"key": "construction", "value": "steps", "object_types": ["way"], "description": "Steps under construction."}, {"key": "hour_off", "object_types": ["relation"]}, {"key": "hour_on", "object_types": ["relation"]}, {"key": "hov", "object_types": ["way", "node"], "description": "Allows access for hov modes unless the value is no."}, {"description": "Adds conditional access to an edge with a time dependency in opening_hours format, see: https://wiki.openstreetmap.org/wiki/Key:opening_hours", "value": "no @ (opening_hours format)", "key": "hov:conditional", "object_types": ["way"]}, {"description": "Adds conditional access to an edge with a time dependency in opening_hours format, see: https://wiki.openstreetmap.org/wiki/Key:opening_hours", "value": "yes @ (opening_hours format)", "key": "hov:conditional", "object_types": ["way"]}, {"description": "Adds conditional access to an edge with a time dependency in opening_hours format, see: https://wiki.openstreetmap.org/wiki/Key:opening_hours", "value": "private @ (opening_hours format)", "key": "hov:conditional", "object_types": ["way"]}, {"description": "Adds conditional access to an edge with a time dependency in opening_hours format, see: https://wiki.openstreetmap.org/wiki/Key:opening_hours", "value": "delivery @ (opening_hours format)", "key": "hov:conditional", "object_types": ["way"]}, {"description": "Adds conditional access to an edge with a time dependency in opening_hours format, see: https://wiki.openstreetmap.org/wiki/Key:opening_hours", "value": "designated @ (opening_hours format)", "key": "hov:conditional", "object_types": ["way"]}, {"description": "Adds conditional access to an edge with a time dependency in opening_hours format, see: https://wiki.openstreetmap.org/wiki/Key:opening_hours", "value": "destination @ (opening_hours format)", "key": "hov:conditional", "object_types": ["way"]}, {"key": "hov:lanes", "object_types": ["way", "node"], "description": "Allows access for hov."}, {"key": "hov:minimum", "object_types": ["way", "node"], "description": "Allows access for hov."}, {"key": "payment:*", "object_types": ["node"], "description": "Payment keys/values (for tolls)."}, {"key": "impassable", "value": "yes", "object_types": ["node", "way"], "description": "This is used by HOT.  Turns off access unless bus, motorcar, motor_vehicle, foot, etc. is set."}, {"key": "indoor", "object_types": ["node", "way"], "description": "Used for indoor elements."}, {"key": "int_ref", "object_types": ["way"], "description": "International route network and number."}, {"key": "int_ref:pronunciation", "object_types": ["way"], "description": "This tag contains a phonetic guide(IPA) to pronouncing the international route network and number."}, {"key": "int_ref:pronunciation:<alphabet>", "object_types": ["way"], "description": "This tag contains a phonetic guide to pronouncing the international route network and number.  Phonetic Alphabet: jeita|katakana|nt-sampa."}, {"key": "int_ref:<lg>", "object_types": ["way"], "description": "International route network and number in a given language."}, {"key": "int_ref:<lg>:pronunciation", "object_types": ["way"], "description": "This tag contains a phonetic guide(IPA) to pronouncing the international route network and number in a given language."}, {"key": "int_ref:<lg>:pronunciation:<alphabet>", "object_types": ["way"], "description": "This tag contains a phonetic guide to pronouncing the international route network and number in a given language.  Phonetic Alphabet: jeita|katakana|nt-sampa."}, {"key": "junction", "value": "roundabout", "object_types": ["way"], "description": "Sets auto backward to false."}, {"key": "junction", "value": "circular", "object_types": ["way"], "description": "Sets auto backward to false."}, {"key": "junction:ref", "description": "Junction/exit number for signage (i.e. exit information, see: https://wiki.openstreetmap.org/wiki/Exit_Info)", "object_types": ["way"]}, {"key": "junction:ref:pronunciation", "description": "This tag contains a phonetic guide(IPA) to pronouncing the junction/exit number for signage (i.e. exit information, see: https://wiki.openstreetmap.org/wiki/Exit_Info)", "object_types": ["way"]}, {"key": "junction:ref:pronunciation:<alphabet>", "description": "This tag contains a phonetic guide to pronouncing the junction/exit number for signage (i.e. exit information, see: https://wiki.openstreetmap.org/wiki/Exit_Info)  Phonetic Alphabet: jeita|katakana|nt-sampa.", "object_types": ["way"]}, {"key": "junction:ref:<lg>", "description": "Junction/exit number for signage in a given language (i.e. exit information, see: https://wiki.openstreetmap.org/wiki/Exit_Info)", "object_types": ["way"]}, {"key": "junction:ref:<lg>:pronunciation", "description": "This tag contains a phonetic guide(IPA) to pronouncing the junction/exit number for signage in a given language (i.e. exit information, see: https://wiki.openstreetmap.org/wiki/Exit_Info)", "object_types": ["way"]}, {"key": "junction:ref:<lg>:pronunciation:<alphabet>", "description": "This tag contains a phonetic guide to pronouncing the junction/exit number for signage in a given language (i.e. exit information, see: https://wiki.openstreetmap.org/wiki/Exit_Info)  Phonetic Alphabet: jeita|katakana|nt-sampa.", "object_types": ["way"]}, {"key": "junction:name", "description": "Junction name", "object_types": ["way"]}, {"key": "junction:name:pronunciation", "description": "This tag contains a phonetic guide(IPA) to pronouncing the junction name", "object_types": ["way"]}, {"key": "junction:name:pronunciation:<alphabet>", "description": "This tag contains a phonetic guide to pronouncing the junction name.  Phonetic Alphabet: jeita|katakana|nt-sampa.", "object_types": ["way"]}, {"key": "junction:name:<lg>", "description": "Junction name in a given language", "object_types": ["way"]}, {"key": "junction:name:<lg>:pronunciation", "description": "This tag contains a phonetic guide(IPA) to pronouncing the junction name in a given language", "object_types": ["way"]}, {"key": "junction:name:<lg>:pronunciation:<alphabet>", "description": "This tag contains a phonetic guide to pronouncing the junction name in a given language.  Phonetic Alphabet: jeita|katakana|nt-sampa.", "object_types": ["way"]}, {"key": "lanes", "description": "Lane count", "object_types": ["way"]}, {"key": "lanes:backward", "description": "Lane count in the backward direction", "object_types": ["way"]}, {"key": "lanes:bus", "value": "1", "object_types": ["way"], "description": "Bus forward override.  Sets bus forward to true."}, {"key": "lanes:bus", "value": "2", "object_types": ["way"], "description": "Bus forward and backward overrides.  Sets bus forward and backward to true."}, {"key": "lanes:forward", "description": "Lane count in the forward direction", "object_types": ["way"]}, {"key": "lanes:psv:backward", "value": "bus", "object_types": ["way"], "description": "Overrides oneway flag.  Sets bus reverse flag to true."}, {"key": "lanes:psv:backward", "value": "yes", "object_types": ["way"], "description": "Overrides oneway flag.  Sets bus reverse flag to true."}, {"key": "lanes:psv:backward", "value": "designated", "object_types": ["way"], "description": "Overrides oneway flag.  Sets bus reverse flag to true."}, {"key": "lanes:psv:backward", "value": "permissive", "object_types": ["way"], "description": "Overrides oneway flag.  Sets bus reverse flag to true."}, {"key": "lanes:psv:backward", "value": "1", "object_types": ["way"], "description": "Overrides oneway flag.  Sets bus reverse flag to true."}, {"key": "lanes:psv:backward", "value": "2", "object_types": ["way"], "description": "Overrides oneway flag.  Sets bus reverse flag to true."}, {"key": "lanes:psv:forward", "value": "no", "object_types": ["node", "way"], "description": "Overrides access flags for bus.  Bus Routing not allowed."}, {"key": "lanes:psv:forward", "value": "delivery", "object_types": ["node", "way"], "description": "Overrides access flags for bus.  Bus Routing not allowed."}, {"key": "lanes:psv:forward", "value": "yes", "object_types": ["node", "way"], "description": "Overrides access flags for bus.  Bus Routing allowed."}, {"key": "lanes:psv:forward", "value": "designated", "object_types": ["node", "way"], "description": "Overrides access flags for bus.  Bus Routing allowed."}, {"key": "lanes:psv:forward", "value": "urban", "object_types": ["node", "way"], "description": "Overrides access flags for bus.  Bus Routing allowed."}, {"key": "lanes:psv:forward", "value": "permissive", "object_types": ["node", "way"], "description": "Overrides access flags for bus.  Bus Routing allowed."}, {"key": "lanes:psv:forward", "value": "restricted", "object_types": ["node", "way"], "description": "Overrides access flags for bus.  Bus Routing allowed."}, {"key": "lanes:psv:forward", "value": "destination", "object_types": ["node", "way"], "description": "Overrides access flags for bus.  Bus Routing allowed."}, {"key": "lanes:psv:forward", "value": "bus", "object_types": ["node", "way"], "description": "Overrides access flags for bus.  Bus Routing allowed."}, {"key": "lanes:psv:forward", "value": "1", "object_types": ["node", "way"], "description": "Overrides access flags for bus.  Bus Routing allowed."}, {"key": "lanes:psv:forward", "value": "2", "object_types": ["node", "way"], "description": "Overrides access flags for bus.  Bus Routing allowed."}, {"key": "level", "description": "Floor level of a building.", "object_types": ["way"]}, {"key": "level:ref", "description": "The ref or name of the floor level of a building.", "object_types": ["way"]}, {"key": "lcn", "value": "yes", "description": "Local Bike Network.  Set bitmask for network", "object_types": ["way"]}, {"key": "lcn_ref", "description": "Local Bike Network ref.  Set bitmask for network", "object_types": ["way"]}, {"key": "maxaxleload", "object_types": ["way"], "description": "Maxaxleload for hgv."}, {"key": "maxaxles", "object_types": ["way"], "description": "Maxaxles for hgv."}, {"key": "maxheight", "object_types": ["way"], "description": "Maxheight for hgv."}, {"key": "maxheight:forward", "object_types": ["way"], "description": "Maxheight for hgv in forward direction."}, {"key": "maxheight:backward", "object_types": ["way"], "description": "Maxheight for hgv in backward direction."}, {"key": "maxheight:physical", "object_types": ["way"], "description": "Maxheight for hgv."}, {"key": "maxlength", "object_types": ["way"], "description": "Maxlength for hgv."}, {"key": "maxlength:forward", "object_types": ["way"], "description": "Maxlength for hgv in forward direction."}, {"key": "maxlength:backward", "object_types": ["way"], "description": "Maxlength for hgv in backward direction."}, {"key": "maxspeed", "description": "Speed Limit (assume kph except where mph given)", "object_types": ["way"]}, {"key": "maxspeed:advisory", "description": "Speed Limit - normally used on a curve or on/off ramps (assume kph except where mph given)", "object_types": ["way"]}, {"key": "maxspeed:backward", "description": "maximum speed which only applies in backward direction (assume kph except where mph given)", "object_types": ["way"]}, {"key": "maxspeed:forward", "description": "maximum speed which only applies in forward direction (assume kph except where mph given)", "object_types": ["way"]}, {"key": "maxspeed:hgv", "object_types": ["way"], "description": "Maxspeed for hgv."}, {"key": "maxspeed:hgv:forward", "object_types": ["way"], "description": "Maxspeed for hgv in forward direction."}, {"key": "maxspeed:hgv:backward", "object_types": ["way"], "description": "Maxspeed for hgv in backward direction."}, {"key": "maxspeed:practical", "description": "Considered the average speed on a road (assume kph except where mph given)", "object_types": ["way"]}, {"key": "maxweight", "object_types": ["way"], "description": "Maxweight for hgv."}, {"key": "maxweight:forward", "object_types": ["way"], "description": "Maxweight for hgv in forward direction."}, {"key": "maxweight:backward", "object_types": ["way"], "description": "Maxweight for hgv in backward direction."}, {"key": "maxwidth", "object_types": ["way"], "description": "Maxwidth for hgv."}, {"key": "maxwidth:forward", "object_types": ["way"], "description": "Maxwidth for hgv in forward direction."}, {"key": "maxwidth:backward", "object_types": ["way"], "description": "Maxwidth for hgv in backward direction."}, {"key": "maxwidth:physical", "object_types": ["way"], "description": "Maxwidth for hgv."}, {"key": "mofa", "value": "yes", "object_types": ["node", "way"], "description": "Overrides access flags for motor scooter.  Motor Scooter Routing allowed."}, {"key": "mofa", "value": "designated", "object_types": ["node", "way"], "description": "Overrides access flags for motor scooter.  Motor Scooter Routing allowed."}, {"key": "mofa", "value": "private", "object_types": ["node", "way"], "description": "Overrides access flags for motor scooter.  Motor Scooter Routing allowed."}, {"key": "mofa", "value": "permissive", "object_types": ["node", "way"], "description": "Overrides access flags for motor scooter.  Motor Scooter Routing allowed."}, {"key": "mofa", "value": "destination", "object_types": ["node", "way"], "description": "Overrides access flags for motor scooter.  Motor Scooter Routing allowed."}, {"key": "mofa", "value": "delivery", "object_types": ["node", "way"], "description": "Overrides access flags for motor scooter.  Motor Scooter Routing allowed."}, {"key": "mofa", "value": "dismount", "object_types": ["node", "way"], "description": "Overrides access flags for motor scooter.  Motor Scooter Routing allowed."}, {"key": "mofa", "value": "no", "object_types": ["node", "way"], "description": "Overrides access flags for motor scooter.  Motor Scooter Routing not allowed."}, {"key": "mofa", "value": "unknown", "object_types": ["node", "way"], "description": "Overrides access flags for motor scooter.  Motor Scooter Routing not allowed."}, {"key": "mofa", "value": "agricultural", "object_types": ["node", "way"], "description": "Overrides access flags for motor scooter.  Motor Scooter Routing not allowed."}, {"key": "mofa:backward", "value": "yes", "object_types": ["way"], "description": "Sets the motor scooter reverse flag to true."}, {"description": "Adds conditional access to an edge with a time dependency in opening_hours format, see: https://wiki.openstreetmap.org/wiki/Key:opening_hours", "value": "no @ (opening_hours format)", "key": "mofa:conditional", "object_types": ["way"]}, {"description": "Adds conditional access to an edge with a time dependency in opening_hours format, see: https://wiki.openstreetmap.org/wiki/Key:opening_hours", "value": "yes @ (opening_hours format)", "key": "mofa:conditional", "object_types": ["way"]}, {"description": "Adds conditional access to an edge with a time dependency in opening_hours format, see: https://wiki.openstreetmap.org/wiki/Key:opening_hours", "value": "private @ (opening_hours format)", "key": "mofa:conditional", "object_types": ["way"]}, {"description": "Adds conditional access to an edge with a time dependency in opening_hours format, see: https://wiki.openstreetmap.org/wiki/Key:opening_hours", "value": "delivery @ (opening_hours format)", "key": "mofa:conditional", "object_types": ["way"]}, {"description": "Adds conditional access to an edge with a time dependency in opening_hours format, see: https://wiki.openstreetmap.org/wiki/Key:opening_hours", "value": "designated @ (opening_hours format)", "key": "mofa:conditional", "object_types": ["way"]}, {"description": "Adds conditional access to an edge with a time dependency in opening_hours format, see: https://wiki.openstreetmap.org/wiki/Key:opening_hours", "value": "destination @ (opening_hours format)", "key": "mofa:conditional", "object_types": ["way"]}, {"key": "moped", "value": "yes", "object_types": ["node", "way"], "description": "Overrides access flags for motor scooter.  Motor Scooter Routing allowed."}, {"key": "moped", "value": "designated", "object_types": ["node", "way"], "description": "Overrides access flags for motor scooter.  Motor Scooter Routing allowed."}, {"key": "moped", "value": "private", "object_types": ["node", "way"], "description": "Overrides access flags for motor scooter.  Motor Scooter Routing allowed."}, {"key": "moped", "value": "permit", "object_types": ["node", "way"], "description": "Overrides access flags for motor scooter.  Motor Scooter Routing allowed."}, {"key": "moped", "value": "residents", "object_types": ["node", "way"], "description": "Overrides access flags for motor scooter.  Motor Scooter Routing allowed."}, {"key": "moped", "value": "permissive", "object_types": ["node", "way"], "description": "Overrides access flags for motor scooter.  Motor Scooter Routing allowed."}, {"key": "moped", "value": "destination", "object_types": ["node", "way"], "description": "Overrides access flags for motor scooter.  Motor Scooter Routing allowed."}, {"key": "moped", "value": "delivery", "object_types": ["node", "way"], "description": "Overrides access flags for motor scooter.  Motor Scooter Routing allowed."}, {"key": "moped", "value": "dismount", "object_types": ["node", "way"], "description": "Overrides access flags for motor scooter.  Motor Scooter Routing allowed."}, {"key": "moped", "value": "no", "object_types": ["node", "way"], "description": "Overrides access flags for motor scooter.  Motor Scooter Routing not allowed."}, {"key": "moped", "value": "unknown", "object_types": ["node", "way"], "description": "Overrides access flags for motor scooter.  Motor Scooter Routing not allowed."}, {"key": "moped", "value": "agricultural", "object_types": ["node", "way"], "description": "Overrides access flags for motor scooter.  Motor Scooter Routing not allowed."}, {"key": "moped:backward", "value": "yes", "object_types": ["way"], "description": "Sets the motor scooter reverse flag to true."}, {"description": "Adds conditional access to an edge with a time dependency in opening_hours format, see: https://wiki.openstreetmap.org/wiki/Key:opening_hours", "value": "no @ (opening_hours format)", "key": "moped:conditional", "object_types": ["way"]}, {"description": "Adds conditional access to an edge with a time dependency in opening_hours format, see: https://wiki.openstreetmap.org/wiki/Key:opening_hours", "value": "yes @ (opening_hours format)", "key": "moped:conditional", "object_types": ["way"]}, {"description": "Adds conditional access to an edge with a time dependency in opening_hours format, see: https://wiki.openstreetmap.org/wiki/Key:opening_hours", "value": "private @ (opening_hours format)", "key": "moped:conditional", "object_types": ["way"]}, {"description": "Adds conditional access to an edge with a time dependency in opening_hours format, see: https://wiki.openstreetmap.org/wiki/Key:opening_hours", "value": "delivery @ (opening_hours format)", "key": "moped:conditional", "object_types": ["way"]}, {"description": "Adds conditional access to an edge with a time dependency in opening_hours format, see: https://wiki.openstreetmap.org/wiki/Key:opening_hours", "value": "designated @ (opening_hours format)", "key": "moped:conditional", "object_types": ["way"]}, {"description": "Adds conditional access to an edge with a time dependency in opening_hours format, see: https://wiki.openstreetmap.org/wiki/Key:opening_hours", "value": "destination @ (opening_hours format)", "key": "moped:conditional", "object_types": ["way"]}, {"key": "motor_vehicle", "value": "no", "object_types": ["node", "way"], "description": "Overrides access flags for auto.  Auto Routing not allowed."}, {"key": "motor_vehicle", "value": "agricultural", "object_types": ["node", "way"], "description": "Overrides access flags for auto.  Auto Routing not allowed."}, {"key": "motor_vehicle", "value": "discouraged", "object_types": ["node", "way"], "description": "Overrides access flags for auto.  Auto Routing not allowed."}, {"key": "motor_vehicle", "value": "forestry", "object_types": ["node", "way"], "description": "Overrides access flags for auto.  Auto Routing not allowed."}, {"key": "motor_vehicle", "value": "official", "object_types": ["node", "way"], "description": "Overrides access flags for auto.  Auto Routing not allowed."}, {"key": "motor_vehicle", "value": "restricted", "object_types": ["node", "way"], "description": "Overrides access flags for auto.  Auto Routing allowed."}, {"key": "motor_vehicle", "value": "yes", "object_types": ["node", "way"], "description": "Overrides access flags for auto.  Auto Routing allowed."}, {"key": "motor_vehicle", "value": "private", "object_types": ["node", "way"], "description": "Overrides access flags for auto.  Auto Routing allowed."}, {"key": "motor_vehicle", "value": "permit", "object_types": ["node", "way"], "description": "Overrides access flags for auto.  Auto Routing allowed."}, {"key": "motor_vehicle", "value": "residents", "object_types": ["node", "way"], "description": "Overrides access flags for auto.  Auto Routing allowed."}, {"key": "motor_vehicle", "value": "permissive", "object_types": ["node", "way"], "description": "Overrides access flags for auto.  Auto Routing allowed."}, {"key": "motor_vehicle", "value": "use_sidepath", "object_types": ["node", "way"], "description": "Overrides access flags for auto.  Auto Routing allowed."}, {"key": "motor_vehicle", "value": "delivery", "object_types": ["node", "way"], "description": "Overrides access flags for auto.  Auto Routing allowed."}, {"key": "motor_vehicle", "value": "designated", "object_types": ["node", "way"], "description": "Overrides access flags for auto.  Auto Routing allowed."}, {"key": "motor_vehicle", "value": "dismount", "object_types": ["node", "way"], "description": "Overrides access flags for auto.  Auto Routing allowed."}, {"key": "motor_vehicle", "value": "destination", "object_types": ["node", "way"], "description": "Overrides access flags for auto.  Auto Routing allowed."}, {"key": "motor_vehicle", "value": "customers", "object_types": ["node", "way"], "description": "Overrides access flags for auto.  Auto Routing allowed."}, {"key": "motor_vehicle", "value": "public", "object_types": ["node", "way"], "description": "Overrides access flags for auto.  Auto Routing allowed."}, {"key": "motor_vehicle", "value": "allowed", "object_types": ["node", "way"], "description": "Overrides access flags for auto.  Auto Routing allowed."}, {"description": "Adds conditional access to an edge with a time dependency in opening_hours format, see: https://wiki.openstreetmap.org/wiki/Key:opening_hours", "value": "no @ (opening_hours format)", "key": "motor_vehicle:conditional", "object_types": ["way"]}, {"description": "Adds conditional access to an edge with a time dependency in opening_hours format, see: https://wiki.openstreetmap.org/wiki/Key:opening_hours", "value": "yes @ (opening_hours format)", "key": "motor_vehicle:conditional", "object_types": ["way"]}, {"description": "Adds conditional access to an edge with a time dependency in opening_hours format, see: https://wiki.openstreetmap.org/wiki/Key:opening_hours", "value": "private @ (opening_hours format)", "key": "motor_vehicle:conditional", "object_types": ["way"]}, {"description": "Adds conditional access to an edge with a time dependency in opening_hours format, see: https://wiki.openstreetmap.org/wiki/Key:opening_hours", "value": "delivery @ (opening_hours format)", "key": "motor_vehicle:conditional", "object_types": ["way"]}, {"description": "Adds conditional access to an edge with a time dependency in opening_hours format, see: https://wiki.openstreetmap.org/wiki/Key:opening_hours", "value": "designated @ (opening_hours format)", "key": "motor_vehicle:conditional", "object_types": ["way"]}, {"description": "Adds conditional access to an edge with a time dependency in opening_hours format, see: https://wiki.openstreetmap.org/wiki/Key:opening_hours", "value": "destination @ (opening_hours format)", "key": "motor_vehicle:conditional", "object_types": ["way"]}, {"key": "motorcar", "value": "no", "object_types": ["node", "way"], "description": "Overrides access flags for auto.  Auto Routing not allowed."}, {"key": "motorcar", "value": "agricultural", "object_types": ["node", "way"], "description": "Overrides access flags for auto.  Auto Routing not allowed."}, {"key": "motorcar", "value": "discouraged", "object_types": ["node", "way"], "description": "Overrides access flags for auto.  Auto Routing not allowed."}, {"key": "motorcar", "value": "forestry", "object_types": ["node", "way"], "description": "Overrides access flags for auto.  Auto Routing not allowed."}, {"key": "motorcar", "value": "official", "object_types": ["node", "way"], "description": "Overrides access flags for auto.  Auto Routing not allowed."}, {"key": "motorcar", "value": "restricted", "object_types": ["node", "way"], "description": "Overrides access flags for auto.  Auto Routing allowed."}, {"key": "motorcar", "value": "yes", "object_types": ["node", "way"], "description": "Overrides access flags for auto.  Auto Routing allowed."}, {"key": "motorcar", "value": "private", "object_types": ["node", "way"], "description": "Overrides access flags for auto.  Auto Routing allowed."}, {"key": "motorcar", "value": "permit", "object_types": ["node", "way"], "description": "Overrides access flags for auto.  Auto Routing allowed."}, {"key": "motorcar", "value": "residents", "object_types": ["node", "way"], "description": "Overrides access flags for auto.  Auto Routing allowed."}, {"key": "motorcar", "value": "permissive", "object_types": ["node", "way"], "description": "Overrides access flags for auto.  Auto Routing allowed."}, {"key": "motorcar", "value": "use_sidepath", "object_types": ["node", "way"], "description": "Overrides access flags for auto.  Auto Routing allowed."}, {"key": "motorcar", "value": "delivery", "object_types": ["node", "way"], "description": "Overrides access flags for auto.  Auto Routing allowed."}, {"key": "motorcar", "value": "designated", "object_types": ["node", "way"], "description": "Overrides access flags for auto.  Auto Routing allowed."}, {"key": "motorcar", "value": "dismount", "object_types": ["node", "way"], "description": "Overrides access flags for auto.  Auto Routing allowed."}, {"key": "motorcar", "value": "destination", "object_types": ["node", "way"], "description": "Overrides access flags for auto.  Auto Routing allowed."}, {"key": "motorcar", "value": "customers", "object_types": ["node", "way"], "description": "Overrides access flags for auto.  Auto Routing allowed."}, {"key": "motorcar", "value": "public", "object_types": ["node", "way"], "description": "Overrides access flags for auto.  Auto Routing allowed."}, {"key": "motorcar", "value": "allowed", "object_types": ["node", "way"], "description": "Overrides access flags for auto.  Auto Routing allowed."}, {"description": "Adds conditional access to an edge with a time dependency in opening_hours format, see: https://wiki.openstreetmap.org/wiki/Key:opening_hours", "value": "no @ (opening_hours format)", "key": "motorcar:conditional", "object_types": ["way"]}, {"description": "Adds conditional access to an edge with a time dependency in opening_hours format, see: https://wiki.openstreetmap.org/wiki/Key:opening_hours", "value": "yes @ (opening_hours format)", "key": "motorcar:conditional", "object_types": ["way"]}, {"description": "Adds conditional access to an edge with a time dependency in opening_hours format, see: https://wiki.openstreetmap.org/wiki/Key:opening_hours", "value": "private @ (opening_hours format)", "key": "motorcar:conditional", "object_types": ["way"]}, {"description": "Adds conditional access to an edge with a time dependency in opening_hours format, see: https://wiki.openstreetmap.org/wiki/Key:opening_hours", "value": "delivery @ (opening_hours format)", "key": "motorcar:conditional", "object_types": ["way"]}, {"description": "Adds conditional access to an edge with a time dependency in opening_hours format, see: https://wiki.openstreetmap.org/wiki/Key:opening_hours", "value": "designated @ (opening_hours format)", "key": "motorcar:conditional", "object_types": ["way"]}, {"description": "Adds conditional access to an edge with a time dependency in opening_hours format, see: https://wiki.openstreetmap.org/wiki/Key:opening_hours", "value": "destination @ (opening_hours format)", "key": "motorcar:conditional", "object_types": ["way"]}, {"description": "Adds conditional access to an edge with a time dependency in opening_hours format, see: https://wiki.openstreetmap.org/wiki/Key:opening_hours", "value": "no @ (opening_hours format)", "key": "motorcycle:conditional", "object_types": ["way"]}, {"description": "Adds conditional access to an edge with a time dependency in opening_hours format, see: https://wiki.openstreetmap.org/wiki/Key:opening_hours", "value": "yes @ (opening_hours format)", "key": "motorcycle:conditional", "object_types": ["way"]}, {"description": "Adds conditional access to an edge with a time dependency in opening_hours format, see: https://wiki.openstreetmap.org/wiki/Key:opening_hours", "value": "private @ (opening_hours format)", "key": "motorcycle:conditional", "object_types": ["way"]}, {"description": "Adds conditional access to an edge with a time dependency in opening_hours format, see: https://wiki.openstreetmap.org/wiki/Key:opening_hours", "value": "delivery @ (opening_hours format)", "key": "motorcycle:conditional", "object_types": ["way"]}, {"description": "Adds conditional access to an edge with a time dependency in opening_hours format, see: https://wiki.openstreetmap.org/wiki/Key:opening_hours", "value": "designated @ (opening_hours format)", "key": "motorcycle:conditional", "object_types": ["way"]}, {"description": "Adds conditional access to an edge with a time dependency in opening_hours format, see: https://wiki.openstreetmap.org/wiki/Key:opening_hours", "value": "destination @ (opening_hours format)", "key": "motorcycle:conditional", "object_types": ["way"]}, {"description": "Sets the hiking difficulty for cycle and pedestrian passes", "value": "difficult_alpine_hiking", "key": "mtb:description", "object_types": ["way"]}, {"description": "Sets the hiking difficulty for cycle and pedestrian passes", "value": "demanding_alpine_hiking", "key": "mtb:description", "object_types": ["way"]}, {"description": "Sets the hiking difficulty for cycle and pedestrian passes", "value": "alpine_hiking", "key": "mtb:description", "object_types": ["way"]}, {"description": "Sets the hiking difficulty for cycle and pedestrian passes", "value": "demanding_mountain_hiking", "key": "mtb:description", "object_types": ["way"]}, {"description": "Sets the hiking difficulty for cycle and pedestrian passes", "value": "mountain_hiking", "key": "mtb:description", "object_types": ["way"]}, {"description": "Sets the hiking difficulty for cycle and pedestrian passes", "value": "hiking", "key": "mtb:description", "object_types": ["way"]}, {"description": "Sets the hiking difficulty for cycle and pedestrian passes", "value": "difficult_alpine_hiking", "key": "mtb:scale", "object_types": ["way"]}, {"description": "Sets the hiking difficulty for cycle and pedestrian passes", "value": "demanding_alpine_hiking", "key": "mtb:scale", "object_types": ["way"]}, {"description": "Sets the hiking difficulty for cycle and pedestrian passes", "value": "alpine_hiking", "key": "mtb:scale", "object_types": ["way"]}, {"description": "Sets the hiking difficulty for cycle and pedestrian passes", "value": "demanding_mountain_hiking", "key": "mtb:scale", "object_types": ["way"]}, {"description": "Sets the hiking difficulty for cycle and pedestrian passes", "value": "mountain_hiking", "key": "mtb:scale", "object_types": ["way"]}, {"description": "Sets the hiking difficulty for cycle and pedestrian passes", "value": "hiking", "key": "mtb:scale", "object_types": ["way"]}, {"description": "Sets the hiking difficulty for cycle and pedestrian passes", "value": "difficult_alpine_hiking", "key": "mtb:scale:imba", "object_types": ["way"]}, {"description": "Sets the hiking difficulty for cycle and pedestrian passes", "value": "demanding_alpine_hiking", "key": "mtb:scale:imba", "object_types": ["way"]}, {"description": "Sets the hiking difficulty for cycle and pedestrian passes", "value": "alpine_hiking", "key": "mtb:scale:imba", "object_types": ["way"]}, {"description": "Sets the hiking difficulty for cycle and pedestrian passes", "value": "demanding_mountain_hiking", "key": "mtb:scale:imba", "object_types": ["way"]}, {"description": "Sets the hiking difficulty for cycle and pedestrian passes", "value": "mountain_hiking", "key": "mtb:scale:imba", "object_types": ["way"]}, {"description": "Sets the hiking difficulty for cycle and pedestrian passes", "value": "hiking", "key": "mtb:scale:imba", "object_types": ["way"]}, {"description": "Sets the hiking difficulty for cycle and pedestrian passes", "value": "difficult_alpine_hiking", "key": "mtb:scale:uphill", "object_types": ["way"]}, {"description": "Sets the hiking difficulty for cycle and pedestrian passes", "value": "demanding_alpine_hiking", "key": "mtb:scale:uphill", "object_types": ["way"]}, {"description": "Sets the hiking difficulty for cycle and pedestrian passes", "value": "alpine_hiking", "key": "mtb:scale:uphill", "object_types": ["way"]}, {"description": "Sets the hiking difficulty for cycle and pedestrian passes", "value": "demanding_mountain_hiking", "key": "mtb:scale:uphill", "object_types": ["way"]}, {"description": "Sets the hiking difficulty for cycle and pedestrian passes", "value": "mountain_hiking", "key": "mtb:scale:uphill", "object_types": ["way"]}, {"description": "Sets the hiking difficulty for cycle and pedestrian passes", "value": "hiking", "key": "mtb:scale:uphill", "object_types": ["way"]}, {"key": "name", "object_types": ["node", "way", "relation"], "description": "The name of the feature, street names, river names, POI names etc."}, {"key": "name:pronunciation", "object_types": ["node", "way"], "description": "This tag contains a phonetic guide(IPA) to pronouncing the name of the feature, street names, river names, POI names etc."}, {"key": "name:pronunciation:<alphabet>", "object_types": ["node", "way"], "description": "This tag contains a phonetic guide to pronouncing the name of the feature, street names, river names, POI names etc.  Phonetic Alphabet: jeita|katakana|nt-sampa."}, {"key": "name:<lg>", "object_types": ["way", "relation"], "description": "The name of the feature in a given language"}, {"key": "name:<lg>:pronunciation", "object_types": ["way"], "description": "This tag contains a phonetic guide(IPA) to pronouncing the name of the feature in a given language"}, {"key": "name:<lg>:pronunciation:<alphabet>", "object_types": ["way"], "description": "This tag contains a phonetic guide to pronouncing the name of the feature in a given language.  Phonetic Alphabet: jeita|katakana|nt-sampa."}, {"key": "name:left", "object_types": ["node", "way", "relation"], "description": "The name of the feature, street names, river names, POI names etc. for the left side"}, {"key": "name:left:pronunciation", "object_types": ["node", "way"], "description": "This tag contains a phonetic guide(IPA) to pronouncing the name of the feature, street names, river names, POI names etc. for the left side"}, {"key": "name:left:pronunciation:<alphabet>", "object_types": ["node", "way"], "description": "This tag contains a phonetic guide to pronouncing the name of the feature, street names, river names, POI names etc. for the left side.  Phonetic Alphabet: jeita|katakana|nt-sampa."}, {"key": "name:left:<lg>", "object_types": ["way", "relation"], "description": "The name of the feature in a given language for the left side"}, {"key": "name:left:<lg>:pronunciation", "object_types": ["way"], "description": "This tag contains a phonetic guide(IPA) to pronouncing the name of the feature in a given language for the left side"}, {"key": "name:left:<lg>:pronunciation:<alphabet>", "object_types": ["way"], "description": "This tag contains a phonetic guide to pronouncing the name of the feature in a given language for the left side.  Phonetic Alphabet: jeita|katakana|nt-sampa."}, {"key": "name:right", "object_types": ["node", "way", "relation"], "description": "The name of the feature, street names, river names, POI names etc. for the right side"}, {"key": "name:right:pronunciation", "object_types": ["node", "way"], "description": "This tag contains a phonetic guide(IPA) to pronouncing the name of the feature, street names, river names, POI names etc. for the right side"}, {"key": "name:right:pronunciation:<alphabet>", "object_types": ["node", "way"], "description": "This tag contains a phonetic guide to pronouncing the name of the feature, street names, river names, POI names etc. for the right side.  Phonetic Alphabet: jeita|katakana|nt-sampa."}, {"key": "name:right:<lg>", "object_types": ["way", "relation"], "description": "The name of the feature in a given language for the right side"}, {"key": "name:right:<lg>:pronunciation", "object_types": ["way"], "description": "This tag contains a phonetic guide(IPA) to pronouncing the name of the feature in a given language for the right side"}, {"key": "name:right:<lg>:pronunciation:<alphabet>", "object_types": ["way"], "description": "This tag contains a phonetic guide to pronouncing the name of the feature in a given language for the right side.  Phonetic Alphabet: jeita|katakana|nt-sampa."}, {"key": "name:forward", "object_types": ["node", "way", "relation"], "description": "The name of the feature, street names, river names, POI names etc. for the forward direction"}, {"key": "name:forward:pronunciation", "object_types": ["node", "way"], "description": "This tag contains a phonetic guide(IPA) to pronouncing the name of the feature, street names, river names, POI names etc. for the forward direction"}, {"key": "name:forward:pronunciation:<alphabet>", "object_types": ["node", "way"], "description": "This tag contains a phonetic guide to pronouncing the name of the feature, street names, river names, POI names etc. for the forward direction.  Phonetic Alphabet: jeita|katakana|nt-sampa."}, {"key": "name:forward:<lg>", "object_types": ["way", "relation"], "description": "The name of the feature in a given language for the forward direction"}, {"key": "name:forward:<lg>:pronunciation", "object_types": ["way"], "description": "This tag contains a phonetic guide(IPA) to pronouncing the name of the feature in a given language for the forward direction"}, {"key": "name:forward:<lg>:pronunciation:<alphabet>", "object_types": ["way"], "description": "This tag contains a phonetic guide to pronouncing the name of the feature in a given language for the forward direction.  Phonetic Alphabet: jeita|katakana|nt-sampa."}, {"key": "name:backward", "object_types": ["node", "way", "relation"], "description": "The name of the feature, street names, river names, POI names etc. for the backward direction"}, {"key": "name:backward:pronunciation", "object_types": ["node", "way"], "description": "This tag contains a phonetic guide(IPA) to pronouncing the name of the feature, street names, river names, POI names etc. for the backward direction"}, {"key": "name:backward:pronunciation:<alphabet>", "object_types": ["node", "way"], "description": "This tag contains a phonetic guide to pronouncing the name of the feature, street names, river names, POI names etc. for the backward direction.  Phonetic Alphabet: jeita|katakana|nt-sampa."}, {"key": "name:backward:<lg>", "object_types": ["way", "relation"], "description": "The name of the feature in a given language for the backward direction"}, {"key": "name:backward:<lg>:pronunciation", "object_types": ["way"], "description": "This tag contains a phonetic guide(IPA) to pronouncing the name of the feature in a given language for the backward direction"}, {"key": "name:backward:<lg>:pronunciation:<alphabet>", "object_types": ["way"], "description": "This tag contains a phonetic guide to pronouncing the name of the feature in a given language for the backward direction.  Phonetic Alphabet: jeita|katakana|nt-sampa."}, {"key": "tunnel:name", "object_types": ["way"], "description": "The tunnel name of the way."}, {"key": "tunnel:name:pronunciation", "object_types": ["node", "way"], "description": "This tag contains a phonetic guide(IPA) to pronouncing the tunnel name of the way."}, {"key": "tunnel:name:pronunciation:<alphabet>", "object_types": ["node", "way"], "description": "This tag contains a phonetic guide to pronouncing the tunnel name of the way.  Phonetic Alphabet: jeita|katakana|nt-sampa."}, {"key": "tunnel:name:<lg>", "object_types": ["way"], "description": "The tunnel name in a given language of the way."}, {"key": "tunnel:name:<lg>:pronunciation", "object_types": ["node", "way"], "description": "This tag contains a phonetic guide(IPA) to pronouncing the tunnel name in a given language of the way."}, {"key": "tunnel:name:<lg>:pronunciation:<alphabet>", "object_types": ["node", "way"], "description": "This tag contains a phonetic guide to pronouncing the tunnel name in a given language of the way.  Phonetic Alphabet: jeita|katakana|nt-sampa."}, {"key": "tunnel:name:left", "object_types": ["way"], "description": "The tunnel name of the way for the left side."}, {"key": "tunnel:name:left:pronunciation", "object_types": ["node", "way"], "description": "This tag contains a phonetic guide(IPA) to pronouncing the tunnel name of the way for the left side."}, {"key": "tunnel:name:left:pronunciation:<alphabet>", "object_types": ["node", "way"], "description": "This tag contains a phonetic guide to pronouncing the tunnel name of the way for the left side.  Phonetic Alphabet: jeita|katakana|nt-sampa."}, {"key": "tunnel:name:left:<lg>", "object_types": ["way"], "description": "The tunnel name in a given language of the way for the left side."}, {"key": "tunnel:name:left:<lg>:pronunciation", "object_types": ["node", "way"], "description": "This tag contains a phonetic guide(IPA) to pronouncing the tunnel name in a given language of the way for the left side."}, {"key": "tunnel:name:left:<lg>:pronunciation:<alphabet>", "object_types": ["node", "way"], "description": "This tag contains a phonetic guide to pronouncing the tunnel name in a given language of the way for the left side.  Phonetic Alphabet: jeita|katakana|nt-sampa."}, {"key": "tunnel:name:right", "object_types": ["way"], "description": "The tunnel name of the way for the right side."}, {"key": "tunnel:name:right:pronunciation", "object_types": ["node", "way"], "description": "This tag contains a phonetic guide(IPA) to pronouncing the tunnel name of the way for the right side."}, {"key": "tunnel:name:right:pronunciation:<alphabet>", "object_types": ["node", "way"], "description": "This tag contains a phonetic guide to pronouncing the tunnel name of the way for the right side.  Phonetic Alphabet: jeita|katakana|nt-sampa."}, {"key": "tunnel:name:right:<lg>", "object_types": ["way"], "description": "The tunnel name in a given language of the way for the right side."}, {"key": "tunnel:name:right:<lg>:pronunciation", "object_types": ["node", "way"], "description": "This tag contains a phonetic guide(IPA) to pronouncing the tunnel name in a given language of the way for the right side."}, {"key": "tunnel:name:right:<lg>:pronunciation:<alphabet>", "object_types": ["node", "way"], "description": "This tag contains a phonetic guide to pronouncing the tunnel name in a given language of the way for the right side.  Phonetic Alphabet: jeita|katakana|nt-sampa."}, {"key": "ncn", "value": "yes", "description": "National Bike Network.  Set bitmask for network", "object_types": ["way"]}, {"key": "ncn_ref", "description": "National Bike Network ref.  Set bitmask for network", "object_types": ["way"]}, {"key": "network", "value": "ncn", "object_types": ["relation"], "description": "National Bike Network.  Set bitmask for network.  Route key must equal bicycle too."}, {"key": "network", "value": "rcn", "object_types": ["relation"], "description": "Regional Bike Network.  Set bitmask for network.  Route key must equal bicycle too."}, {"key": "network", "value": "lcn", "object_types": ["relation"], "description": "Local Bike Network.  Set bitmask for network.  Route key must equal bicycle too."}, {"key": "network", "value": "mtb", "object_types": ["relation"], "description": "Mountain Bike Network.  Set bitmask for network.  Route key must equal bicycle too."}, {"key": "network", "object_types": ["relation"]}, {"key": "official_name", "object_types": ["way"], "description": "Official name for a country or street"}, {"key": "official_name:pronunciation", "object_types": ["way"], "description": "This tag contains a phonetic guide(IPA) to pronouncing the official name for a street"}, {"key": "official_name:pronunciation:<alphabet>", "object_types": ["way"], "description": "This tag contains a phonetic guide to pronouncing the official name for a street.  Phonetic Alphabet: jeita|katakana|nt-sampa."}, {"key": "official_name:<lg>", "object_types": ["way"], "description": "Official name in a given language for a street"}, {"key": "official_name:<lg>:pronunciation", "object_types": ["way"], "description": "This tag contains a phonetic guide(IPA) to pronouncing the official name in a given language for a street"}, {"key": "official_name:<lg>:pronunciation:<alphabet>", "object_types": ["way"], "description": "This tag contains a phonetic guide to pronouncing the official name in a given language for a street.  Phonetic Alphabet: jeita|katakana|nt-sampa."}, {"key": "official_name:left", "object_types": ["way"], "description": "Official name for a street for the left side."}, {"key": "official_name:left:pronunciation", "object_types": ["way"], "description": "This tag contains a phonetic guide(IPA) to pronouncing the official name a street for the left side."}, {"key": "official_name:left:pronunciation:<alphabet>", "object_types": ["way"], "description": "This tag contains a phonetic guide to pronouncing the official name a street for the left side.  Phonetic Alphabet: jeita|katakana|nt-sampa."}, {"key": "official_name:left:<lg>", "object_types": ["way"], "description": "Official name in a given language for a street for the left side."}, {"key": "official_name:left:<lg>:pronunciation", "object_types": ["way"], "description": "This tag contains a phonetic guide(IPA) to pronouncing the official name in a given language for a street for the left side."}, {"key": "official_name:left:<lg>:pronunciation:<alphabet>", "object_types": ["way"], "description": "This tag contains a phonetic guide to pronouncing the official name in a given language for a street for the left side.  Phonetic Alphabet: jeita|katakana|nt-sampa."}, {"key": "official_name:right", "object_types": ["way"], "description": "Official name for a street for the right side."}, {"key": "official_name:right:pronunciation", "object_types": ["way"], "description": "This tag contains a phonetic guide(IPA) to pronouncing the official name a street for the right side."}, {"key": "official_name:right:pronunciation:<alphabet>", "object_types": ["way"], "description": "This tag contains a phonetic guide to pronouncing the official name a street for the right side.  Phonetic Alphabet: jeita|katakana|nt-sampa."}, {"key": "official_name:right:<lg>", "object_types": ["way"], "description": "Official name in a given language for a street for the right side."}, {"key": "official_name:right:<lg>:pronunciation", "object_types": ["way"], "description": "This tag contains a phonetic guide(IPA) to pronouncing the official name in a given language for a street for the right side."}, {"key": "official_name:right:<lg>:pronunciation:<alphabet>", "object_types": ["way"], "description": "This tag contains a phonetic guide to pronouncing the official name in a given language for a street for the right side.  Phonetic Alphabet: jeita|katakana|nt-sampa."}, {"key": "access", "value": "conditional", "object_types": ["way"], "description": "Indicates the access on the way can change conditionally."}, {"key": "oneway", "value": "-1", "object_types": ["way"], "description": "Sets reverse flag to true for all types of routing.  A value of -1 will flip the direction of the oneway."}, {"key": "oneway", "value": "yes", "object_types": ["way"], "description": "Sets reverse flag to true for all types of routing."}, {"key": "oneway", "value": "true", "object_types": ["way"], "description": "Sets reverse flag to true for all types of routing."}, {"key": "oneway", "value": "1", "object_types": ["way"], "description": "Sets reverse flag to true for all types of routing."}, {"key": "oneway", "value": "reversible", "object_types": ["way"], "description": "Indicates the travel direction on the way can change."}, {"key": "oneway", "value": "conditional", "object_types": ["way"], "description": "Indicates the travel direction on the way can change conditionally."}, {"key": "oneway", "value": "alternating", "object_types": ["way"], "description": "Indicates the travel direction on the way can change."}, {"key": "oneway:bicycle", "value": "-1", "object_types": ["way"], "description": "Overrides oneway flag.  Sets bike reverse flag to true.  A value of -1 will flip the direction of the oneway for bicycles."}, {"key": "oneway:bicycle", "value": "yes", "object_types": ["way"], "description": "Overrides oneway flag.  Sets bike reverse flag to true."}, {"key": "oneway:bicycle", "value": "true", "object_types": ["way"], "description": "Overrides oneway flag.  Sets bike reverse flag to true."}, {"key": "oneway:bicycle", "value": "1", "object_types": ["way"], "description": "Overrides oneway flag.  Sets bike reverse flag to true."}, {"key": "oneway:bicycle", "value": "no", "object_types": ["way"], "description": "If oneway = yes and oneway:bicycle = no then set the bike reverse flag to true."}, {"key": "oneway:foot", "value": "-1", "object_types": ["way"], "description": "Overrides oneway flag.  Sets foot reverse flag to true.  A value of -1 will flip the direction of the oneway for pedestrians."}, {"key": "oneway:foot", "value": "yes", "object_types": ["way"], "description": "Overrides oneway flag.  Sets foot reverse flag to true."}, {"key": "oneway:foot", "value": "true", "object_types": ["way"], "description": "Overrides oneway flag.  Sets foot reverse flag to true."}, {"key": "oneway:foot", "value": "1", "object_types": ["way"], "description": "Overrides oneway flag.  Sets foot reverse flag to true."}, {"key": "oneway:foot", "value": "no", "object_types": ["way"], "description": "If oneway = yes and oneway:foot = no then set the foot reverse flag to true."}, {"key": "oneway:bus", "value": "-1", "object_types": ["way"], "description": "Overrides oneway flag.  Sets bus reverse flag to true.  A value of -1 will flip the direction of the oneway for buses."}, {"key": "oneway:bus", "value": "yes", "object_types": ["way"], "description": "Overrides oneway flag.  Sets bus reverse flag to true."}, {"key": "oneway:bus", "value": "true", "object_types": ["way"], "description": "Overrides oneway flag.  Sets bus reverse flag to true."}, {"key": "oneway:bus", "value": "1", "object_types": ["way"], "description": "Overrides oneway flag.  Sets bus reverse flag to true."}, {"key": "oneway:bus", "value": "no", "object_types": ["way"], "description": "If oneway = yes and oneway:bus = no then set the bus reverse flag to true."}, {"key": "oneway:mofa", "value": "-1", "object_types": ["way"], "description": "Overrides oneway flag.  Sets motor scooter reverse flag to true.  A value of -1 will flip the direction of the oneway for motor scooters."}, {"key": "oneway:mofa", "value": "yes", "object_types": ["way"], "description": "Overrides oneway flag.  Sets motor scooter reverse flag to true."}, {"key": "oneway:mofa", "value": "1", "object_types": ["way"], "description": "Overrides oneway flag.  Sets motor scooter reverse flag to true."}, {"key": "oneway:mofa", "value": "true", "object_types": ["way"], "description": "Overrides oneway flag.  Sets motor scooter reverse flag to true."}, {"key": "oneway:mofa", "value": "no", "object_types": ["way"], "description": "If oneway = yes and oneway:mofa = no then set the motor scooter reverse flag to true."}, {"key": "oneway:moped", "value": "-1", "object_types": ["way"], "description": "Overrides oneway flag.  Sets motor scooter reverse flag to true.  A value of -1 will flip the direction of the oneway for motor scooters."}, {"key": "oneway:moped", "value": "yes", "object_types": ["way"], "description": "Overrides oneway flag.  Sets motor scooter reverse flag to true."}, {"key": "oneway:moped", "value": "1", "object_types": ["way"], "description": "Overrides oneway flag.  Sets motor scooter reverse flag to true."}, {"key": "oneway:moped", "value": "true", "object_types": ["way"], "description": "Overrides oneway flag.  Sets motor scooter reverse flag to true."}, {"key": "oneway:moped", "value": "no", "object_types": ["way"], "description": "If oneway = yes and oneway:moped = no then set the motor scooter reverse flag to true."}, {"key": "pedestrian", "value": "no", "object_types": ["way"], "description": "Overrides access flags for pedestrian.  Pedestrian Routing not allowed."}, {"key": "pedestrian", "value": "agricultural", "object_types": ["way"], "description": "Overrides access flags for pedestrian.  Pedestrian Routing not allowed."}, {"key": "pedestrian", "value": "discouraged", "object_types": ["way"], "description": "Overrides access flags for pedestrian.  Pedestrian Routing not allowed."}, {"key": "pedestrian", "value": "forestry", "object_types": ["way"], "description": "Overrides access flags for pedestrian.  Pedestrian Routing allowed."}, {"key": "pedestrian", "value": "official", "object_types": ["way"], "description": "Overrides access flags for pedestrian.  Pedestrian Routing allowed."}, {"key": "pedestrian", "value": "restricted", "object_types": ["way"], "description": "Overrides access flags for pedestrian.  Pedestrian Routing allowed."}, {"key": "pedestrian", "value": "yes", "object_types": ["way"], "description": "Overrides access flags for pedestrian.  Pedestrian Routing allowed."}, {"key": "pedestrian", "value": "private", "object_types": ["way"], "description": "Overrides access flags for pedestrian.  Pedestrian Routing allowed."}, {"key": "pedestrian", "value": "permit", "object_types": ["way"], "description": "Overrides access flags for pedestrian.  Pedestrian Routing allowed."}, {"key": "pedestrian", "value": "residents", "object_types": ["way"], "description": "Overrides access flags for pedestrian.  Pedestrian Routing allowed."}, {"key": "pedestrian", "value": "permissive", "object_types": ["way"], "description": "Overrides access flags for pedestrian.  Pedestrian Routing allowed."}, {"key": "pedestrian", "value": "use_sidepath", "object_types": ["way"], "description": "Overrides access flags for pedestrian.  Pedestrian Routing allowed."}, {"key": "pedestrian", "value": "delivery", "object_types": ["way"], "description": "Overrides access flags for pedestrian.  Pedestrian Routing allowed."}, {"key": "pedestrian", "value": "designated", "object_types": ["way"], "description": "Overrides access flags for pedestrian.  Pedestrian Routing allowed."}, {"key": "pedestrian", "value": "destination", "object_types": ["way"], "description": "Overrides access flags for pedestrian.  Pedestrian Routing allowed."}, {"key": "pedestrian", "value": "customers", "object_types": ["way"], "description": "Overrides access flags for pedestrian.  Pedestrian Routing allowed."}, {"key": "pedestrian", "value": "public", "object_types": ["way"], "description": "Overrides access flags for pedestrian.  Pedestrian Routing allowed."}, {"key": "pedestrian", "value": "crossing", "object_types": ["way", "node"], "description": "Overrides access flags for pedestrian.  Pedestrian Routing allowed.  For nodes: Allows access for all modes unless the mode override is set."}, {"key": "pedestrian", "value": "sidewalk", "object_types": ["way"], "description": "Overrides access flags for pedestrian.  Pedestrian Routing allowed."}, {"key": "pedestrian", "value": "allowed", "object_types": ["way"], "description": "Overrides access flags for pedestrian.  Pedestrian Routing allowed."}, {"key": "pedestrian", "value": "passable", "object_types": ["way"], "description": "Overrides access flags for pedestrian.  Pedestrian Routing allowed."}, {"key": "pedestrian", "value": "footway", "object_types": ["way"], "description": "Overrides access flags for pedestrian.  Pedestrian Routing allowed."}, {"description": "Adds conditional access to an edge with a time dependency in opening_hours format, see: https://wiki.openstreetmap.org/wiki/Key:opening_hours", "value": "no @ (opening_hours format)", "key": "pedestrian:conditional", "object_types": ["way"]}, {"description": "Adds conditional access to an edge with a time dependency in opening_hours format, see: https://wiki.openstreetmap.org/wiki/Key:opening_hours", "value": "yes @ (opening_hours format)", "key": "pedestrian:conditional", "object_types": ["way"]}, {"description": "Adds conditional access to an edge with a time dependency in opening_hours format, see: https://wiki.openstreetmap.org/wiki/Key:opening_hours", "value": "private @ (opening_hours format)", "key": "pedestrian:conditional", "object_types": ["way"]}, {"description": "Adds conditional access to an edge with a time dependency in opening_hours format, see: https://wiki.openstreetmap.org/wiki/Key:opening_hours", "value": "delivery @ (opening_hours format)", "key": "pedestrian:conditional", "object_types": ["way"]}, {"description": "Adds conditional access to an edge with a time dependency in opening_hours format, see: https://wiki.openstreetmap.org/wiki/Key:opening_hours", "value": "designated @ (opening_hours format)", "key": "pedestrian:conditional", "object_types": ["way"]}, {"description": "Adds conditional access to an edge with a time dependency in opening_hours format, see: https://wiki.openstreetmap.org/wiki/Key:opening_hours", "value": "destination @ (opening_hours format)", "key": "pedestrian:conditional", "object_types": ["way"]}, {"key": "psv", "value": "no", "object_types": ["node", "way"], "description": "Overrides access flags for bus.  Bus Routing not allowed."}, {"key": "psv", "value": "delivery", "object_types": ["node", "way"], "description": "Overrides access flags for bus.  Bus Routing not allowed."}, {"key": "psv", "value": "yes", "object_types": ["node", "way"], "description": "Overrides access flags for bus.  Bus Routing allowed."}, {"key": "psv", "value": "designated", "object_types": ["node", "way"], "description": "Overrides access flags for bus.  Bus Routing allowed."}, {"key": "psv", "value": "urban", "object_types": ["node", "way"], "description": "Overrides access flags for bus.  Bus Routing allowed."}, {"key": "psv", "value": "permissive", "object_types": ["node", "way"], "description": "Overrides access flags for bus.  Bus Routing allowed."}, {"key": "psv", "value": "restricted", "object_types": ["node", "way"], "description": "Overrides access flags for bus.  Bus Routing allowed."}, {"key": "psv", "value": "destination", "object_types": ["node", "way"], "description": "Overrides access flags for bus.  Bus Routing allowed."}, {"key": "psv", "value": "bus", "object_types": ["node", "way"], "description": "Overrides access flags for bus.  Bus Routing allowed."}, {"key": "psv", "value": "taxi", "object_types": ["node", "way"], "description": "Overrides access flags for taxi.  Taxi Routing allowed."}, {"key": "psv", "value": "1", "object_types": ["node", "way"], "description": "Overrides access flags for bus.  Bus Routing allowed."}, {"key": "psv", "value": "2", "object_types": ["node", "way"], "description": "Overrides access flags for bus.  Bus Routing allowed."}, {"description": "Adds conditional access to an edge with a time dependency in opening_hours format, see: https://wiki.openstreetmap.org/wiki/Key:opening_hours", "value": "no @ (opening_hours format)", "key": "psv:conditional", "object_types": ["way"]}, {"description": "Adds conditional access to an edge with a time dependency in opening_hours format, see: https://wiki.openstreetmap.org/wiki/Key:opening_hours", "value": "yes @ (opening_hours format)", "key": "psv:conditional", "object_types": ["way"]}, {"description": "Adds conditional access to an edge with a time dependency in opening_hours format, see: https://wiki.openstreetmap.org/wiki/Key:opening_hours", "value": "private @ (opening_hours format)", "key": "psv:conditional", "object_types": ["way"]}, {"description": "Adds conditional access to an edge with a time dependency in opening_hours format, see: https://wiki.openstreetmap.org/wiki/Key:opening_hours", "value": "delivery @ (opening_hours format)", "key": "psv:conditional", "object_types": ["way"]}, {"description": "Adds conditional access to an edge with a time dependency in opening_hours format, see: https://wiki.openstreetmap.org/wiki/Key:opening_hours", "value": "designated @ (opening_hours format)", "key": "psv:conditional", "object_types": ["way"]}, {"description": "Adds conditional access to an edge with a time dependency in opening_hours format, see: https://wiki.openstreetmap.org/wiki/Key:opening_hours", "value": "destination @ (opening_hours format)", "key": "psv:conditional", "object_types": ["way"]}, {"key": "railway", "value": "rail", "description": "If auto access then this is a auto train.", "object_types": ["way"]}, {"key": "railway", "value": "crossing", "object_types": ["node"], "description": "Allows access for all modes unless the mode override is set."}, {"key": "rcn", "value": "yes", "description": "Regional Bike Network.  Set bitmask for network", "object_types": ["way"]}, {"key": "rcn_ref", "description": "Regional Bike Network ref.  Set bitmask for network", "object_types": ["way"]}, {"key": "ref", "object_types": ["node", "way", "relation"], "description": "Route network and number.  Junction/exit number, see: https://wiki.openstreetmap.org/wiki/Exit_Info"}, {"key": "ref:pronunciation", "object_types": ["node", "way"], "description": "This tag contains a phonetic guide(IPA) to pronouncing the junction/exit number, see: https://wiki.openstreetmap.org/wiki/Exit_Info"}, {"key": "ref:pronunciation:<alphabet>", "object_types": ["node", "way"], "description": "This tag contains a phonetic guide to pronouncing the junction/exit number, see: https://wiki.openstreetmap.org/wiki/Exit_Info  Phonetic Alphabet: jeita|katakana|nt-sampa."}, {"key": "ref:<lg>", "object_types": ["node", "way", "relation"], "description": "Route network and number.  Junction/exit number in a given language, see: https://wiki.openstreetmap.org/wiki/Exit_Info"}, {"key": "ref:<lg>:pronunciation", "object_types": ["node", "way"], "description": "This tag contains a phonetic guide(IPA) to pronouncing the junction/exit number in a given language, see: https://wiki.openstreetmap.org/wiki/Exit_Info"}, {"key": "ref:<lg>:pronunciation:<alphabet>", "object_types": ["node", "way"], "description": "This tag contains a phonetic guide(IPA) to pronouncing the junction/exit number in a given language, see: https://wiki.openstreetmap.org/wiki/Exit_Info  Phonetic Alphabet: jeita|katakana|nt-sampa."}, {"key": "restriction", "value": "no_left_turn", "object_types": ["relation"]}, {"key": "restriction", "value": "no_right_turn", "object_types": ["relation"]}, {"key": "restriction", "value": "no_straight_on", "object_types": ["relation"]}, {"key": "restriction", "value": "no_u_turn", "object_types": ["relation"]}, {"key": "restriction", "value": "only_left_turn", "object_types": ["relation"]}, {"key": "restriction", "value": "only_right_turn", "object_types": ["relation"]}, {"key": "restriction", "value": "only_straight_on", "object_types": ["relation"]}, {"key": "restriction", "value": "no_entry", "object_types": ["relation"]}, {"key": "restriction", "value": "no_exit", "object_types": ["relation"]}, {"key": "restriction", "value": "no_turn", "object_types": ["relation"]}, {"description": "Conditional turn restrictions. See opening_hours format for the time component: https://wiki.openstreetmap.org/wiki/Key:opening_hours", "value": "no_right_turn @ (opening_hours format);", "key": "restriction:conditional", "object_types": ["relation"]}, {"description": "Conditional turn restrictions. See opening_hours format for the time component: https://wiki.openstreetmap.org/wiki/Key:opening_hours", "value": "no_left_turn @ (opening_hours format);", "key": "restriction:conditional", "object_types": ["relation"]}, {"description": "Conditional turn restrictions. See opening_hours format for the time component: https://wiki.openstreetmap.org/wiki/Key:opening_hours", "value": "no_u_turn @ (opening_hours format);", "key": "restriction:conditional", "object_types": ["relation"]}, {"description": "Conditional turn restrictions. See opening_hours format for the time component: https://wiki.openstreetmap.org/wiki/Key:opening_hours", "value": "no_straight_on @ (opening_hours format);", "key": "restriction:conditional", "object_types": ["relation"]}, {"description": "Conditional turn restrictions. See opening_hours format for the time component: https://wiki.openstreetmap.org/wiki/Key:opening_hours", "value": "only_right_turn @ (opening_hours format);", "key": "restriction:conditional", "object_types": ["relation"]}, {"description": "Conditional turn restrictions. See opening_hours format for the time component: https://wiki.openstreetmap.org/wiki/Key:opening_hours", "value": "only_left_turn @ (opening_hours format);", "key": "restriction:conditional", "object_types": ["relation"]}, {"description": "Conditional turn restrictions. See opening_hours format for the time component: https://wiki.openstreetmap.org/wiki/Key:opening_hours", "value": "only_straight_on @ (opening_hours format);", "key": "restriction:conditional", "object_types": ["relation"]}, {"description": "Conditional turn restrictions. See opening_hours format for the time component: https://wiki.openstreetmap.org/wiki/Key:opening_hours", "value": "no_entry @ (opening_hours format);", "key": "restriction:conditional", "object_types": ["relation"]}, {"description": "Conditional turn restrictions. See opening_hours format for the time component: https://wiki.openstreetmap.org/wiki/Key:opening_hours", "value": "no_exit @ (opening_hours format);", "key": "restriction:conditional", "object_types": ["relation"]}, {"value": "no_right_turn", "object_types": ["relation"], "key": "restriction:bicycle", "description": "Mode specific turn restrictions"}, {"value": "no_left_turn", "object_types": ["relation"], "key": "restriction:bicycle", "description": "Mode specific turn restrictions"}, {"value": "no_u_turn", "object_types": ["relation"], "key": "restriction:bicycle", "description": "Mode specific turn restrictions"}, {"value": "no_straight_on", "object_types": ["relation"], "key": "restriction:bicycle", "description": "Mode specific turn restrictions"}, {"value": "only_right_turn", "object_types": ["relation"], "key": "restriction:bicycle", "description": "Mode specific turn restrictions"}, {"value": "only_left_turn", "object_types": ["relation"], "key": "restriction:bicycle", "description": "Mode specific turn restrictions"}, {"value": "only_straight_on", "object_types": ["relation"], "key": "restriction:bicycle", "description": "Mode specific turn restrictions"}, {"value": "no_entry", "object_types": ["relation"], "key": "restriction:bicycle", "description": "Mode specific turn restrictions"}, {"value": "no_exit", "object_types": ["relation"], "key": "restriction:bicycle", "description": "Mode specific turn restrictions"}, {"description": "Mode specific conditional turn restrictions. See opening_hours format for the time component: https://wiki.openstreetmap.org/wiki/Key:opening_hours", "value": "no_right_turn @ (opening_hours format);", "key": "restriction:bicycle:conditional", "object_types": ["relation"]}, {"description": "Mode specific conditional turn restrictions. See opening_hours format for the time component: https://wiki.openstreetmap.org/wiki/Key:opening_hours", "value": "no_left_turn @ (opening_hours format);", "key": "restriction:bicycle:conditional", "object_types": ["relation"]}, {"description": "Mode specific conditional turn restrictions. See opening_hours format for the time component: https://wiki.openstreetmap.org/wiki/Key:opening_hours", "value": "no_u_turn @ (opening_hours format);", "key": "restriction:bicycle:conditional", "object_types": ["relation"]}, {"description": "Mode specific conditional turn restrictions. See opening_hours format for the time component: https://wiki.openstreetmap.org/wiki/Key:opening_hours", "value": "no_straight_on @ (opening_hours format);", "key": "restriction:bicycle:conditional", "object_types": ["relation"]}, {"description": "Mode specific conditional turn restrictions. See opening_hours format for the time component: https://wiki.openstreetmap.org/wiki/Key:opening_hours", "value": "only_right_turn @ (opening_hours format);", "key": "restriction:bicycle:conditional", "object_types": ["relation"]}, {"description": "Mode specific conditional turn restrictions. See opening_hours format for the time component: https://wiki.openstreetmap.org/wiki/Key:opening_hours", "value": "only_left_turn @ (opening_hours format);", "key": "restriction:bicycle:conditional", "object_types": ["relation"]}, {"description": "Mode specific conditional turn restrictions. See opening_hours format for the time component: https://wiki.openstreetmap.org/wiki/Key:opening_hours", "value": "only_straight_on @ (opening_hours format);", "key": "restriction:bicycle:conditional", "object_types": ["relation"]}, {"description": "Mode specific conditional turn restrictions. See opening_hours format for the time component: https://wiki.openstreetmap.org/wiki/Key:opening_hours", "value": "no_entry @ (opening_hours format);", "key": "restriction:bicycle:conditional", "object_types": ["relation"]}, {"description": "Mode specific conditional turn restrictions. See opening_hours format for the time component: https://wiki.openstreetmap.org/wiki/Key:opening_hours", "value": "no_exit @ (opening_hours format);", "key": "restriction:bicycle:conditional", "object_types": ["relation"]}, {"value": "no_right_turn", "object_types": ["relation"], "key": "restriction:bus", "description": "Mode specific turn restrictions"}, {"value": "no_left_turn", "object_types": ["relation"], "key": "restriction:bus", "description": "Mode specific turn restrictions"}, {"value": "no_u_turn", "object_types": ["relation"], "key": "restriction:bus", "description": "Mode specific turn restrictions"}, {"value": "no_straight_on", "object_types": ["relation"], "key": "restriction:bus", "description": "Mode specific turn restrictions"}, {"value": "only_right_turn", "object_types": ["relation"], "key": "restriction:bus", "description": "Mode specific turn restrictions"}, {"value": "only_left_turn", "object_types": ["relation"], "key": "restriction:bus", "description": "Mode specific turn restrictions"}, {"value": "only_straight_on", "object_types": ["relation"], "key": "restriction:bus", "description": "Mode specific turn restrictions"}, {"value": "no_entry", "object_types": ["relation"], "key": "restriction:bus", "description": "Mode specific turn restrictions"}, {"value": "no_exit", "object_types": ["relation"], "key": "restriction:bus", "description": "Mode specific turn restrictions"}, {"description": "Mode specific conditional turn restrictions. See opening_hours format for the time component: https://wiki.openstreetmap.org/wiki/Key:opening_hours", "value": "no_right_turn @ (opening_hours format);", "key": "restriction:bus:conditional", "object_types": ["relation"]}, {"description": "Mode specific conditional turn restrictions. See opening_hours format for the time component: https://wiki.openstreetmap.org/wiki/Key:opening_hours", "value": "no_left_turn @ (opening_hours format);", "key": "restriction:bus:conditional", "object_types": ["relation"]}, {"description": "Mode specific conditional turn restrictions. See opening_hours format for the time component: https://wiki.openstreetmap.org/wiki/Key:opening_hours", "value": "no_u_turn @ (opening_hours format);", "key": "restriction:bus:conditional", "object_types": ["relation"]}, {"description": "Mode specific conditional turn restrictions. See opening_hours format for the time component: https://wiki.openstreetmap.org/wiki/Key:opening_hours", "value": "no_straight_on @ (opening_hours format);", "key": "restriction:bus:conditional", "object_types": ["relation"]}, {"description": "Mode specific conditional turn restrictions. See opening_hours format for the time component: https://wiki.openstreetmap.org/wiki/Key:opening_hours", "value": "only_right_turn @ (opening_hours format);", "key": "restriction:bus:conditional", "object_types": ["relation"]}, {"description": "Mode specific conditional turn restrictions. See opening_hours format for the time component: https://wiki.openstreetmap.org/wiki/Key:opening_hours", "value": "only_left_turn @ (opening_hours format);", "key": "restriction:bus:conditional", "object_types": ["relation"]}, {"description": "Mode specific conditional turn restrictions. See opening_hours format for the time component: https://wiki.openstreetmap.org/wiki/Key:opening_hours", "value": "only_straight_on @ (opening_hours format);", "key": "restriction:bus:conditional", "object_types": ["relation"]}, {"description": "Mode specific conditional turn restrictions. See opening_hours format for the time component: https://wiki.openstreetmap.org/wiki/Key:opening_hours", "value": "no_entry @ (opening_hours format);", "key": "restriction:bus:conditional", "object_types": ["relation"]}, {"description": "Mode specific conditional turn restrictions. See opening_hours format for the time component: https://wiki.openstreetmap.org/wiki/Key:opening_hours", "value": "no_exit @ (opening_hours format);", "key": "restriction:bus:conditional", "object_types": ["relation"]}, {"value": "no_right_turn", "object_types": ["relation"], "key": "restriction:caravan", "description": "Mode specific turn restrictions"}, {"value": "no_left_turn", "object_types": ["relation"], "key": "restriction:caravan", "description": "Mode specific turn restrictions"}, {"value": "no_u_turn", "object_types": ["relation"], "key": "restriction:caravan", "description": "Mode specific turn restrictions"}, {"value": "no_straight_on", "object_types": ["relation"], "key": "restriction:caravan", "description": "Mode specific turn restrictions"}, {"value": "only_right_turn", "object_types": ["relation"], "key": "restriction:caravan", "description": "Mode specific turn restrictions"}, {"value": "only_left_turn", "object_types": ["relation"], "key": "restriction:caravan", "description": "Mode specific turn restrictions"}, {"value": "only_straight_on", "object_types": ["relation"], "key": "restriction:caravan", "description": "Mode specific turn restrictions"}, {"value": "no_entry", "object_types": ["relation"], "key": "restriction:caravan", "description": "Mode specific turn restrictions"}, {"value": "no_exit", "object_types": ["relation"], "key": "restriction:caravan", "description": "Mode specific turn restrictions"}, {"description": "Mode specific conditional turn restrictions. See opening_hours format for the time component: https://wiki.openstreetmap.org/wiki/Key:opening_hours", "value": "no_right_turn @ (opening_hours format);", "key": "restriction:caravan:conditional", "object_types": ["relation"]}, {"description": "Mode specific conditional turn restrictions. See opening_hours format for the time component: https://wiki.openstreetmap.org/wiki/Key:opening_hours", "value": "no_left_turn @ (opening_hours format);", "key": "restriction:caravan:conditional", "object_types": ["relation"]}, {"description": "Mode specific conditional turn restrictions. See opening_hours format for the time component: https://wiki.openstreetmap.org/wiki/Key:opening_hours", "value": "no_u_turn @ (opening_hours format);", "key": "restriction:caravan:conditional", "object_types": ["relation"]}, {"description": "Mode specific conditional turn restrictions. See opening_hours format for the time component: https://wiki.openstreetmap.org/wiki/Key:opening_hours", "value": "no_straight_on @ (opening_hours format);", "key": "restriction:caravan:conditional", "object_types": ["relation"]}, {"description": "Mode specific conditional turn restrictions. See opening_hours format for the time component: https://wiki.openstreetmap.org/wiki/Key:opening_hours", "value": "only_right_turn @ (opening_hours format);", "key": "restriction:caravan:conditional", "object_types": ["relation"]}, {"description": "Mode specific conditional turn restrictions. See opening_hours format for the time component: https://wiki.openstreetmap.org/wiki/Key:opening_hours", "value": "only_left_turn @ (opening_hours format);", "key": "restriction:caravan:conditional", "object_types": ["relation"]}, {"description": "Mode specific conditional turn restrictions. See opening_hours format for the time component: https://wiki.openstreetmap.org/wiki/Key:opening_hours", "value": "only_straight_on @ (opening_hours format);", "key": "restriction:caravan:conditional", "object_types": ["relation"]}, {"description": "Mode specific conditional turn restrictions. See opening_hours format for the time component: https://wiki.openstreetmap.org/wiki/Key:opening_hours", "value": "no_entry @ (opening_hours format);", "key": "restriction:caravan:conditional", "object_types": ["relation"]}, {"description": "Mode specific conditional turn restrictions. See opening_hours format for the time component: https://wiki.openstreetmap.org/wiki/Key:opening_hours", "value": "no_exit @ (opening_hours format);", "key": "restriction:caravan:conditional", "object_types": ["relation"]}, {"value": "no_right_turn", "object_types": ["relation"], "key": "restriction:hazmat", "description": "Mode specific turn restrictions"}, {"value": "no_left_turn", "object_types": ["relation"], "key": "restriction:hazmat", "description": "Mode specific turn restrictions"}, {"value": "no_u_turn", "object_types": ["relation"], "key": "restriction:hazmat", "description": "Mode specific turn restrictions"}, {"value": "no_straight_on", "object_types": ["relation"], "key": "restriction:hazmat", "description": "Mode specific turn restrictions"}, {"value": "only_right_turn", "object_types": ["relation"], "key": "restriction:hazmat", "description": "Mode specific turn restrictions"}, {"value": "only_left_turn", "object_types": ["relation"], "key": "restriction:hazmat", "description": "Mode specific turn restrictions"}, {"value": "only_straight_on", "object_types": ["relation"], "key": "restriction:hazmat", "description": "Mode specific turn restrictions"}, {"value": "no_entry", "object_types": ["relation"], "key": "restriction:hazmat", "description": "Mode specific turn restrictions"}, {"value": "no_exit", "object_types": ["relation"], "key": "restriction:hazmat", "description": "Mode specific turn restrictions"}, {"description": "Mode specific conditional turn restrictions. See opening_hours format for the time component: https://wiki.openstreetmap.org/wiki/Key:opening_hours", "value": "no_right_turn @ (opening_hours format);", "key": "restriction:hazmat:conditional", "object_types": ["relation"]}, {"description": "Mode specific conditional turn restrictions. See opening_hours format for the time component: https://wiki.openstreetmap.org/wiki/Key:opening_hours", "value": "no_left_turn @ (opening_hours format);", "key": "restriction:hazmat:conditional", "object_types": ["relation"]}, {"description": "Mode specific conditional turn restrictions. See opening_hours format for the time component: https://wiki.openstreetmap.org/wiki/Key:opening_hours", "value": "no_u_turn @ (opening_hours format);", "key": "restriction:hazmat:conditional", "object_types": ["relation"]}, {"description": "Mode specific conditional turn restrictions. See opening_hours format for the time component: https://wiki.openstreetmap.org/wiki/Key:opening_hours", "value": "no_straight_on @ (opening_hours format);", "key": "restriction:hazmat:conditional", "object_types": ["relation"]}, {"description": "Mode specific conditional turn restrictions. See opening_hours format for the time component: https://wiki.openstreetmap.org/wiki/Key:opening_hours", "value": "only_right_turn @ (opening_hours format);", "key": "restriction:hazmat:conditional", "object_types": ["relation"]}, {"description": "Mode specific conditional turn restrictions. See opening_hours format for the time component: https://wiki.openstreetmap.org/wiki/Key:opening_hours", "value": "only_left_turn @ (opening_hours format);", "key": "restriction:hazmat:conditional", "object_types": ["relation"]}, {"description": "Mode specific conditional turn restrictions. See opening_hours format for the time component: https://wiki.openstreetmap.org/wiki/Key:opening_hours", "value": "only_straight_on @ (opening_hours format);", "key": "restriction:hazmat:conditional", "object_types": ["relation"]}, {"description": "Mode specific conditional turn restrictions. See opening_hours format for the time component: https://wiki.openstreetmap.org/wiki/Key:opening_hours", "value": "no_entry @ (opening_hours format);", "key": "restriction:hazmat:conditional", "object_types": ["relation"]}, {"description": "Mode specific conditional turn restrictions. See opening_hours format for the time component: https://wiki.openstreetmap.org/wiki/Key:opening_hours", "value": "no_exit @ (opening_hours format);", "key": "restriction:hazmat:conditional", "object_types": ["relation"]}, {"value": "no_right_turn", "object_types": ["relation"], "key": "restriction:hgv", "description": "Mode specific turn restrictions"}, {"value": "no_left_turn", "object_types": ["relation"], "key": "restriction:hgv", "description": "Mode specific turn restrictions"}, {"value": "no_u_turn", "object_types": ["relation"], "key": "restriction:hgv", "description": "Mode specific turn restrictions"}, {"value": "no_straight_on", "object_types": ["relation"], "key": "restriction:hgv", "description": "Mode specific turn restrictions"}, {"value": "only_right_turn", "object_types": ["relation"], "key": "restriction:hgv", "description": "Mode specific turn restrictions"}, {"value": "only_left_turn", "object_types": ["relation"], "key": "restriction:hgv", "description": "Mode specific turn restrictions"}, {"value": "only_straight_on", "object_types": ["relation"], "key": "restriction:hgv", "description": "Mode specific turn restrictions"}, {"value": "no_entry", "object_types": ["relation"], "key": "restriction:hgv", "description": "Mode specific turn restrictions"}, {"value": "no_exit", "object_types": ["relation"], "key": "restriction:hgv", "description": "Mode specific turn restrictions"}, {"description": "Mode specific conditional turn restrictions. See opening_hours format for the time component: https://wiki.openstreetmap.org/wiki/Key:opening_hours", "value": "no_right_turn @ (opening_hours format);", "key": "restriction:hgv:conditional", "object_types": ["relation"]}, {"description": "Mode specific conditional turn restrictions. See opening_hours format for the time component: https://wiki.openstreetmap.org/wiki/Key:opening_hours", "value": "no_left_turn @ (opening_hours format);", "key": "restriction:hgv:conditional", "object_types": ["relation"]}, {"description": "Mode specific conditional turn restrictions. See opening_hours format for the time component: https://wiki.openstreetmap.org/wiki/Key:opening_hours", "value": "no_u_turn @ (opening_hours format);", "key": "restriction:hgv:conditional", "object_types": ["relation"]}, {"description": "Mode specific conditional turn restrictions. See opening_hours format for the time component: https://wiki.openstreetmap.org/wiki/Key:opening_hours", "value": "no_straight_on @ (opening_hours format);", "key": "restriction:hgv:conditional", "object_types": ["relation"]}, {"description": "Mode specific conditional turn restrictions. See opening_hours format for the time component: https://wiki.openstreetmap.org/wiki/Key:opening_hours", "value": "only_right_turn @ (opening_hours format);", "key": "restriction:hgv:conditional", "object_types": ["relation"]}, {"description": "Mode specific conditional turn restrictions. See opening_hours format for the time component: https://wiki.openstreetmap.org/wiki/Key:opening_hours", "value": "only_left_turn @ (opening_hours format);", "key": "restriction:hgv:conditional", "object_types": ["relation"]}, {"description": "Mode specific conditional turn restrictions. See opening_hours format for the time component: https://wiki.openstreetmap.org/wiki/Key:opening_hours", "value": "only_straight_on @ (opening_hours format);", "key": "restriction:hgv:conditional", "object_types": ["relation"]}, {"description": "Mode specific conditional turn restrictions. See opening_hours format for the time component: https://wiki.openstreetmap.org/wiki/Key:opening_hours", "value": "no_entry @ (opening_hours format);", "key": "restriction:hgv:conditional", "object_types": ["relation"]}, {"description": "Mode specific conditional turn restrictions. See opening_hours format for the time component: https://wiki.openstreetmap.org/wiki/Key:opening_hours", "value": "no_exit @ (opening_hours format);", "key": "restriction:hgv:conditional", "object_types": ["relation"]}, {"value": "no_right_turn", "object_types": ["relation"], "key": "restriction:motorcar", "description": "Mode specific turn restrictions"}, {"value": "no_left_turn", "object_types": ["relation"], "key": "restriction:motorcar", "description": "Mode specific turn restrictions"}, {"value": "no_u_turn", "object_types": ["relation"], "key": "restriction:motorcar", "description": "Mode specific turn restrictions"}, {"value": "no_straight_on", "object_types": ["relation"], "key": "restriction:motorcar", "description": "Mode specific turn restrictions"}, {"value": "only_right_turn", "object_types": ["relation"], "key": "restriction:motorcar", "description": "Mode specific turn restrictions"}, {"value": "only_left_turn", "object_types": ["relation"], "key": "restriction:motorcar", "description": "Mode specific turn restrictions"}, {"value": "only_straight_on", "object_types": ["relation"], "key": "restriction:motorcar", "description": "Mode specific turn restrictions"}, {"value": "no_entry", "object_types": ["relation"], "key": "restriction:motorcar", "description": "Mode specific turn restrictions"}, {"value": "no_exit", "object_types": ["relation"], "key": "restriction:motorcar", "description": "Mode specific turn restrictions"}, {"description": "Mode specific conditional turn restrictions. See opening_hours format for the time component: https://wiki.openstreetmap.org/wiki/Key:opening_hours", "value": "no_right_turn @ (opening_hours format);", "key": "restriction:motorcar:conditional", "object_types": ["relation"]}, {"description": "Mode specific conditional turn restrictions. See opening_hours format for the time component: https://wiki.openstreetmap.org/wiki/Key:opening_hours", "value": "no_left_turn @ (opening_hours format);", "key": "restriction:motorcar:conditional", "object_types": ["relation"]}, {"description": "Mode specific conditional turn restrictions. See opening_hours format for the time component: https://wiki.openstreetmap.org/wiki/Key:opening_hours", "value": "no_u_turn @ (opening_hours format);", "key": "restriction:motorcar:conditional", "object_types": ["relation"]}, {"description": "Mode specific conditional turn restrictions. See opening_hours format for the time component: https://wiki.openstreetmap.org/wiki/Key:opening_hours", "value": "no_straight_on @ (opening_hours format);", "key": "restriction:motorcar:conditional", "object_types": ["relation"]}, {"description": "Mode specific conditional turn restrictions. See opening_hours format for the time component: https://wiki.openstreetmap.org/wiki/Key:opening_hours", "value": "only_right_turn @ (opening_hours format);", "key": "restriction:motorcar:conditional", "object_types": ["relation"]}, {"description": "Mode specific conditional turn restrictions. See opening_hours format for the time component: https://wiki.openstreetmap.org/wiki/Key:opening_hours", "value": "only_left_turn @ (opening_hours format);", "key": "restriction:motorcar:conditional", "object_types": ["relation"]}, {"description": "Mode specific conditional turn restrictions. See opening_hours format for the time component: https://wiki.openstreetmap.org/wiki/Key:opening_hours", "value": "only_straight_on @ (opening_hours format);", "key": "restriction:motorcar:conditional", "object_types": ["relation"]}, {"description": "Mode specific conditional turn restrictions. See opening_hours format for the time component: https://wiki.openstreetmap.org/wiki/Key:opening_hours", "value": "no_entry @ (opening_hours format);", "key": "restriction:motorcar:conditional", "object_types": ["relation"]}, {"description": "Mode specific conditional turn restrictions. See opening_hours format for the time component: https://wiki.openstreetmap.org/wiki/Key:opening_hours", "value": "no_exit @ (opening_hours format);", "key": "restriction:motorcar:conditional", "object_types": ["relation"]}, {"value": "no_right_turn", "object_types": ["relation"], "key": "restriction:motorcycle", "description": "Mode specific turn restrictions"}, {"value": "no_left_turn", "object_types": ["relation"], "key": "restriction:motorcycle", "description": "Mode specific turn restrictions"}, {"value": "no_u_turn", "object_types": ["relation"], "key": "restriction:motorcycle", "description": "Mode specific turn restrictions"}, {"value": "no_straight_on", "object_types": ["relation"], "key": "restriction:motorcycle", "description": "Mode specific turn restrictions"}, {"value": "only_right_turn", "object_types": ["relation"], "key": "restriction:motorcycle", "description": "Mode specific turn restrictions"}, {"value": "only_left_turn", "object_types": ["relation"], "key": "restriction:motorcycle", "description": "Mode specific turn restrictions"}, {"value": "only_straight_on", "object_types": ["relation"], "key": "restriction:motorcycle", "description": "Mode specific turn restrictions"}, {"value": "no_entry", "object_types": ["relation"], "key": "restriction:motorcycle", "description": "Mode specific turn restrictions"}, {"value": "no_exit", "object_types": ["relation"], "key": "restriction:motorcycle", "description": "Mode specific turn restrictions"}, {"description": "Mode specific conditional turn restrictions. See opening_hours format for the time component: https://wiki.openstreetmap.org/wiki/Key:opening_hours", "value": "no_right_turn @ (opening_hours format);", "key": "restriction:motorcycle:conditional", "object_types": ["relation"]}, {"description": "Mode specific conditional turn restrictions. See opening_hours format for the time component: https://wiki.openstreetmap.org/wiki/Key:opening_hours", "value": "no_left_turn @ (opening_hours format);", "key": "restriction:motorcycle:conditional", "object_types": ["relation"]}, {"description": "Mode specific conditional turn restrictions. See opening_hours format for the time component: https://wiki.openstreetmap.org/wiki/Key:opening_hours", "value": "no_u_turn @ (opening_hours format);", "key": "restriction:motorcycle:conditional", "object_types": ["relation"]}, {"description": "Mode specific conditional turn restrictions. See opening_hours format for the time component: https://wiki.openstreetmap.org/wiki/Key:opening_hours", "value": "no_straight_on @ (opening_hours format);", "key": "restriction:motorcycle:conditional", "object_types": ["relation"]}, {"description": "Mode specific conditional turn restrictions. See opening_hours format for the time component: https://wiki.openstreetmap.org/wiki/Key:opening_hours", "value": "only_right_turn @ (opening_hours format);", "key": "restriction:motorcycle:conditional", "object_types": ["relation"]}, {"description": "Mode specific conditional turn restrictions. See opening_hours format for the time component: https://wiki.openstreetmap.org/wiki/Key:opening_hours", "value": "only_left_turn @ (opening_hours format);", "key": "restriction:motorcycle:conditional", "object_types": ["relation"]}, {"description": "Mode specific conditional turn restrictions. See opening_hours format for the time component: https://wiki.openstreetmap.org/wiki/Key:opening_hours", "value": "only_straight_on @ (opening_hours format);", "key": "restriction:motorcycle:conditional", "object_types": ["relation"]}, {"description": "Mode specific conditional turn restrictions. See opening_hours format for the time component: https://wiki.openstreetmap.org/wiki/Key:opening_hours", "value": "no_entry @ (opening_hours format);", "key": "restriction:motorcycle:conditional", "object_types": ["relation"]}, {"description": "Mode specific conditional turn restrictions. See opening_hours format for the time component: https://wiki.openstreetmap.org/wiki/Key:opening_hours", "value": "no_exit @ (opening_hours format);", "key": "restriction:motorcycle:conditional", "object_types": ["relation"]}, {"key": "route", "value": "ferry", "object_types": ["way"], "description": "If access not already set then enable access for each type of routing."}, {"key": "route", "value": "mtb", "object_types": ["relation"], "description": "Mountain Bike Network.  Set bitmask for network"}, {"key": "route", "value": "road", "object_types": ["relation"]}, {"description": "Sets the hiking difficulty for cycle and pedestrian passes", "value": "difficult_alpine_hiking", "key": "sac_scale", "object_types": ["way"]}, {"description": "Sets the hiking difficulty for cycle and pedestrian passes", "value": "demanding_alpine_hiking", "key": "sac_scale", "object_types": ["way"]}, {"description": "Sets the hiking difficulty for cycle and pedestrian passes", "value": "alpine_hiking", "key": "sac_scale", "object_types": ["way"]}, {"description": "Sets the hiking difficulty for cycle and pedestrian passes", "value": "demanding_mountain_hiking", "key": "sac_scale", "object_types": ["way"]}, {"description": "Sets the hiking difficulty for cycle and pedestrian passes", "value": "mountain_hiking", "key": "sac_scale", "object_types": ["way"]}, {"description": "Sets the hiking difficulty for cycle and pedestrian passes", "value": "hiking", "key": "sac_scale", "object_types": ["way"]}, {"key": "segregated", "value": "yes", "object_types": ["way"]}, {"key": "segregated", "value": "no", "object_types": ["way"]}, {"key": "service", "value": "emergency_access", "object_types": ["node", "way"], "description": "Overrides access flags for emergency vehicles.  Emergency Routing is allowed."}, {"key": "service", "object_types": ["way"], "description": "Sets the use and if value = driveway and access not already set then enable access for each type of routing."}, {"key": "shoulder", "value": "yes", "object_types": ["way"]}, {"key": "shoulder", "value": "no", "object_types": ["way"]}, {"key": "shoulder", "value": "both", "object_types": ["way"]}, {"key": "shoulder", "value": "right", "object_types": ["way"]}, {"key": "shoulder", "value": "left", "object_types": ["way"]}, {"key": "shoulder:both", "value": "yes", "object_types": ["way"]}, {"key": "shoulder:both", "value": "no", "object_types": ["way"]}, {"key": "shoulder:left", "value": "yes", "object_types": ["way"]}, {"key": "shoulder:left", "value": "no", "object_types": ["way"]}, {"key": "shoulder:right", "value": "yes", "object_types": ["way"]}, {"key": "shoulder:right", "value": "no", "object_types": ["way"]}, {"key": "sidewalk", "value": "both", "object_types": ["way"], "description": "Sets the sidewalk flag to true."}, {"key": "sidewalk", "value": "none", "object_types": ["way"], "description": "Sets the sidewalk flag to false."}, {"key": "sidewalk", "value": "no", "object_types": ["way"], "description": "Sets the sidewalk flag to false."}, {"key": "sidewalk", "value": "right", "object_types": ["way"], "description": "Sets the sidewalk flag to true."}, {"key": "sidewalk", "value": "left", "object_types": ["way"], "description": "Sets the sidewalk flag to true."}, {"key": "sidewalk", "value": "separate", "object_types": ["way"], "description": "Sets the sidewalk flag to false."}, {"key": "sidewalk", "value": "yes", "object_types": ["way"], "description": "Sets the sidewalk flag to true."}, {"key": "sidewalk", "value": "shared", "object_types": ["way"], "description": "Sets the sidewalk flag to true."}, {"key": "sidewalk", "value": "this", "object_types": ["way"], "description": "Sets the sidewalk flag to true."}, {"key": "sidewalk", "value": "detached", "object_types": ["way"], "description": "Sets the sidewalk flag to false."}, {"key": "sidewalk", "value": "raised", "object_types": ["way"], "description": "Sets the sidewalk flag to true."}, {"key": "sidewalk", "value": "separate_double", "object_types": ["way"], "description": "Sets the sidewalk flag to false."}, {"key": "sidewalk", "value": "sidepath", "object_types": ["way"], "description": "Sets the sidewalk flag to false."}, {"key": "sidewalk", "value": "explicit", "object_types": ["way"], "description": "Sets the sidewalk flag to true."}, {"key": "surface", "description": "Road surface", "object_types": ["way"]}, {"key": "smoothness", "description": "Road smoothness.  Note that surface and tracktype keys have a higher priority than smoothness.", "object_types": ["way"]}, {"key": "taxi", "value": "no", "object_types": ["node", "way"], "description": "Overrides access flags for taxi.  taxi Routing not allowed."}, {"key": "taxi", "value": "delivery", "object_types": ["node", "way"], "description": "Overrides access flags for taxi.  taxi Routing not allowed."}, {"key": "taxi", "value": "yes", "object_types": ["node", "way"], "description": "Overrides access flags for taxi.  taxi Routing allowed."}, {"key": "taxi", "value": "designated", "object_types": ["node", "way"], "description": "Overrides access flags for taxi.  taxi Routing allowed."}, {"key": "taxi", "value": "urban", "object_types": ["node", "way"], "description": "Overrides access flags for taxi.  taxi Routing allowed."}, {"key": "taxi", "value": "permissive", "object_types": ["node", "way"], "description": "Overrides access flags for taxi.  taxi Routing allowed."}, {"key": "taxi", "value": "restricted", "object_types": ["node", "way"], "description": "Overrides access flags for taxi.  taxi Routing allowed."}, {"key": "taxi", "value": "destination", "object_types": ["node", "way"], "description": "Overrides access flags for taxi.  taxi Routing allowed."}, {"key": "taxi:backward", "value": "designated", "object_types": ["way"], "description": "Sets the taxi reverse flag to true."}, {"key": "taxi:backward", "value": "yes", "object_types": ["way"], "description": "Sets the taxi reverse flag to true."}, {"description": "Adds conditional access to an edge with a time dependency in opening_hours format, see: https://wiki.openstreetmap.org/wiki/Key:opening_hours", "value": "no @ (opening_hours format)", "key": "taxi:conditional", "object_types": ["way"]}, {"description": "Adds conditional access to an edge with a time dependency in opening_hours format, see: https://wiki.openstreetmap.org/wiki/Key:opening_hours", "value": "yes @ (opening_hours format)", "key": "taxi:conditional", "object_types": ["way"]}, {"description": "Adds conditional access to an edge with a time dependency in opening_hours format, see: https://wiki.openstreetmap.org/wiki/Key:opening_hours", "value": "private @ (opening_hours format)", "key": "taxi:conditional", "object_types": ["way"]}, {"description": "Adds conditional access to an edge with a time dependency in opening_hours format, see: https://wiki.openstreetmap.org/wiki/Key:opening_hours", "value": "delivery @ (opening_hours format)", "key": "taxi:conditional", "object_types": ["way"]}, {"description": "Adds conditional access to an edge with a time dependency in opening_hours format, see: https://wiki.openstreetmap.org/wiki/Key:opening_hours", "value": "designated @ (opening_hours format)", "key": "taxi:conditional", "object_types": ["way"]}, {"description": "Adds conditional access to an edge with a time dependency in opening_hours format, see: https://wiki.openstreetmap.org/wiki/Key:opening_hours", "value": "destination @ (opening_hours format)", "key": "taxi:conditional", "object_types": ["way"]}, {"key": "toll", "value": "yes", "object_types": ["way"]}, {"key": "toll", "value": "true", "object_types": ["way"]}, {"key": "toll", "value": "1", "object_types": ["way"]}, {"key": "toll", "value": "interval", "object_types": ["way"]}, {"key": "toll", "value": "snowmobile", "object_types": ["way"]}, {"key": "toll", "value": "no", "object_types": ["way"]}, {"key": "toll", "value": "false", "object_types": ["way"]}, {"key": "toll", "value": "0", "object_types": ["way"]}, {"key": "tracktype", "value": "grade1", "description": "Track types", "object_types": ["way"]}, {"key": "tracktype", "value": "grade2", "description": "Track types", "object_types": ["way"]}, {"key": "tracktype", "value": "grade3", "description": "Track types", "object_types": ["way"]}, {"key": "tracktype", "value": "grade4", "description": "Track types", "object_types": ["way"]}, {"key": "traffic_signals", "object_types": ["node"], "description": "Sets the traffic signal flag."}, {"key": "traffic_signals:direction", "value": "forward", "object_types": ["node"], "description": "Traffic signal direction."}, {"key": "traffic_signals:direction", "value": "backward", "object_types": ["node"], "description": "Traffic signal direction."}, {"key": "direction", "value": "forward", "object_types": ["node"], "description": "stop or give_way sign direction."}, {"key": "direction", "value": "backward", "object_types": ["node"], "description": "stop or give_way sign direction."}, {"key": "direction", "value": "both", "object_types": ["node"], "description": "stop or give_way sign direction."}, {"key": "stop", "value": "minor", "object_types": ["node"], "description": "used to set the stop signs at the minor roads."}, {"key": "give_way", "value": "minor", "object_types": ["node"], "description": "used to set the give_way signs at the minor roads."}, {"key": "tunnel", "value": "yes", "object_types": ["way"]}, {"key": "tunnel", "value": "true", "object_types": ["way"]}, {"key": "tunnel", "value": "1", "object_types": ["way"]}, {"key": "tunnel", "value": "building_passage", "object_types": ["way"]}, {"key": "tunnel", "value": "no", "object_types": ["way"]}, {"key": "tunnel", "value": "false", "object_types": ["way"]}, {"key": "tunnel", "value": "0", "object_types": ["way"]}, {"description": "Adds sinage information to the given edge in the graph which is helpful to know to where a given road leads. Forward and backward allow for distinct values based on the direction the way is drawn specifically useful for single carriageways.", "key": "turn:lanes", "object_types": ["way"]}, {"description": "Adds sinage information to the given edge in the graph which is helpful to know to where a given road leads. Forward and backward allow for distinct values based on the direction the way is drawn specifically useful for single carriageways.", "key": "turn:lanes:backward", "object_types": ["way"]}, {"description": "Adds sinage information to the given edge in the graph which is helpful to know to where a given road leads. Forward and backward allow for distinct values based on the direction the way is drawn specifically useful for single carriageways.", "key": "turn:lanes:forward", "object_types": ["way"]}, {"key": "type", "value": "restriction", "description": "Turn Restriction", "object_types": ["relation"]}, {"description": "Alternate means of specifying mode specific turn restrictions", "value": "restriction:hgv", "key": "type", "object_types": ["relation"]}, {"description": "Alternate means of specifying mode specific turn restrictions", "value": "restriction:caravan", "key": "type", "object_types": ["relation"]}, {"description": "Alternate means of specifying mode specific turn restrictions", "value": "restriction:motorcar", "key": "type", "object_types": ["relation"]}, {"description": "Alternate means of specifying mode specific turn restrictions", "value": "restriction:bus", "key": "type", "object_types": ["relation"]}, {"description": "Alternate means of specifying mode specific turn restrictions", "value": "restriction:bicycle", "key": "type", "object_types": ["relation"]}, {"description": "Alternate means of specifying mode specific turn restrictions", "value": "restriction:hazmat", "key": "type", "object_types": ["relation"]}, {"key": "type", "value": "route", "description": "Determine route number and name.", "object_types": ["relation"]}, {"key": "type", "value": "boundary", "object_types": ["relation"], "description": "Used in combination with boundary=administrative to denote a boarder between territories"}, {"key": "wheelchair", "value": "no", "object_types": ["node", "way"], "description": "Overrides access flags for wheelchair.  Wheelchair access not allowed."}, {"key": "wheelchair", "value": "yes", "object_types": ["node", "way"], "description": "Overrides access flags for wheelchair.  Wheelchair access allowed."}, {"key": "wheelchair", "value": "designated", "object_types": ["node", "way"], "description": "Overrides access flags for wheelchair.  Wheelchair access allowed."}, {"key": "wheelchair", "value": "limited", "object_types": ["node", "way"], "description": "Overrides access flags for wheelchair.  Wheelchair access allowed."}, {"key": "wheelchair", "value": "official", "object_types": ["node", "way"], "description": "Overrides access flags for wheelchair.  Wheelchair access allowed."}, {"key": "wheelchair", "value": "destination", "object_types": ["node", "way"], "description": "Overrides access flags for wheelchair.  Wheelchair access allowed."}, {"key": "wheelchair", "value": "public", "object_types": ["node", "way"], "description": "Overrides access flags for wheelchair.  Wheelchair access allowed."}, {"key": "wheelchair", "value": "permissive", "object_types": ["node", "way"], "description": "Overrides access flags for wheelchair.  Wheelchair access allowed."}, {"key": "wheelchair", "value": "only", "object_types": ["node", "way"], "description": "Overrides access flags for wheelchair.  Wheelchair access allowed."}, {"key": "wheelchair", "value": "private", "object_types": ["node", "way"], "description": "Overrides access flags for wheelchair.  Wheelchair access allowed."}, {"key": "wheelchair", "value": "permit", "object_types": ["node", "way"], "description": "Overrides access flags for wheelchair.  Wheelchair access allowed."}, {"key": "wheelchair", "value": "residents", "object_types": ["node", "way"], "description": "Overrides access flags for wheelchair.  Wheelchair access allowed."}, {"key": "wheelchair", "value": "impassable", "object_types": ["node", "way"], "description": "Overrides access flags for wheelchair.  Wheelchair access not allowed."}, {"key": "wheelchair", "value": "partial", "object_types": ["node", "way"], "description": "Overrides access flags for wheelchair.  Wheelchair access not allowed."}, {"key": "wheelchair", "value": "bad", "object_types": ["node", "way"], "description": "Overrides access flags for wheelchair.  Wheelchair access not allowed."}, {"key": "wheelchair", "value": "half", "object_types": ["node", "way"], "description": "Overrides access flags for wheelchair.  Wheelchair access not allowed."}, {"key": "wheelchair", "value": "assisted", "object_types": ["node", "way"], "description": "Overrides access flags for wheelchair.  Wheelchair access allowed."}, {"key": "lit", "value": "yes", "object_types": ["way"], "description": "Considers the way lit"}, {"key": "lit", "value": "no", "object_types": ["way"], "description": "Considers the way unlit"}, {"key": "lit", "value": "automatic", "object_types": ["way"], "description": "Considers the way lit"}, {"key": "lit", "value": "24/7", "object_types": ["way"], "description": "Considers the way lit"}, {"key": "lit", "value": "disused", "object_types": ["way"], "description": "Considers the way unlit"}, {"key": "lit", "value": "limited", "object_types": ["way"], "description": "Considers the way unlit"}, {"key": "lit", "value": "sunset-sunrise", "object_types": ["way"], "description": "Considers the way lit"}, {"key": "lit", "value": "dusk-dawn", "object_types": ["way"], "description": "Considers the way lit"}, {"key": "motor_vehicle:forward", "value": "yes", "object_types": ["way"], "description": "Enables motor_vehicle access in the forward direction"}, {"key": "motor_vehicle:forward", "value": "no", "object_types": ["way"], "description": "Disables motor_vehicle access in the forward direction"}, {"key": "motor_vehicle:backward", "value": "yes", "object_types": ["way"], "description": "Enables motor_vehicle access in the backward direction"}, {"key": "motor_vehicle:backward", "value": "no", "object_types": ["way"], "description": "Disables motor_vehicle access in the backward direction"}, {"key": "vehicle:forward", "value": "yes", "object_types": ["way"], "description": "Enables vehicle access in the forward direction"}, {"key": "vehicle:forward", "value": "no", "object_types": ["way"], "description": "Disables vehicle access in the forward direction"}, {"key": "vehicle:backward", "value": "yes", "object_types": ["way"], "description": "Enables vehicle access in the backward direction"}, {"key": "vehicle:backward", "value": "no", "object_types": ["way"], "description": "Disables vehicle access in the backward direction"}, {"key": "foot:forward", "value": "yes", "object_types": ["way"], "description": "Enables foot access in the forward direction"}, {"key": "foot:forward", "value": "no", "object_types": ["way"], "description": "Disables foot access in the forward direction"}, {"key": "foot:backward", "value": "yes", "object_types": ["way"], "description": "Enables foot access in the backward direction"}, {"key": "foot:backward", "value": "no", "object_types": ["way"], "description": "Disables foot access in the backward direction"}, {"key": "bicycle:forward", "value": "yes", "object_types": ["way"], "description": "Enables bicycle access in the forward direction"}, {"key": "bicycle:forward", "value": "no", "object_types": ["way"], "description": "Disables bicycle access in the forward direction"}, {"key": "bicycle:backward", "value": "yes", "object_types": ["way"], "description": "Enables bicycle access in the backward direction"}, {"key": "bicycle:backward", "value": "no", "object_types": ["way"], "description": "Disables bicycle access in the backward direction"}]}
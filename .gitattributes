# Set the default behavior, in case people don't have core.autocrlf set.
* text=auto

# Explicitly declare text files you want to always be normalized and converted
# to native line endings on checkout.
*.cc text
*.cpp text
*.h text
*.hpp text
*.in text

# Declare files that will always have LF line endings on checkout.
*.ac text eol=lf
*.am text eol=lf
*.csv text eol=lf
*.sh text eol=lf

# Declare files that will always have CRLF line endings on checkout.
*.bat text eol=crlf
*.cmd text eol=crlf
*.ps1 text eol=crlf

# Declare changelog to be union merged, as that's tyically
# how you want to deal with conflicts in a changelog
/CHANGELOG.md merge=union

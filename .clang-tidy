---
# https://clang.llvm.org/extra/clang-tidy/checks/list.html
Checks: >
    bugprone-throw-keyword-missing,
    clang-*,
    clang-analyzer-*,
    performance*,
    -performance-move-const-arg,
    -performance-avoid-endl,
    misc-unused-parameters,
    modernize-make-shared,
    modernize-make-unique,
WarningsAsErrors: ''
CheckOptions:
  - key:             google-readability-braces-around-statements.ShortStatementLines
    value:           '1'
  - key:             google-readability-function-size.StatementThreshold
    value:           '800'
  - key:             google-readability-namespace-comments.ShortNamespaceLines
    value:           '10'
  - key:             google-readability-namespace-comments.SpacesBeforeComments
    value:           '2'
  - key:             modernize-loop-convert.MaxCopySize
    value:           '16'
  - key:             modernize-loop-convert.MinConfidence
    value:           reasonable
  - key:             modernize-loop-convert.NamingStyle
    value:           CamelCase
  - key:             modernize-pass-by-value.IncludeStyle
    value:           llvm
  - key:             modernize-replace-auto-ptr.IncludeStyle
    value:           llvm
  - key:             modernize-use-nullptr.NullMacros
    value:           'NULL'
...

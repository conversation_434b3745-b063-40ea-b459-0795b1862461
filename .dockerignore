# same as .gitignore
# editor temporary / backup files
*~

# built objects
valhalla_run_map_match
valhalla_meili_worker
valhalla_benchmark_admins
valhalla_build_admins
valhalla_build_connectivity
valhalla_build_tiles
valhalla_query_transit
valhalla_ways_to_edges
valhalla_build_speeds
valhalla_build_statistics
valhalla_benchmark_adjacency_list
valhalla_benchmark_loki
valhalla_benchmark_skadi
valhalla_export_edges
valhalla_loki_worker
valhalla_odin_worker
valhalla_service
valhalla_validate_transit
valhalla_run_matrix
valhalla_run_isochrone
valhalla_run_route
valhalla_path_comparison
valhalla_skadi_worker
valhalla_thor_worker
valhalla_tyr_worker
valhalla_associate_segments
valhalla_pack_elevation
genfiles/
locales/*.UTF-8
!locales/merge-en.sh
py-compile
*.la
*.o
.deps/
.dirstamp
*.lo
.libs/
libvalhalla.la
date_time_zonespec.csv
mason/
mason_packages/
.DS_Store

# scripts
scripts/gdal-2.0.0/
scripts/gdal-2.0.0.tar.gz

# tools
.cproject
.project
.settings
CMakeLists.txt.user
/.vs*/
/*build*/
/CMakeSettings.json
.idea
/.tidytmp
vcpkg*/
overlay-ports-vcpkg/
vcpkg-configuration.json
run_route_scripts/
test_requests/

# docker
Dockerfile

# CI
.github/
.circleci/
.pre-commit-config.yaml

# docs etc
site/
docs/
CONTRIBUTING.md
NEWS 
taginfo.json

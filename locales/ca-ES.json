{"posix_locale": "ca_ES.UTF-8", "aliases": ["ca"], "instructions": {"arrive": {"phrases": {"0": "Arribada: <TIME>.", "1": "Arribada: <TIME> a <TRANSIT_STOP>."}, "example_phrases": {"0": ["Arribada: 8:02 AM."], "1": ["Arribada: 8:02 AM a 8 St - NYU."]}}, "arrive_verbal": {"phrases": {"0": "Arribada a les <TIME>.", "1": "Arribada a les <TIME> at <TRANSIT_STOP>."}, "example_phrases": {"0": ["Arribada a les 8:02 AM."], "1": ["Arribada a les 8:02 AM at 8 St - NYU."]}}, "bear": {"phrases": {"0": "Queda't a <RELATIVE_DIRECTION>.", "1": "Queda't a <RELATIVE_DIRECTION> cap a <STREET_NAMES>.", "2": "Queda't a <RELATIVE_DIRECTION> cap a <BEGIN_STREET_NAMES>. Continua per <STREET_NAMES>.", "3": "Queda't a <RELATIVE_DIRECTION> per continuar per <STREET_NAMES>.", "4": "Bear <RELATIVE_DIRECTION> at <JUNCTION_NAME>.", "5": "Bear <RELATIVE_DIRECTION> toward <TOWARD_SIGN>."}, "empty_street_name_labels": ["la vorera", "el carril bici", "la pista de bicicleta de muntanya", "the crosswalk", "the stairs", "the bridge", "the tunnel"], "relative_directions": ["l'esquerra", "la dreta"], "example_phrases": {"0": ["Queda't a la dreta."], "1": ["Queda't a l'esquerra cap a Arlen Road."], "2": ["queda't a la dreta cap a Belair Road/US 1 Business. Continua per US 1 Business."], "3": ["Queda't a l'esquerra per continuar per US 15 South."], "4": ["Bear right at Mannenbashi East."], "5": ["Bear left toward Baltimore."]}}, "bear_verbal": {"phrases": {"0": "Queda't a <RELATIVE_DIRECTION>.", "1": "Queda't a <RELATIVE_DIRECTION> cap a <STREET_NAMES>.", "2": "Queda't a <RELATIVE_DIRECTION> cap a <BEGIN_STREET_NAMES>.", "3": "Queda't a <RELATIVE_DIRECTION> per continuar per <STREET_NAMES>.", "4": "Bear <RELATIVE_DIRECTION> at <JUNCTION_NAME>.", "5": "Bear <RELATIVE_DIRECTION> toward <TOWARD_SIGN>."}, "empty_street_name_labels": ["la vorera", "el carril bici", "la pista de bicicleta de muntanya", "the crosswalk", "the stairs", "the bridge", "the tunnel"], "relative_directions": ["l'esquerra", "la dreta"], "example_phrases": {"0": ["Queda't a la dreta."], "1": ["Queda't a l'esquerra cap a Arlen Road."], "2": ["queda't a la dreta cap a Belair Road/US 1 Business. Continua per US 1 Business."], "3": ["Queda't a l'esquerra per continuar per US 15 South."], "4": ["Bear right at Mannenbashi East."], "5": ["Bear left toward Baltimore."]}}, "becomes": {"phrases": {"0": "<PREVIOUS_STREET_NAMES> ara és <STREET_NAMES>."}, "example_phrases": {"0": ["Vine Street ara és Middletown Road."]}}, "becomes_verbal": {"phrases": {"0": "<PREVIOUS_STREET_NAMES> ara és <STREET_NAMES>."}, "example_phrases": {"0": ["Vine Street ara és Middletown Road."]}}, "continue": {"phrases": {"0": "Continua.", "1": "Continua per <STREET_NAMES>.", "2": "Continue at <JUNCTION_NAME>.", "3": "Continue toward <TOWARD_SIGN>."}, "empty_street_name_labels": ["la vorera", "el carril bici", "la pista de bicicleta de muntanya", "the crosswalk", "the stairs", "the bridge", "the tunnel"], "example_phrases": {"0": ["Continua."], "1": ["Continua per 10th Avenue."], "2": ["Continue at Mannenbashi East."], "3": ["Continue toward Baltimore."]}}, "continue_verbal": {"phrases": {"0": "Continua.", "1": "Continua durant <LENGTH>.", "2": "Continua per <STREET_NAMES>.", "3": "Continua per <STREET_NAMES> durant <LENGTH>.", "4": "Continue at <JUNCTION_NAME>.", "5": "Continue at <JUNCTION_NAME> for <LENGTH>.", "6": "Continue toward <TOWARD_SIGN>.", "7": "Continue toward <TOWARD_SIGN> for <LENGTH>."}, "empty_street_name_labels": ["la vorera", "el carril bici", "la pista de bicicleta de muntanya", "the crosswalk", "the stairs", "the bridge", "the tunnel"], "metric_lengths": ["<KILOMETERS> quilòmetres", "1 quilòmetre", "<METERS> metres", "menys de 10 metres"], "us_customary_lengths": ["<MILES> milles", "1 milla", "mitja milla", "a quarter mile", "<FEET> peus", "menys de 10 peus"], "example_phrases": {"0": ["Continue."], "1": ["Continue for 300 feet."], "2": ["Continue on 10th Avenue."], "3": ["Continue on 10th Avenue for 3 miles."], "4": ["Continue at Mannenbashi East."], "5": ["Continue at Mannenbashi East for 2 miles."], "6": ["Continue toward Baltimore."], "7": ["Continue toward Baltimore for 5 miles."]}}, "continue_verbal_alert": {"phrases": {"0": "Continua.", "1": "Continua per <STREET_NAMES>.", "2": "Continue at <JUNCTION_NAME>.", "3": "Continue toward <TOWARD_SIGN>."}, "empty_street_name_labels": ["la vorera", "el carril bici", "la pista de bicicleta de muntanya", "the crosswalk", "the stairs", "the bridge", "the tunnel"], "example_phrases": {"0": ["Continua."], "1": ["Continua per 10th Avenue."], "2": ["Continue at Mannenbashi East."], "3": ["Continue toward Baltimore."]}}, "depart": {"phrases": {"0": "Sortida: <TIME>.", "1": "Sortida: <TIME> de <TRANSIT_STOP>."}, "example_phrases": {"0": ["Sortida: 8:02 AM."], "1": ["Sortida: 8:02 AM de 8 St - NYU."]}}, "depart_verbal": {"phrases": {"0": "Sortida a les <TIME>.", "1": "Sortida a les <TIME> de <TRANSIT_STOP>."}, "example_phrases": {"0": ["Sortida a les 8:02 AM."], "1": ["Sortida a les 8:02 AM de 8 St - NYU."]}}, "destination": {"phrases": {"0": "Has arribat a la teva destinació.", "1": "Has arribat a <DESTINATION>.", "2": "La teva destinació és a <RELATIVE_DIRECTION>.", "3": "<DESTINATION> és a <RELATIVE_DIRECTION>."}, "relative_directions": ["l'esquerra", "la dreta"], "example_phrases": {"0": ["Has arribat a la teva destinació."], "1": ["Has arribat a 3206 Powelton Avenue."], "2": ["La teva destinació és a l'esquerra.", "La teva destinació és a la dreta."], "3": ["Lancaster Brewing Company és a l'esquerra."]}}, "destination_verbal": {"phrases": {"0": "Has arribat a la teva destinació.", "1": "Has arribat a <DESTINATION>.", "2": "La teva destinació és a <RELATIVE_DIRECTION>.", "3": "<DESTINATION> és a <RELATIVE_DIRECTION>."}, "relative_directions": ["l'esquerra", "la dreta"], "example_phrases": {"0": ["Has arribat a la teva destinació."], "1": ["Has arribat a 32 o6 Powelton Avenue."], "2": ["La teva destinació és a l'esquerra.", "La teva destinació és a la dreta."], "3": ["Lancaster Brewing Company és a l'esquerra."]}}, "destination_verbal_alert": {"phrases": {"0": "Arribaràs a la teva destinació.", "1": "Arribaràs a <DESTINATION>.", "2": "La teva destinació estarà a <RELATIVE_DIRECTION>.", "3": "<DESTINATION> estarà a <RELATIVE_DIRECTION>."}, "relative_directions": ["l'esquerra", "la dreta"], "example_phrases": {"0": ["Arribaràs a la teva destinació."], "1": ["Arribaràs a 32 o6 Powelton Avenue."], "2": ["La teva destinació estarà a l'esquerra.", "La teva destinació estarà a la dreta."], "3": ["Lancaster Brewing Company estarà a l'esquerra."]}}, "enter_ferry": {"phrases": {"0": "Agafa el ferri.", "1": "Agafa el <STREET_NAMES>.", "2": "Agafa el <STREET_NAMES> <FERRY_LABEL>.", "3": "Take the ferry toward <TOWARD_SIGN>."}, "empty_street_name_labels": ["la vorera", "el carril bici", "la pista de bicicleta de muntanya", "the crosswalk", "the stairs", "the bridge", "the tunnel"], "ferry_label": "Ferry", "example_phrases": {"0": ["Agafa el ferri."], "1": ["Agafa el Millersburg Ferry."], "2": ["Agafa el Bridgeport - Port Jefferson Ferry."], "3": ["Take the ferry toward Cape May."]}}, "enter_ferry_verbal": {"phrases": {"0": "Agafa el ferri.", "1": "Agafa el <STREET_NAMES>.", "2": "Agafa el <STREET_NAMES> <FERRY_LABEL>.", "3": "Take the ferry toward <TOWARD_SIGN>."}, "empty_street_name_labels": ["la vorera", "el carril bici", "la pista de bicicleta de muntanya", "the crosswalk", "the stairs", "the bridge", "the tunnel"], "ferry_label": "Ferry", "example_phrases": {"0": ["Agafa el ferri."], "1": ["Agafa el Millersburg Ferry."], "2": ["Agafa el Bridgeport - Port Jefferson Ferry."], "3": ["Take the ferry toward Cape May."]}}, "enter_roundabout": {"phrases": {"0": "Entra a la rotonda.", "1": "Entra a la rotonda i pren la sortida <ORDINAL_VALUE>.", "2": "Enter the roundabout and take the <ORDINAL_VALUE> exit onto <ROUNDABOUT_EXIT_STREET_NAMES>.", "3": "Enter the roundabout and take the <ORDINAL_VALUE> exit onto <ROUNDABOUT_EXIT_BEGIN_STREET_NAMES>. Continue on <ROUNDABOUT_EXIT_STREET_NAMES>.", "4": "Enter the roundabout and take the <ORDINAL_VALUE> exit toward <TOWARD_SIGN>.", "5": "Enter the roundabout and take the exit onto <ROUNDABOUT_EXIT_STREET_NAMES>.", "6": "Enter the roundabout and take the exit onto <ROUNDABOUT_EXIT_BEGIN_STREET_NAMES>. Continue on <ROUNDABOUT_EXIT_STREET_NAMES>.", "7": "Enter the roundabout and take the exit toward <TOWARD_SIGN>.", "8": "Enter <STREET_NAMES>", "9": "Enter <STREET_NAMES> and take the <ORDINAL_VALUE> exit.", "10": "Enter <STREET_NAMES> and take the <ORDINAL_VALUE> exit onto <ROUNDABOUT_EXIT_STREET_NAMES>.", "11": "Enter <STREET_NAMES> and take the <ORDINAL_VALUE> exit onto <ROUNDABOUT_EXIT_BEGIN_STREET_NAMES>. Continue on <ROUNDABOUT_EXIT_STREET_NAMES>.", "12": "Enter <STREET_NAMES> and take the <ORDINAL_VALUE> exit toward <TOWARD_SIGN>.", "13": "Enter <STREET_NAMES> and take the exit onto <ROUNDABOUT_EXIT_STREET_NAMES>.", "14": "Enter <STREET_NAMES> and take the exit onto <ROUNDABOUT_EXIT_BEGIN_STREET_NAMES>. Continue on <ROUNDABOUT_EXIT_STREET_NAMES>.", "15": "Enter <STREET_NAMES> and take the exit toward <TOWARD_SIGN>."}, "ordinal_values": ["1a", "2a", "3a", "4a", "5ena", "6ena", "7ena", "8ena", "9ena", "10ena"], "empty_street_name_labels": ["la vorera", "el carril bici", "la pista de bicicleta de muntanya", "the crosswalk", "the stairs", "the bridge", "the tunnel"], "example_phrases": {"0": ["Entra a la rotonda."], "1": ["Entra a la rotonda i pren la sortida 1a.", "Entra a la rotonda i pren la sortida 2a.", "Entra a la rotonda i pren la sortida 3a.", "Entra a la rotonda i pren la sortida 4a.", "Entra a la rotonda i pren la sortida 5ena.", "Entra a la rotonda i pren la sortida 6ena.", "Entra a la rotonda i pren la sortida 7ena.", "Entra a la rotonda i pren la sortida 8ena.", "Entra a la rotonda i pren la sortida 9ena.", "Entra a la rotonda i pren la sortida 10ena."], "2": ["Enter the roundabout and take the 3rd exit onto Main Street."], "3": ["Enter the roundabout and take the 3rd exit onto US 322/Main Street. Continue on US 322."], "4": ["Enter the roundabout and take the 3rd exit toward Baltimore."], "5": ["Enter the roundabout and take the exit onto Main Street."], "6": ["Enter the roundabout and take the exit onto US 322/Main Street. Continue on US 322."], "7": ["Enter the roundabout and take the exit toward Baltimore."], "8": ["Enter Dupont Circle."], "9": ["Enter Dupont Circle and take the 1st exit."], "10": ["Enter Dupont Circle and take the 3rd exit onto Main Street."], "11": ["Enter Dupont Circle and take the 3rd exit onto US 322/Main Street. Continue on US 322."], "12": ["Enter Dupont Circle and take the 3rd exit toward Baltimore."], "13": ["Enter Dupont Circle and take the exit onto Main Street."], "14": ["Enter Dupont Circle and take the exit onto US 322/Main Street. Continue on US 322."], "15": ["Enter Dupont Circle and take the exit toward Baltimore."]}}, "enter_roundabout_verbal": {"phrases": {"0": "Entra a la rotonda.", "1": "Entra a la rotonda i pren la sortida <ORDINAL_VALUE>.", "2": "Enter the roundabout and take the <ORDINAL_VALUE> exit onto <ROUNDABOUT_EXIT_STREET_NAMES>.", "3": "Enter the roundabout and take the <ORDINAL_VALUE> exit onto <ROUNDABOUT_EXIT_BEGIN_STREET_NAMES>.", "4": "Enter the roundabout and take the <ORDINAL_VALUE> exit toward <TOWARD_SIGN>.", "5": "Enter the roundabout and take the exit onto <ROUNDABOUT_EXIT_STREET_NAMES>.", "6": "Enter the roundabout and take the exit onto <ROUNDABOUT_EXIT_BEGIN_STREET_NAMES>.", "7": "Enter the roundabout and take the exit toward <TOWARD_SIGN>.", "8": "Enter <STREET_NAMES>", "9": "Enter <STREET_NAMES> and take the <ORDINAL_VALUE> exit.", "10": "Enter <STREET_NAMES> and take the <ORDINAL_VALUE> exit onto <ROUNDABOUT_EXIT_STREET_NAMES>.", "11": "Enter <STREET_NAMES> and take the <ORDINAL_VALUE> exit onto <ROUNDABOUT_EXIT_BEGIN_STREET_NAMES>.", "12": "Enter <STREET_NAMES> and take the <ORDINAL_VALUE> exit toward <TOWARD_SIGN>.", "13": "Enter <STREET_NAMES> and take the exit onto <ROUNDABOUT_EXIT_STREET_NAMES>.", "14": "Enter <STREET_NAMES> and take the exit onto <ROUNDABOUT_EXIT_BEGIN_STREET_NAMES>.", "15": "Enter <STREET_NAMES> and take the exit toward <TOWARD_SIGN>."}, "ordinal_values": ["1a", "2a", "3a", "4a", "5ena", "6ena", "7ena", "8ena", "9ena", "10ena"], "empty_street_name_labels": ["la vorera", "el carril bici", "la pista de bicicleta de muntanya", "the crosswalk", "the stairs", "the bridge", "the tunnel"], "example_phrases": {"0": ["Entra a la rotonda."], "1": ["Entra a la rotonda i pren la sortida 1a.", "Entra a la rotonda i pren la sortida 2a.", "Entra a la rotonda i pren la sortida 3a.", "Entra a la rotonda i pren la sortida 4a.", "Entra a la rotonda i pren la sortida 5ena.", "Entra a la rotonda i pren la sortida 6ena.", "Entra a la rotonda i pren la sortida 7ena.", "Entra a la rotonda i pren la sortida 8ena.", "Entra a la rotonda i pren la sortida 9ena.", "Entra a la rotonda i pren la sortida 10ena."], "2": ["Enter the roundabout and take the 3rd exit onto Main Street."], "3": ["Enter the roundabout and take the 3rd exit onto U.S. 3 22/Main Street."], "4": ["Enter the roundabout and take the 3rd exit toward Baltimore."], "5": ["Enter the roundabout and take the exit onto Main Street."], "6": ["Enter the roundabout and take the exit onto U.S. 3 22/Main Street."], "7": ["Enter the roundabout and take the exit toward Baltimore."], "8": ["Enter Dupont Circle."], "9": ["Enter Dupont Circle and take the 1st exit."], "10": ["Enter Dupont Circle and take the 3rd exit onto Main Street."], "11": ["Enter Dupont Circle and take the 3rd exit onto U.S. 3 22/Main Street."], "12": ["Enter Dupont Circle and take the 3rd exit toward Baltimore."], "13": ["Enter Dupont Circle and take the exit onto Main Street."], "14": ["Enter Dupont Circle and take the exit onto U.S. 3 22/Main Street."], "15": ["Enter Dupont Circle and take the exit toward Baltimore."]}}, "exit": {"phrases": {"0": "Pren la sortida de <RELATIVE_DIRECTION>.", "1": "Pren la sortida <NUMBER_SIGN> de <RELATIVE_DIRECTION>.", "2": "Pren la sortida <BRANCH_SIGN> de <RELATIVE_DIRECTION>.", "3": "Pren la sortida <NUMBER_SIGN> de <RELATIVE_DIRECTION> cap a <BRANCH_SIGN>.", "4": "Pren la sortida de <RELATIVE_DIRECTION> cap a <TOWARD_SIGN>.", "5": "Pren la sortida <NUMBER_SIGN> de <RELATIVE_DIRECTION> cap a <TOWARD_SIGN>.", "6": "Pren la sortida <BRANCH_SIGN> de <RELATIVE_DIRECTION> cap a <TOWARD_SIGN>.", "7": "Pren la sortida <NUMBER_SIGN> de <RELATIVE_DIRECTION> a <BRANCH_SIGN> cap a <TOWARD_SIGN>.", "8": "Pren la sortida <NAME_SIGN> de <RELATIVE_DIRECTION>.", "10": "Pren la sortida <NAME_SIGN> de <RELATIVE_DIRECTION> cap a <BRANCH_SIGN>.", "12": "Pren la sortida <NAME_SIGN> de <RELATIVE_DIRECTION> cap a <TOWARD_SIGN>.", "14": "Pren la sortida <NAME_SIGN> de <RELATIVE_DIRECTION> a <BRANCH_SIGN> cap a <TOWARD_SIGN>.", "15": "Pren la sortida.", "16": "Pren la sortida <NUMBER_SIGN>.", "17": "Pren la sortida <BRANCH_SIGN>.", "18": "Pren la sortida <NUMBER_SIGN> cap a <BRANCH_SIGN>.", "19": "Pren la sortida cap a <TOWARD_SIGN>.", "20": "Pren la sortida <NUMBER_SIGN> cap a <TOWARD_SIGN>.", "21": "Pren la sortida <BRANCH_SIGN> cap a <TOWARD_SIGN>.", "22": "Pren la sortida <NUMBER_SIGN> a <BRANCH_SIGN> cap a <TOWARD_SIGN>.", "23": "Pren la sortida <NAME_SIGN>.", "25": "Pren la sortida <NAME_SIGN> cap a <BRANCH_SIGN>.", "27": "Pren la sortida <NAME_SIGN> cap a <TOWARD_SIGN>.", "29": "Pren la sortida <NAME_SIGN> a <BRANCH_SIGN> cap a <TOWARD_SIGN>."}, "relative_directions": ["l'esquerra", "la dreta"], "example_phrases": {"0": ["Pren la sortida de l'esquerra.", "Pren la sortida de la dreta."], "1": ["Pren la sortida 67 B-A de la dreta."], "2": ["Pren la sortida US 322 West de la dreta."], "3": ["Pren la sortida 67 B-A de la dreta cap a US 322 West."], "4": ["Pren la sortida de la dreta cap a Lewistown."], "5": ["Pren la sortida 67 B-A de la dreta cap a Lewistown."], "6": ["Pren la sortida US 322 West de la dreta cap a Lewistown."], "7": ["Pren la sortida 67 B-A de la dreta a US 322 West cap a Lewistown/State College."], "8": ["Pren la sortida White Marsh Boulevard de l'esquerra."], "10": ["Pren la sortida White Marsh Boulevard de l'esquerra cap a MD 43 East."], "12": ["Pren la sortida White Marsh Boulevard de l'esquerra cap a White Marsh."], "14": ["Pren la sortida White Marsh Boulevard de l'esquerra a MD 43 East cap a White Marsh."], "15": ["Pren la sortida."], "16": ["Pren la sortida 67 B-A."], "17": ["Pren la sortida US 322 West."], "18": ["Pren la sortida 67 B-A cap a US 322 West."], "19": ["Pren la sortida cap a Lewistown."], "20": ["Pren la sortida 67 B-A cap a Lewistown."], "21": ["Pren la sortida US 322 West cap a Lewistown."], "22": ["Pren la sortida 67 B-A cap a US 322 West cap a Lewistown/State College."], "23": ["Pren la sortida White Marsh Boulevard."], "25": ["Pren la sortida White Marsh Boulevard cap a MD 43 East."], "27": ["Pren la sortida White Marsh Boulevard cap a White Marsh."], "29": ["Pren la sortida White Marsh Boulevard cap a MD 43 East cap a White Marsh."]}}, "exit_roundabout": {"phrases": {"0": "Surt de la rotonda.", "1": "Surt de la rotonda a <STREET_NAMES>.", "2": "Surt de la rotonda a <BEGIN_STREET_NAMES>. Continua per <STREET_NAMES>.", "3": "Exit the roundabout toward <TOWARD_SIGN>."}, "empty_street_name_labels": ["la vorera", "el carril bici", "la pista de bicicleta de muntanya", "the crosswalk", "the stairs", "the bridge", "the tunnel"], "example_phrases": {"0": ["Surt de la rotonda."], "1": ["Surt de la rotonda a Philadelphia Road/MD 7."], "2": ["Surt de la rotonda a Catoctin Mountain Highway/US 15. Continua per US 15."], "3": ["Exit the roundabout toward Baltimore."]}}, "exit_roundabout_verbal": {"phrases": {"0": "Surt de la rotonda.", "1": "Surt de la rotonda a <STREET_NAMES>.", "2": "Surt de la rotonda a <BEGIN_STREET_NAMES>. Continua per <STREET_NAMES>.", "3": "Exit the roundabout toward <TOWARD_SIGN>."}, "empty_street_name_labels": ["la vorera", "el carril bici", "la pista de bicicleta de muntanya", "the crosswalk", "the stairs", "the bridge", "the tunnel"], "example_phrases": {"0": ["Surt de la rotonda."], "1": ["Surt de la rotonda a Philadelphia Road, Maryland 7."], "2": ["Surt de la rotonda a Catoctin Mountain Highway, U.S. 15."], "3": ["Exit the roundabout toward Baltimore."]}}, "exit_verbal": {"phrases": {"0": "Pren la sortida de <RELATIVE_DIRECTION>.", "1": "Pren la sortida <NUMBER_SIGN> de <RELATIVE_DIRECTION>.", "2": "Pren la sortida <BRANCH_SIGN> de <RELATIVE_DIRECTION>.", "3": "Pren la sortida <NUMBER_SIGN> de <RELATIVE_DIRECTION> cap a <BRANCH_SIGN>.", "4": "Pren la sortida de <RELATIVE_DIRECTION> cap a <TOWARD_SIGN>.", "5": "Pren la sortida <NUMBER_SIGN> de <RELATIVE_DIRECTION> cap a <TOWARD_SIGN>.", "6": "Pren la sortida <BRANCH_SIGN> de <RELATIVE_DIRECTION> cap a <TOWARD_SIGN>.", "7": "Pren la sortida <NUMBER_SIGN> de <RELATIVE_DIRECTION> a <BRANCH_SIGN> cap a <TOWARD_SIGN>.", "8": "Pren la sortida <NAME_SIGN> de <RELATIVE_DIRECTION>.", "10": "Pren la sortida <NAME_SIGN> de <RELATIVE_DIRECTION> cap a <BRANCH_SIGN>.", "12": "Pen la sortida <NAME_SIGN> de <RELATIVE_DIRECTION> cap a <TOWARD_SIGN>.", "14": "Pren la sortida <NAME_SIGN> de <RELATIVE_DIRECTION> a <BRANCH_SIGN> cap a <TOWARD_SIGN>.", "15": "Pren la sortida", "16": "Pren la sortida <NUMBER_SIGN>.", "17": "Pren la sortida <BRANCH_SIGN>.", "18": "Pren la sortida <NUMBER_SIGN> cap a <BRANCH_SIGN>.", "19": "Pren la sortida cap a <TOWARD_SIGN>.", "20": "Pren la sortida <NUMBER_SIGN> cap a <TOWARD_SIGN>.", "21": "Pren la sortida <BRANCH_SIGN> cap a <TOWARD_SIGN>.", "22": "Pren la sortida <NUMBER_SIGN> a <BRANCH_SIGN> cap a <TOWARD_SIGN>.", "23": "Pren la sortida <NAME_SIGN>.", "25": "Pren la sortida <NAME_SIGN> cap a <BRANCH_SIGN>.", "27": "Pren la sortida <NAME_SIGN> cap a <TOWARD_SIGN>.", "29": "Pren la sortida <NAME_SIGN> a <BRANCH_SIGN> cap a <TOWARD_SIGN>."}, "relative_directions": ["l'esquerra", "la dreta"], "example_phrases": {"0": ["Pren la sortida de l'esquerra.", "Pren la sortida de la dreta."], "1": ["Pren la sortida 67 B-A de la dreta."], "2": ["Pren la sortida U.S. 3 22 West de la dreta."], "3": ["Pren la sortida 67 B-A de la dreta cap a U.S. 3 22 West."], "4": ["Pren la sortida de la dreta cap a Lewistown."], "5": ["Pren la sortida 67 B-A de la dreta cap a Lewistown."], "6": ["Pren la sortida U.S. 3 22 West de la dreta cap a Lewistown."], "7": ["Pren la sortida 67 B-A de la dreta a U.S. 3 22 West cap a Lewistown, State College."], "8": ["Pren la sortida White Marsh Boulevard de l'esquerra."], "10": ["Pren la sortida White Marsh Boulevard de l'esquerra a Maryland 43 East."], "12": ["Pren la sortida White Marsh Boulevard de l'esquerra cap a White Marsh."], "14": ["Pren la sortida White Marsh Boulevard de l'esquerra a Maryland 43 East cap a White Marsh."], "15": ["Pren la sortida."], "16": ["Pren la sortida 67 B-A."], "17": ["Pren la sortida US 322 West."], "18": ["Pren la sortida 67 B-A cap a US 322 West."], "19": ["Pren la sortida cap a Lewistown."], "20": ["Pren la sortida 67 B-A cap a Lewistown."], "21": ["Pren la sortida US 322 West cap a Lewistown."], "22": ["Pren la sortida 67 B-A a US 322 West cap a Lewistown/State College."], "23": ["Pren la sortida White Marsh Boulevard."], "25": ["Pren la sortida White Marsh Boulevard onto MD 43 East."], "27": ["Pren la sortida White Marsh Boulevard cap a White Marsh."], "29": ["Pren la sortida White Marsh Boulevard a MD 43 East cap a White Marsh."]}}, "exit_visual": {"phrases": {"0": "Exit <EXIT_NUMBERS>"}, "example_phrases": {"0": ["Exit 1A"]}}, "keep": {"phrases": {"0": "Queda't a <RELATIVE_DIRECTION> a la bifurcació.", "1": "Queda't a <RELATIVE_DIRECTION> per prendre la sortida <NUMBER_SIGN>.", "2": "Queda't a <RELATIVE_DIRECTION> per prendre <STREET_NAMES>.", "3": "Queda't a <RELATIVE_DIRECTION> per prendre la sortida <NUMBER_SIGN> a <STREET_NAMES>.", "4": "Queda't a <RELATIVE_DIRECTION> cap a <TOWARD_SIGN>.", "5": "Queda't a <RELATIVE_DIRECTION> per prendre la sortida <NUMBER_SIGN> cap a <TOWARD_SIGN>.", "6": "Queda't a <RELATIVE_DIRECTION> per prendre <STREET_NAMES> a <TOWARD_SIGN>.", "7": "Queda't a <RELATIVE_DIRECTION> per prendre la sortida <NUMBER_SIGN> a <STREET_NAMES> cap a <TOWARD_SIGN>."}, "empty_street_name_labels": ["la vorera", "el carril bici", "la pista de bicicleta de muntanya", "the crosswalk", "the stairs", "the bridge", "the tunnel"], "relative_directions": ["l'esquerra", "el centre", "la dreta"], "example_phrases": {"0": ["Queda't a l'esquerra a la bifuració.", "Queda't al centre a la bifurcació", "Queda't a la dreta a la bifurcació."], "1": ["Queda't a la dreta per prendre la sortida 62."], "2": ["Queda't a la dreta per prendre la I 895 South."], "3": ["Queda't a la dreta per prendre la sortida 62 cap a I 895 South."], "4": ["Queda't a la dreta cap a Annapolis."], "5": ["Queda't a la dreta per prendre la sortida 62 cap a Annapolis."], "6": ["Queda't a la dreta per prendre la I 895 South cap a Annapolis."], "7": ["Queda't a la dreta per prendre la sortida 62 a I 895 South cap a Annapolis."]}}, "keep_to_stay_on": {"phrases": {"0": "Queda't a <RELATIVE_DIRECTION> per continuar per <STREET_NAMES>.", "1": "Queda't a <RELATIVE_DIRECTION> per prendre la sortida <NUMBER_SIGN> per continuar per <STREET_NAMES>.", "2": "Queda't a <RELATIVE_DIRECTION> per continuar per <STREET_NAMES> cap a <TOWARD_SIGN>.", "3": "Queda't a <RELATIVE_DIRECTION> per prendre la sortida <NUMBER_SIGN> per continuar per <STREET_NAMES> cap a <TOWARD_SIGN>."}, "empty_street_name_labels": ["la vorera", "el carril bici", "la pista de bicicleta de muntanya", "the crosswalk", "the stairs", "the bridge", "the tunnel"], "relative_directions": ["l'esquerra", "el centre", "la dreta"], "example_phrases": {"0": ["Queda't a l'esquerra per continuar per I 95 South.", "Queda't al centre per continuar per I 95 South.", "Queda't a la dreta per continuar per I 95 South."], "1": ["Queda't a l'esquerra per prendre la sortida 62 per continuar per I 95 South."], "2": ["Queda't a l'esquerra per continuar per I 95 South cap a Baltimore."], "3": ["Queda't a l'esquerra per prendre la sortida 62 per continuar per I 95 South cap a Baltimore."]}}, "keep_to_stay_on_verbal": {"phrases": {"0": "Queda't a <RELATIVE_DIRECTION> per continuar per <STREET_NAMES>.", "1": "Queda't a <RELATIVE_DIRECTION> per prendre la sortida <NUMBER_SIGN> per continuar per <STREET_NAMES>.", "2": "Queda't a <RELATIVE_DIRECTION> per continuar per <STREET_NAMES> cap a <TOWARD_SIGN>.", "3": "Queda't a <RELATIVE_DIRECTION> per prendre la sortida <NUMBER_SIGN> per continuar per <STREET_NAMES> cap a <TOWARD_SIGN>."}, "empty_street_name_labels": ["la vorera", "el carril bici", "la pista de bicicleta de muntanya", "the crosswalk", "the stairs", "the bridge", "the tunnel"], "relative_directions": ["l'esquerra", "el centre", "la dreta"], "example_phrases": {"0": ["Queda't a l'esquerra per continuar per Interstate 95 South.", "Queda't al centre per continuar per Interstate 95 South.", "Queda't a la dreta per continuar per Interstate 95 South."], "1": ["Queda't a l'esquerra per prendre la sortida 62 per continuar per Interstate 95 South."], "2": ["Queda't a l'esquerra per continuar per Interstate 95 South cap a Baltimore."], "3": ["Queda't a l'esquerra per prendre la sortida 62 per continuar per Interstate 95 South cap a Baltimore."]}}, "keep_verbal": {"phrases": {"0": "Queda't a <RELATIVE_DIRECTION> a la bifurcació.", "1": "Queda't a <RELATIVE_DIRECTION> per prendre la sortida <NUMBER_SIGN>.", "2": "Queda't a <RELATIVE_DIRECTION> per prendre <STREET_NAMES>.", "3": "Queda't a <RELATIVE_DIRECTION> per prendre la sortida <NUMBER_SIGN> a <STREET_NAMES>.", "4": "Queda't a <RELATIVE_DIRECTION> cap a <TOWARD_SIGN>.", "5": "Queda't a <RELATIVE_DIRECTION> per prendre la sortida <NUMBER_SIGN> cap a <TOWARD_SIGN>.", "6": "Queda't a <RELATIVE_DIRECTION> per prendre <STREET_NAMES> a <TOWARD_SIGN>.", "7": "Queda't a <RELATIVE_DIRECTION> per prendre la sortida <NUMBER_SIGN> a <STREET_NAMES> cap a <TOWARD_SIGN>."}, "empty_street_name_labels": ["la vorera", "el carril bici", "la pista de bicicleta de muntanya", "the crosswalk", "the stairs", "the bridge", "the tunnel"], "relative_directions": ["l'esquerra", "el centre", "la dreta"], "example_phrases": {"0": ["Queda't a l'esquerra a la bifuració.", "Queda't al centre a la bifurcació", "Queda't a la dreta a la bifurcació."], "1": ["Queda't a la dreta per prendre la sortida 62."], "2": ["Queda't a la dreta per prendre la Interstate 895 South."], "3": ["Queda't a la dreta per prendre la sortida 62 cap a Interstate 895 South."], "4": ["Queda't a la dreta cap a Annapolis."], "5": ["Queda't a la dreta per prendre la sortida 62 cap a Annapolis."], "6": ["Queda't a la dreta per prendre la Interstate 895 South cap a Annapolis."], "7": ["Queda't a la dreta per prendre la sortida 62 a Interstate 895 South cap a Annapolis."]}}, "merge": {"phrases": {"0": "Enllaça.", "1": "Enllaça a <RELATIVE_DIRECTION>.", "2": "Enllaça amb <STREET_NAMES>.", "3": "Enllaça a <RELATIVE_DIRECTION> amb <STREET_NAMES>.", "4": "Merge toward <TOWARD_SIGN>.", "5": "Merge <RELATIVE_DIRECTION> toward <TOWARD_SIGN>."}, "relative_directions": ["l'esquerra", "la dreta"], "empty_street_name_labels": ["la vorera", "el carril bici", "la pista de bicicleta de muntanya", "the crosswalk", "the stairs", "the bridge", "the tunnel"], "example_phrases": {"0": ["Enllaça."], "1": ["Enllaça a l'esquerra."], "2": ["Enllaça amb I 76 West/Pennsylvania Turnpike."], "3": ["Enllaça a la dreta amb I 83 South."], "4": ["Merge toward Baltimore."], "5": ["Merge right toward Baltimore."]}}, "merge_verbal": {"phrases": {"0": "Enllaça.", "1": "Enllaça a <RELATIVE_DIRECTION>.", "2": "Enllaça amb <STREET_NAMES>.", "3": "Enllaça a <RELATIVE_DIRECTION> amb <STREET_NAMES>.", "4": "Merge toward <TOWARD_SIGN>.", "5": "Merge <RELATIVE_DIRECTION> toward <TOWARD_SIGN>."}, "relative_directions": ["l'esquerra", "la dreta"], "empty_street_name_labels": ["la vorera", "el carril bici", "la pista de bicicleta de muntanya", "the crosswalk", "the stairs", "the bridge", "the tunnel"], "example_phrases": {"0": ["Merge."], "1": ["Enllaça a l'esquerra."], "2": ["Enllaça amb I 76 West/Pennsylvania Turnpike."], "3": ["Enllaça a la dreta amb I 83 South."], "4": ["Merge toward Baltimore."], "5": ["Merge right toward Baltimore."]}}, "post_transition_transit_verbal": {"phrases": {"0": "<PERSON><PERSON><PERSON> <TRANSIT_STOP_COUNT> <TRANSIT_STOP_COUNT_LABEL>."}, "transit_stop_count_labels": {"one": "parada", "other": "parades"}, "example_phrases": {"0": ["Travel 1 stop.", "Travel 3 stops."]}}, "post_transition_verbal": {"phrases": {"0": "Continua durant <LENGTH>.", "1": "Continua per <STREET_NAMES> durant <LENGTH>."}, "empty_street_name_labels": ["la vorera", "el carril bici", "la pista de bicicleta de muntanya", "the crosswalk", "the stairs", "the bridge", "the tunnel"], "metric_lengths": ["<KILOMETERS> quilòmetres", "1 quilòmetre", "<METERS> metres", "menys de 10 metres"], "us_customary_lengths": ["<MILES> milles", "1 milla", "mitja milla", "a quarter mile", "<FEET> peus", "menys de 10 peus"], "example_phrases": {"0": ["Continue for 300 feet.", "Continue for 9 miles."], "1": ["Continue on Pennsylvania 7 43 for 6.2 miles.", "Continue on Main Street, Vermont 30 for 1 tenth of a mile."]}}, "ramp": {"phrases": {"0": "Pren l'accés de <RELATIVE_DIRECTION>.", "1": "Pren l'accés <BRANCH_SIGN> de <RELATIVE_DIRECTION>.", "2": "Pren l'accés de <RELATIVE_DIRECTION> cap a <TOWARD_SIGN>.", "3": "Pren l'accés <BRANCH_SIGN> de <RELATIVE_DIRECTION> cap a <TOWARD_SIGN>.", "4": "Pren l'accés <NAME_SIGN> de <RELATIVE_DIRECTION>.", "5": "Gira cap a <RELATIVE_DIRECTION> per prendre l'accés.", "6": "Gira cap a <RELATIVE_DIRECTION> per prendre l'accés <BRANCH_SIGN>.", "7": "Gira cap a <RELATIVE_DIRECTION> per prendre l'accés cap a <TOWARD_SIGN>.", "8": "Gira cap a <RELATIVE_DIRECTION> per prendre l'accés <BRANCH_SIGN> cap a <TOWARD_SIGN>.", "9": "Gira cap a <RELATIVE_DIRECTION> per prendre l'accés <NAME_SIGN>.", "10": "Pren l'accés.", "11": "Pren l'accés <BRANCH_SIGN>.", "12": "Pren l'accés cap a <TOWARD_SIGN>.", "13": "Pren l'accés <BRANCH_SIGN> cap a <TOWARD_SIGN>.", "14": "Pren l'accés <NAME_SIGN>."}, "relative_directions": ["l'esquerra", "la dreta"], "example_phrases": {"0": ["Pren l'accés de l'esquerra.", "Pren l'accés de la dreta."], "1": ["Pren l'accés I 95 ramp de la dreta."], "2": ["Pren l'accés de l'esquerra cap a JFK."], "3": ["Pren l'accés South Conduit Avenue de l'esquerra cap a JFK."], "4": ["Pren l'accés Gettysburg Pike de la dreta."], "5": ["Gira cap a l'esquerra per prendre l'accés.", "Gira cap a la dreta per prendre l'accés."], "6": ["Gira cap a l'esquerra per prendre l'accés PA 283 West."], "7": ["Gira cap a l'esquerra per prendre l'accés cap a Harrisburg/Harrisburg International Airport."], "8": ["Gira cap a l'esquerra per prendre l'accés PA 283 West cap a Harrisburg/Harrisburg International Airport."], "9": ["Gira cap a la dreta per prendre l'accés Gettysburg Pike."], "10": ["Pren l'accés."], "11": ["Pren l'accés I 95."], "12": ["Pren l'accés cap a JFK."], "13": ["Pren l'accés South Conduit Avenue cap a JFK."], "14": ["Pren l'accés Gettysburg."]}}, "ramp_straight": {"phrases": {"0": "Continua recte per agafar l'accés.", "1": "Continua recte per agafar l'accés <BRANCH_SIGN>.", "2": "Continua recte per agafar l'accés cap a <TOWARD_SIGN>.", "3": "Continua recte per agafar l'accés <BRANCH_SIGN> cap a <TOWARD_SIGN>.", "4": "Continua recte per agafar l'accés <NAME_SIGN>."}, "example_phrases": {"0": ["Continua recte per agafar l'accés."], "1": ["Continua recte per agafar l'accés US 322 East."], "2": ["Continua recte per agafar l'accés cap a Hershey."], "3": ["Continua recte per agafar l'accés US 322 East/US 422 East/US 522 East/US 622 East ramp cap a  Hershey/Palmdale/Palmyra/Campbelltown."], "4": ["Continua recte per agafar l'accés Gettysburg Pike."]}}, "ramp_straight_verbal": {"phrases": {"0": "Continua recte per agafar l'accés.", "1": "Continua recte per agafar l'accés <BRANCH_SIGN>.", "2": "Continua recte per agafar l'accés cap a <TOWARD_SIGN>.", "3": "Continua recte per agafar l'accés <BRANCH_SIGN> cap a <TOWARD_SIGN>.", "4": "Continua recte per agafar l'accés <NAME_SIGN>."}, "example_phrases": {"0": ["Continua recte per agafar l'accés."], "1": ["Continua recte per agafar l'accés U.S. 3 22 East."], "2": ["Continua recte per agafar l'accés cap a Hershey."], "3": ["Continua recte per agafar l'accés U.S. 3 22 East, U.S. 4 22 East cap a Hershey, Palmdale."], "4": ["Continua recte per agafar l'accés Gettysburg Pike."]}}, "ramp_verbal": {"phrases": {"0": "Pren l'accés de <RELATIVE_DIRECTION>.", "1": "Pren l'accés <BRANCH_SIGN> de <RELATIVE_DIRECTION>.", "2": "Pren l'accés de <RELATIVE_DIRECTION> cap a <TOWARD_SIGN>.", "3": "Pren l'accés <BRANCH_SIGN> de <RELATIVE_DIRECTION> cap a <TOWARD_SIGN>.", "4": "Pren l'accés <NAME_SIGN> de <RELATIVE_DIRECTION>.", "5": "Gira cap a <RELATIVE_DIRECTION> per prendre l'accés.", "6": "Gira cap a <RELATIVE_DIRECTION> per prendre l'accés <BRANCH_SIGN>.", "7": "Gira cap a <RELATIVE_DIRECTION> per prendre l'accés cap a <TOWARD_SIGN>.", "8": "Gira cap a <RELATIVE_DIRECTION> per prendre l'accés <BRANCH_SIGN> cap a <TOWARD_SIGN>.", "9": "Gira cap a <RELATIVE_DIRECTION> per prendre l'accés <NAME_SIGN>.", "10": "Pren l'accés.", "11": "Pren l'accés <BRANCH_SIGN>.", "12": "Pren l'accés cap a <TOWARD_SIGN>.", "13": "Pren l'accés <BRANCH_SIGN> cap a <TOWARD_SIGN>.", "14": "Pren l'accés <NAME_SIGN>."}, "relative_directions": ["l'esquerra", "la dreta"], "example_phrases": {"0": ["Pren l'accés de l'esquerra.", "Pren l'accés de la dreta."], "1": ["Pren l'accés I 95 de la dreta."], "2": ["Pren l'accés de l'esquerra cap a JFK."], "3": ["Pren l'accés South Conduit Avenue de l'esquerra cap a JFK."], "4": ["Pren l'accés Gettysburg Pike de la dreta."], "5": ["Gira cap a l'esquerra per prendre l'accés.", "Gira cap a la dreta per prendre l'accés."], "6": ["Gira cap a l'esquerra per prendre l'accés Pennsylvania 283 West."], "7": ["Gira cap a l'esquerra per prendre l'accés cap a Harrisburg/Harrisburg International Airport."], "8": ["Gira cap a l'esquerra per prendre l'accés Pennsylvania 283 West cap a Harrisburg/Harrisburg International Airport."], "9": ["Gira cap a la dreta per prendre l'accés Gettysburg Pike."], "10": ["Pren l'accés."], "11": ["Pren l'accés Interstate 95."], "12": ["Pren l'accés cap a JFK."], "13": ["Pren l'accés South Conduit Avenue cap a JFK."], "14": ["Pren l'accés Gettysburg Pike."]}}, "sharp": {"phrases": {"0": "Gir tancat a <RELATIVE_DIRECTION>.", "1": "Gir tancat a <RELATIVE_DIRECTION> cap a <STREET_NAMES>.", "2": "Gir tancat a <RELATIVE_DIRECTION> cap a <BEGIN_STREET_NAMES>. Continua per <STREET_NAMES>.", "3": "Gir tancat a <RELATIVE_DIRECTION> per continuar per <STREET_NAMES>.", "4": "Make a sharp <RELATIVE_DIRECTION> at <JUNCTION_NAME>.", "5": "Make a sharp <RELATIVE_DIRECTION> toward <TOWARD_SIGN>."}, "empty_street_name_labels": ["la vorera", "el carril bici", "la pista de bicicleta de muntanya", "the crosswalk", "the stairs", "the bridge", "the tunnel"], "relative_directions": ["l'esquerra", "la dreta"], "example_phrases": {"0": ["Gir tancat a l'esquerra."], "1": ["Gir tancat a la dreta cap a Flatbush Avenue."], "2": ["Gir tancat a l'esquerra cap a North Bond Street/US 1 Business/MD 924. Continua per MD 924."], "3": ["Gir tancat a la dreta per continuar per Sunstone Drive."], "4": ["Make a sharp right at Mannenbashi East."], "5": ["Make a sharp left toward Baltimore."]}}, "sharp_verbal": {"phrases": {"0": "Gir tancat a <RELATIVE_DIRECTION>.", "1": "Gir tancat a <RELATIVE_DIRECTION> cap a <STREET_NAMES>.", "2": "Gir tancat a <RELATIVE_DIRECTION> cap a <BEGIN_STREET_NAMES>.", "3": "Gir tancat a <RELATIVE_DIRECTION> per continuar per <STREET_NAMES>.", "4": "Make a sharp <RELATIVE_DIRECTION> at <JUNCTION_NAME>.", "5": "Make a sharp <RELATIVE_DIRECTION> toward <TOWARD_SIGN>."}, "empty_street_name_labels": ["la vorera", "el carril bici", "la pista de bicicleta de muntanya", "the crosswalk", "the stairs", "the bridge", "the tunnel"], "relative_directions": ["l'esquerra", "la dreta"], "example_phrases": {"0": ["Gir tancat a l'esquerra."], "1": ["Gir tancat a la dreta cap a Flatbush Avenue."], "2": ["Gir tancat a l'esquerra cap a North Bond Street/US 1 Business/MD 924."], "3": ["Gir tancat a la dreta per continuar per Sunstone Drive."], "4": ["Make a sharp right at Mannenbashi East."], "5": ["Make a sharp left toward Baltimore."]}}, "start": {"phrases": {"0": "Vés cap <CARDINAL_DIRECTION>.", "1": "Vés cap <CARDINAL_DIRECTION> per <STREET_NAMES>.", "2": "Vés cap <CARDINAL_DIRECTION> per <BEGIN_STREET_NAMES>. Continua per <STREET_NAMES>.", "4": "Condueix cap <CARDINAL_DIRECTION>.", "5": "Condueix cap <CARDINAL_DIRECTION> per <STREET_NAMES>.", "6": "Condueix cap <CARDINAL_DIRECTION> per <BEGIN_STREET_NAMES>. Continua per <STREET_NAMES>.", "8": "Camina cap <CARDINAL_DIRECTION>.", "9": "Camina cap <CARDINAL_DIRECTION> per <STREET_NAMES>.", "10": "Camina cap <CARDINAL_DIRECTION> per <BEGIN_STREET_NAMES>. Continua per <STREET_NAMES>.", "16": "Pedala cap <CARDINAL_DIRECTION>.", "17": "Pedala cap <CARDINAL_DIRECTION> per <STREET_NAMES>.", "18": "Pedala cap <CARDINAL_DIRECTION> per <BEGIN_STREET_NAMES>. Continua per <STREET_NAMES>."}, "cardinal_directions": ["el nord", "el nordest", "l'est", "el sudest", "el sud", "el sudoest", "l'oest", "el nordoest"], "empty_street_name_labels": ["la vorera", "el carril bici", "la pista de bicicleta de muntanya", "the crosswalk", "the stairs", "the bridge", "the tunnel"], "example_phrases": {"0": ["Vés cap a l'est.", "Vés cap al nord."], "1": ["Vés cap al sudoest per 5th Avenue.", "Vés cap a l'oest per la vorera", "Vés cap a l'est pel carril bici", "Vés cap al nort per la pista de bicicleta de muntanya"], "2": ["Vés cap al sud per North Prince Street/US 222/PA 272. Continua per US 222/PA 272."], "4": ["Condueix cap a l'est.", "Condueix cap al nord."], "5": ["Condueix cap al sudoest per 5th Avenue."], "6": ["Condueix cap al sud per North Prince Street/US 222/PA 272. Continua per US 222/PA 272."], "8": ["Camina cap a l'est.", "Camina cap al nord."], "9": ["Camina cap al sudoest per 5th Avenue.", "Camina cap a l'est per la vorera"], "10": ["Camina cap al sud per North Prince Street/US 222/PA 272. Continua per US 222/PA 272."], "16": ["Pedala cap a l'est.", "Pedala cap al nord."], "17": ["Pedala cap al sudoestper 5th Avenue.", "Pedala cap a l'est pel carril bici", "Pedala cap al nord per la pista de bicicleta de muntanya"], "18": ["Pedala cap al sud per North Prince Street/US 222/PA 272. Continua per US 222/PA 272."]}}, "start_verbal": {"phrases": {"0": "Vés cap <CARDINAL_DIRECTION>.", "1": "Vés cap <CARDINAL_DIRECTION> durant <LENGTH>.", "2": "Vés cap <CARDINAL_DIRECTION> a <STREET_NAMES>.", "3": "Vés cap <CARDINAL_DIRECTION> a <STREET_NAMES> durant <LENGTH>.", "4": "Vés cap <CARDINAL_DIRECTION> a <BEGIN_STREET_NAMES>.", "5": "Condueix cap <CARDINAL_DIRECTION>.", "6": "Condueix cap <CARDINAL_DIRECTION> durant <LENGTH>.", "7": "Condueix cap <CARDINAL_DIRECTION> a <STREET_NAMES>.", "8": "Condueix cap <CARDINAL_DIRECTION> a <STREET_NAMES> durant <LENGTH>.", "9": "Condueix cap <CARDINAL_DIRECTION> a <BEGIN_STREET_NAMES>.", "10": "Camina cap <CARDINAL_DIRECTION>.", "11": "Camina cap <CARDINAL_DIRECTION> durant <LENGTH>.", "12": "Camina cap <CARDINAL_DIRECTION> a <STREET_NAMES>.", "13": "Camina cap <CARDINAL_DIRECTION> a <STREET_NAMES> durant <LENGTH>.", "14": "Camina cap <CARDINAL_DIRECTION> a <BEGIN_STREET_NAMES>.", "15": "Pedala cap <CARDINAL_DIRECTION>.", "16": "Pedala cap <CARDINAL_DIRECTION> durant <LENGTH>.", "17": "Pedala cap <CARDINAL_DIRECTION> a <STREET_NAMES>.", "18": "Pedala cap <CARDINAL_DIRECTION> a <STREET_NAMES> durant <LENGTH>.", "19": "Pedala cap <CARDINAL_DIRECTION> a <BEGIN_STREET_NAMES>."}, "cardinal_directions": ["el nord", "el nordest", "l'est", "el sudest", "el sud", "el sudoest", "l'oest", "el nordoest"], "empty_street_name_labels": ["la vorera", "el carril bici", "la pista de bicicleta de muntanya", "the crosswalk", "the stairs", "the bridge", "the tunnel"], "metric_lengths": ["<KILOMETERS> quilòmetres", "1 quilòmetre", "<METERS> metres", "menys de 10 metres"], "us_customary_lengths": ["<MILES> milles", "1 milla", "mitja milla", "a quarter mile", "<FEET> peus", "menys de 10 peus"], "example_phrases": {"0": ["Vés cap l'est.", "Vés cap el nord."], "1": ["Vés cap l'est for a half mile.", "Head north for 1 kilometer."], "2": ["Head southwest on 5th Avenue."], "3": ["Head southwest on 5th Avenue for 1 tenth of a mile."], "4": ["Head south on North Prince Street, U.S. 2 22."], "5": ["Condueix cap l'est.", "Condueix cap el nord."], "6": ["Condueix cap l'est durant a half mile.", "Drive north for 1 kilometer."], "7": ["Drive southwest on 5th Avenue."], "8": ["Drive southwest on 5th Avenue for 1 tenth of a mile."], "9": ["Drive south on North Prince Street, U.S. 2 22."], "10": ["Camina cap l'est.", "Camina cap el nord."], "11": ["Walk east for a half mile.", "Walk north for 1 kilometer."], "12": ["Walk southwest on 5th Avenue."], "13": ["Walk southwest on 5th Avenue for 1 tenth of a mile."], "14": ["Walk south on North Prince Street, U.S. 2 22."], "15": ["Bike east.", "Bike north."], "16": ["Pedala cap l'est durant a half mile.", "Bike north for 1 kilometer."], "17": ["Bike southwest on 5th Avenue."], "18": ["Bike southwest on 5th Avenue for 1 tenth of a mile."], "19": ["Bike south on North Prince Street, U.S. 2 22."]}}, "transit": {"phrases": {"0": "Agafa el <TRANSIT_NAME>. (<TRANSIT_STOP_COUNT> <TRANSIT_STOP_COUNT_LABEL>)", "1": "Agafa el <TRANSIT_NAME> cap a <TRANSIT_HEADSIGN>. (<TRANSIT_STOP_COUNT> <TRANSIT_STOP_COUNT_LABEL>)"}, "empty_transit_name_labels": ["tramvia", "metro", "tren", "bus", "ferri", "telefèric", "gòndola", "funicular"], "transit_stop_count_labels": {"one": "parada", "other": "parades"}, "example_phrases": {"0": ["Agafa el New Haven. (1 parada)", "Agafa el metro. (2 parades)", "Agafa el bus. (12 parades)"], "1": ["Agafa el F cap a JAMAICA - 179 ST. (10 parades)", "Agafa el ferri cap a Staten Island. (1 parada)"]}}, "transit_connection_destination": {"phrases": {"0": "Surt de l'estació.", "1": "Surt de <TRANSIT_STOP>.", "2": "Surt de <TRANSIT_STOP> <STATION_LABEL>."}, "station_label": "Station", "example_phrases": {"0": ["Surt de l'estació."], "1": ["Surt de Embarcadero Station."], "2": ["Surt de 8 St - NYU Station."]}}, "transit_connection_destination_verbal": {"phrases": {"0": "Surt de l'estació.", "1": "Surt de <TRANSIT_STOP>.", "2": "Surt de <TRANSIT_STOP> <STATION_LABEL>."}, "station_label": "Station", "example_phrases": {"0": ["Surt de l'estació."], "1": ["Surt de Embarcadero Station."], "2": ["Surt de 8 St - NYU Station."]}}, "transit_connection_start": {"phrases": {"0": "Entra a l'estació.", "1": "Entra a <TRANSIT_STOP>.", "2": "Entra a <TRANSIT_STOP> <STATION_LABEL>."}, "station_label": "Station", "example_phrases": {"0": ["Entra a l'estació."], "1": ["Entra a Embarcadero Station."], "2": ["Entra a 8 St - NYU Station."]}}, "transit_connection_start_verbal": {"phrases": {"0": "Entra a l'estació.", "1": "Entra a <TRANSIT_STOP>.", "2": "Entra a <TRANSIT_STOP> <STATION_LABEL>."}, "station_label": "Station", "example_phrases": {"0": ["Entra a l'estació."], "1": ["Entra a Embarcadero Station."], "2": ["Entra a 8 St - NYU Station."]}}, "transit_connection_transfer": {"phrases": {"0": "Transbordament a l'estació.", "1": "Transbordament a <TRANSIT_STOP>.", "2": "Transbordament a <TRANSIT_STOP> <STATION_LABEL>."}, "station_label": "Station", "example_phrases": {"0": ["Transbordament a l'estació."], "1": ["Transbordament a Embarcadero Station."], "2": ["Transbordament a 8 St - NYU Station."]}}, "transit_connection_transfer_verbal": {"phrases": {"0": "Transbordament a l'estació.", "1": "Transbordament a <TRANSIT_STOP>.", "2": "Transbordament a <TRANSIT_STOP> <STATION_LABEL>."}, "station_label": "Station", "example_phrases": {"0": ["Transbordament a l'estació."], "1": ["Transbordament a Embarcadero Station."], "2": ["Transbordament a 8 St - NYU Station."]}}, "transit_remain_on": {"phrases": {"0": "Queda't al <TRANSIT_NAME>. (<TRANSIT_STOP_COUNT> <TRANSIT_STOP_COUNT_LABEL>)", "1": "Queda't al <TRANSIT_NAME> cap a <TRANSIT_HEADSIGN>. (<TRANSIT_STOP_COUNT> <TRANSIT_STOP_COUNT_LABEL>)"}, "empty_transit_name_labels": ["tramvia", "metro", "tren", "bus", "ferri", "telefèric", "gòndola", "funicular"], "transit_stop_count_labels": {"one": "parada", "other": "parades"}, "example_phrases": {"0": ["Queda't al New Haven. (1 parada)", "Queda't al tren. (3 parades)"], "1": ["Queda't al F toward JAMAICA - 179 ST. (10 parades)"]}}, "transit_remain_on_verbal": {"phrases": {"0": "Queda't al <TRANSIT_NAME>.", "1": "Queda't <TRANSIT_NAME> cap a <TRANSIT_HEADSIGN>."}, "empty_transit_name_labels": ["tramvia", "metro", "tren", "bus", "ferri", "telefèric", "gòndola", "funicular"], "example_phrases": {"0": ["Queda't al New Haven."], "1": ["Queda't al F toward JAMAICA - 179 ST."]}}, "transit_transfer": {"phrases": {"0": "Transbordament per agafar el <TRANSIT_NAME>. (<TRANSIT_STOP_COUNT> <TRANSIT_STOP_COUNT_LABEL>)", "1": "Transbordament per agafar el <TRANSIT_NAME> cap a <TRANSIT_HEADSIGN>. (<TRANSIT_STOP_COUNT> <TRANSIT_STOP_COUNT_LABEL>)"}, "empty_transit_name_labels": ["tramvia", "metro", "tren", "bus", "ferri", "telefèric", "gòndola", "funicular"], "transit_stop_count_labels": {"one": "parada", "other": "parades"}, "example_phrases": {"0": ["Transbordament per agafar el New Haven. (1 parada)", "Transbordament per agafar el tramvia. (4 parades)"], "1": ["Transbordament per agafar el F cap a JAMAICA - 179 ST. (10 parades)"]}}, "transit_transfer_verbal": {"phrases": {"0": "Transbordament per agafar el <TRANSIT_NAME>.", "1": "Transbordament per agafar el <TRANSIT_NAME> toward <TRANSIT_HEADSIGN>."}, "empty_transit_name_labels": ["tramvia", "metro", "tren", "bus", "ferri", "telefèric", "gòndola", "funicular"], "example_phrases": {"0": ["Transbordament per agafar el New Haven."], "1": ["Transbordament per agafar el F cap a JAMAICA - 179 ST."]}}, "transit_verbal": {"phrases": {"0": "Agafa el <TRANSIT_NAME>.", "1": "Agafa el <TRANSIT_NAME> cap a <TRANSIT_HEADSIGN>."}, "empty_transit_name_labels": ["tramvia", "metro", "tren", "bus", "ferri", "telefèric", "gòndola", "funicular"], "example_phrases": {"0": ["Agafa el New Haven."], "1": ["Agafa el F toward JAMAICA - 179 ST."]}}, "turn": {"phrases": {"0": "Gira a <RELATIVE_DIRECTION>.", "1": "Gira a <RELATIVE_DIRECTION> cap a <STREET_NAMES>.", "2": "Gira a <RELATIVE_DIRECTION> cap a <BEGIN_STREET_NAMES>. Continua per <STREET_NAMES>.", "3": "Gira a <RELATIVE_DIRECTION> per continuar per <STREET_NAMES>.", "4": "Turn <RELATIVE_DIRECTION> at <JUNCTION_NAME>.", "5": "Turn <RELATIVE_DIRECTION> toward <TOWARD_SIGN>."}, "empty_street_name_labels": ["la vorera", "el carril bici", "la pista de bicicleta de muntanya", "the crosswalk", "the stairs", "the bridge", "the tunnel"], "relative_directions": ["l'esquerra", "la dreta"], "example_phrases": {"0": ["Gira a l'esquerra."], "1": ["Gira a la dreta cap a Flatbush Avenue."], "2": ["gira a l'esquerra cap a North Bond Street/US 1 Business/MD 924. Continua per MD 924."], "3": ["Gira a la dreta per continuar per Sunstone Drive."], "4": ["Turn right at Mannenbashi East."], "5": ["Turn left toward Baltimore."]}}, "turn_verbal": {"phrases": {"0": "Gira a <RELATIVE_DIRECTION>.", "1": "Gira a <RELATIVE_DIRECTION> cap a <STREET_NAMES>.", "2": "Gira a <RELATIVE_DIRECTION> cap a <BEGIN_STREET_NAMES>.", "3": "Gira a <RELATIVE_DIRECTION> per continuar per <STREET_NAMES>.", "4": "Turn <RELATIVE_DIRECTION> at <JUNCTION_NAME>.", "5": "Turn <RELATIVE_DIRECTION> toward <TOWARD_SIGN>."}, "empty_street_name_labels": ["la vorera", "el carril bici", "la pista de bicicleta de muntanya", "the crosswalk", "the stairs", "the bridge", "the tunnel"], "relative_directions": ["l'esquerra", "la dreta"], "example_phrases": {"0": ["Turn left."], "1": ["Gira a la dreta cap a Flatbush Avenue."], "2": ["gira a l'esquerra cap a North Bond Street/US 1 Business/MD 924. Continua per MD 924."], "3": ["Gira a la dreta per continuar per Sunstone Drive."], "4": ["Turn right at Mannenbashi East."], "5": ["Turn left toward Baltimore."]}}, "uturn": {"phrases": {"0": "Mitja volta a <RELATIVE_DIRECTION>.", "1": "Mitja volta a <RELATIVE_DIRECTION> cap a <STREET_NAMES>.", "2": "Mitja volta a <RELATIVE_DIRECTION> per continuar per <STREET_NAMES>.", "3": "Mitja volta a <RELATIVE_DIRECTION> a <CROSS_STREET_NAMES>.", "4": "Mitja volta a <RELATIVE_DIRECTION> a <CROSS_STREET_NAMES> cap a <STREET_NAMES>.", "5": "Mitja volta a <RELATIVE_DIRECTION> a <CROSS_STREET_NAMES> per continuar per <STREET_NAMES>.", "6": "Make a <RELATIVE_DIRECTION> U-turn at <JUNCTION_NAME>.", "7": "Make a <RELATIVE_DIRECTION> U-turn toward <TOWARD_SIGN>."}, "empty_street_name_labels": ["la vorera", "el carril bici", "la pista de bicicleta de muntanya", "the crosswalk", "the stairs", "the bridge", "the tunnel"], "relative_directions": ["l'esquerra", "la dreta"], "example_phrases": {"0": ["Mitja volta a l'esquerra."], "1": ["Mitja volta a la dreta cap a Bunker Hill Road."], "2": ["Mitja volta a l'esquerra per continuar per Bunker Hill Road."], "3": ["Mitja volta a l'esquerra a Devonshire Road."], "4": ["Mitja volta a l'esquerra a Devonshire Road cap a Jonestown Road/US 22."], "5": ["Mitja volta a l'esquerra a Devonshire Road per continuar per Jonestown Road/US 22."], "6": ["Make a right U-turn at Mannenbashi East."], "7": ["Make a left U-turn toward Baltimore."]}}, "uturn_verbal": {"phrases": {"0": "Mitja volta a <RELATIVE_DIRECTION>.", "1": "Mitja volta a <RELATIVE_DIRECTION> cap a <STREET_NAMES>.", "2": "Mitja volta a <RELATIVE_DIRECTION> per continuar per <STREET_NAMES>.", "3": "Mitja volta a <RELATIVE_DIRECTION> a <CROSS_STREET_NAMES>.", "4": "Mitja volta a <RELATIVE_DIRECTION> a <CROSS_STREET_NAMES> cap a <STREET_NAMES>.", "5": "Mitja volta a <RELATIVE_DIRECTION> a <CROSS_STREET_NAMES> per continuar per <STREET_NAMES>.", "6": "Make a <RELATIVE_DIRECTION> U-turn at <JUNCTION_NAME>.", "7": "Make a <RELATIVE_DIRECTION> U-turn toward <TOWARD_SIGN>."}, "empty_street_name_labels": ["la vorera", "el carril bici", "la pista de bicicleta de muntanya", "the crosswalk", "the stairs", "the bridge", "the tunnel"], "relative_directions": ["l'esquerra", "la dreta"], "example_phrases": {"0": ["Mitja volta a l'esquerra."], "1": ["Mitja volta a la dreta cap a Bunker Hill Road."], "2": ["Mitja volta a l'esquerra per continuar per Bunker Hill Road."], "3": ["Mitja volta a l'esquerra a Devonshire Road."], "4": ["Mitja volta a l'esquerra a Devonshire Road cap a Jonestown Road/US 22."], "5": ["Mitja volta a l'esquerra a Devonshire Road per continuar per Jonestown Road/US 22."], "6": ["Make a right U-turn at Mannenbashi East."], "7": ["Make a left U-turn toward Baltimore."]}}, "verbal_multi_cue": {"phrases": {"0": "<CURRENT_VERBAL_CUE> i després <NEXT_VERBAL_CUE>", "1": "<CURRENT_VERBAL_CUE> Then, in <LENGTH>, <NEXT_VERBAL_CUE>"}, "metric_lengths": ["<KILOMETERS> quilòmetres", "1 quilòmetre", "<METERS> metres", "menys de 10 metres"], "us_customary_lengths": ["<MILES> milles", "1 milla", "mitja milla", "a quarter mile", "<FEET> peus", "menys de 10 peus"], "example_phrases": {"0": ["Queda't a la dreta cap a East Fayette Street. I després gira a la dreta cap a North Gay Street."], "1": ["Bear right onto East Fayette Street. Then, in 500 feet, Turn right onto North Gay Street."]}}, "approach_verbal_alert": {"phrases": {"0": "In <LENGTH>, <CURRENT_VERBAL_CUE>"}, "metric_lengths": ["<KILOMETERS> quilòmetres", "1 quilòmetre", "<METERS> metres", "menys de 10 metres"], "us_customary_lengths": ["<MILES> milles", "1 milla", "mitja milla", "a quarter mile", "<FEET> peus", "menys de 10 peus"], "example_phrases": {"0": ["In a quarter mile, Turn right onto North Gay Street."]}}, "pass": {"phrases": {"0": "Pass <OBJECT_LABEL>.", "1": "Pass traffic signals on <OBJECT_LABEL>."}, "object_labels": ["the gate", "the bollards", "ways intersection"], "example_phrases": {"0": ["Pass the gate."]}}, "elevator": {"phrases": {"0": "Take the elevator.", "1": "Take the elevator to <LEVEL>."}, "example_phrases": {"0": ["Take the elevator."], "1": ["Take the elevator to Level 1."]}}, "steps": {"phrases": {"0": "Take the stairs.", "1": "Take the stairs to <LEVEL>."}, "example_phrases": {"0": ["Take the stairs."], "1": ["Take the stairs to Level 2."]}}, "escalator": {"phrases": {"0": "Take the escalator.", "1": "Take the escalator to <LEVEL>."}, "example_phrases": {"0": ["Take the escalator."], "1": ["Take the escalator to Level 3."]}}, "enter_building": {"phrases": {"0": "Enter the building.", "1": "Enter the building, and continue on <STREET_NAMES>."}, "empty_street_name_labels": ["la vorera", "el carril bici", "la pista de bicicleta de muntanya", "the crosswalk"], "example_phrases": {"0": ["Enter the building."], "1": ["Enter the building, and continue on the walkway."]}}, "exit_building": {"phrases": {"0": "Exit the building.", "1": "Exit the building, and continue on <STREET_NAMES>."}, "empty_street_name_labels": ["la vorera", "el carril bici", "la pista de bicicleta de muntanya", "the crosswalk"], "example_phrases": {"0": ["Exit the building."], "1": ["Exit the building, and continue on the walkway."]}}}}
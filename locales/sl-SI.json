{"posix_locale": "sl_SI.UTF-8", "aliases": ["sl"], "instructions": {"arrive": {"phrases": {"0": "Prihod: <TIME>.", "1": "Prihod: <TIME> na <TRANSIT_STOP>."}, "example_phrases": {"0": ["Prihod: 8:02 AM."], "1": ["Prihod: 8:02 AM na 8 St - NYU."]}}, "arrive_verbal": {"phrases": {"0": "Prihod ob <TIME>.", "1": "Prihod ob <TIME> na <TRANSIT_STOP>."}, "example_phrases": {"0": ["Prihod ob 8:02 AM."], "1": ["Prihod ob 8:02 AM na 8 St - NYU."]}}, "bear": {"phrases": {"0": "Držite se <RELATIVE_DIRECTION>.", "1": "<PERSON><PERSON><PERSON> se <RELATIVE_DIRECTION>, da pridete na <STREET_NAMES>.", "2": "<PERSON><PERSON><PERSON> se <RELATIVE_DIRECTION>, da pridete na <BEGIN_STREET_NAMES>. Nadal<PERSON>j<PERSON> po <STREET_NAMES>.", "3": "<PERSON><PERSON><PERSON> se <RELATIVE_DIRECTION>, da ostanete na <STREET_NAMES>.", "4": "Bear <RELATIVE_DIRECTION> at <JUNCTION_NAME>.", "5": "Bear <RELATIVE_DIRECTION> toward <TOWARD_SIGN>."}, "empty_street_name_labels": ["p<PERSON><PERSON><PERSON>", "kolesarsko stezo", "stezo za gorsko kolo", "the crosswalk", "the stairs", "the bridge", "the tunnel"], "relative_directions": ["levo", "<PERSON>no"], "example_phrases": {"0": ["<PERSON><PERSON><PERSON> se desno."], "1": ["<PERSON><PERSON><PERSON> se levo, da pridete na Arlen Road."], "2": ["<PERSON><PERSON><PERSON> se <PERSON>no, da pridete na Belair Road/US 1 Business. Nadaljujte po US 1 Business."], "3": ["<PERSON><PERSON><PERSON> se levo, da ostanete na US 15 South."], "4": ["Bear right at Mannenbashi East."], "5": ["Bear left toward Baltimore."]}}, "bear_verbal": {"phrases": {"0": "Držite se <RELATIVE_DIRECTION>.", "1": "<PERSON><PERSON><PERSON> se <RELATIVE_DIRECTION>, da pridete na <STREET_NAMES>.", "2": "<PERSON><PERSON><PERSON> se <RELATIVE_DIRECTION>, da pridete na <BEGIN_STREET_NAMES>.", "3": "<PERSON><PERSON><PERSON> se <RELATIVE_DIRECTION>, da ostanete na <STREET_NAMES>.", "4": "Bear <RELATIVE_DIRECTION> at <JUNCTION_NAME>.", "5": "Bear <RELATIVE_DIRECTION> toward <TOWARD_SIGN>."}, "empty_street_name_labels": ["p<PERSON><PERSON><PERSON>", "kolesarsko stezo", "stezo za gorsko kolo", "the crosswalk", "the stairs", "the bridge", "the tunnel"], "relative_directions": ["levo", "<PERSON>no"], "example_phrases": {"0": ["<PERSON><PERSON><PERSON> se desno."], "1": ["<PERSON><PERSON><PERSON> se levo, da pridete na Arlen Road."], "2": ["<PERSON><PERSON><PERSON> <PERSON>, da pridete na Belair Road, U.S. 1 Business."], "3": ["<PERSON><PERSON><PERSON> se levo, da ostanete na U.S. 15 South."], "4": ["Bear right at Mannenbashi East."], "5": ["Bear left toward Baltimore."]}}, "becomes": {"phrases": {"0": "<PREVIOUS_STREET_NAMES> se spremeni v <STREET_NAMES>."}, "example_phrases": {"0": ["Vine Street se spremeni v Middletown Road."]}}, "becomes_verbal": {"phrases": {"0": "<PREVIOUS_STREET_NAMES> se spremeni v <STREET_NAMES>."}, "example_phrases": {"0": ["Vine Street se spremeni v Middletown Road."]}}, "continue": {"phrases": {"0": "Nadaljuj<PERSON>.", "1": "Nadaljujte po <STREET_NAMES>.", "2": "Continue at <JUNCTION_NAME>.", "3": "Continue toward <TOWARD_SIGN>."}, "empty_street_name_labels": ["p<PERSON><PERSON><PERSON><PERSON>", "kolesarski stezi", "stezi za gorsko kolo", "the crosswalk", "the stairs", "the bridge", "the tunnel"], "example_phrases": {"0": ["Nadaljuj<PERSON>."], "1": ["Nadaljujte po 10th Avenue."], "2": ["Continue at Mannenbashi East."], "3": ["Continue toward Baltimore."]}}, "continue_verbal": {"phrases": {"0": "Nadaljuj<PERSON>.", "1": "Nadaljujte <LENGTH>.", "2": "Nadaljujte po <STREET_NAMES>.", "3": "Po <STREET_NAMES> nadaljujte <LENGTH>.", "4": "Continue at <JUNCTION_NAME>.", "5": "Continue at <JUNCTION_NAME> for <LENGTH>.", "6": "Continue toward <TOWARD_SIGN>.", "7": "Continue toward <TOWARD_SIGN> for <LENGTH>."}, "empty_street_name_labels": ["p<PERSON><PERSON><PERSON><PERSON>", "kolesarski stezi", "stezi za gorsko kolo", "the crosswalk", "the stairs", "the bridge", "the tunnel"], "metric_lengths": ["<KILOMETERS> kilometrov", "1 kilometer", "<METERS> metrov", "manj kot 10 metrov"], "us_customary_lengths": ["<MILES> milj", "1 miljo", "pol milje", "a quarter mile", "<FEET> čevljev", "Manj kot 10 čevljev"], "example_phrases": {"0": ["Continue."], "1": ["Continue for 300 feet."], "2": ["Continue on 10th Avenue."], "3": ["Continue on 10th Avenue for 3 miles."], "4": ["Continue at Mannenbashi East."], "5": ["Continue at Mannenbashi East for 2 miles."], "6": ["Continue toward Baltimore."], "7": ["Continue toward Baltimore for 5 miles."]}}, "continue_verbal_alert": {"phrases": {"0": "Nadaljuj<PERSON>.", "1": "Nadaljujte po <STREET_NAMES>.", "2": "Continue at <JUNCTION_NAME>.", "3": "Continue toward <TOWARD_SIGN>."}, "empty_street_name_labels": ["p<PERSON><PERSON><PERSON><PERSON>", "kolesarski stezi", "stezi za gorsko kolo", "the crosswalk", "the stairs", "the bridge", "the tunnel"], "example_phrases": {"0": ["Nadaljuj<PERSON>."], "1": ["Nadaljujte po 10th Avenue."], "2": ["Continue at Mannenbashi East."], "3": ["Continue toward Baltimore."]}}, "depart": {"phrases": {"0": "Odhod: <TIME>.", "1": "Odhod: <TIME> s/z <TRANSIT_STOP>."}, "example_phrases": {"0": ["Odhod: 8:02 AM."], "1": ["Odhod: 8:02 AM s/z 8 St - NYU."]}}, "depart_verbal": {"phrases": {"0": "<PERSON><PERSON><PERSON> ob <TIME>.", "1": "Odhod ob <TIME> s/z <TRANSIT_STOP>."}, "example_phrases": {"0": ["Odhod ob 8:02 AM."], "1": ["Odhod ob 8:02 AM s/z 8 St - NYU."]}}, "destination": {"phrases": {"0": "Prispeli ste na cilj.", "1": "Prispeli ste na <DESTINATION>.", "2": "<PERSON><PERSON><PERSON> cilj je na <RELATIVE_DIRECTION>.", "3": "<DESTINATION> je na <RELATIVE_DIRECTION>."}, "relative_directions": ["levi", "<PERSON><PERSON>"], "example_phrases": {"0": ["Prispeli ste na cilj."], "1": ["Prispeli ste na 3206 Powelton Avenue."], "2": ["<PERSON><PERSON><PERSON> cilj je na levi.", "<PERSON><PERSON><PERSON> cilj je na desni."], "3": ["Lancaster Brewing Company je na levi."]}}, "destination_verbal": {"phrases": {"0": "Prispeli ste na cilj.", "1": "Prispeli ste na <DESTINATION>.", "2": "<PERSON><PERSON><PERSON> cilj je na <RELATIVE_DIRECTION>.", "3": "<DESTINATION> je na <RELATIVE_DIRECTION>."}, "relative_directions": ["levi", "<PERSON><PERSON>"], "example_phrases": {"0": ["Prispeli ste na cilj."], "1": ["Prispeli ste na 32 o6 Powelton Avenue."], "2": ["<PERSON><PERSON><PERSON> cilj je na levi.", "<PERSON><PERSON><PERSON> cilj je na desni."], "3": ["Lancaster Brewing Company je na levi."]}}, "destination_verbal_alert": {"phrases": {"0": "Prispeli boste na cilj.", "1": "Prispeli boste na <DESTINATION>.", "2": "<PERSON><PERSON><PERSON> cilj bo na <RELATIVE_DIRECTION>.", "3": "<DESTINATION> bo na <RELATIVE_DIRECTION>."}, "relative_directions": ["levi", "<PERSON><PERSON>"], "example_phrases": {"0": ["Prispeli boste na cilj."], "1": ["Prispeli boste na 32 o6 Powelton Avenue."], "2": ["<PERSON><PERSON><PERSON> cilj bo na levi.", "<PERSON><PERSON><PERSON> cilj bo na desni."], "3": ["Lancaster Brewing Company bo na levi."]}}, "enter_ferry": {"phrases": {"0": "Pojdite na trajekt.", "1": "Pojdite na <STREET_NAMES>.", "2": "Pojdite na <FERRY_LABEL> <STREET_NAMES>.", "3": "Take the ferry toward <TOWARD_SIGN>."}, "empty_street_name_labels": ["p<PERSON><PERSON><PERSON>", "kolesarsko stezo", "stezo za gorsko kolo", "the crosswalk", "the stairs", "the bridge", "the tunnel"], "ferry_label": "Trajekt", "example_phrases": {"0": ["Pojdite na trajekt."], "1": ["Pojdite na Millersburg Ferry."], "2": ["Pojdite na Bridgeport - Port Jefferson Ferry."], "3": ["Take the ferry toward Cape May."]}}, "enter_ferry_verbal": {"phrases": {"0": "Pojdite na trajekt.", "1": "Pojdite na <STREET_NAMES>.", "2": "Pojdite na <FERRY_LABEL> <STREET_NAMES>.", "3": "Take the ferry toward <TOWARD_SIGN>."}, "empty_street_name_labels": ["p<PERSON><PERSON><PERSON>", "kolesarsko stezo", "stezo za gorsko kolo", "the crosswalk", "the stairs", "the bridge", "the tunnel"], "ferry_label": "Trajekt", "example_phrases": {"0": ["Pojdite na trajekt."], "1": ["Pojdite na Millersburg Ferry."], "2": ["Pojdite na Bridgeport - Port Jefferson Ferry."], "3": ["Take the ferry toward Cape May."]}}, "enter_roundabout": {"phrases": {"0": "Pojdite v krožišče.", "1": "Pojdite v krožišče in uporabite <ORDINAL_VALUE>. izhod.", "2": "Enter the roundabout and take the <ORDINAL_VALUE> exit onto <ROUNDABOUT_EXIT_STREET_NAMES>.", "3": "Enter the roundabout and take the <ORDINAL_VALUE> exit onto <ROUNDABOUT_EXIT_BEGIN_STREET_NAMES>. Continue on <ROUNDABOUT_EXIT_STREET_NAMES>.", "4": "Enter the roundabout and take the <ORDINAL_VALUE> exit toward <TOWARD_SIGN>.", "5": "Enter the roundabout and take the exit onto <ROUNDABOUT_EXIT_STREET_NAMES>.", "6": "Enter the roundabout and take the exit onto <ROUNDABOUT_EXIT_BEGIN_STREET_NAMES>. Continue on <ROUNDABOUT_EXIT_STREET_NAMES>.", "7": "Enter the roundabout and take the exit toward <TOWARD_SIGN>.", "8": "Enter <STREET_NAMES>", "9": "Enter <STREET_NAMES> and take the <ORDINAL_VALUE> exit.", "10": "Enter <STREET_NAMES> and take the <ORDINAL_VALUE> exit onto <ROUNDABOUT_EXIT_STREET_NAMES>.", "11": "Enter <STREET_NAMES> and take the <ORDINAL_VALUE> exit onto <ROUNDABOUT_EXIT_BEGIN_STREET_NAMES>. Continue on <ROUNDABOUT_EXIT_STREET_NAMES>.", "12": "Enter <STREET_NAMES> and take the <ORDINAL_VALUE> exit toward <TOWARD_SIGN>.", "13": "Enter <STREET_NAMES> and take the exit onto <ROUNDABOUT_EXIT_STREET_NAMES>.", "14": "Enter <STREET_NAMES> and take the exit onto <ROUNDABOUT_EXIT_BEGIN_STREET_NAMES>. Continue on <ROUNDABOUT_EXIT_STREET_NAMES>.", "15": "Enter <STREET_NAMES> and take the exit toward <TOWARD_SIGN>."}, "ordinal_values": ["1", "2", "3", "4", "5", "6", "7", "8", "9", "10"], "empty_street_name_labels": ["the walkway", "the cycleway", "the mountain bike trail", "the crosswalk", "the stairs", "the bridge", "the tunnel"], "example_phrases": {"0": ["Pojdite v krožišče."], "1": ["Pojdite v krožišče in uporabite 1. izhod.", "Pojdite v krožišče in uporabite 2. izhod.", "Pojdite v krožišče in uporabite 3. izhod.", "Pojdite v krožišče in uporabite 4. izhod.", "Pojdite v krožišče in uporabite 5. izhod.", "Pojdite v krožišče in uporabite 6. izhod.", "Pojdite v krožišče in uporabite 7. izhod.", "Pojdite v krožišče in uporabite 8. izhod.", "Pojdite v krožišče in uporabite 9. izhod.", "Pojdite v krožišče in uporabite 10. izhod."], "2": ["Enter the roundabout and take the 3rd exit onto Main Street."], "3": ["Enter the roundabout and take the 3rd exit onto US 322/Main Street. Continue on US 322."], "4": ["Enter the roundabout and take the 3rd exit toward Baltimore."], "5": ["Enter the roundabout and take the exit onto Main Street."], "6": ["Enter the roundabout and take the exit onto US 322/Main Street. Continue on US 322."], "7": ["Enter the roundabout and take the exit toward Baltimore."], "8": ["Enter Dupont Circle."], "9": ["Enter Dupont Circle and take the 1st exit."], "10": ["Enter Dupont Circle and take the 3rd exit onto Main Street."], "11": ["Enter Dupont Circle and take the 3rd exit onto US 322/Main Street. Continue on US 322."], "12": ["Enter Dupont Circle and take the 3rd exit toward Baltimore."], "13": ["Enter Dupont Circle and take the exit onto Main Street."], "14": ["Enter Dupont Circle and take the exit onto US 322/Main Street. Continue on US 322."], "15": ["Enter Dupont Circle and take the exit toward Baltimore."]}}, "enter_roundabout_verbal": {"phrases": {"0": "Pojdite v krožišče.", "1": "Pojdite v krožišče in uporabite <ORDINAL_VALUE>. izhod.", "2": "Enter the roundabout and take the <ORDINAL_VALUE> exit onto <ROUNDABOUT_EXIT_STREET_NAMES>.", "3": "Enter the roundabout and take the <ORDINAL_VALUE> exit onto <ROUNDABOUT_EXIT_BEGIN_STREET_NAMES>.", "4": "Enter the roundabout and take the <ORDINAL_VALUE> exit toward <TOWARD_SIGN>.", "5": "Enter the roundabout and take the exit onto <ROUNDABOUT_EXIT_STREET_NAMES>.", "6": "Enter the roundabout and take the exit onto <ROUNDABOUT_EXIT_BEGIN_STREET_NAMES>.", "7": "Enter the roundabout and take the exit toward <TOWARD_SIGN>.", "8": "Enter <STREET_NAMES>", "9": "Enter <STREET_NAMES> and take the <ORDINAL_VALUE> exit.", "10": "Enter <STREET_NAMES> and take the <ORDINAL_VALUE> exit onto <ROUNDABOUT_EXIT_STREET_NAMES>.", "11": "Enter <STREET_NAMES> and take the <ORDINAL_VALUE> exit onto <ROUNDABOUT_EXIT_BEGIN_STREET_NAMES>.", "12": "Enter <STREET_NAMES> and take the <ORDINAL_VALUE> exit toward <TOWARD_SIGN>.", "13": "Enter <STREET_NAMES> and take the exit onto <ROUNDABOUT_EXIT_STREET_NAMES>.", "14": "Enter <STREET_NAMES> and take the exit onto <ROUNDABOUT_EXIT_BEGIN_STREET_NAMES>.", "15": "Enter <STREET_NAMES> and take the exit toward <TOWARD_SIGN>."}, "ordinal_values": ["1", "2", "3", "4", "5", "6", "7", "8", "9", "10"], "empty_street_name_labels": ["the walkway", "the cycleway", "the mountain bike trail", "the crosswalk", "the stairs", "the bridge", "the tunnel"], "example_phrases": {"0": ["Pojdite v krožišče."], "1": ["Pojdite v krožišče in uporabite 1. izhod.", "Pojdite v krožišče in uporabite 2. izhod.", "Pojdite v krožišče in uporabite 3. izhod.", "Pojdite v krožišče in uporabite 4. izhod.", "Pojdite v krožišče in uporabite 5. izhod.", "Pojdite v krožišče in uporabite 6. izhod.", "Pojdite v krožišče in uporabite 7. izhod.", "Pojdite v krožišče in uporabite 8. izhod.", "Pojdite v krožišče in uporabite 9. izhod.", "Pojdite v krožišče in uporabite 10. izhod."], "2": ["Enter the roundabout and take the 3rd exit onto Main Street."], "3": ["Enter the roundabout and take the 3rd exit onto U.S. 3 22/Main Street."], "4": ["Enter the roundabout and take the 3rd exit toward Baltimore."], "5": ["Enter the roundabout and take the exit onto Main Street."], "6": ["Enter the roundabout and take the exit onto U.S. 3 22/Main Street."], "7": ["Enter the roundabout and take the exit toward Baltimore."], "8": ["Enter Dupont Circle."], "9": ["Enter Dupont Circle and take the 1st exit."], "10": ["Enter Dupont Circle and take the 3rd exit onto Main Street."], "11": ["Enter Dupont Circle and take the 3rd exit onto U.S. 3 22/Main Street."], "12": ["Enter Dupont Circle and take the 3rd exit toward Baltimore."], "13": ["Enter Dupont Circle and take the exit onto Main Street."], "14": ["Enter Dupont Circle and take the exit onto U.S. 3 22/Main Street."], "15": ["Enter Dupont Circle and take the exit toward Baltimore."]}}, "exit": {"phrases": {"0": "Zapeljite na izhod na <RELATIVE_DIRECTION>.", "1": "Zapeljite na izhod <NUMBER_SIGN> na <RELATIVE_DIRECTION>.", "2": "Zapeljite na izhod <BRANCH_SIGN> na <RELATIVE_DIRECTION>.", "3": "Zapeljite na izhod <NUMBER_SIGN> na <RELATIVE_DIRECTION> in nato na <BRANCH_SIGN>.", "4": "Zapeljite na izhod na <RELATIVE_DIRECTION> proti <TOWARD_SIGN>.", "5": "Zapeljite na izhod <NUMBER_SIGN> na <RELATIVE_DIRECTION> proti <TOWARD_SIGN>.", "6": "Zapeljite na izhod <BRANCH_SIGN> na <RELATIVE_DIRECTION> proti <TOWARD_SIGN>.", "7": "Zapeljite na izhod <NUMBER_SIGN> na <RELATIVE_DIRECTION> in nato na <BRANCH_SIGN> proti <TOWARD_SIGN>.", "8": "Zapeljite na izhod <NAME_SIGN> na <RELATIVE_DIRECTION>.", "10": "Zapeljite na izhod <NAME_SIGN> na <RELATIVE_DIRECTION> in nato na <BRANCH_SIGN>.", "12": "Zapeljite na izhod <NAME_SIGN> na <RELATIVE_DIRECTION> proti <TOWARD_SIGN>.", "14": "Zapeljite na izhod <NAME_SIGN> na <RELATIVE_DIRECTION> in nato na <BRANCH_SIGN> proti <TOWARD_SIGN>.", "15": "Zapeljite na izhod.", "16": "Zapeljite na izhod <NUMBER_SIGN>.", "17": "Zapeljite na izhod <BRANCH_SIGN>.", "18": "Zapeljite na izhod <NUMBER_SIGN> in nato na <BRANCH_SIGN>.", "19": "Zapeljite na izhod proti <TOWARD_SIGN>.", "20": "Zapeljite na izhod <NUMBER_SIGN> proti <TOWARD_SIGN>.", "21": "Zapeljite na izhod <BRANCH_SIGN> proti <TOWARD_SIGN>.", "22": "Zapeljite na izhod <NUMBER_SIGN> in nato na <BRANCH_SIGN> proti <TOWARD_SIGN>.", "23": "Zapeljite na izhod <NAME_SIGN>.", "25": "Zapeljite na izhod <NAME_SIGN> in nato na <BRANCH_SIGN>.", "27": "Zapeljite na izhod <NAME_SIGN> proti <TOWARD_SIGN>.", "29": "Zapeljite na izhod <NAME_SIGN> in nato na <BRANCH_SIGN> proti <TOWARD_SIGN>."}, "relative_directions": ["levi", "<PERSON><PERSON>"], "example_phrases": {"0": ["Zapeljite na izhod na levi.", "Zapeljite na izhod na desni."], "1": ["Zapeljite na izhod 67 B-A na desni."], "2": ["Zapeljite na izhod US 322 West na desni."], "3": ["Zapeljite na izhod 67 B-A na desni in nato na US 322 West."], "4": ["Zapeljite na izhod na desni proti Lewistown."], "5": ["Zapeljite na izhod 67 B-A na desni proti Lewistown."], "6": ["Zapeljite na izhod US 322 West na desni proti Lewistown."], "7": ["Zapeljite na izhod 67 B-A na desni in nato na US 322 West proti Lewistown/State College."], "8": ["Zapeljite na izhod White Marsh Boulevard na levi."], "10": ["Zapeljite na izhod White Marsh Boulevard na levi in nato na MD 43 East."], "12": ["Zapeljite na izhod White Marsh Boulevard na levi proti White Marsh."], "14": ["Zapeljite na izhod White Marsh Boulevard na levi in nato na MD 43 East proti White Marsh."], "15": ["Take the exit."], "16": ["Take exit 67 B-A."], "17": ["Take the US 322 West exit."], "18": ["Take exit 67 B-A onto US 322 West."], "19": ["Take the exit toward Lewistown."], "20": ["Take exit 67 B-A toward Lewistown."], "21": ["Take the US 322 West exit toward Lewistown."], "22": ["Take exit 67 B-A onto US 322 West toward Lewistown/State College."], "23": ["Take the White Marsh Boulevard exit."], "25": ["Take the White Marsh Boulevard exit onto MD 43 East."], "27": ["Take the White Marsh Boulevard exit toward White Marsh."], "29": ["Take the White Marsh Boulevard exit onto MD 43 East toward White Marsh."]}}, "exit_roundabout": {"phrases": {"0": "Zapustite krožišče.", "1": "<PERSON><PERSON> krožišča pojdite na <STREET_NAMES>.", "2": "<PERSON><PERSON> k<PERSON> pojdite na <BEGIN_STREET_NAMES>. Nadaljujte po <STREET_NAMES>.", "3": "Exit the roundabout toward <TOWARD_SIGN>."}, "empty_street_name_labels": ["p<PERSON><PERSON><PERSON>", "kolesarsko stezo", "stezo za gorsko kolo", "the crosswalk", "the stairs", "the bridge", "the tunnel"], "example_phrases": {"0": ["Zapustite krožišče."], "1": ["<PERSON>z kro<PERSON>šča pojdite na Philadelphia Road/MD 7."], "2": ["<PERSON>z krožišča pojdite na Catoctin Mountain Highway/US 15. Nadaljujte po US 15."], "3": ["Exit the roundabout toward Baltimore."]}}, "exit_roundabout_verbal": {"phrases": {"0": "Zapustite krožišče.", "1": "<PERSON><PERSON> krožišča pojdite na <STREET_NAMES>.", "2": "<PERSON><PERSON> k<PERSON>ž<PERSON>šča pojdite na <BEGIN_STREET_NAMES>.", "3": "Exit the roundabout toward <TOWARD_SIGN>."}, "empty_street_name_labels": ["p<PERSON><PERSON><PERSON>", "kolesarsko stezo", "stezo za gorsko kolo", "the crosswalk", "the stairs", "the bridge", "the tunnel"], "example_phrases": {"0": ["Zapustite krožišče."], "1": ["<PERSON>z <PERSON>šča pojdite na Philadelphia Road, Maryland 7."], "2": ["<PERSON>z krožišča pojdite na Catoctin Mountain Highway, U.S. 15."], "3": ["Exit the roundabout toward Baltimore."]}}, "exit_verbal": {"phrases": {"0": "Zapeljite na izhod na <RELATIVE_DIRECTION>.", "1": "Zapeljite na izhod <NUMBER_SIGN> na <RELATIVE_DIRECTION>.", "2": "Zapeljite na izhod <BRANCH_SIGN> na <RELATIVE_DIRECTION>.", "3": "Zapeljite na izhod <NUMBER_SIGN> na <RELATIVE_DIRECTION> in nato na <BRANCH_SIGN>.", "4": "Zapeljite na izhod <RELATIVE_DIRECTION> proti <TOWARD_SIGN>.", "5": "Zapeljite na izhod <NUMBER_SIGN> na <RELATIVE_DIRECTION> proti <TOWARD_SIGN>.", "6": "Zapeljite na izhod <BRANCH_SIGN> na <RELATIVE_DIRECTION> proti <TOWARD_SIGN>.", "7": "Zapeljite na izhod <NUMBER_SIGN> na <RELATIVE_DIRECTION> in nato na <BRANCH_SIGN> proti <TOWARD_SIGN>.", "8": "Zapeljite na izhod <NAME_SIGN> na <RELATIVE_DIRECTION>.", "10": "Zapeljite na izhod <NAME_SIGN> na <RELATIVE_DIRECTION> in nato na <BRANCH_SIGN>.", "12": "Zapeljite na izhod <NAME_SIGN> na <RELATIVE_DIRECTION> proti <TOWARD_SIGN>.", "14": "Zapeljite na izhod <NAME_SIGN> na <RELATIVE_DIRECTION> in nato na <BRANCH_SIGN> proti <TOWARD_SIGN>.", "15": "Zapeljite na izhod.", "16": "Zapeljite na izhod <NUMBER_SIGN>.", "17": "Zapeljite na izhod <BRANCH_SIGN>.", "18": "Zapeljite na izhod <NUMBER_SIGN> in nato na <BRANCH_SIGN>.", "19": "Zapeljite na izhod proti <TOWARD_SIGN>.", "20": "Zapeljite na izhod <NUMBER_SIGN> proti <TOWARD_SIGN>.", "21": "Zapeljite na izhod <BRANCH_SIGN> proti <TOWARD_SIGN>.", "22": "Zapeljite na izhod <NUMBER_SIGN> in nato na <BRANCH_SIGN> proti <TOWARD_SIGN>.", "23": "Zapeljite na izhod <NAME_SIGN>.", "25": "Zapeljite na izhod <NAME_SIGN> in nato na <BRANCH_SIGN>.", "27": "Zapeljite na izhod <NAME_SIGN> proti <TOWARD_SIGN>.", "29": "Zapeljite na izhod <NAME_SIGN> in nato na <BRANCH_SIGN> proti <TOWARD_SIGN>."}, "relative_directions": ["left", "right"], "example_phrases": {"0": ["Zapeljite na izhod na levi.", "Zapeljite na izhod na desni."], "1": ["Zapeljite na izhod 67 B-A na desni."], "2": ["Zapeljite na izhod US 322 West na desni."], "3": ["Zapeljite na izhod 67 B-A na desni in nato na U.S. 322 West."], "4": ["Zapeljite na izhod na desni proti Lewistown."], "5": ["Zapeljite na izhod 67 B-A na desni proti Lewistown."], "6": ["Zapeljite na izhod US 322 West na desni proti Lewistown."], "7": ["Zapeljite na izhod 67 B-A na desni in nato na U.S. 322 West proti Lewistown, State College."], "8": ["Zapeljite na izhod White Marsh Boulevard na levi."], "10": ["Zapeljite na izhod White Marsh Boulevard na levi in nato na Maryland 43 East."], "12": ["Zapeljite na izhod White Marsh Boulevard na levi proti White Marsh."], "14": ["Zapeljite na izhod White Marsh Boulevard na levi in nato na Maryland 43 East proti White Marsh."], "15": ["Take the exit."], "16": ["Take exit 67 B-A."], "17": ["Take the US 322 West exit."], "18": ["Take exit 67 B-A onto US 322 West."], "19": ["Take the exit toward Lewistown."], "20": ["Take exit 67 B-A toward Lewistown."], "21": ["Take the US 322 West exit toward Lewistown."], "22": ["Take exit 67 B-A onto US 322 West toward Lewistown/State College."], "23": ["Take the White Marsh Boulevard exit."], "25": ["Take the White Marsh Boulevard exit onto MD 43 East."], "27": ["Take the White Marsh Boulevard exit toward White Marsh."], "29": ["Take the White Marsh Boulevard exit onto MD 43 East toward White Marsh."]}}, "exit_visual": {"phrases": {"0": "<PERSON><PERSON><PERSON><PERSON> št. <EXIT_NUMBERS>"}, "example_phrases": {"0": ["Izhod št. 1A"]}}, "keep": {"phrases": {"0": "Na razcepu pojdite <RELATIVE_DIRECTION>.", "1": "<PERSON><PERSON><PERSON> se <RELATIVE_DIRECTION>, da zapeljete na izhod <NUMBER_SIGN>.", "2": "<PERSON><PERSON><PERSON> se <RELATIVE_DIRECTION>, da zapeljete na <STREET_NAMES>.", "3": "<PERSON><PERSON><PERSON> se <RELATIVE_DIRECTION>, da zapeljete na izhod <NUMBER_SIGN> in nato na <STREET_NAMES>.", "4": "<PERSON><PERSON><PERSON> se <RELATIVE_DIRECTION> proti <TOWARD_SIGN>.", "5": "<PERSON><PERSON><PERSON> se <RELATIVE_DIRECTION>, da zapeljete na izhod <NUMBER_SIGN> proti <TOWARD_SIGN>.", "6": "<PERSON><PERSON><PERSON> se <RELATIVE_DIRECTION>, da zapeljete na <STREET_NAMES> proti <TOWARD_SIGN>.", "7": "<PERSON><PERSON><PERSON> se <RELATIVE_DIRECTION>, da zapeljete na izhod <NUMBER_SIGN> in nato na <STREET_NAMES> proti <TOWARD_SIGN>."}, "empty_street_name_labels": ["p<PERSON><PERSON><PERSON>", "kolesarsko stezo", "stezo za gorsko kolo", "the crosswalk", "the stairs", "the bridge", "the tunnel"], "relative_directions": ["levo", "naravnost", "<PERSON>no"], "example_phrases": {"0": ["Na razcepu pojdite levo.", "Na razcepu pojdite naravnost.", "Na razcepu pojdite desno."], "1": ["<PERSON><PERSON><PERSON> <PERSON>, da zapeljete na izhod 62."], "2": ["<PERSON><PERSON><PERSON> se <PERSON>, da zapeljete na I 895 South."], "3": ["<PERSON><PERSON><PERSON> <PERSON>, da zapeljete na izhod 62 in nato na I 895 South."], "4": ["<PERSON><PERSON><PERSON> se desno proti <PERSON>."], "5": ["<PERSON><PERSON><PERSON> se <PERSON>, da zapeljete na izhod 62 proti Annapolis."], "6": ["<PERSON><PERSON><PERSON> <PERSON>, da zapeljete na I 895 South proti Annapolis."], "7": ["<PERSON><PERSON><PERSON> <PERSON>, da zapeljete na izhod 62 in nato na I 895 South proti Annapolis."]}}, "keep_to_stay_on": {"phrases": {"0": "<PERSON>j<PERSON>e <RELATIVE_DIRECTION>, da ostanete na <STREET_NAMES>.", "1": "<PERSON><PERSON><PERSON> se <RELATIVE_DIRECTION>, da zapeljete na izhod <NUMBER_SIGN> in ostanete na <STREET_NAMES>.", "2": "<PERSON><PERSON><PERSON> se <RELATIVE_DIRECTION>, da ostanete na <STREET_NAMES> proti <TOWARD_SIGN>.", "3": "<PERSON><PERSON><PERSON> se <RELATIVE_DIRECTION>, da zapeljete na izhod <NUMBER_SIGN> in ostanete na <STREET_NAMES> proti <TOWARD_SIGN>."}, "empty_street_name_labels": ["p<PERSON><PERSON><PERSON><PERSON>", "kolesarski stezi", "stezi za gorsko kolo", "the crosswalk", "the stairs", "the bridge", "the tunnel"], "relative_directions": ["levo", "naravnost", "<PERSON>no"], "example_phrases": {"0": ["<PERSON><PERSON><PERSON><PERSON> levo, da ostanete na on I 95 South.", "Poj<PERSON>e naravnost, da ostanete na I 95 South.", "<PERSON><PERSON><PERSON><PERSON>, da ostanete na I 95 South."], "1": ["<PERSON><PERSON><PERSON> se levo, da zapeljete na izhod 62 in ostanete na I 95 South."], "2": ["<PERSON><PERSON><PERSON> se levo, da ostanete na I 95 South proti Baltimore."], "3": ["<PERSON><PERSON><PERSON> se levo, da zapeljete na izhod 62 in ostanete na I 95 South proti Baltimore."]}}, "keep_to_stay_on_verbal": {"phrases": {"0": "<PERSON>j<PERSON>e <RELATIVE_DIRECTION>, da ostanete na <STREET_NAMES>.", "1": "<PERSON><PERSON><PERSON> se <RELATIVE_DIRECTION>, da zapeljete na izhod <NUMBER_SIGN> in ostanete na <STREET_NAMES>.", "2": "<PERSON><PERSON><PERSON> se <RELATIVE_DIRECTION>, da ostanete na <STREET_NAMES> proti <TOWARD_SIGN>.", "3": "<PERSON><PERSON><PERSON> se <RELATIVE_DIRECTION>, da zapeljete na izhod <NUMBER_SIGN> in ostanete na <STREET_NAMES> proti <TOWARD_SIGN>."}, "empty_street_name_labels": ["p<PERSON><PERSON><PERSON><PERSON>", "kolesarski stezi", "stezi za gorsko kolo", "the crosswalk", "the stairs", "the bridge", "the tunnel"], "relative_directions": ["levo", "naravnost", "<PERSON>no"], "example_phrases": {"0": ["Poj<PERSON>e levo, da ostanete na Interstate 95 South.", "Pojdite naravnost, da ostanete na Interstate 95 South.", "<PERSON><PERSON><PERSON><PERSON>, da ostanete on Interstate 95 South."], "1": ["<PERSON><PERSON><PERSON> se levo, da zapeljete na izhod 62 in ostanete na Interstate 95 South."], "2": ["<PERSON><PERSON><PERSON> se levo, da ostanete na I 95 South proti Baltimore."], "3": ["<PERSON><PERSON><PERSON> se levo, da zapeljete na izhod 62 in ostanete na Interstate 95 South proti Baltimore."]}}, "keep_verbal": {"phrases": {"0": "Na razcepu pojdite <RELATIVE_DIRECTION>.", "1": "<PERSON><PERSON><PERSON> se <RELATIVE_DIRECTION>, da zapeljete na izhod <NUMBER_SIGN>.", "2": "<PERSON><PERSON><PERSON> se <RELATIVE_DIRECTION>, da zapeljete na <STREET_NAMES>.", "3": "<PERSON><PERSON><PERSON> se <RELATIVE_DIRECTION>, da zapeljete na izhod <NUMBER_SIGN> in nato na <STREET_NAMES>.", "4": "<PERSON><PERSON><PERSON> se <RELATIVE_DIRECTION> proti <TOWARD_SIGN>.", "5": "<PERSON><PERSON><PERSON> se <RELATIVE_DIRECTION>, da zapeljete na izhod <NUMBER_SIGN> proti <TOWARD_SIGN>.", "6": "<PERSON><PERSON><PERSON> se <RELATIVE_DIRECTION>, da zapeljete na <STREET_NAMES> proti <TOWARD_SIGN>.", "7": "<PERSON><PERSON><PERSON> se <RELATIVE_DIRECTION>, da zapeljete na izhod <NUMBER_SIGN> in nato na <STREET_NAMES> proti <TOWARD_SIGN>."}, "empty_street_name_labels": ["p<PERSON><PERSON><PERSON>", "kolesarsko stezo", "stezo za gorsko kolo", "the crosswalk", "the stairs", "the bridge", "the tunnel"], "relative_directions": ["levo", "naravnost", "<PERSON>no"], "example_phrases": {"0": ["Na razcepu pojdite levo.", "Na razcepu pojdite naravnost.", "Na razcepu pojdite desno."], "1": ["<PERSON><PERSON><PERSON> <PERSON>, da zapeljete na izhod 62."], "2": ["<PERSON><PERSON><PERSON> se <PERSON>no, da zapeljete na Interstate 8 95 South."], "3": ["<PERSON><PERSON><PERSON> <PERSON>, da zapeljete na izhod 62 in nato na Interstate 8 95 South."], "4": ["<PERSON><PERSON><PERSON> se desno proti <PERSON>."], "5": ["<PERSON><PERSON><PERSON> se <PERSON>, da zapeljete na izhod 62 proti Annapolis."], "6": ["<PERSON><PERSON><PERSON> se <PERSON>, da zapeljete na Interstate 8 95 South proti Annapolis."], "7": ["<PERSON><PERSON><PERSON> <PERSON>, da zapeljete na izhod 62 in nato na Interstate 8 95 South proti Annapolis."]}}, "merge": {"phrases": {"0": "Zapeljite na sosednjo pot.", "1": "Zapeljite <RELATIVE_DIRECTION> na sosednjo pot.", "2": "Zapeljite na <STREET_NAMES>.", "3": "Zapeljite <RELATIVE_DIRECTION> na <STREET_NAMES>.", "4": "Merge toward <TOWARD_SIGN>.", "5": "Merge <RELATIVE_DIRECTION> toward <TOWARD_SIGN>."}, "relative_directions": ["levo", "<PERSON>no"], "empty_street_name_labels": ["p<PERSON><PERSON><PERSON>", "kolesarsko stezo", "stezo za gorsko kolo", "the crosswalk", "the stairs", "the bridge", "the tunnel"], "example_phrases": {"0": ["Zapeljite na sosednjo pot."], "1": ["Zapeljite levo na sosednjo pot."], "2": ["Zapeljite na I 76 West/Pennsylvania Turnpike."], "3": ["Zapeljite desno na I 83 South."], "4": ["Merge toward Baltimore."], "5": ["Merge right toward Baltimore."]}}, "merge_verbal": {"phrases": {"0": "Zapeljite na sosednjo pot.", "1": "Zapeljite <RELATIVE_DIRECTION> na sosednjo pot.", "2": "Zapeljite na <STREET_NAMES>.", "3": "Zapeljite <RELATIVE_DIRECTION> na <STREET_NAMES>.", "4": "Merge toward <TOWARD_SIGN>.", "5": "Merge <RELATIVE_DIRECTION> toward <TOWARD_SIGN>."}, "relative_directions": ["levo", "<PERSON>no"], "empty_street_name_labels": ["p<PERSON><PERSON><PERSON>", "kolesarsko stezo", "stezo za gorsko kolo", "the crosswalk", "the stairs", "the bridge", "the tunnel"], "example_phrases": {"0": ["Merge."], "1": ["Zapeljite levo na sosednjo pot."], "2": ["Zapeljite na I 76 West/Pennsylvania Turnpike."], "3": ["Zapeljite desno na I 83 South."], "4": ["Merge toward Baltimore."], "5": ["Merge right toward Baltimore."]}}, "post_transition_transit_verbal": {"phrases": {"0": "Potujte <TRANSIT_STOP_COUNT> <TRANSIT_STOP_COUNT_LABEL>."}, "transit_stop_count_labels": {"one": "<PERSON><PERSON><PERSON>", "other": "<PERSON><PERSON><PERSON>"}, "example_phrases": {"0": ["Travel 1 stop.", "Travel 3 stops."]}}, "post_transition_verbal": {"phrases": {"0": "Nadaljujte <LENGTH>.", "1": "Po <STREET_NAMES> nadaljujte <LENGTH>."}, "empty_street_name_labels": ["p<PERSON><PERSON><PERSON><PERSON>", "kolesarski stezi", "stezi za gorsko kolo", "the crosswalk", "the stairs", "the bridge", "the tunnel"], "metric_lengths": ["<KILOMETERS> kilometrov", "1 kilometer", "<METERS> metrov", "manj kot 10 metrov"], "us_customary_lengths": ["<MILES> milj", "1 miljo", "pol milje", "a quarter mile", "<FEET> čevljev", "Manj kot 10 čevljev"], "example_phrases": {"0": ["Nadaljujte 300 čevljev.", "Nadaljujte 9 milj."], "1": ["Po Pennsylvania 7 43 nadaljujte 6.2 milj.", "Po Main Street, Vermont 30 nadaljujte 1 desetinko milje."]}}, "ramp": {"phrases": {"0": "Zapeljite <RELATIVE_DIRECTION> na priključek.", "1": "Zapeljite <RELATIVE_DIRECTION> na priključek <BRANCH_SIGN>.", "2": "Zapeljite <RELATIVE_DIRECTION> na priključek proti <TOWARD_SIGN>.", "3": "Zapeljite <RELATIVE_DIRECTION> na priključek <BRANCH_SIGN> proti <TOWARD_SIGN>.", "4": "Zapeljite <RELATIVE_DIRECTION> na priključek <NAME_SIGN>.", "5": "Zavijte <RELATIVE_DIRECTION>, da zapeljete na priključek.", "6": "<PERSON>avij<PERSON> <RELATIVE_DIRECTION>, da zapeljete na priključek <BRANCH_SIGN>.", "7": "<PERSON>avij<PERSON> <RELATIVE_DIRECTION>, da zapeljete na priključek proti <TOWARD_SIGN>.", "8": "<PERSON><PERSON><PERSON><PERSON> <RELATIVE_DIRECTION>, da zapeljete na priključek <BRANCH_SIGN> proti <TOWARD_SIGN>.", "9": "Zavij<PERSON> <RELATIVE_DIRECTION>, da zapeljete na priključek <NAME_SIGN>.", "10": "Zapeljite na priključek.", "11": "Zapeljite na priključek <BRANCH_SIGN>.", "12": "Zapeljite na priključek proti <TOWARD_SIGN>.", "13": "Zapeljite na priključek <BRANCH_SIGN> proti <TOWARD_SIGN>.", "14": "Zapeljite na priključek <NAME_SIGN>."}, "relative_directions": ["levo", "<PERSON>no"], "example_phrases": {"0": ["Zapeljite levo na priključek.", "Zapeljite desno na priključek."], "1": ["Zapeljite desno na priključek I 95."], "2": ["Zapeljite levo na priključek proti JFK."], "3": ["Zapeljite levo na priključek South Conduit Avenue proti JFK."], "4": ["Zapeljite desno na priključek Gettysburg Pike."], "5": ["Zavijte levo, da zapeljete na priključek.", "<PERSON><PERSON><PERSON><PERSON> desno, da zapeljete na priključek."], "6": ["Zavijte levo, da zapeljete na priključek PA 283 West."], "7": ["Zavijte levo, da zapeljete na priključek proti Harrisburg/Harrisburg International Airport."], "8": ["Zavijte levo, da zapeljete na priključek PA 283 West proti Harrisburg/Harrisburg International Airport."], "9": ["<PERSON><PERSON><PERSON><PERSON>, da zapeljete na priključek Gettysburg Pike."], "10": ["Take the ramp."], "11": ["Take the I 95 ramp."], "12": ["Take the ramp toward JFK."], "13": ["Take the Soutch Conduit Avenue ramp toward JFK."], "14": ["Take the Gettysburg ramp."]}}, "ramp_straight": {"phrases": {"0": "Nadaljujte naravnost, da zapeljete na priključek.", "1": "Nadal<PERSON><PERSON><PERSON> naravnost, da zapeljete na priključek <BRANCH_SIGN>.", "2": "Nadal<PERSON><PERSON><PERSON> naravnost, da zapeljete na priključek proti <TOWARD_SIGN>.", "3": "Nadal<PERSON><PERSON><PERSON> naravnost, da zapeljete na priključek <BRANCH_SIGN> proti <TOWARD_SIGN>.", "4": "Nadal<PERSON><PERSON><PERSON> naravnost, da zapeljete na priključek <NAME_SIGN>."}, "example_phrases": {"0": ["Nadaljujte naravnost, da zapeljete na priključek."], "1": ["Nadaljujte naravnost, da zapeljete na priključek US 322 East."], "2": ["Nadaljujte naravnost, da zapeljete na priključek proti Hershey."], "3": ["Nadal<PERSON><PERSON><PERSON> naravnost, da zapeljete na priključek US 322 East/US 422 East/US 522 East/US 622 East proti Hershey/Palmdale/Palmyra/Campbelltown."], "4": ["Nadaljujte naravnost, da zapeljete na priključek Gettysburg Pike."]}}, "ramp_straight_verbal": {"phrases": {"0": "Nadaljujte naravnost, da zapeljete na priključek.", "1": "Nadal<PERSON><PERSON><PERSON> naravnost, da zapeljete na priključek <BRANCH_SIGN>.", "2": "Nadal<PERSON><PERSON><PERSON> naravnost, da zapeljete na priključek proti <TOWARD_SIGN>.", "3": "Nadal<PERSON><PERSON><PERSON> naravnost, da zapeljete na priključek <BRANCH_SIGN> proti <TOWARD_SIGN>.", "4": "Nadal<PERSON><PERSON><PERSON> naravnost, da zapeljete na priključek <NAME_SIGN>."}, "example_phrases": {"0": ["Nadaljujte naravnost, da zapeljete na priključek."], "1": ["Nadaljujte naravnost, da zapeljete na priključek U.S. 322 East."], "2": ["Nadaljujte naravnost, da zapeljete na priključek proti Hershey."], "3": ["<PERSON>dal<PERSON><PERSON><PERSON> naravnost, da zapeljete na priključek U.S. 322 East, U.S. 422 East proti Hershey, Palmdale."], "4": ["Nadaljujte naravnost, da zapeljete na priključek Gettysburg Pike."]}}, "ramp_verbal": {"phrases": {"0": "Zapeljite <RELATIVE_DIRECTION> na priključek.", "1": "Zapeljite <RELATIVE_DIRECTION> na priključek <BRANCH_SIGN>.", "2": "Zapeljite <RELATIVE_DIRECTION> na priključek proti <TOWARD_SIGN>.", "3": "Zapeljite <RELATIVE_DIRECTION> na priključek <BRANCH_SIGN> proti <TOWARD_SIGN>.", "4": "Zapeljite <RELATIVE_DIRECTION> na priključek <NAME_SIGN>.", "5": "Zavijte <RELATIVE_DIRECTION>, da zapeljete na priključek.", "6": "<PERSON>avij<PERSON> <RELATIVE_DIRECTION>, da zapeljete na priključek <BRANCH_SIGN>.", "7": "<PERSON>avij<PERSON> <RELATIVE_DIRECTION>, da zapeljete na priključek proti <TOWARD_SIGN>.", "8": "<PERSON><PERSON><PERSON><PERSON> <RELATIVE_DIRECTION>, da zapeljete na priključek <BRANCH_SIGN> proti <TOWARD_SIGN>.", "9": "Zavij<PERSON> <RELATIVE_DIRECTION>, da zapeljete na priključek <NAME_SIGN>.", "10": "Zapeljite na priključek.", "11": "Zapeljite na priključek <BRANCH_SIGN>.", "12": "Zapeljite na priključek proti <TOWARD_SIGN>.", "13": "Zapeljite na priključek <BRANCH_SIGN> proti <TOWARD_SIGN>.", "14": "Zapeljite na priključek <NAME_SIGN>."}, "relative_directions": ["levo", "<PERSON>no"], "example_phrases": {"0": ["Zapeljite levo na priključek.", "Zapeljite desno na priključek."], "1": ["Zapeljite desno na priključek Interstate 95."], "2": ["Zapeljite levo na priključek proti JFK."], "3": ["Zapeljite levo na priključek South Conduit Avenue proti JFK."], "4": ["Zapeljite desno na priključek Gettysburg Pike."], "5": ["Zavijte levo, da zapeljete na priključek.", "<PERSON><PERSON><PERSON><PERSON> desno, da zapeljete na priključek."], "6": ["<PERSON><PERSON><PERSON><PERSON> levo, da zapeljete na priključek Pennsylvania 2 83 West."], "7": ["Zavijte levo, da zapeljete na priključek proti Harrisburg/Harrisburg International Airport."], "8": ["Z<PERSON>jte levo, da zapeljete na priključek Pennsylvania 2 83 West proti Harrisburg, Harrisburg International Airport."], "9": ["<PERSON><PERSON><PERSON><PERSON>, da zapeljete na priključek Gettysburg Pike."], "10": ["Take the ramp."], "11": ["Take the Interstate 95 ramp."], "12": ["Take the ramp toward JFK."], "13": ["Take the South Conduit Avenue ramp toward JFK."], "14": ["Take the Gettysburg Pike ramp."]}}, "sharp": {"phrases": {"0": "Zavijte ostro <RELATIVE_DIRECTION>.", "1": "<PERSON><PERSON><PERSON><PERSON> ostro <RELATIVE_DIRECTION> na <STREET_NAMES>.", "2": "<PERSON><PERSON><PERSON><PERSON> ostro <RELATIVE_DIRECTION> na <BEGIN_STREET_NAMES>. Nadaljujte po <STREET_NAMES>.", "3": "<PERSON><PERSON><PERSON><PERSON> ostro <RELATIVE_DIRECTION>, da ostanete na <STREET_NAMES>.", "4": "Make a sharp <RELATIVE_DIRECTION> at <JUNCTION_NAME>.", "5": "Make a sharp <RELATIVE_DIRECTION> toward <TOWARD_SIGN>."}, "empty_street_name_labels": ["p<PERSON><PERSON><PERSON>", "kolesarsko stezo", "stezo za gorsko kolo", "the crosswalk", "the stairs", "the bridge", "the tunnel"], "relative_directions": ["levo", "<PERSON>no"], "example_phrases": {"0": ["Zavijte ostro levo."], "1": ["Zavijte ostro desno na Flatbush Avenue."], "2": ["Zavijte ostro levo na North Bond Street/US 1 Business/MD 924. Nadaljujte po MD 924."], "3": ["<PERSON><PERSON><PERSON><PERSON> o<PERSON>, da ostanete na Sunstone Drive."], "4": ["Make a sharp right at Mannenbashi East."], "5": ["Make a sharp left toward Baltimore."]}}, "sharp_verbal": {"phrases": {"0": "Zavijte ostro <RELATIVE_DIRECTION>.", "1": "<PERSON><PERSON><PERSON><PERSON> ostro <RELATIVE_DIRECTION> na <STREET_NAMES>.", "2": "<PERSON><PERSON><PERSON><PERSON> ostro <RELATIVE_DIRECTION> na <BEGIN_STREET_NAMES>.", "3": "<PERSON><PERSON><PERSON><PERSON> ostro <RELATIVE_DIRECTION>, da ostanete na <STREET_NAMES>.", "4": "Make a sharp <RELATIVE_DIRECTION> at <JUNCTION_NAME>.", "5": "Make a sharp <RELATIVE_DIRECTION> toward <TOWARD_SIGN>."}, "empty_street_name_labels": ["p<PERSON><PERSON><PERSON>", "kolesarsko stezo", "stezo za gorsko kolo", "the crosswalk", "the stairs", "the bridge", "the tunnel"], "relative_directions": ["levo", "<PERSON>no"], "example_phrases": {"0": ["Zavijte ostro levo."], "1": ["Zavijte ostro desno na Flatbush Avenue."], "2": ["Zavijte ostro levo na North Bond Street, U.S. 1 Business."], "3": ["<PERSON><PERSON><PERSON><PERSON> o<PERSON>, da ostanete na Sunstone Drive."], "4": ["Make a sharp right at Mannenbashi East."], "5": ["Make a sharp left toward Baltimore."]}}, "start": {"phrases": {"0": "Pojdite <CARDINAL_DIRECTION>.", "1": "Na <STREET_NAMES> pojdite <CARDINAL_DIRECTION>.", "2": "Na <BEGIN_STREET_NAMES> pojdite <CARDINAL_DIRECTION>. Nadaljujte po <STREET_NAMES>.", "4": "Peljite <CARDINAL_DIRECTION>.", "5": "Na <STREET_NAMES> peljite <CARDINAL_DIRECTION>.", "6": "Na <BEGIN_STREET_NAMES> peljite <CARDINAL_DIRECTION>. Nadal<PERSON><PERSON><PERSON> po <STREET_NAMES>.", "8": "Pojdite <CARDINAL_DIRECTION>.", "9": "Na <STREET_NAMES> pojdite <CARDINAL_DIRECTION>.", "10": "Na <BEGIN_STREET_NAMES> pojdite <CARDINAL_DIRECTION>. Nadaljujte po <STREET_NAMES>.", "16": "Pojdite <CARDINAL_DIRECTION>.", "17": "Na <STREET_NAMES> pojdite <CARDINAL_DIRECTION>.", "18": "Na <BEGIN_STREET_NAMES> pojdite <CARDINAL_DIRECTION>. Nadaljujte po <STREET_NAMES>."}, "cardinal_directions": ["proti severu", "proti severovzhodu", "proti vzhodu", "proti jugovz<PERSON>du", "proti jugu", "proti jugozahodu", "proti zahodu", "proti sever<PERSON><PERSON><PERSON>u"], "empty_street_name_labels": ["p<PERSON><PERSON><PERSON><PERSON>", "kolesarski stezi", "stezi za gorsko kolo", "the crosswalk", "the stairs", "the bridge", "the tunnel"], "example_phrases": {"0": ["Pojdite proti vzhodu.", "<PERSON>j<PERSON><PERSON> proti severu."], "1": ["Na 5th Avenue pojdite proti jugozahodu.", "Na pešpoti pojdite proti zahodu.", "Na kolesarski stezi pojdite proti vzhodu.", "Na stezi za gorsko kolo pojdite proti severu."], "2": ["Na North Prince Street/US 222/PA 272 pojdite proti jugu. Nadaljujte po US 222/PA 272."], "4": ["Peljite proti vzhodu.", "<PERSON><PERSON><PERSON><PERSON> proti severu."], "5": ["Na 5th Avenue peljite proti jugozahodu."], "6": ["Na North Prince Street/US 222/PA 272 peljite proti jugu. Nadaljujte po US 222/PA 272."], "8": ["Pojdite proti vzhodu.", "<PERSON>j<PERSON><PERSON> proti severu."], "9": ["Na 5th Avenue pojdite proti jugozahodu.", "Na pešpoti pojdite proti zahodu."], "10": ["Na North Prince Street/US 222/PA 272 pojdite proti jugu. Nadaljujte po US 222/PA 272."], "16": ["Pojdite proti vzhodu.", "<PERSON>j<PERSON><PERSON> proti severu."], "17": ["Na 5th Avenue pojdite proti jugozahodu.", "Na kolesarski stezi pojdite proti vzhodu.", "Na stezi za gorsko kolo pojdite proti severu."], "18": ["Na North Prince Street/US 222/PA 272 pojdite proti jugu. Nadaljujte po US 222/PA 272."]}}, "start_verbal": {"phrases": {"0": "Pojdite <CARDINAL_DIRECTION>.", "1": "Pojdite <LENGTH> <CARDINAL_DIRECTION>.", "2": "Na <STREET_NAMES> pojdite <CARDINAL_DIRECTION>.", "3": "Na <STREET_NAMES> pojdite <LENGTH> <CARDINAL_DIRECTION>.", "4": "Na <BEGIN_STREET_NAMES> pojdite <CARDINAL_DIRECTION>.", "5": "Peljite <CARDINAL_DIRECTION>.", "6": "Peljite <LENGTH> <CARDINAL_DIRECTION>.", "7": "Na <STREET_NAMES> peljite <CARDINAL_DIRECTION>.", "8": "Na <STREET_NAMES> peljite <LENGTH> <CARDINAL_DIRECTION>.", "9": "Na <BEGIN_STREET_NAMES> peljite <CARDINAL_DIRECTION>.", "10": "Pojdite <CARDINAL_DIRECTION>.", "11": "Pojdite <LENGTH> <CARDINAL_DIRECTION>.", "12": "Na <STREET_NAMES> pojdite <CARDINAL_DIRECTION>.", "13": "Na <STREET_NAMES> pojdite <LENGTH> <CARDINAL_DIRECTION>.", "14": "Pojdite <CARDINAL_DIRECTION> on <BEGIN_STREET_NAMES>.", "15": "Pojdite <CARDINAL_DIRECTION>.", "16": "Pojdite <LENGTH> <CARDINAL_DIRECTION>.", "17": "Na <STREET_NAMES> pojdite <CARDINAL_DIRECTION>.", "18": "Na <STREET_NAMES> pojdite <LENGTH> <CARDINAL_DIRECTION>.", "19": "Na <BEGIN_STREET_NAMES> pojdite <CARDINAL_DIRECTION>."}, "cardinal_directions": ["proti severu", "proti severovzhodu", "proti vzhodu", "proti jugovz<PERSON>du", "proti jugu", "proti jugozahodu", "proti zahodu", "proti sever<PERSON><PERSON><PERSON>u"], "empty_street_name_labels": ["p<PERSON><PERSON><PERSON><PERSON>", "kolesarski stezi", "stezi za gorsko kolo", "the crosswalk", "the stairs", "the bridge", "the tunnel"], "metric_lengths": ["<KILOMETERS> kilometrov", "1 kilometer", "<METERS> metrov", "manj kot 10 metrov"], "us_customary_lengths": ["<MILES> milj", "1 miljo", "pol milje", "a quarter mile", "<FEET> čevljev", "Manj kot 10 čevljev"], "example_phrases": {"0": ["Head east.", "Head north."], "1": ["Head east for a half mile.", "Head north for 1 kilometer."], "2": ["Head southwest on 5th Avenue."], "3": ["Head southwest on 5th Avenue for 1 tenth of a mile."], "4": ["Head south on North Prince Street, U.S. 2 22."], "5": ["Drive east.", "Drive north."], "6": ["Drive east for a half mile.", "Drive north for 1 kilometer."], "7": ["Drive southwest on 5th Avenue."], "8": ["Drive southwest on 5th Avenue for 1 tenth of a mile."], "9": ["Drive south on North Prince Street, U.S. 2 22."], "10": ["Walk east.", "Walk north."], "11": ["Walk east for a half mile.", "Walk north for 1 kilometer."], "12": ["Walk southwest on 5th Avenue."], "13": ["Walk southwest on 5th Avenue for 1 tenth of a mile."], "14": ["Walk south on North Prince Street, U.S. 2 22."], "15": ["Bike east.", "Bike north."], "16": ["Bike east for a half mile.", "Bike north for 1 kilometer."], "17": ["Bike southwest on 5th Avenue."], "18": ["Bike southwest on 5th Avenue for 1 tenth of a mile."], "19": ["Bike south on North Prince Street, U.S. 2 22."]}}, "transit": {"phrases": {"0": "Pojdite na <TRANSIT_NAME> (<TRANSIT_STOP_COUNT> <TRANSIT_STOP_COUNT_LABEL>).", "1": "Pojdite na <TRANSIT_NAME> proti <TRANSIT_HEADSIGN> (<TRANSIT_STOP_COUNT> <TRANSIT_STOP_COUNT_LABEL>)."}, "empty_transit_name_labels": ["tramvaj", "podzemno železnico", "vlak", "avtobus", "trajekt", "nihal<PERSON>", "kabin<PERSON> žičnico", "žično vzpenjačo"], "transit_stop_count_labels": {"one": "<PERSON><PERSON><PERSON>", "other": "<PERSON><PERSON><PERSON>"}, "example_phrases": {"0": ["Pojdite na New Haven (1 postanek).", "Pojdite na podzemno železnico (2 postanka).", "Pojdite na avtobus (12 postankov)."], "1": ["Pojdite na F proti JAMAICA - 179 ST. (10 postankov).", "Pojdite na trajekt proti Staten Island (1 postanek)."]}}, "transit_connection_destination": {"phrases": {"0": "Zapustite postajo.", "1": "Zapustite <TRANSIT_STOP>.", "2": "Zapustite <STATION_LABEL> <TRANSIT_STOP>."}, "station_label": "<PERSON><PERSON>", "example_phrases": {"0": ["Zapustite postajo."], "1": ["Zapustite Embarcadero Station."], "2": ["Zapustite 8 St - NYU Station."]}}, "transit_connection_destination_verbal": {"phrases": {"0": "Zapustite postajo.", "1": "Zapustite <TRANSIT_STOP>.", "2": "Zapustite <STATION_LABEL> <TRANSIT_STOP>."}, "station_label": "<PERSON><PERSON>", "example_phrases": {"0": ["Zapustite postajo."], "1": ["Zapustite Embarcadero Station."], "2": ["Zapustite 8 St - NYU Station."]}}, "transit_connection_start": {"phrases": {"0": "Pojdite na postajo.", "1": "Pojdite na <TRANSIT_STOP>.", "2": "Pojdite na <STATION_LABEL> <TRANSIT_STOP>."}, "station_label": "Station", "example_phrases": {"0": ["Pojdite na postajo."], "1": ["Pojdite na Embarcadero Station."], "2": ["Pojdite na 8 St - NYU Station."]}}, "transit_connection_start_verbal": {"phrases": {"0": "Pojdite na postajo.", "1": "Pojdite na <TRANSIT_STOP>.", "2": "Pojdite na <STATION_LABEL> <TRANSIT_STOP>."}, "station_label": "<PERSON><PERSON>", "example_phrases": {"0": ["Pojdite na postajo."], "1": ["Pojdite na Embarcadero Station."], "2": ["Pojdite na 8 St - NYU Station."]}}, "transit_connection_transfer": {"phrases": {"0": "Prestopite na postaji.", "1": "Prestopite na <TRANSIT_STOP>.", "2": "Prestopite na <STATION_LABEL> <TRANSIT_STOP>."}, "station_label": "Station", "example_phrases": {"0": ["Prestopite na postaji."], "1": ["Prestopite na Embarcadero Station."], "2": ["Prestopite na 8 St - NYU Station."]}}, "transit_connection_transfer_verbal": {"phrases": {"0": "Prestopite na postaji.", "1": "Prestopite na <TRANSIT_STOP>.", "2": "Prestopite na <STATION_LABEL> <TRANSIT_STOP>."}, "station_label": "<PERSON><PERSON>", "example_phrases": {"0": ["Prestopite na postaji."], "1": ["Prestopite na Embarcadero Station."], "2": ["Prestopite na 8 St - NYU Station."]}}, "transit_remain_on": {"phrases": {"0": "Ostanite na <TRANSIT_NAME> (<TRANSIT_STOP_COUNT> <TRANSIT_STOP_COUNT_LABEL>).", "1": "Ostanite na <TRANSIT_NAME> proti <TRANSIT_HEADSIGN> (<TRANSIT_STOP_COUNT> <TRANSIT_STOP_COUNT_LABEL>)."}, "empty_transit_name_labels": ["tramvaju", "podzemni železnici", "v<PERSON>u", "avtobus<PERSON>", "trajektu", "<PERSON>halki", "kabinski žičnici", "žični vzpenjači"], "transit_stop_count_labels": {"one": "<PERSON><PERSON><PERSON>", "other": "<PERSON><PERSON><PERSON>"}, "example_phrases": {"0": ["Ostanite na New Haven. (1 stop)", "Ostanite na vlaku (3 postanki)."], "1": ["Ostanite na F proti JAMAICA - 179 ST. (10 postankov)."]}}, "transit_remain_on_verbal": {"phrases": {"0": "Ostanite na <TRANSIT_NAME>.", "1": "Ostanite na <TRANSIT_NAME> proti <TRANSIT_HEADSIGN>."}, "empty_transit_name_labels": ["tramvaju", "podzemni železnici", "v<PERSON>u", "avtobus<PERSON>", "trajektu", "<PERSON>halki", "kabinski žičnici", "žični vzpenjači"], "example_phrases": {"0": ["Ostanite na New Haven."], "1": ["Ostanite na F proti JAMAICA - 179 ST."]}}, "transit_transfer": {"phrases": {"0": "Prestopite na <TRANSIT_NAME> (<TRANSIT_STOP_COUNT> <TRANSIT_STOP_COUNT_LABEL>).", "1": "Prestopite na <TRANSIT_NAME> proti <TRANSIT_HEADSIGN> (<TRANSIT_STOP_COUNT> <TRANSIT_STOP_COUNT_LABEL>)."}, "empty_transit_name_labels": ["tramvaj", "podzemno železnico", "vlak", "avtobus", "trajekt", "nihal<PERSON>", "kabin<PERSON> žičnico", "žično vzpenjačo"], "transit_stop_count_labels": {"one": "<PERSON><PERSON><PERSON>", "other": "<PERSON><PERSON><PERSON>"}, "example_phrases": {"0": ["Prestopite na New Haven (1 postanek).", "Prestopite na tramvaj (4 postanki)."], "1": ["Prestopite na F proti JAMAICA - 179 ST. (10 postankov)."]}}, "transit_transfer_verbal": {"phrases": {"0": "Prestopite na <TRANSIT_NAME>.", "1": "Prestopite na <TRANSIT_NAME> proti <TRANSIT_HEADSIGN>."}, "empty_transit_name_labels": ["tramvaj", "podzemno železnico", "vlak", "avtobus", "trajekt", "nihal<PERSON>", "kabin<PERSON> žičnico", "žično vzpenjačo"], "example_phrases": {"0": ["Prestopite na New Haven."], "1": ["Prestopite na F proti JAMAICA - 179 ST."]}}, "transit_verbal": {"phrases": {"0": "Pojdite na <TRANSIT_NAME>.", "1": "Pojdite na <TRANSIT_NAME> proti <TRANSIT_HEADSIGN>."}, "empty_transit_name_labels": ["tramvaj", "podzemno železnico", "vlak", "avtobus", "trajekt", "nihal<PERSON>", "kabin<PERSON> žičnico", "žično vzpenjačo"], "example_phrases": {"0": ["Pojdite na New Haven."], "1": ["Pojdite na F proti JAMAICA - 179 ST."]}}, "turn": {"phrases": {"0": "Zavijte <RELATIVE_DIRECTION>.", "1": "Zavijte <RELATIVE_DIRECTION> na <STREET_NAMES>.", "2": "Zavijte <RELATIVE_DIRECTION> na <BEGIN_STREET_NAMES>. Nadaljujte po <STREET_NAMES>.", "3": "<PERSON><PERSON><PERSON><PERSON> <RELATIVE_DIRECTION>, da ostanete na <STREET_NAMES>.", "4": "Turn <RELATIVE_DIRECTION> at <JUNCTION_NAME>.", "5": "Turn <RELATIVE_DIRECTION> toward <TOWARD_SIGN>."}, "empty_street_name_labels": ["p<PERSON><PERSON><PERSON>", "kolesarsko stezo", "stezo za gorsko kolo", "the crosswalk", "the stairs", "the bridge", "the tunnel"], "relative_directions": ["levo", "<PERSON>no"], "example_phrases": {"0": ["Zavijte levo."], "1": ["Zavijte desno na Flatbush Avenue."], "2": ["Zavijte levo na North Bond Street/US 1 Business/MD 924. Nadaljujte po MD 924."], "3": ["<PERSON><PERSON><PERSON><PERSON>, da ostanete na Sunstone Drive."], "4": ["Turn right at Mannenbashi East."], "5": ["Turn left toward Baltimore."]}}, "turn_verbal": {"phrases": {"0": "Zavijte <RELATIVE_DIRECTION>.", "1": "Zavijte <RELATIVE_DIRECTION> na <STREET_NAMES>.", "2": "<PERSON><PERSON>jte <RELATIVE_DIRECTION> na <BEGIN_STREET_NAMES>.", "3": "<PERSON><PERSON><PERSON><PERSON> <RELATIVE_DIRECTION>, da ostanete na <STREET_NAMES>.", "4": "Turn <RELATIVE_DIRECTION> at <JUNCTION_NAME>.", "5": "Turn <RELATIVE_DIRECTION> toward <TOWARD_SIGN>."}, "empty_street_name_labels": ["p<PERSON><PERSON><PERSON>", "kolesarsko stezo", "stezo za gorsko kolo", "the crosswalk", "the stairs", "the bridge", "the tunnel"], "relative_directions": ["levo", "<PERSON>no"], "example_phrases": {"0": ["Turn left."], "1": ["Zavijte desno na Flatbush Avenue."], "2": ["Zavijte levo na North Bond Street, U.S. 1 Business."], "3": ["<PERSON><PERSON><PERSON><PERSON>, da ostanete na Sunstone Drive."], "4": ["Turn right at Mannenbashi East."], "5": ["Turn left toward Baltimore."]}}, "uturn": {"phrases": {"0": "Polkrožno obrnite <RELATIVE_DIRECTION>.", "1": "Polkrožno obrnite <RELATIVE_DIRECTION> na <STREET_NAMES>.", "2": "Polkrožno obrnite <RELATIVE_DIRECTION>, da ostanete na <STREET_NAMES>.", "3": "Pri <CROSS_STREET_NAMES> polkrožno obrnite <RELATIVE_DIRECTION>.", "4": "Pri <CROSS_STREET_NAMES> polkrožno obrnite <RELATIVE_DIRECTION> na <STREET_NAMES>.", "5": "Pri <CROSS_STREET_NAMES> polkrožno obrnite <RELATIVE_DIRECTION>, da ostanete na <STREET_NAMES>.", "6": "Make a <RELATIVE_DIRECTION> U-turn at <JUNCTION_NAME>.", "7": "Make a <RELATIVE_DIRECTION> U-turn toward <TOWARD_SIGN>."}, "empty_street_name_labels": ["p<PERSON><PERSON><PERSON>", "kolesarsko stezo", "stezo za gorsko kolo", "the crosswalk", "the stairs", "the bridge", "the tunnel"], "relative_directions": ["levo", "<PERSON>no"], "example_phrases": {"0": ["Polkrožno obrnite levo."], "1": ["Polkrožno obrnite desno na Bunker Hill Road."], "2": ["Polkrožno obrnite levo, da ostanete na Bunker Hill Road."], "3": ["Pri Devonshire Road polkrožno obrnite levo."], "4": ["Pr Devonshire Road polkrožno obrnite levo na Jonestown Road/US 22."], "5": ["Pri Devonshire Road polkrožno obrnite levo, da ostanete na Jonestown Road/US 22."], "6": ["Make a right U-turn at Mannenbashi East."], "7": ["Make a left U-turn toward Baltimore."]}}, "uturn_verbal": {"phrases": {"0": "Polkrožno obrnite <RELATIVE_DIRECTION>.", "1": "Polkrožno obrnite <RELATIVE_DIRECTION> na <STREET_NAMES>.", "2": "Polkrožno obrnite <RELATIVE_DIRECTION>, da ostanete na <STREET_NAMES>.", "3": "Pri <CROSS_STREET_NAMES> polkrožno obrnite <RELATIVE_DIRECTION>.", "4": "Pri <CROSS_STREET_NAMES> polkrožno obrnite <RELATIVE_DIRECTION> na <STREET_NAMES>.", "5": "Pri <CROSS_STREET_NAMES> polkrožno obrnite <RELATIVE_DIRECTION>, da ostanete na <STREET_NAMES>.", "6": "Make a <RELATIVE_DIRECTION> U-turn at <JUNCTION_NAME>.", "7": "Make a <RELATIVE_DIRECTION> U-turn toward <TOWARD_SIGN>."}, "empty_street_name_labels": ["p<PERSON><PERSON><PERSON>", "kolesarsko stezo", "stezo za gorsko kolo", "the crosswalk", "the stairs", "the bridge", "the tunnel"], "relative_directions": ["levo", "<PERSON>no"], "example_phrases": {"0": ["Polkrožno obrnite levo."], "1": ["Polkrožno obrnite desno na Bunker Hill Road."], "2": ["Polkrožno obrnite levo, da ostanete na Bunker Hill Road."], "3": ["Pri Devonshire Road polkrožno obrnite levo."], "4": ["Pri Devonshire Road polkrožno obrnite levo na Jonestown Road, U.S. 22."], "5": ["Pri Devonshire Road polkrožno obrnite levo, da ostanete na Jonestown Road, U.S. 22."], "6": ["Make a right U-turn at Mannenbashi East."], "7": ["Make a left U-turn toward Baltimore."]}}, "verbal_multi_cue": {"phrases": {"0": "<CURRENT_VERBAL_CUE> Nato <NEXT_VERBAL_CUE>", "1": "<CURRENT_VERBAL_CUE> Then, in <LENGTH>, <NEXT_VERBAL_CUE>"}, "metric_lengths": ["<KILOMETERS> kilometrov", "1 kilometer", "<METERS> metrov", "manj kot 10 metrov"], "us_customary_lengths": ["<MILES> milj", "1 miljo", "pol milje", "a quarter mile", "<FEET> čevljev", "Manj kot 10 čevljev"], "example_phrases": {"0": ["<PERSON><PERSON><PERSON> <PERSON>, da pridete na East Fayette Street. Nato zavijte desno na North Gay Street."], "1": ["Bear right onto East Fayette Street. Then, in 500 feet, Turn right onto North Gay Street."]}}, "approach_verbal_alert": {"phrases": {"0": "In <LENGTH>, <CURRENT_VERBAL_CUE>"}, "metric_lengths": ["<KILOMETERS> kilometrov", "1 kilometer", "<METERS> metrov", "manj kot 10 metrov"], "us_customary_lengths": ["<MILES> milj", "1 miljo", "pol milje", "a quarter mile", "<FEET> čevljev", "Manj kot 10 čevljev"], "example_phrases": {"0": ["In a quarter mile, Turn right onto North Gay Street."]}}, "pass": {"phrases": {"0": "Pass <OBJECT_LABEL>.", "1": "Pass traffic signals on <OBJECT_LABEL>."}, "object_labels": ["the gate", "the bollards", "ways intersection"], "example_phrases": {"0": ["Pass the gate."]}}, "elevator": {"phrases": {"0": "Take the elevator.", "1": "Take the elevator to <LEVEL>."}, "example_phrases": {"0": ["Take the elevator."], "1": ["Take the elevator to Level 1."]}}, "steps": {"phrases": {"0": "Take the stairs.", "1": "Take the stairs to <LEVEL>."}, "example_phrases": {"0": ["Take the stairs."], "1": ["Take the stairs to Level 2."]}}, "escalator": {"phrases": {"0": "Take the escalator.", "1": "Take the escalator to <LEVEL>."}, "example_phrases": {"0": ["Take the escalator."], "1": ["Take the escalator to Level 3."]}}, "enter_building": {"phrases": {"0": "Enter the building.", "1": "Enter the building, and continue on <STREET_NAMES>."}, "empty_street_name_labels": ["the walkway", "the cycleway", "stezo za gorsko kolo", "the crosswalk"], "example_phrases": {"0": ["Enter the building."], "1": ["Enter the building, and continue on the walkway."]}}, "exit_building": {"phrases": {"0": "Exit the building.", "1": "Exit the building, and continue on <STREET_NAMES>."}, "empty_street_name_labels": ["the walkway", "the cycleway", "stezo za gorsko kolo", "the crosswalk"], "example_phrases": {"0": ["Exit the building."], "1": ["Exit the building, and continue on the walkway."]}}}}
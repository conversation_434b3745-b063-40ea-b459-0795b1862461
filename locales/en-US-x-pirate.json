{"posix_locale": "en_US.UTF-8", "aliases": ["en-x-pirate"], "instructions": {"arrive": {"phrases": {"0": "Dock: <TIME>.", "1": "Dock: <TIME> at <TRANSIT_STOP>."}, "example_phrases": {"0": ["Dock: 8:02 AM."], "1": ["Dock: 8:02 AM at 8 St - NYU."]}}, "arrive_verbal": {"phrases": {"0": "Dock at <TIME>.", "1": "Dock at <TIME> at <TRANSIT_STOP>."}, "example_phrases": {"0": ["Dock at 8:02 AM."], "1": ["Dock at 8:02 AM at 8 St - NYU."]}}, "bear": {"phrases": {"0": "Bear <RELATIVE_DIRECTION> me matey.", "1": "Bear <RELATIVE_DIRECTION> me matey onto <STREET_NAMES>.", "2": "Bear <RELATIVE_DIRECTION> me matey onto <BEGIN_STREET_NAMES>. Continue on <STREET_NAMES>.", "3": "Bear <RELATIVE_DIRECTION> me matey to stay on <STREET_NAMES>.", "4": "Bear <RELATIVE_DIRECTION> me matey at <JUNCTION_NAME>.", "5": "Bear <RELATIVE_DIRECTION> me matey toward <TOWARD_SIGN>."}, "empty_street_name_labels": ["solid ground", "th' bike pirate way", "th' treasure trail", "the crosswalk", "the stairs", "the bridge", "the tunnel"], "relative_directions": ["left", "right"], "example_phrases": {"0": ["Bear right me matey."], "1": ["Bear left me matey onto Arlen Road."], "2": ["Bear right me matey onto Belair Road/US 1 Business. Continue on US 1 Business."], "3": ["<PERSON> left me matey to stay on US 15 South."], "4": ["Bear right at Mannenbashi East."], "5": ["Bear left toward Baltimore."]}}, "bear_verbal": {"phrases": {"0": "Bear <RELATIVE_DIRECTION> me matey.", "1": "Bear <RELATIVE_DIRECTION> me matey onto <STREET_NAMES>.", "2": "Bear <RELATIVE_DIRECTION> me matey onto <BEGIN_STREET_NAMES>.", "3": "Bear <RELATIVE_DIRECTION> me matey to stay on <STREET_NAMES>.", "4": "Bear <RELATIVE_DIRECTION> me matey at <JUNCTION_NAME>.", "5": "Bear <RELATIVE_DIRECTION> toward <TOWARD_SIGN>."}, "empty_street_name_labels": ["solid ground", "th' bike pirate way", "th' treasure trail", "the crosswalk", "the stairs", "the bridge", "the tunnel"], "relative_directions": ["left", "right"], "example_phrases": {"0": ["Bear right me matey."], "1": ["Bear left me matey onto Arlen Road."], "2": ["Bear right me matey onto Belair Road, U.S. 1 Business."], "3": ["<PERSON> left me matey to stay on U.S. 15 South."], "4": ["Bear right at Mannenbashi East."], "5": ["Bear left toward Baltimore."]}}, "becomes": {"phrases": {"0": "<PREVIOUS_STREET_NAMES> becomes <STREET_NAMES>."}, "example_phrases": {"0": ["Vine Street becomes Middletown Road."]}}, "becomes_verbal": {"phrases": {"0": "<PREVIOUS_STREET_NAMES> becomes <STREET_NAMES>."}, "example_phrases": {"0": ["Vine Street becomes Middletown Road."]}}, "continue": {"phrases": {"0": "Aye, continue.", "1": "Heave ho on <STREET_NAMES>.", "2": "Continue at <JUNCTION_NAME>.", "3": "Continue toward <TOWARD_SIGN>."}, "empty_street_name_labels": ["solid ground", "th' bike pirate way", "th' treasure trail", "the crosswalk", "the stairs", "the bridge", "the tunnel"], "example_phrases": {"0": ["Aye, continue."], "1": ["Heave ho on 10th Avenue."], "2": ["Continue at Mannenbashi East."], "3": ["Continue toward Baltimore."]}}, "continue_verbal": {"phrases": {"0": "Aye, continue.", "1": "Aye, continue fer <LENGTH>.", "2": "Heave ho on <STREET_NAMES>", "3": "Heave ho on <STREET_NAMES> fer <LENGTH>.", "4": "Continue at <JUNCTION_NAME>.", "5": "Continue at <JUNCTION_NAME> for <LENGTH>.", "6": "Continue toward <TOWARD_SIGN>.", "7": "Continue toward <TOWARD_SIGN> for <LENGTH>."}, "empty_street_name_labels": ["solid ground", "th' bike pirate way", "th' treasure trail", "the crosswalk", "the stairs", "the bridge", "the tunnel"], "metric_lengths": ["<KILOMETERS> kilometers", "1 kilometer", "<METERS> meters", "less than 10 meters"], "us_customary_lengths": ["<MILES> miles", "1 mile", "a half mile", "a quarter mile", "<FEET> feet", "less than 10 feet"], "example_phrases": {"0": ["Continue."], "1": ["Continue for 300 feet."], "2": ["Continue on 10th Avenue."], "3": ["Continue on 10th Avenue for 3 miles."], "4": ["Continue at Mannenbashi East."], "5": ["Continue at Mannenbashi East for 2 miles."], "6": ["Continue toward Baltimore."], "7": ["Continue toward Baltimore for 5 miles."]}}, "continue_verbal_alert": {"phrases": {"0": "Aye, continue.", "1": "Heave ho on <STREET_NAMES>.", "2": "Continue at <JUNCTION_NAME>.", "3": "Continue toward <TOWARD_SIGN>."}, "empty_street_name_labels": ["solid ground", "th' bike pirate way", "th' treasure trail", "the crosswalk", "the stairs", "the bridge", "the tunnel"], "example_phrases": {"0": ["Aye, continue."], "1": ["Heave ho on 10th Avenue."], "2": ["Continue at Mannenbashi East."], "3": ["Continue toward Baltimore."]}}, "depart": {"phrases": {"0": "Set sail: <TIME>.", "1": "Set sail: <TIME> from <TRANSIT_STOP>."}, "example_phrases": {"0": ["Set sail: 8:02 AM."], "1": ["Set sail: 8:02 AM from 8 St - NYU."]}}, "depart_verbal": {"phrases": {"0": "Set sail at <TIME>.", "1": "Set sail at <TIME> from <TRANSIT_STOP>."}, "example_phrases": {"0": ["Set sail at 8:02 AM."], "1": ["Set sail at 8:02 AM from 8 St - NYU."]}}, "destination": {"phrases": {"0": "Blimey! Ye have arrived at yer destination. Savvy?", "1": "Shiver me timbers! Ye have arrived at <DESTINATION>. Savvy?", "2": "Sink me! Yer destination be on th' <RELATIVE_DIRECTION>. Savvy?", "3": "Aye <DESTINATION> be on th' <RELATIVE_DIRECTION>. Savvy?"}, "relative_directions": ["left", "right"], "example_phrases": {"0": ["Blimey! Ye have arrived at yer destination. Savvy?"], "1": ["Shiver me timbers! Ye have arrived at 3206 Powelton Avenue. Savvy?"], "2": ["Sink me! Yer destination be on th' left.", "Sink me! Yer destination be on th' right. Savvy?"], "3": ["Aye Lancaster Brewing Company be on th' left. Savvy?"]}}, "destination_verbal": {"phrases": {"0": "Shiver me timbers! Ye have arrived at yer destination. Savvy?", "1": "Blimey! Ye have arrived at <DESTINATION>. Savvy?", "2": "Yer destination be on th' <RELATIVE_DIRECTION>. Savvy?", "3": "<DESTINATION> be on th' <RELATIVE_DIRECTION>. Savvy?"}, "relative_directions": ["left", "right"], "example_phrases": {"0": ["Shiver me timbers! Ye have arrived at yer destination. Savvy?"], "1": ["Blimey! Ye have arrived at 32 o6 Powelton Avenue. Savvy?"], "2": ["Yer destination be on th' left.", "Yer destination be on th' right. Savvy?"], "3": ["Lancaster Brewing Company be on th' left. Savvy?"]}}, "destination_verbal_alert": {"phrases": {"0": "Blimey! Ye will arrive at yer destination.", "1": "Shiver me timbers! Ye will arrive at <DESTINATION>.", "2": "Yer destination will be on the <RELATIVE_DIRECTION>.", "3": "<DESTINATION> will be on the <RELATIVE_DIRECTION>."}, "relative_directions": ["left", "right"], "example_phrases": {"0": ["Blimey! Ye will arrive at yer destination."], "1": ["Shiver me timbers! Ye will arrive at 32 o6 Powelton Avenue."], "2": ["Yer destination will be on the left.", "Yer destination will be on the right."], "3": ["Lancaster Brewing Company will be on the left."]}}, "enter_ferry": {"phrases": {"0": "Ahoy, me matey! Take the Black Pearl.", "1": "Ahoy, me matey! Take the <STREET_NAMES>.", "2": "Ahoy, me matey! Take the <STREET_NAMES> <FERRY_LABEL>.", "3": "Take the ferry toward <TOWARD_SIGN>."}, "empty_street_name_labels": ["solid ground", "th' bike pirate way", "th' treasure trail", "the crosswalk", "the stairs", "the bridge", "the tunnel"], "ferry_label": "Ferry", "example_phrases": {"0": ["Ahoy, me matey! Take the Black Pearl."], "1": ["Ahoy, me matey! Take the Millersburg Ferry."], "2": ["Ahoy, me matey! Take the Bridgeport - Port Jefferson Ferry."], "3": ["Take the ferry toward Cape May."]}}, "enter_ferry_verbal": {"phrases": {"0": "Ahoy, me matey! Take the Black Pearl.", "1": "Ahoy, me matey! Take the <STREET_NAMES>.", "2": "Ahoy, me matey! Take the <STREET_NAMES> <FERRY_LABEL>.", "3": "Take the ferry toward <TOWARD_SIGN>."}, "empty_street_name_labels": ["solid ground", "th' bike pirate way", "th' treasure trail", "the crosswalk", "the stairs", "the bridge", "the tunnel"], "ferry_label": "Ferry", "example_phrases": {"0": ["Ahoy, me matey! Take the Black Pearl."], "1": ["Ahoy, me matey! Take the Millersburg Ferry."], "2": ["Ahoy, me matey! Take the Bridgeport - Port Jefferson Ferry."], "3": ["Take the ferry toward Cape May."]}}, "enter_roundabout": {"phrases": {"0": "Enter thee Cap'n wheel.", "1": "Enter thee Cap'n wheel and take th' <ORDINAL_VALUE> spoke.", "2": "Enter the roundabout and take the <ORDINAL_VALUE> exit onto <ROUNDABOUT_EXIT_STREET_NAMES>.", "3": "Enter the roundabout and take the <ORDINAL_VALUE> exit onto <ROUNDABOUT_EXIT_BEGIN_STREET_NAMES>. Continue on <ROUNDABOUT_EXIT_STREET_NAMES>.", "4": "Enter the roundabout and take the <ORDINAL_VALUE> exit toward <TOWARD_SIGN>.", "5": "Enter the roundabout and take the exit onto <ROUNDABOUT_EXIT_STREET_NAMES>.", "6": "Enter the roundabout and take the exit onto <ROUNDABOUT_EXIT_BEGIN_STREET_NAMES>. Continue on <ROUNDABOUT_EXIT_STREET_NAMES>.", "7": "Enter the roundabout and take the exit toward <TOWARD_SIGN>.", "8": "Enter <STREET_NAMES>", "9": "Enter <STREET_NAMES> and take the <ORDINAL_VALUE> exit.", "10": "Enter <STREET_NAMES> and take the <ORDINAL_VALUE> exit onto <ROUNDABOUT_EXIT_STREET_NAMES>.", "11": "Enter <STREET_NAMES> and take the <ORDINAL_VALUE> exit onto <ROUNDABOUT_EXIT_BEGIN_STREET_NAMES>. Continue on <ROUNDABOUT_EXIT_STREET_NAMES>.", "12": "Enter <STREET_NAMES> and take the <ORDINAL_VALUE> exit toward <TOWARD_SIGN>.", "13": "Enter <STREET_NAMES> and take the exit onto <ROUNDABOUT_EXIT_STREET_NAMES>.", "14": "Enter <STREET_NAMES> and take the exit onto <ROUNDABOUT_EXIT_BEGIN_STREET_NAMES>. Continue on <ROUNDABOUT_EXIT_STREET_NAMES>.", "15": "Enter <STREET_NAMES> and take the exit toward <TOWARD_SIGN>."}, "ordinal_values": ["1st", "2nd", "3rd", "4th", "5th", "6th", "7th", "8th", "9th", "10th"], "empty_street_name_labels": ["the walkway", "the cycleway", "the mountain bike trail", "the crosswalk", "the stairs", "the bridge", "the tunnel"], "example_phrases": {"0": ["Enter thee Cap'n wheel."], "1": ["Enter thee Cap'n wheel and take th' 1st spoke.", "Enter thee Cap'n wheel and take th' 2nd spoke.", "Enter thee Cap'n wheel and take th' 3rd spoke.", "Enter thee Cap'n wheel and take th' 4th spoke.", "Enter thee Cap'n wheel and take th' 5th spoke.", "Enter thee Cap'n wheel and take th' 6th spoke.", "Enter thee Cap'n wheel and take th' 7th spoke.", "Enter thee Cap'n wheel and take th' 8th spoke.", "Enter thee Cap'n wheel and take th' 9th spoke.", "Enter thee Cap'n wheel and take th' 10th spoke."], "2": ["Enter the roundabout and take the 3rd exit onto Main Street."], "3": ["Enter the roundabout and take the 3rd exit onto US 322/Main Street. Continue on US 322."], "4": ["Enter the roundabout and take the 3rd exit toward Baltimore."], "5": ["Enter the roundabout and take the exit onto Main Street."], "6": ["Enter the roundabout and take the exit onto US 322/Main Street. Continue on US 322."], "7": ["Enter the roundabout and take the exit toward Baltimore."], "8": ["Enter Dupont Circle."], "9": ["Enter Dupont Circle and take the 1st exit."], "10": ["Enter Dupont Circle and take the 3rd exit onto Main Street."], "11": ["Enter Dupont Circle and take the 3rd exit onto US 322/Main Street. Continue on US 322."], "12": ["Enter Dupont Circle and take the 3rd exit toward Baltimore."], "13": ["Enter Dupont Circle and take the exit onto Main Street."], "14": ["Enter Dupont Circle and take the exit onto US 322/Main Street. Continue on US 322."], "15": ["Enter Dupont Circle and take the exit toward Baltimore."]}}, "enter_roundabout_verbal": {"phrases": {"0": "Enter thee Cap'n wheel.", "1": "Enter thee Cap'n wheel and take th' <ORDINAL_VALUE> spoke.", "2": "Enter the roundabout and take the <ORDINAL_VALUE> exit onto <ROUNDABOUT_EXIT_STREET_NAMES>.", "3": "Enter the roundabout and take the <ORDINAL_VALUE> exit onto <ROUNDABOUT_EXIT_BEGIN_STREET_NAMES>.", "4": "Enter the roundabout and take the <ORDINAL_VALUE> exit toward <TOWARD_SIGN>.", "5": "Enter the roundabout and take the exit onto <ROUNDABOUT_EXIT_STREET_NAMES>.", "6": "Enter the roundabout and take the exit onto <ROUNDABOUT_EXIT_BEGIN_STREET_NAMES>.", "7": "Enter the roundabout and take the exit toward <TOWARD_SIGN>.", "8": "Enter <STREET_NAMES>", "9": "Enter <STREET_NAMES> and take the <ORDINAL_VALUE> exit.", "10": "Enter <STREET_NAMES> and take the <ORDINAL_VALUE> exit onto <ROUNDABOUT_EXIT_STREET_NAMES>.", "11": "Enter <STREET_NAMES> and take the <ORDINAL_VALUE> exit onto <ROUNDABOUT_EXIT_BEGIN_STREET_NAMES>.", "12": "Enter <STREET_NAMES> and take the <ORDINAL_VALUE> exit toward <TOWARD_SIGN>.", "13": "Enter <STREET_NAMES> and take the exit onto <ROUNDABOUT_EXIT_STREET_NAMES>.", "14": "Enter <STREET_NAMES> and take the exit onto <ROUNDABOUT_EXIT_BEGIN_STREET_NAMES>.", "15": "Enter <STREET_NAMES> and take the exit toward <TOWARD_SIGN>."}, "ordinal_values": ["1st", "2nd", "3rd", "4th", "5th", "6th", "7th", "8th", "9th", "10th"], "empty_street_name_labels": ["the walkway", "the cycleway", "the mountain bike trail", "the crosswalk", "the stairs", "the bridge", "the tunnel"], "example_phrases": {"0": ["Enter thee Cap'n wheel."], "1": ["Enter thee Cap'n wheel and take th' 1st spoke.", "Enter thee Cap'n wheel and take th' 2nd spoke.", "Enter thee Cap'n wheel and take th' 3rd spoke.", "Enter thee Cap'n wheel and take th' 4th spoke.", "Enter thee Cap'n wheel and take th' 5th spoke.", "Enter thee Cap'n wheel and take th' 6th spoke.", "Enter thee Cap'n wheel and take th' 7th spoke.", "Enter thee Cap'n wheel and take th' 8th spoke.", "Enter thee Cap'n wheel and take th' 9th spoke.", "Enter thee Cap'n wheel and take th' 10th spoke."], "2": ["Enter the roundabout and take the 3rd exit onto Main Street."], "3": ["Enter the roundabout and take the 3rd exit onto U.S. 3 22/Main Street."], "4": ["Enter the roundabout and take the 3rd exit toward Baltimore."], "5": ["Enter the roundabout and take the exit onto Main Street."], "6": ["Enter the roundabout and take the exit onto U.S. 3 22/Main Street."], "7": ["Enter the roundabout and take the exit toward Baltimore."], "8": ["Enter Dupont Circle."], "9": ["Enter Dupont Circle and take the 1st exit."], "10": ["Enter Dupont Circle and take the 3rd exit onto Main Street."], "11": ["Enter Dupont Circle and take the 3rd exit onto U.S. 3 22/Main Street."], "12": ["Enter Dupont Circle and take the 3rd exit toward Baltimore."], "13": ["Enter Dupont Circle and take the exit onto Main Street."], "14": ["Enter Dupont Circle and take the exit onto U.S. 3 22/Main Street."], "15": ["Enter Dupont Circle and take the exit toward Baltimore."]}}, "exit": {"phrases": {"0": "Blimey! Take thee exit on th' <RELATIVE_DIRECTION>.", "1": "Shiver me timbers! Take exit <NUMBER_SIGN> on th' <RELATIVE_DIRECTION>.", "2": "Blow me down! Take thee <BRANCH_SIGN> exit on th' <RELATIVE_DIRECTION>.", "3": "Yo ho ho! Take exit <NUMBER_SIGN> on th' <RELATIVE_DIRECTION> onto <BRANCH_SIGN>.", "4": "Sink me! Take thee exit on th' <RELATIVE_DIRECTION> toward <TOWARD_SIGN>.", "5": "Avast ye, take exit <NUMBER_SIGN> on th' <RELATIVE_DIRECTION> toward <TOWARD_SIGN>.", "6": "Blimey! Take thee <BRANCH_SIGN> exit on th' <RELATIVE_DIRECTION> toward <TOWARD_SIGN>.", "7": "Sink me! Take exit <NUMBER_SIGN> on th' <RELATIVE_DIRECTION> onto <BRANCH_SIGN> toward <TOWARD_SIGN>.", "8": "Blow me down! Take thee <NAME_SIGN> exit on th' <RELATIVE_DIRECTION>.", "10": "Blow me down! Take thee <NAME_SIGN> exit on th' <RELATIVE_DIRECTION> onto <BRANCH_SIGN>.", "12": "Blow me down! Take thee <NAME_SIGN> exit on th' <RELATIVE_DIRECTION> toward <TOWARD_SIGN>.", "14": "Blow me down! Take thee <NAME_SIGN> exit on th' <RELATIVE_DIRECTION> onto <BRANCH_SIGN> toward <TOWARD_SIGN>.", "15": "Blimey! Take thee exit.", "16": "Shiver me timbers! Take exit <NUMBER_SIGN>.", "17": "Blow me down! Take thee <BRANCH_SIGN> exit.", "18": "Yo ho ho! Take exit <NUMBER_SIGN> onto <BRANCH_SIGN>.", "19": "Sink me! Take thee exit toward <TOWARD_SIGN>.", "20": "Avast ye, take exit <NUMBER_SIGN> toward <TOWARD_SIGN>.", "21": "Blimey! Take thee <BRANCH_SIGN> exit toward <TOWARD_SIGN>.", "22": "Sink me! Take exit <NUMBER_SIGN> onto <BRANCH_SIGN> toward <TOWARD_SIGN>.", "23": "Blow me down! Take thee <NAME_SIGN> exit.", "25": "Blow me down! Take thee <NAME_SIGN> exit onto <BRANCH_SIGN>.", "27": "Blow me down! Take thee <NAME_SIGN> exit toward <TOWARD_SIGN>.", "29": "Blow me down! Take thee <NAME_SIGN> exit onto <BRANCH_SIGN> toward <TOWARD_SIGN>."}, "relative_directions": ["port", "starboard"], "example_phrases": {"0": ["Blimey! Take thee exit on th' port.", "Take thee exit on th' starboard."], "1": ["Shiver me timbers! Take exit 67 B-A on th' starboard."], "2": ["Blow me down! Take thee US 322 West exit on th' starboard."], "3": ["Yo ho ho! Take exit 67 B-A on th' starboard onto US 322 West."], "4": ["Sink me! Take thee exit on th' starboard toward Lewistown."], "5": ["Avast ye, take exit 67 B-A on th' starboard toward Lewistown."], "6": ["Blimey! Take thee US 322 West exit on th' starboard toward Lewistown."], "7": ["Sink me! Take exit 67 B-A on th' starboard onto US 322 West toward Lewistown/State College."], "8": ["Blow me down! Take thee White Marsh Boulevard exit on th' port."], "10": ["Blow me down! Take thee White Marsh Boulevard exit on th' port onto MD 43 East."], "12": ["Blow me down! Take thee White Marsh Boulevard exit on th' port toward White Marsh."], "14": ["Blow me down! Take thee White Marsh Boulevard exit on th' port onto MD 43 East toward White Marsh."], "15": ["Blimey! Take thee exit."], "16": ["Shiver me timbers! Take exit 67 B-A."], "17": ["Blow me down! Take thee US 322 West exit."], "18": ["Yo ho ho! Take exit 67 B-A onto US 322 West."], "19": ["Sink me! Take thee exit toward Lewistown."], "20": ["Avast ye, take exit 67 B-A toward Lewistown."], "21": ["Blimey! Take thee US 322 West exit toward Lewistown."], "22": ["Sink me! Take exit 67 B-A onto US 322 West toward Lewistown/State College."], "23": ["Blow me down! Take thee White Marsh Boulevard exit."], "25": ["Blow me down! Take thee White Marsh Boulevard exit onto MD 43 East."], "27": ["Blow me down! Take thee White Marsh Boulevard exit toward White Marsh."], "29": ["Blow me down! Take thee White Marsh Boulevard exit onto MD 43 East toward White Marsh."]}}, "exit_roundabout": {"phrases": {"0": "Exit thee wheel.", "1": "Exit thee wheel onto <STREET_NAMES>.", "2": "Exit thee wheel onto <BEGIN_STREET_NAMES>. Aye, continue on <STREET_NAMES>.", "3": "Exit the roundabout toward <TOWARD_SIGN>."}, "empty_street_name_labels": ["solid ground", "th' bike pirate way", "th' treasure trail", "the crosswalk", "the stairs", "the bridge", "the tunnel"], "example_phrases": {"0": ["Exit thee wheel."], "1": ["Exit thee wheel onto Philadelphia Road/MD 7."], "2": ["Exit thee wheel onto Catoctin Mountain Highway/US 15. Aye, continue on US 15."], "3": ["Exit the roundabout toward Baltimore."]}}, "exit_roundabout_verbal": {"phrases": {"0": "Exit thee wheel.", "1": "Exit thee wheel onto <STREET_NAMES>.", "2": "Exit thee wheel onto <BEGIN_STREET_NAMES>.", "3": "Exit the roundabout toward <TOWARD_SIGN>."}, "empty_street_name_labels": ["solid ground", "th' bike pirate way", "th' treasure trail", "the crosswalk", "the stairs", "the bridge", "the tunnel"], "example_phrases": {"0": ["Exit thee wheel."], "1": ["Exit thee wheel onto Philadelphia Road, Maryland 7."], "2": ["Exit thee wheel onto Catoctin Mountain Highway, U.S. 15."], "3": ["Exit the roundabout toward Baltimore."]}}, "exit_verbal": {"phrases": {"0": "Blimey! Take thee exit on th' <RELATIVE_DIRECTION>.", "1": "Shiver me timbers! Take exit <NUMBER_SIGN> on th' <RELATIVE_DIRECTION>.", "2": "Blow me down! Take thee <BRANCH_SIGN> exit on th' <RELATIVE_DIRECTION>.", "3": "Yo ho ho! Take exit <NUMBER_SIGN> on th' <RELATIVE_DIRECTION> onto <BRANCH_SIGN>.", "4": "Sink me! Take thee exit on th' <RELATIVE_DIRECTION> toward <TOWARD_SIGN>.", "5": "Avast ye, take exit <NUMBER_SIGN> on th' <RELATIVE_DIRECTION> toward <TOWARD_SIGN>.", "6": "Blimey! Take thee <BRANCH_SIGN> exit on th' <RELATIVE_DIRECTION> toward <TOWARD_SIGN>.", "7": "Sink me! Take exit <NUMBER_SIGN> on th' <RELATIVE_DIRECTION> onto <BRANCH_SIGN> toward <TOWARD_SIGN>.", "8": "Blow me down! Take thee <NAME_SIGN> exit on th' <RELATIVE_DIRECTION>.", "10": "Blow me down! Take thee <NAME_SIGN> exit on th' <RELATIVE_DIRECTION> onto <BRANCH_SIGN>.", "12": "Blow me down! Take thee <NAME_SIGN> exit on th' <RELATIVE_DIRECTION> toward <TOWARD_SIGN>.", "14": "Blow me down! Take thee <NAME_SIGN> exit on th' <RELATIVE_DIRECTION> onto <BRANCH_SIGN> toward <TOWARD_SIGN>.", "15": "Blimey! Take thee exit.", "16": "Shiver me timbers! Take exit <NUMBER_SIGN>.", "17": "Blow me down! Take thee <BRANCH_SIGN> exit.", "18": "Yo ho ho! Take exit <NUMBER_SIGN> onto <BRANCH_SIGN>.", "19": "Sink me! Take thee exit toward <TOWARD_SIGN>.", "20": "Avast ye, take exit <NUMBER_SIGN> toward <TOWARD_SIGN>.", "21": "Blimey! Take thee <BRANCH_SIGN> exit toward <TOWARD_SIGN>.", "22": "Sink me! Take exit <NUMBER_SIGN> onto <BRANCH_SIGN> toward <TOWARD_SIGN>.", "23": "Blow me down! Take thee <NAME_SIGN> exit.", "25": "Blow me down! Take thee <NAME_SIGN> exit onto <BRANCH_SIGN>.", "27": "Blow me down! Take thee <NAME_SIGN> exit toward <TOWARD_SIGN>.", "29": "Blow me down! Take thee <NAME_SIGN> exit onto <BRANCH_SIGN> toward <TOWARD_SIGN>."}, "relative_directions": ["port", "starboard"], "example_phrases": {"0": ["Blimey! Take thee exit on th' port.", "Blimey! Take thee exit on th' starboard."], "1": ["Shiver me timbers! Take exit 67 B-A on th' starboard."], "2": ["Blow me down! Take thee U.S. 3 22 West exit on th' starboard."], "3": ["Yo ho ho! Take exit 67 B-A on th' starboard onto U.S. 3 22 West."], "4": ["Sink me! Take thee exit on th' starboard toward Lewistown."], "5": ["Avast ye, take exit 67 B-A on th' starboard toward Lewistown."], "6": ["Blimey! Take thee U.S. 3 22 West exit on th' starboard toward Lewistown."], "7": ["Sink me! Take exit 67 B-A on th' starboard onto U.S. 3 22 West toward Lewistown, State College."], "8": ["Blow me down! Take thee White Marsh Boulevard exit on th' port."], "10": ["Blow me down! Take thee White Marsh Boulevard exit on th' port onto Maryland 43 East."], "12": ["Blow me down! Take thee White Marsh Boulevard exit on th' port toward White Marsh."], "14": ["Blow me down! Take thee White Marsh Boulevard exit on th' port onto Maryland 43 East toward White Marsh."], "15": ["Blimey! Take thee exit."], "16": ["Shiver me timbers! Take exit 67 B-A."], "17": ["Blow me down! Take thee US 322 West exit."], "18": ["Yo ho ho! Take exit 67 B-A onto US 322 West."], "19": ["Sink me! Take thee exit exit toward Lewistown."], "20": ["Avast ye, take exit 67 B-A toward Lewistown."], "21": ["Blimey! Take thee US 322 West exit toward Lewistown."], "22": ["Sink me! Take exit 67 B-A onto US 322 West toward Lewistown/State College."], "23": ["Blow me down! Take thee White Marsh Boulevard exit."], "25": ["Blow me down! Take thee White Marsh Boulevard exit onto MD 43 East."], "27": ["Blow me down! Take thee White Marsh Boulevard exit toward White Marsh."], "29": ["Blow me down! Take thee White Marsh Boulevard exit onto MD 43 East toward White Marsh."]}}, "exit_visual": {"phrases": {"0": "Exit <EXIT_NUMBERS>"}, "example_phrases": {"0": ["Exit 1A"]}}, "keep": {"phrases": {"0": "Keep <RELATIVE_DIRECTION> at the fork me hearties.", "1": "Keep <RELATIVE_DIRECTION> ye landlubber to take exit <NUMBER_SIGN>.", "2": "Keep <RELATIVE_DIRECTION> me buccaneer to take <STREET_NAMES>.", "3": "Keep <RELATIVE_DIRECTION> ye scallywag to take exit <NUMBER_SIGN> onto <STREET_NAMES>.", "4": "Keep <RELATIVE_DIRECTION> me bucko toward <TOWARD_SIGN>.", "5": "Keep <RELATIVE_DIRECTION> ye scurvy dog to take exit <NUMBER_SIGN> toward <TOWARD_SIGN>.", "6": "Keep <RELATIVE_DIRECTION> me hearties to take <STREET_NAMES> toward <TOWARD_SIGN>.", "7": "Keep <RELATIVE_DIRECTION> ye landlubber to take exit <NUMBER_SIGN> onto <STREET_NAMES> toward <TOWARD_SIGN>."}, "empty_street_name_labels": ["solid ground", "th' bike pirate way", "th' treasure trail", "the crosswalk", "the stairs", "the bridge", "the tunnel"], "relative_directions": ["left", "straight", "right"], "example_phrases": {"0": ["Keep left at the fork me hearties.", "Keep straight at the fork me hearties.", "Keep right at the fork me hearties."], "1": ["Keep right ye landlubber to take exit 62."], "2": ["Keep right me buccaneer to take I 895 South."], "3": ["Keep right ye scallywag to take exit 62 onto I 895 South."], "4": ["Keep right me bucko toward Annapolis."], "5": ["Keep right ye scurvy dog to take exit 62 toward Annapolis."], "6": ["Keep right me hearties to take I 895 South toward Annapolis."], "7": ["Keep right ye landlubber to take exit 62 onto I 895 South toward Annapolis."]}}, "keep_to_stay_on": {"phrases": {"0": "Keep <RELATIVE_DIRECTION> me hearties to stay on <STREET_NAMES>.", "1": "Keep <RELATIVE_DIRECTION> ye landlubber to take exit <NUMBER_SIGN> to stay on <STREET_NAMES>.", "2": "Keep <RELATIVE_DIRECTION> me bucko to stay on <STREET_NAMES> toward <TOWARD_SIGN>.", "3": "Keep <RELATIVE_DIRECTION> ye scallywag to take exit <NUMBER_SIGN> to stay on <STREET_NAMES> toward <TOWARD_SIGN>."}, "empty_street_name_labels": ["solid ground", "th' bike pirate way", "th' treasure trail", "the crosswalk", "the stairs", "the bridge", "the tunnel"], "relative_directions": ["left", "straight", "right"], "example_phrases": {"0": ["Keep left me hearties to stay on I 95 South.", "Keep straight me hearties to stay on I 95 South.", "Keep right me hearties to stay on I 95 South."], "1": ["Keep left ye landlubber to take exit 62 to stay on I 95 South."], "2": ["Keep left me bucko to stay on I 95 South toward Baltimore."], "3": ["Keep left ye scallywag to take exit 62 to stay on I 95 South toward Baltimore."]}}, "keep_to_stay_on_verbal": {"phrases": {"0": "Keep <RELATIVE_DIRECTION> me hearties to stay on <STREET_NAMES>.", "1": "Keep <RELATIVE_DIRECTION> ye landlubber to take exit <NUMBER_SIGN> to stay on <STREET_NAMES>.", "2": "Keep <RELATIVE_DIRECTION> me bucko to stay on <STREET_NAMES> toward <TOWARD_SIGN>.", "3": "Keep <RELATIVE_DIRECTION> ye scallywag to take exit <NUMBER_SIGN> to stay on <STREET_NAMES> toward <TOWARD_SIGN>."}, "empty_street_name_labels": ["solid ground", "th' bike pirate way", "th' treasure trail", "the crosswalk", "the stairs", "the bridge", "the tunnel"], "relative_directions": ["left", "straight", "right"], "example_phrases": {"0": ["Keep left me hearties to stay on Interstate 95 South.", "Keep straight me hearties to stay on Interstate 95 South.", "Keep right me hearties to stay on Interstate 95 South."], "1": ["Keep left ye landlubber to take exit 62 to stay on Interstate 95 South."], "2": ["Keep left me bucko to stay on I 95 South toward Baltimore."], "3": ["Keep left ye scallywag to take exit 62 to stay on Interstate 95 South toward Baltimore."]}}, "keep_verbal": {"phrases": {"0": "Keep <RELATIVE_DIRECTION> at the fork me hearties.", "1": "Keep <RELATIVE_DIRECTION> ye landlubber to take exit <NUMBER_SIGN>.", "2": "Keep <RELATIVE_DIRECTION> me buccaneer to take <STREET_NAMES>.", "3": "Keep <RELATIVE_DIRECTION> ye scallywag to take exit <NUMBER_SIGN> onto <STREET_NAMES>.", "4": "Keep <RELATIVE_DIRECTION> me bucko toward <TOWARD_SIGN>.", "5": "Keep <RELATIVE_DIRECTION> ye scurvy dog to take exit <NUMBER_SIGN> toward <TOWARD_SIGN>.", "6": "Keep <RELATIVE_DIRECTION> me hearties to take <STREET_NAMES> toward <TOWARD_SIGN>.", "7": "Keep <RELATIVE_DIRECTION> ye landlubber to take exit <NUMBER_SIGN> onto <STREET_NAMES> toward <TOWARD_SIGN>."}, "empty_street_name_labels": ["solid ground", "th' bike pirate way", "th' treasure trail", "the crosswalk", "the stairs", "the bridge", "the tunnel"], "relative_directions": ["left", "straight", "right"], "example_phrases": {"0": ["Keep left at the fork me hearties.", "Keep straight at the fork me hearties.", "Keep right at the fork me hearties."], "1": ["Keep right ye landlubber to take exit 62."], "2": ["Keep right me buccaneer to take Interstate 8 95 South."], "3": ["Keep right ye scallywag to take exit 62 onto Interstate 8 95 South."], "4": ["Keep right me bucko toward Annapolis."], "5": ["Keep right ye scurvy dog to take exit 62 toward Annapolis."], "6": ["Keep right me hearties to take Interstate 8 95 South toward Annapolis."], "7": ["Keep right ye landlubber to take exit 62 onto Interstate 8 95 South toward Annapolis."]}}, "merge": {"phrases": {"0": "Avast ye, merge.", "1": "Avast ye, merge to th' <RELATIVE_DIRECTION>.", "2": "Merge onto <STREET_NAMES> me matey.", "3": "Merge to th' <RELATIVE_DIRECTION> onto <STREET_NAMES> me matey.", "4": "Merge toward <TOWARD_SIGN>.", "5": "Merge <RELATIVE_DIRECTION> toward <TOWARD_SIGN>."}, "relative_directions": ["port", "starboard"], "empty_street_name_labels": ["solid ground", "th' bike pirate way", "th' treasure trail", "the crosswalk", "the stairs", "the bridge", "the tunnel"], "example_phrases": {"0": ["Avast ye, merge."], "1": ["Avast ye, merge to th' port."], "2": ["Merge onto I 76 West/Pennsylvania Turnpike me matey."], "3": ["Merge to th' starboard onto I 83 South me matey."], "4": ["Merge toward Baltimore."], "5": ["Merge right toward Baltimore."]}}, "merge_verbal": {"phrases": {"0": "Avast ye, merge.", "1": "Avast ye, merge to th' <RELATIVE_DIRECTION>.", "2": "Merge onto <STREET_NAMES> me matey.", "3": "Merge to th' <RELATIVE_DIRECTION> onto <STREET_NAMES> me matey.", "4": "Merge toward <TOWARD_SIGN>.", "5": "Merge <RELATIVE_DIRECTION> toward <TOWARD_SIGN>."}, "relative_directions": ["port", "starboard"], "empty_street_name_labels": ["solid ground", "th' bike pirate way", "th' treasure trail", "the crosswalk", "the stairs", "the bridge", "the tunnel"], "example_phrases": {"0": ["Merge."], "1": ["Avast ye, merge to th' port."], "2": ["Merge onto I 76 West/Pennsylvania Turnpike me matey."], "3": ["Merge to th' starboard onto I 83 South me matey."], "4": ["Merge toward Baltimore."], "5": ["Merge right toward Baltimore."]}}, "post_transition_transit_verbal": {"phrases": {"0": "Voyage <TRANSIT_STOP_COUNT> <TRANSIT_STOP_COUNT_LABEL>."}, "transit_stop_count_labels": {"one": "port", "other": "ports"}, "example_phrases": {"0": ["Travel 1 stop.", "Travel 3 stops."]}}, "post_transition_verbal": {"phrases": {"0": "Aye, continue fer <LENGTH>.", "1": "Avast ye, continue on <STREET_NAMES> fer <LENGTH>."}, "empty_street_name_labels": ["solid ground", "th' bike pirate way", "th' treasure trail", "the crosswalk", "the stairs", "the bridge", "the tunnel"], "metric_lengths": ["<KILOMETERS> kilometers", "1 kilometer", "<METERS> meters", "less than 10 meters"], "us_customary_lengths": ["<MILES> miles", "1 mile", "a half mile", "a quarter mile", "<FEET> feet", "less than 10 feet"], "example_phrases": {"0": ["Aye, continue fer 300 feet.", "Aye, continue fer 9 miles."], "1": ["Avast ye, continue on Pennsylvania 7 43 fer 6.2 miles.", "Avast ye, continue on Main Street, Vermont 30 fer 1 tenth of a mile."]}}, "ramp": {"phrases": {"0": "Take thee gangplank on th' <RELATIVE_DIRECTION>.", "1": "Take thee <BRANCH_SIGN> gangplank on th' <RELATIVE_DIRECTION>.", "2": "Take thee gangplank on th' <RELATIVE_DIRECTION> toward <TOWARD_SIGN>.", "3": "Take thee <BRANCH_SIGN> gangplank on th' <RELATIVE_DIRECTION> toward <TOWARD_SIGN>.", "4": "Take thee <NAME_SIGN> gangplank on th' <RELATIVE_DIRECTION>.", "5": "Turn <RELATIVE_DIRECTION> to take th' gangplank.", "6": "Turn <RELATIVE_DIRECTION> to take th' <BRANCH_SIGN> gangplank.", "7": "Turn <RELATIVE_DIRECTION> to take th' gangplank toward <TOWARD_SIGN>.", "8": "Turn <RELATIVE_DIRECTION> to take th' <BRANCH_SIGN> gangplank toward <TOWARD_SIGN>.", "9": "Turn <RELATIVE_DIRECTION> to take th' <NAME_SIGN> gangplank.", "10": "Take thee gangplank.", "11": "Take thee <BRANCH_SIGN> gangplank.", "12": "Take thee gangplank toward <TOWARD_SIGN>.", "13": "Take thee <BR<PERSON>CH_SIGN> gangplank toward <TOWARD_SIGN>.", "14": "Take thee <NAME_SIGN> gangplank."}, "relative_directions": ["port", "starboard"], "example_phrases": {"0": ["Take thee gangplank on th' port.", "Take thee gangplank on th' starboard."], "1": ["Take thee I 95 gangplank on th' starboard."], "2": ["Take thee gangplank on th' port toward JFK."], "3": ["Take thee South Conduit Avenue gangplank on th' port toward JFK."], "4": ["Take thee Gettysburg Pike gangplank on th' starboard."], "5": ["Turn port to take th' gangplank.", "Turn starboard to take th' gangplank."], "6": ["Turn port to take th' PA 283 West gangplank."], "7": ["Turn port to take th' gangplank toward Harrisburg/Harrisburg International Airport."], "8": ["Turn port to take th' PA 283 West gangplank toward Harrisburg/Harrisburg International Airport."], "9": ["Turn starboard to take th' Gettysburg Pike gangplank."], "10": ["Take thee gangplank."], "11": ["Take thee I 95 gangplank."], "12": ["Take thee gangplank toward JFK."], "13": ["Take thee Soutch Conduit Avenue gangplank toward JFK."], "14": ["Take thee Gettysburg gangplank."]}}, "ramp_straight": {"phrases": {"0": "Keep yer head'n straight to take th' gangplank.", "1": "Keep yer head'n straight to take th' <BRANCH_SIGN> gangplank.", "2": "Keep yer head'n straight to take th' gangplank toward <TOWARD_SIGN>.", "3": "Keep yer head'n straight to take th' <BRANCH_SIGN> gangplank toward <TOWARD_SIGN>.", "4": "Keep yer head'n straight to take th' <NAME_SIGN> gangplank."}, "example_phrases": {"0": ["Keep yer head'n straight to take th' gangplank."], "1": ["Keep yer head'n straight to take th' US 322 East gangplank."], "2": ["Keep yer head'n straight to take th' gangplank toward Hershey."], "3": ["Keep yer head'n straight to take th' US 322 East/US 422 East/US 522 East/US 622 East gangplank toward Hershey/Palmdale/Palmyra/Campbelltown."], "4": ["Keep yer head'n straight to take th' Gettysburg Pike gangplank."]}}, "ramp_straight_verbal": {"phrases": {"0": "Keep yer head'n straight to take th' gangplank.", "1": "Keep yer head'n straight to take th' <BRANCH_SIGN> gangplank.", "2": "Keep yer head'n straight to take th' gangplank toward <TOWARD_SIGN>.", "3": "Keep yer head'n straight to take th' <BRANCH_SIGN> gangplank toward <TOWARD_SIGN>.", "4": "Keep yer head'n straight to take th' <NAME_SIGN> gangplank."}, "example_phrases": {"0": ["Keep yer head'n straight to take th' gangplank."], "1": ["Keep yer head'n straight to take th' U.S. 3 22 East gangplank."], "2": ["Keep yer head'n straight to take th' gangplank toward Hershey."], "3": ["Keep yer head'n straight to take th' U.S. 3 22 East, U.S. 4 22 East gangplank toward Hershey, Palmdale."], "4": ["Keep yer head'n straight to take th' Gettysburg Pike gangplank."]}}, "ramp_verbal": {"phrases": {"0": "Take thee gangplank on th' <RELATIVE_DIRECTION>.", "1": "Take thee <BRANCH_SIGN> gangplank on th' <RELATIVE_DIRECTION>.", "2": "Take thee gangplank on th' <RELATIVE_DIRECTION> toward <TOWARD_SIGN>.", "3": "Take thee <BRANCH_SIGN> gangplank on th' <RELATIVE_DIRECTION> toward <TOWARD_SIGN>.", "4": "Take thee <NAME_SIGN> gangplank on th' <RELATIVE_DIRECTION>.", "5": "Turn <RELATIVE_DIRECTION> to take th' gangplank.", "6": "Turn <RELATIVE_DIRECTION> to take th' <BRANCH_SIGN> gangplank.", "7": "Turn <RELATIVE_DIRECTION> to take th' gangplank toward <TOWARD_SIGN>.", "8": "Turn <RELATIVE_DIRECTION> to take th' <BRANCH_SIGN> gangplank toward <TOWARD_SIGN>.", "9": "Turn <RELATIVE_DIRECTION> to take th' <NAME_SIGN> gangplank.", "10": "Take thee gangplank.", "11": "Take thee <BRANCH_SIGN> gangplank.", "12": "Take thee gangplank toward <TOWARD_SIGN>.", "13": "Take thee <BR<PERSON>CH_SIGN> gangplank toward <TOWARD_SIGN>.", "14": "Take thee <NAME_SIGN> gangplank."}, "relative_directions": ["port", "starboard"], "example_phrases": {"0": ["Take thee gangplank on th' port.", "Take thee gangplank on th' starboard."], "1": ["Take thee Interstate 95 gangplank on th' starboard."], "2": ["Take thee gangplank on th' port toward JFK."], "3": ["Take thee South Conduit Avenue gangplank on th' port toward JFK."], "4": ["Take thee Gettysburg Pike gangplank on th' starboard."], "5": ["Turn port to take th' gangplank.", "Turn starboard to take th' gangplank."], "6": ["Turn port to take th' Pennsylvania 2 83 West gangplank."], "7": ["Turn port to take th' gangplank toward Harrisburg/Harrisburg International Airport."], "8": ["Turn port to take th' Pennsylvania 2 83 West gangplank toward Harrisburg, Harrisburg International Airport."], "9": ["Turn starboard to take th' Gettysburg Pike gangplank."], "10": ["Take thee gangplank."], "11": ["Take thee Interstate 95 gangplank."], "12": ["Take thee gangplank toward JFK."], "13": ["Take thee South Conduit Avenue gangplank toward JFK."], "14": ["Take thee Gettysburg Pike gangplank."]}}, "sharp": {"phrases": {"0": "Make a sharp <RELATIVE_DIRECTION> bucko.", "1": "Make a sharp <RELATIVE_DIRECTION> onto <STREET_NAMES> ye scallywag.", "2": "Arrr, make a sharp <RELATIVE_DIRECTION> onto <BEGIN_STREET_NAMES>. Continue on <STREET_NAMES>.", "3": "Make a sharp <RELATIVE_DIRECTION> matey to stay on <STREET_NAMES>.", "4": "Make a sharp <RELATIVE_DIRECTION> at <JUNCTION_NAME>.", "5": "Make a sharp <RELATIVE_DIRECTION> toward <TOWARD_SIGN>."}, "empty_street_name_labels": ["solid ground", "th' bike pirate way", "th' treasure trail", "the crosswalk", "the stairs", "the bridge", "the tunnel"], "relative_directions": ["left", "right"], "example_phrases": {"0": ["Make a sharp left bucko."], "1": ["Make a sharp right onto Flatbush Avenue ye scallywag."], "2": ["Arrr, make a sharp left onto North Bond Street/US 1 Business/MD 924. Continue on MD 924."], "3": ["Make a sharp right matey to stay on Sunstone Drive."], "4": ["Make a sharp right at Mannenbashi East."], "5": ["Make a sharp left toward Baltimore."]}}, "sharp_verbal": {"phrases": {"0": "Make a sharp <RELATIVE_DIRECTION> bucko.", "1": "Make a sharp <RELATIVE_DIRECTION> onto <STREET_NAMES> ye scallywag.", "2": "Arrr, make a sharp <RELATIVE_DIRECTION> onto <BEGIN_STREET_NAMES>.", "3": "Make a sharp <RELATIVE_DIRECTION> matey to stay on <STREET_NAMES>.", "4": "Make a sharp <RELATIVE_DIRECTION> at <JUNCTION_NAME>.", "5": "Make a sharp <RELATIVE_DIRECTION> toward <TOWARD_SIGN>."}, "empty_street_name_labels": ["solid ground", "th' bike pirate way", "th' treasure trail", "the crosswalk", "the stairs", "the bridge", "the tunnel"], "relative_directions": ["left", "right"], "example_phrases": {"0": ["Make a sharp left bucko."], "1": ["Make a sharp right onto Flatbush Avenue ye scallywag."], "2": ["Arrr, make a sharp left onto North Bond Street, U.S. 1 Business."], "3": ["Make a sharp right matey to stay on Sunstone Drive."], "4": ["Make a sharp right at Mannenbashi East."], "5": ["Make a sharp left toward Baltimore."]}}, "start": {"phrases": {"0": "Head <CARDINAL_DIRECTION>.", "1": "Head <CARDINAL_DIRECTION> on <STREET_NAMES>.", "2": "Head <CARDINAL_DIRECTION> on <BEGIN_STREET_NAMES>. Continue on <STREET_NAMES>.", "4": "Set sail <CARDINAL_DIRECTION>.", "5": "Point yer bow <CARDINAL_DIRECTION> and set sail on <STREET_NAMES>.", "6": "Weigh anchor! Set sail <CARDINAL_DIRECTION> on <BEGIN_STREET_NAMES>. Continue on <STREET_NAMES>.", "8": "Saunter <CARDINAL_DIRECTION> on solid ground.", "9": "Saunter <CARDINAL_DIRECTION> on <STREET_NAMES>.", "10": "Saunter <CARDINAL_DIRECTION> on <BEGIN_STREET_NAMES>. Continue on <STREET_NAMES>.", "16": "Ride <CARDINAL_DIRECTION>.", "17": "Ride <CARDINAL_DIRECTION> on <STREET_NAMES>.", "18": "Ride <CARDINAL_DIRECTION> on <BEGIN_STREET_NAMES>. Continue on <STREET_NAMES>."}, "cardinal_directions": ["north", "northeast", "east", "southeast", "south", "southwest", "west", "northwest"], "empty_street_name_labels": ["solid ground", "th' bike pirate way", "th' treasure trail", "the crosswalk", "the stairs", "the bridge", "the tunnel"], "example_phrases": {"0": ["Head east.", "Head north."], "1": ["Head southwest on 5th Avenue.", "Head west on solid ground", "Head east on th' bike pirate way", "Head north on th' treasure trail"], "2": ["Head south on North Prince Street/US 222/PA 272. Continue on US 222/PA 272."], "4": ["Set sail east.", "Set sail north."], "5": ["Point yer bow southwest and set sail on 5th Avenue."], "6": ["Weigh anchor! Set sail south on North Prince Street/US 222/PA 272. Continue on US 222/PA 272."], "8": ["Saunter east on solid ground.", "Saunter north on solid ground."], "9": ["Saunter southwest on 5th Avenue.", "Saunter west on solid ground"], "10": ["Saunter south on North Prince Street/US 222/PA 272. Continue on US 222/PA 272."], "16": ["Ride east.", "Ride north."], "17": ["Ride southwest on 5th Avenue.", "Ride east on th' bike pirate way", "Ride north on th' treasure trail"], "18": ["Ride south on North Prince Street/US 222/PA 272. Continue on US 222/PA 272."]}}, "start_verbal": {"phrases": {"0": "Head <CARDINAL_DIRECTION>.", "1": "Head <CARDINAL_DIRECTION> fer <LENGTH>.", "2": "Head <CARDINAL_DIRECTION> on <STREET_NAMES>.", "3": "Head <CARDINAL_DIRECTION> on <STREET_NAMES> fer <LENGTH>.", "4": "Head <CARDINAL_DIRECTION> on <BEGIN_STREET_NAMES>.", "5": "Set sail <CARDINAL_DIRECTION>.", "6": "Set sail <CARDINAL_DIRECTION> fer <LENGTH>.", "7": "Point yer bow <CARDINAL_DIRECTION> and set sail on <STREET_NAMES>.", "8": "Point yer bow <CARDINAL_DIRECTION> and set sail on <STREET_NAMES> fer <LENGTH>.", "9": "Weigh anchor! Set sail <CARDINAL_DIRECTION> on <BEGIN_STREET_NAMES>.", "10": "Saunter <CARDINAL_DIRECTION>.", "11": "Saunter <CARDINAL_DIRECTION> fer <LENGTH>.", "12": "Saunter <CARDINAL_DIRECTION> on <STREET_NAMES>.", "13": "Saunter <CARDINAL_DIRECTION> on <STREET_NAMES> fer <LENGTH>.", "14": "Saunter <CARDINAL_DIRECTION> on <BEGIN_STREET_NAMES>.", "15": "Ride <CARDINAL_DIRECTION>.", "16": "Ride <CARDINAL_DIRECTION> fer <LENGTH>.", "17": "Ride <CARDINAL_DIRECTION> on <STREET_NAMES>.", "18": "Ride <CARDINAL_DIRECTION> on <STREET_NAMES> fer <LENGTH>.", "19": "Ride <CARDINAL_DIRECTION> on <BEGIN_STREET_NAMES>."}, "cardinal_directions": ["north", "northeast", "east", "southeast", "south", "southwest", "west", "northwest"], "empty_street_name_labels": ["solid ground", "th' bike pirate way", "th' treasure trail", "the crosswalk", "the stairs", "the bridge", "the tunnel"], "metric_lengths": ["<KILOMETERS> kilometers", "1 kilometer", "<METERS> meters", "less than 10 meters"], "us_customary_lengths": ["<MILES> miles", "1 mile", "a half mile", "a quarter mile", "<FEET> feet", "less than 10 feet"], "example_phrases": {"0": ["Head east.", "Head north."], "1": ["Head east for a half mile.", "Head north for 1 kilometer."], "2": ["Head southwest on 5th Avenue."], "3": ["Head southwest on 5th Avenue for 1 tenth of a mile."], "4": ["Head south on North Prince Street, U.S. 2 22."], "5": ["Drive east.", "Drive north."], "6": ["Drive east for a half mile.", "Drive north for 1 kilometer."], "7": ["Drive southwest on 5th Avenue."], "8": ["Drive southwest on 5th Avenue for 1 tenth of a mile."], "9": ["Drive south on North Prince Street, U.S. 2 22."], "10": ["Walk east.", "Walk north."], "11": ["Walk east for a half mile.", "Walk north for 1 kilometer."], "12": ["Walk southwest on 5th Avenue."], "13": ["Walk southwest on 5th Avenue for 1 tenth of a mile."], "14": ["Walk south on North Prince Street, U.S. 2 22."], "15": ["Bike east.", "Bike north."], "16": ["Bike east for a half mile.", "Bike north for 1 kilometer."], "17": ["Bike southwest on 5th Avenue."], "18": ["Bike southwest on 5th Avenue for 1 tenth of a mile."], "19": ["Bike south on North Prince Street, U.S. 2 22."]}}, "transit": {"phrases": {"0": "Aye, take thee <TRANSIT_NAME>. (<TRANSIT_STOP_COUNT> <TRANSIT_STOP_COUNT_LABEL>)", "1": "Heave ho on thee <TRANSIT_NAME> toward <TRANSIT_HEADSIGN>. (<TRANSIT_STOP_COUNT> <TRANSIT_STOP_COUNT_LABEL>)"}, "empty_transit_name_labels": ["tram", "metro", "train", "bus", "ferry", "cable car", "gondola", "funicular"], "transit_stop_count_labels": {"one": "port", "other": "ports"}, "example_phrases": {"0": ["Aye, take thee New Haven. (1 port)", "Aye, take thee metro. (2 ports)", "Aye, take thee bus. (12 ports)"], "1": ["Heave ho on thee F toward JAMAICA - 179 ST. (10 ports)", "Heave ho on thee ferry toward Staten Island. (1 port)"]}}, "transit_connection_destination": {"phrases": {"0": "Sink me! Exit thee station ye scallywag.", "1": "Avast ye, exit thee <TRANSIT_STOP>.", "2": "Exit thee <TRANSIT_STOP> <STATION_LABEL> me buccaneer."}, "station_label": "Station", "example_phrases": {"0": ["Sink me! Exit thee station ye scallywag."], "1": ["Avast ye, exit thee Embarcadero Station."], "2": ["Exit thee 8 St - NYU Station me buccaneer."]}}, "transit_connection_destination_verbal": {"phrases": {"0": "Sink me! Exit thee station ye scallywag.", "1": "Avast ye, exit thee <TRANSIT_STOP>.", "2": "Exit thee <TRANSIT_STOP> <STATION_LABEL> me buccaneer."}, "station_label": "Station", "example_phrases": {"0": ["Sink me! Exit thee station ye scallywag."], "1": ["Avast ye, exit thee Embarcadero Station."], "2": ["Exit thee 8 St - NYU Station me buccaneer."]}}, "transit_connection_start": {"phrases": {"0": "Shiver me timbers! Enter thee station ye landlubber.", "1": "Yo ho ho! Enter thee <TRANSIT_STOP>.", "2": "Enter thee <TRANSIT_STOP> <STATION_LABEL> me hearties."}, "station_label": "Station", "example_phrases": {"0": ["Shiver me timbers! Enter thee station ye landlubber."], "1": ["Yo ho ho! Enter thee Embarcadero Station."], "2": ["Enter thee 8 St - NYU Station me hearties."]}}, "transit_connection_start_verbal": {"phrases": {"0": "Shiver me timbers! Enter thee station ye landlubber.", "1": "Yo ho ho! Enter thee <TRANSIT_STOP>.", "2": "Enter thee <TRANSIT_STOP> <STATION_LABEL> me hearties."}, "station_label": "Station", "example_phrases": {"0": ["Shiver me timbers! Enter thee station ye landlubber."], "1": ["Yo ho ho! Enter thee Embarcadero Station."], "2": ["Enter thee 8 St - NYU Station me hearties."]}}, "transit_connection_transfer": {"phrases": {"0": "Blimey! Transfer at thee station me buccaneer.", "1": "Blow me down! Transfer at thee <TRANSIT_STOP>.", "2": "Transfer at thee <TRANSIT_STOP> <STATION_LABEL> ye scurvy dog."}, "station_label": "Station", "example_phrases": {"0": ["Blimey! Transfer at thee station me buccaneer."], "1": ["Blow me down! Transfer at thee Embarcadero Station."], "2": ["Transfer at thee 8 St - NYU Station ye scurvy dog."]}}, "transit_connection_transfer_verbal": {"phrases": {"0": "Blimey! Transfer at thee station me buccaneer.", "1": "Blow me down! Transfer at thee <TRANSIT_STOP>.", "2": "Transfer at thee <TRANSIT_STOP> <STATION_LABEL> ye scurvy dog."}, "station_label": "Station", "example_phrases": {"0": ["Blimey! Transfer at thee station me buccaneer."], "1": ["Blow me down! Transfer at thee Embarcadero Station."], "2": ["Transfer at thee 8 St - NYU Station ye scurvy dog."]}}, "transit_remain_on": {"phrases": {"0": "Remain on thee <TRANSIT_NAME> ye landlubber. (<TRANSIT_STOP_COUNT> <TRANSIT_STOP_COUNT_LABEL>)", "1": "Remain on thee <TRANSIT_NAME> toward <TRANSIT_HEADSIGN> ye landlubber. (<TRANSIT_STOP_COUNT> <TRANSIT_STOP_COUNT_LABEL>)"}, "empty_transit_name_labels": ["tram", "metro", "train", "bus", "ferry", "cable car", "gondola", "funicular"], "transit_stop_count_labels": {"one": "port", "other": "ports"}, "example_phrases": {"0": ["Remain on thee New Haven ye landlubber. (1 port)", "Remain on thee train ye landlubber. (3 ports)"], "1": ["Remain on thee F toward JAMAICA - 179 ST ye landlubber. (10 ports)"]}}, "transit_remain_on_verbal": {"phrases": {"0": "Remain on thee <TRANSIT_NAME> ye landlubber.", "1": "Remain on thee <TRANSIT_NAME> toward <TRANSIT_HEADSIGN> ye landlubber."}, "empty_transit_name_labels": ["tram", "metro", "train", "bus", "ferry", "cable car", "gondola", "funicular"], "example_phrases": {"0": ["Remain on thee New Haven ye landlubber."], "1": ["Remain on thee F toward JAMAICA - 179 ST ye landlubber."]}}, "transit_transfer": {"phrases": {"0": "Argh! Transfer to take thee <TRANSIT_NAME>. (<TRANSIT_STOP_COUNT> <TRANSIT_STOP_COUNT_LABEL>)", "1": "Hang the Jib! Transfer to take thee <TRANSIT_NAME> toward <TRANSIT_HEADSIGN>. (<TRANSIT_STOP_COUNT> <TRANSIT_STOP_COUNT_LABEL>)"}, "empty_transit_name_labels": ["tram", "metro", "train", "bus", "ferry", "cable car", "gondola", "funicular"], "transit_stop_count_labels": {"one": "port", "other": "ports"}, "example_phrases": {"0": ["Argh! Transfer to take thee New Haven. (1 port)", "Argh! Transfer to take thee tram. (4 ports)"], "1": ["Hang the Jib! Transfer to take thee F toward JAMAICA - 179 ST. (10 ports)"]}}, "transit_transfer_verbal": {"phrases": {"0": "Argh! Transfer to take thee <TRANSIT_NAME>.", "1": "Hang the Jib! Transfer to take thee <TRANSIT_NAME> toward <TRANSIT_HEADSIGN>."}, "empty_transit_name_labels": ["tram", "metro", "train", "bus", "ferry", "cable car", "gondola", "funicular"], "example_phrases": {"0": ["Argh! Transfer to take thee New Haven."], "1": ["Hang the Jib! Transfer to take thee F toward JAMAICA - 179 ST."]}}, "transit_verbal": {"phrases": {"0": "Aye, take thee <TRANSIT_NAME>.", "1": "Heave ho on thee <TRANSIT_NAME> toward <TRANSIT_HEADSIGN>."}, "empty_transit_name_labels": ["tram", "metro", "train", "bus", "ferry", "cable car", "gondola", "funicular"], "example_phrases": {"0": ["Aye, take thee New Haven."], "1": ["Heave ho on thee F toward JAMAICA - 179 ST."]}}, "turn": {"phrases": {"0": "Turn <RELATIVE_DIRECTION> bucko.", "1": "Turn <RELATIVE_DIRECTION> onto <STREET_NAMES> me hearties.", "2": "Arrr, turn <RELATIVE_DIRECTION> onto <BEGIN_STREET_NAMES>. Aye, continue on <STREET_NAMES>.", "3": "Turn <RELATIVE_DIRECTION> matey to stay on <STREET_NAMES>.", "4": "Turn <RELATIVE_DIRECTION> at <JUNCTION_NAME>.", "5": "Turn <RELATIVE_DIRECTION> toward <TOWARD_SIGN>."}, "empty_street_name_labels": ["solid ground", "th' bike pirate way", "th' treasure trail", "the crosswalk", "the stairs", "the bridge", "the tunnel"], "relative_directions": ["port", "starboard"], "example_phrases": {"0": ["Turn port bucko."], "1": ["Turn starboard onto Flatbush Avenue me hearties."], "2": ["Arrr, turn port onto North Bond Street/US 1 Business/MD 924. Aye, continue on MD 924."], "3": ["Turn starboard matey to stay on Sunstone Drive."], "4": ["Turn right at Mannenbashi East."], "5": ["Turn left toward Baltimore."]}}, "turn_verbal": {"phrases": {"0": "Turn <RELATIVE_DIRECTION> bucko.", "1": "Turn <RELATIVE_DIRECTION> onto <STREET_NAMES> me hearties.", "2": "Arrr, turn <RELATIVE_DIRECTION> onto <BEGIN_STREET_NAMES>.", "3": "Turn <RELATIVE_DIRECTION> matey to stay on <STREET_NAMES>.", "4": "Turn <RELATIVE_DIRECTION> at <JUNCTION_NAME>.", "5": "Turn <RELATIVE_DIRECTION> toward <TOWARD_SIGN>."}, "empty_street_name_labels": ["solid ground", "th' bike pirate way", "th' treasure trail", "the crosswalk", "the stairs", "the bridge", "the tunnel"], "relative_directions": ["port", "starboard"], "example_phrases": {"0": ["Turn left."], "1": ["Turn starboard onto Flatbush Avenue me hearties."], "2": ["Arrr, turn port onto North Bond Street, U.S. 1 Business."], "3": ["Turn starboard matey to stay on Sunstone Drive."], "4": ["Turn right at Mannenbashi East."], "5": ["Turn left toward Baltimore."]}}, "uturn": {"phrases": {"0": "Argh! Make a <RELATIVE_DIRECTION> U-turn.", "1": "Argh! Make a <RELATIVE_DIRECTION> U-turn onto <STREET_NAMES>.", "2": "Argh! Make a <RELATIVE_DIRECTION> U-turn to stay on <STREET_NAMES>.", "3": "Argh! Make a <RELATIVE_DIRECTION> U-turn at <CROSS_STREET_NAMES>.", "4": "Argh! Make a <RELATIVE_DIRECTION> U-turn at <CROSS_STREET_NAMES> onto <STREET_NAMES>.", "5": "Argh! Make a <RELATIVE_DIRECTION> U-turn at <CROSS_STREET_NAMES> to stay on <STREET_NAMES>.", "6": "Make a <RELATIVE_DIRECTION> U-turn at <JUNCTION_NAME>.", "7": "Make a <RELATIVE_DIRECTION> U-turn toward <TOWARD_SIGN>."}, "empty_street_name_labels": ["solid ground", "th' bike pirate way", "th' treasure trail", "the crosswalk", "the stairs", "the bridge", "the tunnel"], "relative_directions": ["port", "starboard"], "example_phrases": {"0": ["Argh! Make a port U-turn."], "1": ["Argh! Make a starboard U-turn onto Bunker Hill Road."], "2": ["Argh! Make a port U-turn to stay on Bunker Hill Road."], "3": ["Argh! Make a port U-turn at Devonshire Road."], "4": ["Argh! Make a port U-turn at Devonshire Road onto Jonestown Road/US 22."], "5": ["Argh! Make a port U-turn at Devonshire Road to stay on Jonestown Road/US 22."], "6": ["Make a right U-turn at Mannenbashi East."], "7": ["Make a left U-turn toward Baltimore."]}}, "uturn_verbal": {"phrases": {"0": "Argh! Make a <RELATIVE_DIRECTION> U-turn.", "1": "Argh! Make a <RELATIVE_DIRECTION> U-turn onto <STREET_NAMES>.", "2": "Argh! Make a <RELATIVE_DIRECTION> U-turn to stay on <STREET_NAMES>.", "3": "Argh! Make a <RELATIVE_DIRECTION> U-turn at <CROSS_STREET_NAMES>.", "4": "Argh! Make a <RELATIVE_DIRECTION> U-turn at <CROSS_STREET_NAMES> onto <STREET_NAMES>.", "5": "Argh! Make a <RELATIVE_DIRECTION> U-turn at <CROSS_STREET_NAMES> to stay on <STREET_NAMES>.", "6": "Make a <RELATIVE_DIRECTION> U-turn at <JUNCTION_NAME>.", "7": "Make a <RELATIVE_DIRECTION> U-turn toward <TOWARD_SIGN>."}, "empty_street_name_labels": ["solid ground", "th' bike pirate way", "th' treasure trail", "the crosswalk", "the stairs", "the bridge", "the tunnel"], "relative_directions": ["port", "starboard"], "example_phrases": {"0": ["Argh! Make a port U-turn."], "1": ["Argh! Make a starboard U-turn onto Bunker Hill Road."], "2": ["Argh! Make a port U-turn to stay on Bunker Hill Road."], "3": ["Argh! Make a port U-turn at Devonshire Road."], "4": ["Argh! Make a port U-turn at Devonshire Road onto Jonestown Road, U.S. 22."], "5": ["Argh! Make a port U-turn at Devonshire Road to stay on Jonestown Road, U.S. 22."], "6": ["Make a right U-turn at Mannenbashi East."], "7": ["Make a left U-turn toward Baltimore."]}}, "verbal_multi_cue": {"phrases": {"0": "<CURRENT_VERBAL_CUE> Then <NEXT_VERBAL_CUE>", "1": "<CURRENT_VERBAL_CUE> Then, in <LENGTH>, <NEXT_VERBAL_CUE>"}, "metric_lengths": ["<KILOMETERS> kilometers", "1 kilometer", "<METERS> meters", "less than 10 meters"], "us_customary_lengths": ["<MILES> miles", "1 mile", "a half mile", "a quarter mile", "<FEET> feet", "less than 10 feet"], "example_phrases": {"0": ["Bear right onto East Fayette Street. Then Turn right onto North Gay Street."], "1": ["Bear right onto East Fayette Street. Then, in 500 feet, Turn right onto North Gay Street."]}}, "approach_verbal_alert": {"phrases": {"0": "In <LENGTH>, <CURRENT_VERBAL_CUE>"}, "metric_lengths": ["<KILOMETERS> kilometers", "1 kilometer", "<METERS> meters", "less than 10 meters"], "us_customary_lengths": ["<MILES> miles", "1 mile", "a half mile", "a quarter mile", "<FEET> feet", "less than 10 feet"], "example_phrases": {"0": ["In a quarter mile, Turn right onto North Gay Street."]}}, "pass": {"phrases": {"0": "Pass <OBJECT_LABEL>.", "1": "Pass traffic signals on <OBJECT_LABEL>."}, "object_labels": ["the gate", "the bollards", "ways intersection"], "example_phrases": {"0": ["Pass the gate."]}}, "elevator": {"phrases": {"0": "Take the elevator.", "1": "Take the elevator to <LEVEL>."}, "example_phrases": {"0": ["Take the elevator."], "1": ["Take the elevator to Level 1."]}}, "steps": {"phrases": {"0": "Take the stairs.", "1": "Take the stairs to <LEVEL>."}, "example_phrases": {"0": ["Take the stairs."], "1": ["Take the stairs to Level 2."]}}, "escalator": {"phrases": {"0": "Take the escalator.", "1": "Take the escalator to <LEVEL>."}, "example_phrases": {"0": ["Take the escalator."], "1": ["Take the escalator to Level 3."]}}, "enter_building": {"phrases": {"0": "Enter the building.", "1": "Enter the building, and continue on <STREET_NAMES>."}, "empty_street_name_labels": ["solid ground", "th' bike pirate way", "the mountain bike trail", "the crosswalk"], "example_phrases": {"0": ["Enter the building."], "1": ["Enter the building, and continue on the walkway."]}}, "exit_building": {"phrases": {"0": "Exit the building.", "1": "Exit the building, and continue on <STREET_NAMES>."}, "empty_street_name_labels": ["solid ground", "th' bike pirate way", "the mountain bike trail", "the crosswalk"], "example_phrases": {"0": ["Exit the building."], "1": ["Exit the building, and continue on the walkway."]}}}}
{"posix_locale": "pl_PL.UTF-8", "aliases": ["pl"], "instructions": {"arrive": {"phrases": {"0": "<PERSON><PERSON>: <TIME>.", "1": "<PERSON> miej<PERSON><PERSON>: <TIME> w <TRANSIT_STOP>."}, "example_phrases": {"0": ["Arrive: 8:02 AM."], "1": ["Arrive: 8:02 AM at 8 St - NYU."]}}, "arrive_verbal": {"phrases": {"0": "Na miejsce dotrzesz o <TIME>.", "1": "O <TIME> dotrz<PERSON>z do <TRANSIT_STOP>."}, "example_phrases": {"0": ["Arrive at 8:02 AM."], "1": ["Arrive at 8:02 AM at 8 St - NYU."]}}, "bear": {"phrases": {"0": "<PERSON><PERSON><PERSON><PERSON><PERSON> <RELATIVE_DIRECTION>.", "1": "<PERSON><PERSON><PERSON><PERSON><PERSON> <RELATIVE_DIRECTION> w: <STREET_NAMES>.", "2": "<PERSON><PERSON><PERSON><PERSON>ć <RELATIVE_DIRECTION> w: <BEGIN_STREET_NAMES>. Kontynu<PERSON>j wz<PERSON><PERSON><PERSON><PERSON>: <STREET_NAMES>.", "3": "<PERSON><PERSON><PERSON><PERSON><PERSON> <RELATIVE_DIRECTION>, pozostając na: <STREET_NAMES>.", "4": "<PERSON><PERSON><PERSON><PERSON>ć <RELATIVE_DIRECTION> na <JUNCTION_NAME>.", "5": "<PERSON><PERSON><PERSON><PERSON><PERSON> <RELATIVE_DIRECTION>, k<PERSON><PERSON><PERSON>: <TOWARD_SIGN>."}, "empty_street_name_labels": ["chodnik", "ścieżka rowerowa", "górski szlak rowerowy", "prz<PERSON><PERSON><PERSON> dla pieszych", "schody", "most", "tunel"], "relative_directions": ["w lewo", "w prawo"], "example_phrases": {"0": ["Bear right."], "1": ["<PERSON> left onto Arlen Road."], "2": ["Bear right onto Belair Road/US 1 Business. Continue on US 1 Business."], "3": ["<PERSON> left to stay on US 15 South."], "4": ["Bear right at Mannenbashi East."], "5": ["Bear left toward Baltimore."]}}, "bear_verbal": {"phrases": {"0": "<PERSON><PERSON><PERSON><PERSON><PERSON> <RELATIVE_DIRECTION>.", "1": "<PERSON><PERSON><PERSON><PERSON><PERSON> <RELATIVE_DIRECTION> w: <STREET_NAMES>.", "2": "<PERSON><PERSON><PERSON><PERSON><PERSON> <RELATIVE_DIRECTION> w: <BEGIN_STREET_NAMES>.", "3": "<PERSON><PERSON><PERSON><PERSON><PERSON> <RELATIVE_DIRECTION>, pozostając na: <STREET_NAMES>.", "4": "<PERSON><PERSON><PERSON><PERSON>ć <RELATIVE_DIRECTION> na <JUNCTION_NAME>.", "5": "<PERSON><PERSON><PERSON><PERSON><PERSON> <RELATIVE_DIRECTION>, k<PERSON><PERSON><PERSON>: <TOWARD_SIGN>."}, "empty_street_name_labels": ["chodnik", "ścieżka rowerowa", "górski szlak rowerowy", "prz<PERSON><PERSON><PERSON> dla pieszych", "schody", "most", "tunel"], "relative_directions": ["w lewo", "w prawo"], "example_phrases": {"0": ["Bear right."], "1": ["<PERSON> left onto Arlen Road."], "2": ["Bear right onto Belair Road, U.S. 1 Business."], "3": ["<PERSON> left to stay on U.S. 15 South."], "4": ["Bear right at Mannenbashi East."], "5": ["Bear left toward Baltimore."]}}, "becomes": {"phrases": {"0": "<PREVIOUS_STREET_NAMES> prz<PERSON><PERSON><PERSON> w: <STREET_NAMES>."}, "example_phrases": {"0": ["Vine Street becomes Middletown Road."]}}, "becomes_verbal": {"phrases": {"0": "<PREVIOUS_STREET_NAMES> prz<PERSON><PERSON><PERSON> w: <STREET_NAMES>."}, "example_phrases": {"0": ["Vine Street becomes Middletown Road."]}}, "continue": {"phrases": {"0": "Podą<PERSON><PERSON> prosto.", "1": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> wzdłuż: <STREET_NAMES>.", "2": "Podąż<PERSON> prosto przez <JUNCTION_NAME>.", "3": "<PERSON><PERSON><PERSON><PERSON><PERSON> prosto, kierunek: <TOWARD_SIGN>."}, "empty_street_name_labels": ["chodnik", "ścieżka rowerowa", "górski szlak rowerowy", "prz<PERSON><PERSON><PERSON> dla pieszych", "schody", "most", "tunel"], "example_phrases": {"0": ["Continue."], "1": ["Continue on 10th Avenue."], "2": ["Continue at Mannenbashi East."], "3": ["Continue toward Baltimore."]}}, "continue_verbal": {"phrases": {"0": "Podą<PERSON><PERSON> prosto.", "1": "Podążaj prosto przez <LENGTH>.", "2": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> wzdłuż: <STREET_NAMES>.", "3": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> wzd<PERSON><PERSON><PERSON>: <STREET_NAMES> przez <LENGTH>.", "4": "Podąż<PERSON> prosto przez <JUNCTION_NAME>.", "5": "Podąż<PERSON> prosto przez <JUNCTION_NAME> przez <LENGTH>.", "6": "<PERSON><PERSON><PERSON><PERSON><PERSON> prosto, kierunek: <TOWARD_SIGN>.", "7": "Pod<PERSON><PERSON><PERSON> prosto przez <LENGTH>, kierunek: <TOWARD_SIGN>."}, "empty_street_name_labels": ["chodnik", "ścieżka rowerowa", "górski szlak rowerowy", "prz<PERSON><PERSON><PERSON> dla pieszych", "schody", "most", "tunel"], "metric_lengths": ["<KILOMETERS> km", "1 kilometr", "<METERS> m", "mniej niż 10 metrów"], "us_customary_lengths": ["<MILES> mi", "1 milę", "pół mili", "<PERSON><PERSON><PERSON><PERSON> mili", "<FEET> ft", "mniej niż 10 stóp"], "example_phrases": {"0": ["Continue."], "1": ["Continue for 300 feet."], "2": ["Continue on 10th Avenue."], "3": ["Continue on 10th Avenue for 3 miles."], "4": ["Continue at Mannenbashi East."], "5": ["Continue at Mannenbashi East for 2 miles."], "6": ["Continue toward Baltimore."], "7": ["Continue toward Baltimore for 5 miles."]}}, "continue_verbal_alert": {"phrases": {"0": "Podą<PERSON><PERSON> prosto.", "1": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> wzdłuż: <STREET_NAMES>.", "2": "Podąż<PERSON> prosto przez <JUNCTION_NAME>.", "3": "<PERSON><PERSON><PERSON><PERSON><PERSON> prosto, kierunek: <TOWARD_SIGN>."}, "empty_street_name_labels": ["chodnik", "ścieżka rowerowa", "górski szlak rowerowy", "prz<PERSON><PERSON><PERSON> dla pieszych", "schody", "most", "tunel"], "example_phrases": {"0": ["Continue."], "1": ["Continue on 10th Avenue."], "2": ["Continue at Mannenbashi East."], "3": ["Continue toward Baltimore."]}}, "depart": {"phrases": {"0": "Odjazd: <TIME>.", "1": "Odjazd: <TIME> z <TRANSIT_STOP>."}, "example_phrases": {"0": ["Depart: 8:02 AM."], "1": ["Depart: 8:02 AM from 8 St - NYU."]}}, "depart_verbal": {"phrases": {"0": "Odjazd o <TIME>.", "1": "Odjazd o <TIME> z <TRANSIT_STOP>."}, "example_phrases": {"0": ["Depart at 8:02 AM."], "1": ["Depart at 8:02 AM from 8 St - NYU."]}}, "destination": {"phrases": {"0": "Dotarłeś do celu.", "1": "Dotarłeś do <DESTINATION>.", "2": "Twój cel znajduje się po <RELATIVE_DIRECTION> stronie.", "3": "<DESTINATION> znajduje się po <RELATIVE_DIRECTION> stronie."}, "relative_directions": ["<PERSON><PERSON><PERSON>", "prawej"], "example_phrases": {"0": ["You have arrived at your destination."], "1": ["You have arrived at 3206 Powelton Avenue."], "2": ["Your destination is on the left.", "Your destination is on the right."], "3": ["Lancaster Brewing Company is on the left."]}}, "destination_verbal": {"phrases": {"0": "Dotarłeś do celu.", "1": "Dotarłeś do <DESTINATION>.", "2": "Twój cel znajduje się po <RELATIVE_DIRECTION> stronie.", "3": "<DESTINATION> znajduje się po <RELATIVE_DIRECTION> stronie."}, "relative_directions": ["<PERSON><PERSON><PERSON>", "prawej"], "example_phrases": {"0": ["You have arrived at your destination."], "1": ["You have arrived at 32 o6 Powelton Avenue."], "2": ["Your destination is on the left.", "Your destination is on the right."], "3": ["Lancaster Brewing Company is on the left."]}}, "destination_verbal_alert": {"phrases": {"0": "Dotrzesz do celu.", "1": "Dotrzesz do <DESTINATION>.", "2": "Tw<PERSON>j cel będzie po <RELATIVE_DIRECTION> stronie.", "3": "<DESTINATION> b<PERSON><PERSON><PERSON> po <RELATIVE_DIRECTION> stronie."}, "relative_directions": ["<PERSON><PERSON><PERSON>", "prawej"], "example_phrases": {"0": ["You will arrive at your destination."], "1": ["You will arrive at 32 o6 Powelton Avenue."], "2": ["Your destination will be on the left.", "Your destination will be on the right."], "3": ["Lancaster Brewing Company will be on the left."]}}, "enter_ferry": {"phrases": {"0": "Wsiądź na prom.", "1": "Wsiądź na: <STREET_NAMES>.", "2": "Wsiądź na <FERRY_LABEL> na: <STREET_NAMES>.", "3": "Wsiądź na prom, kierunek: <TOWARD_SIGN>."}, "empty_street_name_labels": ["chodnik", "ścieżka rowerowa", "górski szlak rowerowy", "prz<PERSON><PERSON><PERSON> dla pieszych", "schody", "most", "tunel"], "ferry_label": "prom", "example_phrases": {"0": ["Take the Ferry."], "1": ["Take the Millersburg Ferry."], "2": ["Take the Bridgeport - Port Jefferson Ferry."], "3": ["Take the ferry toward Cape May."]}}, "enter_ferry_verbal": {"phrases": {"0": "Wsiądź na prom.", "1": "Wsiądź na: <STREET_NAMES>.", "2": "Wsiądź na <FERRY_LABEL> na: <STREET_NAMES>.", "3": "Wsiądź na prom, kierunek: <TOWARD_SIGN>."}, "empty_street_name_labels": ["chodnik", "ścieżka rowerowa", "górski szlak rowerowy", "prz<PERSON><PERSON><PERSON> dla pieszych", "schody", "most", "tunel"], "ferry_label": "prom", "example_phrases": {"0": ["Take the Ferry."], "1": ["Take the Millersburg Ferry."], "2": ["Take the Bridgeport - Port Jefferson Ferry."], "3": ["Take the ferry toward Cape May."]}}, "enter_roundabout": {"phrases": {"0": "Wjedź na rondo.", "1": "Wjedź na rondo i skorzystaj z <ORDINAL_VALUE> zjazdu.", "2": "Wjedź na rondo i skorzystaj z <ORDINAL_VALUE> zjazdu w stronę <ROUNDABOUT_EXIT_STREET_NAMES>.", "3": "Wjedź na rondo i skorzystaj z <ORDINAL_VALUE> zjazdu w stronę <ROUNDABOUT_EXIT_BEGIN_STREET_NAMES>. Podążaj prosto na <ROUNDABOUT_EXIT_STREET_NAMES>.", "4": "Wjedź na rondo i skorzystaj z <ORDINAL_VALUE> zjazdu, kierunek: <TOWARD_SIGN>.", "5": "Wjedź na rondo i skorzystaj ze zjazdu w stronę <ROUNDABOUT_EXIT_STREET_NAMES>.", "6": "Wjedź na rondo i skorzystaj ze zjazdu w stronę <ROUNDABOUT_EXIT_BEGIN_STREET_NAMES>. Podążaj prosto na <ROUNDABOUT_EXIT_STREET_NAMES>.", "7": "Wjedź na rondo i skorzystaj ze zjazdu, kierunek: <TOWARD_SIGN>.", "8": "Wjedź na: <STREET_NAMES>", "9": "<PERSON><PERSON><PERSON><PERSON> na: <STREET_NAMES> i skorzystaj z <ORDINAL_VALUE> zjazdu.", "10": "<PERSON><PERSON><PERSON><PERSON> na: <STREET_NAMES> i skorzystaj z <ORDINAL_VALUE> zjazdu w stronę <ROUNDABOUT_EXIT_STREET_NAMES>.", "11": "W<PERSON><PERSON><PERSON> na: <STREET_NAMES> i skorzystaj z <ORDINAL_VALUE> zja<PERSON>du w stronę <ROUNDABOUT_EXIT_BEGIN_STREET_NAMES>. Podążaj prosto na <ROUNDABOUT_EXIT_STREET_NAMES>.", "12": "<PERSON><PERSON><PERSON><PERSON> na: <STREET_NAMES> i skorzystaj z <ORDINAL_VALUE> zjazdu, kierunek: <TOWARD_SIGN>.", "13": "<PERSON><PERSON><PERSON><PERSON> na: <STREET_NAMES> i skorzystaj ze zjazdu w stronę <ROUNDABOUT_EXIT_STREET_NAMES>.", "14": "Wjed<PERSON> na: <STREET_NAMES> i skorzystaj ze zjazdu w stronę <ROUNDABOUT_EXIT_BEGIN_STREET_NAMES>. Podążaj prosto na <ROUNDABOUT_EXIT_STREET_NAMES>.", "15": "<PERSON><PERSON><PERSON><PERSON> na: <STREET_NAMES> i skorzystaj ze zjazdu, kierunek: <TOWARD_SIGN>."}, "ordinal_values": ["pierwsze<PERSON>", "drugiego", "trzeciego", "czwartego", "piątego", "szóstego", "siódmego", "<PERSON><PERSON><PERSON>", "dziewiątego", "dziesiątego"], "empty_street_name_labels": ["chodnik", "ścieżka rowerowa", "górski szlak rowerowy", "prz<PERSON><PERSON><PERSON> dla pieszych", "schody", "most", "tunel"], "example_phrases": {"0": ["Enter the roundabout."], "1": ["Enter the roundabout and take the 1st exit.", "Enter the roundabout and take the 2nd exit.", "Enter the roundabout and take the 3rd exit.", "Enter the roundabout and take the 4th exit.", "Enter the roundabout and take the 5th exit.", "Enter the roundabout and take the 6th exit.", "Enter the roundabout and take the 7th exit.", "Enter the roundabout and take the 8th exit.", "Enter the roundabout and take the 9th exit.", "Enter the roundabout and take the 10th exit."], "2": ["Enter the roundabout and take the 3rd exit onto Main Street."], "3": ["Enter the roundabout and take the 3rd exit onto US 322/Main Street. Continue on US 322."], "4": ["Enter the roundabout and take the 3rd exit toward Baltimore."], "5": ["Enter the roundabout and take the exit onto Main Street."], "6": ["Enter the roundabout and take the exit onto US 322/Main Street. Continue on US 322."], "7": ["Enter the roundabout and take the exit toward Baltimore."], "8": ["Enter Dupont Circle."], "9": ["Enter Dupont Circle and take the 1st exit."], "10": ["Enter Dupont Circle and take the 3rd exit onto Main Street."], "11": ["Enter Dupont Circle and take the 3rd exit onto US 322/Main Street. Continue on US 322."], "12": ["Enter Dupont Circle and take the 3rd exit toward Baltimore."], "13": ["Enter Dupont Circle and take the exit onto Main Street."], "14": ["Enter Dupont Circle and take the exit onto US 322/Main Street. Continue on US 322."], "15": ["Enter Dupont Circle and take the exit toward Baltimore."]}}, "enter_roundabout_verbal": {"phrases": {"0": "Wjedź na rondo.", "1": "Wjedź na rondo i skorzystaj z <ORDINAL_VALUE> zjazdu.", "2": "Wjedź na rondo i skorzystaj z <ORDINAL_VALUE> zjazdu w stronę <ROUNDABOUT_EXIT_STREET_NAMES>.", "3": "Wjedź na rondo i skorzystaj z <ORDINAL_VALUE> zjazdu w stronę <ROUNDABOUT_EXIT_BEGIN_STREET_NAMES>.", "4": "Wjedź na rondo i skorzystaj z <ORDINAL_VALUE> zjazdu, kierunek: <TOWARD_SIGN>.", "5": "Wjedź na rondo i skorzystaj ze zjazdu w stronę <ROUNDABOUT_EXIT_STREET_NAMES>.", "6": "Wjedź na rondo i skorzystaj ze zjazdu w stronę <ROUNDABOUT_EXIT_BEGIN_STREET_NAMES>.", "7": "Wjedź na rondo i skorzystaj ze zjazdu, kierunek: <TOWARD_SIGN>.", "8": "Wjedź na: <STREET_NAMES>", "9": "<PERSON><PERSON><PERSON><PERSON> na: <STREET_NAMES> i skorzystaj z <ORDINAL_VALUE> zjazdu.", "10": "<PERSON><PERSON><PERSON><PERSON> na: <STREET_NAMES> i skorzystaj z <ORDINAL_VALUE> zjazdu w stronę <ROUNDABOUT_EXIT_STREET_NAMES>.", "11": "<PERSON><PERSON><PERSON><PERSON> na: <STREET_NAMES> i skorzystaj z <ORDINAL_VALUE> zjazdu w stronę <ROUNDABOUT_EXIT_BEGIN_STREET_NAMES>.", "12": "<PERSON><PERSON><PERSON><PERSON> na: <STREET_NAMES> i skorzystaj z <ORDINAL_VALUE> zjazdu, kierunek: <TOWARD_SIGN>.", "13": "<PERSON><PERSON><PERSON><PERSON> na: <STREET_NAMES> i skorzystaj ze zjazdu w stronę <ROUNDABOUT_EXIT_STREET_NAMES>.", "14": "<PERSON><PERSON><PERSON><PERSON> na: <STREET_NAMES> i skorzystaj ze zjazdu w stronę <ROUNDABOUT_EXIT_BEGIN_STREET_NAMES>.", "15": "<PERSON><PERSON><PERSON><PERSON> na: <STREET_NAMES> i skorzystaj ze zjazdu, kierunek: <TOWARD_SIGN>."}, "ordinal_values": ["pierwsze<PERSON>", "drugiego", "trzeciego", "czwartego", "piątego", "szóstego", "siódmego", "<PERSON><PERSON><PERSON>", "dziewiątego", "dziesiątego"], "empty_street_name_labels": ["chodnik", "ścieżka rowerowa", "górski szlak rowerowy", "prz<PERSON><PERSON><PERSON> dla pieszych", "schody", "most", "tunel"], "example_phrases": {"0": ["Enter the roundabout."], "1": ["Enter the roundabout and take the 1st exit.", "Enter the roundabout and take the 2nd exit.", "Enter the roundabout and take the 3rd exit.", "Enter the roundabout and take the 4th exit.", "Enter the roundabout and take the 5th exit.", "Enter the roundabout and take the 6th exit.", "Enter the roundabout and take the 7th exit.", "Enter the roundabout and take the 8th exit.", "Enter the roundabout and take the 9th exit.", "Enter the roundabout and take the 10th exit."], "2": ["Enter the roundabout and take the 3rd exit onto Main Street."], "3": ["Enter the roundabout and take the 3rd exit onto U.S. 3 22/Main Street."], "4": ["Enter the roundabout and take the 3rd exit toward Baltimore."], "5": ["Enter the roundabout and take the exit onto Main Street."], "6": ["Enter the roundabout and take the exit onto U.S. 3 22/Main Street."], "7": ["Enter the roundabout and take the exit toward Baltimore."], "8": ["Enter Dupont Circle."], "9": ["Enter Dupont Circle and take the 1st exit."], "10": ["Enter Dupont Circle and take the 3rd exit onto Main Street."], "11": ["Enter Dupont Circle and take the 3rd exit onto U.S. 3 22/Main Street."], "12": ["Enter Dupont Circle and take the 3rd exit toward Baltimore."], "13": ["Enter Dupont Circle and take the exit onto Main Street."], "14": ["Enter Dupont Circle and take the exit onto U.S. 3 22/Main Street."], "15": ["Enter Dupont Circle and take the exit toward Baltimore."]}}, "exit": {"phrases": {"0": "Wjedź na zjazd <RELATIVE_DIRECTION>.", "1": "Wjedź na zjazd <NUMBER_SIGN> <RELATIVE_DIRECTION>.", "2": "<PERSON><PERSON><PERSON><PERSON> <RELATIVE_DIRECTION> na <BRANCH_SIGN>.", "3": "Wjedź na zjazd <NUMBER_SIGN> <RELATIVE_DIRECTION> w stronę <BRANCH_SIGN>.", "4": "Wjedź na zjazd <RELATIVE_DIRECTION>, kierunek: <TOWARD_SIGN>.", "5": "Wjedź na zjazd <NUMBER_SIGN> <RELATIVE_DIRECTION>, kierunek: <TOWARD_SIGN>.", "6": "<PERSON><PERSON><PERSON><PERSON> <RELATIVE_DIRECTION> na <BRANCH_SIGN>, kierunek: <TOWARD_SIGN>.", "7": "Wjedź na zjazd <NUMBER_SIGN> <RELATIVE_DIRECTION> w stronę <BRANCH_SIGN>, kier<PERSON><PERSON>: <TOWARD_SIGN>.", "8": "<PERSON><PERSON><PERSON><PERSON> <RELATIVE_DIRECTION> na <NAME_SIGN>.", "10": "<PERSON><PERSON><PERSON><PERSON> <RELATIVE_DIRECTION> na <NAME_SIGN> w stronę <BRANCH_SIGN>.", "12": "<PERSON><PERSON><PERSON><PERSON> <RELATIVE_DIRECTION> na <NAME_SIGN>, kierunek: <TOWARD_SIGN>.", "14": "<PERSON><PERSON><PERSON><PERSON> <RELATIVE_DIRECTION> na <NAME_SIGN> w stronę <BRANCH_SIGN>, k<PERSON><PERSON><PERSON>: <TOWARD_SIGN>.", "15": "Wjedź na zjazd.", "16": "Wjedź na zjazd <NUMBER_SIGN>.", "17": "Zjedź na <BRANCH_SIGN>.", "18": "Wjedź na zjazd <NUMBER_SIGN> w stronę <BRANCH_SIGN>.", "19": "Wjedź na zjazd, kierunek: <TOWARD_SIGN>.", "20": "Wjedź na zjazd <NUMBER_SIGN>, kierunek: <TOWARD_SIGN>.", "21": "<PERSON><PERSON><PERSON><PERSON> na <BRANCH_SIGN>, kierunek: <TOWARD_SIGN>.", "22": "Wjedź na zjazd <NUMBER_SIGN> w stronę <BRANCH_SIGN>, kier<PERSON><PERSON>: <TOWARD_SIGN>.", "23": "Zjedź na <NAME_SIGN>.", "25": "<PERSON><PERSON><PERSON><PERSON> na <NAME_SIGN> w stronę <BRANCH_SIGN>.", "27": "<PERSON><PERSON><PERSON><PERSON> na <NAME_SIGN>, kierunek: <TOWARD_SIGN>.", "29": "<PERSON><PERSON><PERSON><PERSON> na <NAME_SIGN> w stronę <BRANCH_SIGN>, k<PERSON>unek: <TOWARD_SIGN>."}, "relative_directions": ["w lewo", "w prawo"], "example_phrases": {"0": ["Take the exit on the left.", "Take the exit on the right."], "1": ["Take exit 67 B-A on the right."], "2": ["Take the US 322 West exit on the right."], "3": ["Take exit 67 B-A on the right onto US 322 West."], "4": ["Take the exit on the right toward Lewistown."], "5": ["Take exit 67 B-A on the right toward Lewistown."], "6": ["Take the US 322 West exit on the right toward Lewistown."], "7": ["Take exit 67 B-A on the right onto US 322 West toward Lewistown/State College."], "8": ["Take the White Marsh Boulevard exit on the left."], "10": ["Take the White Marsh Boulevard exit on the left onto MD 43 East."], "12": ["Take the White Marsh Boulevard exit on the left toward White Marsh."], "14": ["Take the White Marsh Boulevard exit on the left onto MD 43 East toward White Marsh."], "15": ["Take the exit."], "16": ["Take exit 67 B-A."], "17": ["Take the US 322 West exit."], "18": ["Take exit 67 B-A onto US 322 West."], "19": ["Take the exit toward Lewistown."], "20": ["Take exit 67 B-A toward Lewistown."], "21": ["Take the US 322 West exit toward Lewistown."], "22": ["Take exit 67 B-A onto US 322 West toward Lewistown/State College."], "23": ["Take the White Marsh Boulevard exit."], "25": ["Take the White Marsh Boulevard exit onto MD 43 East."], "27": ["Take the White Marsh Boulevard exit toward White Marsh."], "29": ["Take the White Marsh Boulevard exit onto MD 43 East toward White Marsh."]}}, "exit_roundabout": {"phrases": {"0": "Zjedź z ronda.", "1": "Zjedź z ronda na: <STREET_NAMES>.", "2": "Zjedź z ronda na: <BEGIN_STREET_NAMES>. Kontynuuj wzdłuż: <STREET_NAMES>.", "3": "<PERSON><PERSON><PERSON><PERSON> z ronda, kierunek: <TOWARD_SIGN>."}, "empty_street_name_labels": ["chodnik", "ścieżka rowerowa", "górski szlak rowerowy", "prz<PERSON><PERSON><PERSON> dla pieszych", "schody", "most", "tunel"], "example_phrases": {"0": ["Exit the roundabout."], "1": ["Exit the roundabout onto Philadelphia Road/MD 7."], "2": ["Exit the roundabout onto Catoctin Mountain Highway/US 15. Continue on US 15."], "3": ["Exit the roundabout toward Baltimore."]}}, "exit_roundabout_verbal": {"phrases": {"0": "Zjedź z ronda.", "1": "Zjedź z ronda na: <STREET_NAMES>.", "2": "Zjedź z ronda na: <BEGIN_STREET_NAMES>.", "3": "<PERSON><PERSON><PERSON><PERSON> z ronda, kierunek: <TOWARD_SIGN>."}, "empty_street_name_labels": ["chodnik", "ścieżka rowerowa", "górski szlak rowerowy", "prz<PERSON><PERSON><PERSON> dla pieszych", "schody", "most", "tunel"], "example_phrases": {"0": ["Exit the roundabout."], "1": ["Exit the roundabout onto Philadelphia Road, Maryland 7."], "2": ["Exit the roundabout onto Catoctin Mountain Highway, U.S. 15."], "3": ["Exit the roundabout toward Baltimore."]}}, "exit_verbal": {"phrases": {"0": "Wjedź na zjazd <RELATIVE_DIRECTION>.", "1": "Wjedź na zjazd <NUMBER_SIGN> <RELATIVE_DIRECTION>.", "2": "<PERSON><PERSON><PERSON><PERSON> <RELATIVE_DIRECTION> na <BRANCH_SIGN>.", "3": "Wjedź na zjazd <NUMBER_SIGN> <RELATIVE_DIRECTION> w stronę <BRANCH_SIGN>.", "4": "Wjedź na zjazd <RELATIVE_DIRECTION>, kierunek: <TOWARD_SIGN>.", "5": "Wjedź na zjazd <NUMBER_SIGN> <RELATIVE_DIRECTION>, kierunek: <TOWARD_SIGN>.", "6": "<PERSON><PERSON><PERSON><PERSON> <RELATIVE_DIRECTION> na <BRANCH_SIGN>, kierunek: <TOWARD_SIGN>.", "7": "Wjedź na zjazd <NUMBER_SIGN> <RELATIVE_DIRECTION> w stronę <BRANCH_SIGN>, kier<PERSON><PERSON>: <TOWARD_SIGN>.", "8": "<PERSON><PERSON><PERSON><PERSON> <RELATIVE_DIRECTION> na <NAME_SIGN>.", "10": "<PERSON><PERSON><PERSON><PERSON> <RELATIVE_DIRECTION> na <NAME_SIGN> w stronę <BRANCH_SIGN>.", "12": "<PERSON><PERSON><PERSON><PERSON> <RELATIVE_DIRECTION> na <NAME_SIGN>, kierunek: <TOWARD_SIGN>.", "14": "<PERSON><PERSON><PERSON><PERSON> <RELATIVE_DIRECTION> na <NAME_SIGN> w stronę <BRANCH_SIGN>, k<PERSON><PERSON><PERSON>: <TOWARD_SIGN>.", "15": "Wjedź na zjazd.", "16": "Wjedź na zjazd <NUMBER_SIGN>.", "17": "Zjedź na <BRANCH_SIGN>.", "18": "Wjedź na zjazd <NUMBER_SIGN> w stronę <BRANCH_SIGN>.", "19": "Wjedź na zjazd, kierunek: <TOWARD_SIGN>.", "20": "Wjedź na zjazd <NUMBER_SIGN>, kierunek: <TOWARD_SIGN>.", "21": "<PERSON><PERSON><PERSON><PERSON> na <BRANCH_SIGN>, kierunek: <TOWARD_SIGN>.", "22": "Wjedź na zjazd <NUMBER_SIGN> w stronę <BRANCH_SIGN>, kier<PERSON><PERSON>: <TOWARD_SIGN>.", "23": "Zjedź na <NAME_SIGN>.", "25": "<PERSON><PERSON><PERSON><PERSON> na <NAME_SIGN> w stronę <BRANCH_SIGN>.", "27": "<PERSON><PERSON><PERSON><PERSON> na <NAME_SIGN>, kierunek: <TOWARD_SIGN>.", "29": "<PERSON><PERSON><PERSON><PERSON> na <NAME_SIGN> w stronę <BRANCH_SIGN>, k<PERSON>unek: <TOWARD_SIGN>."}, "relative_directions": ["w lewo", "w prawo"], "example_phrases": {"0": ["Take the exit on the left.", "Take the exit on the right."], "1": ["Take exit 67 B-A on the right."], "2": ["Take the U.S. 3 22 West exit on the right."], "3": ["Take exit 67 B-A on the right onto U.S. 3 22 West."], "4": ["Take the exit on the right toward Lewistown."], "5": ["Take exit 67 B-A on the right toward Lewistown."], "6": ["Take the U.S. 3 22 West exit on the right toward Lewistown."], "7": ["Take exit 67 B-A on the right onto U.S. 3 22 West toward Lewistown, State College."], "8": ["Take the White Marsh Boulevard exit on the left."], "10": ["Take the White Marsh Boulevard exit on the left onto Maryland 43 East."], "12": ["Take the White Marsh Boulevard exit on the left toward White Marsh."], "14": ["Take the White Marsh Boulevard exit on the left onto Maryland 43 East toward White Marsh."], "15": ["Take the exit."], "16": ["Take exit 67 B-A."], "17": ["Take the US 322 West exit."], "18": ["Take exit 67 B-A onto US 322 West."], "19": ["Take the exit toward Lewistown."], "20": ["Take exit 67 B-A toward Lewistown."], "21": ["Take the US 322 West exit toward Lewistown."], "22": ["Take exit 67 B-A onto US 322 West toward Lewistown/State College."], "23": ["Take the White Marsh Boulevard exit."], "25": ["Take the White Marsh Boulevard exit onto MD 43 East."], "27": ["Take the White Marsh Boulevard exit toward White Marsh."], "29": ["Take the White Marsh Boulevard exit onto MD 43 East toward White Marsh."]}}, "exit_visual": {"phrases": {"0": "Zjazd <EXIT_NUMBERS>"}, "example_phrases": {"0": ["Exit 1A"]}}, "keep": {"phrases": {"0": "Na rozwidleniu trzymaj się <RELATIVE_DIRECTION>.", "1": "<PERSON><PERSON><PERSON><PERSON> <RELATIVE_DIRECTION>, aby s<PERSON> ze zjazdu <NUMBER_SIGN>.", "2": "<PERSON><PERSON><PERSON><PERSON> <RELATIVE_DIRECTION>, aby w<PERSON><PERSON><PERSON> w: <STREET_NAMES>.", "3": "<PERSON><PERSON><PERSON><PERSON> <RELATIVE_DIRECTION>, aby s<PERSON> ze zjazdu <NUMBER_SIGN> w: <STREET_NAMES>.", "4": "<PERSON><PERSON><PERSON><PERSON> <RELATIVE_DIRECTION>, k<PERSON><PERSON><PERSON>: <TOWARD_SIGN>.", "5": "<PERSON><PERSON><PERSON><PERSON> <RELATIVE_DIRECTION>, aby s<PERSON> ze zjazdu <NUMBER_SIGN>, k<PERSON><PERSON>k: <TOWARD_SIGN>.", "6": "<PERSON><PERSON><PERSON><PERSON> <RELATIVE_DIRECTION>, aby w<PERSON><PERSON><PERSON> w: <STREET_NAMES>, k<PERSON><PERSON><PERSON>: <TOWARD_SIGN>.", "7": "<PERSON><PERSON><PERSON><PERSON> <RELATIVE_DIRECTION>, aby s<PERSON><PERSON> ze zjazdu <NUMBER_SIGN> w: <STREET_NAMES>, k<PERSON><PERSON><PERSON>: <TOWARD_SIGN>."}, "empty_street_name_labels": ["chodnik", "ścieżka rowerowa", "górski szlak rowerowy", "prz<PERSON><PERSON><PERSON> dla pieszych", "schody", "most", "tunel"], "relative_directions": ["<PERSON><PERSON><PERSON> strony", "środka drogi", "prawej strony"], "example_phrases": {"0": ["Keep left at the fork.", "Keep straight at the fork.", "Keep right at the fork."], "1": ["Keep right to take exit 62."], "2": ["Keep right to take I 895 South."], "3": ["Keep right to take exit 62 onto I 895 South."], "4": ["Keep right toward Annapolis."], "5": ["Keep right to take exit 62 toward Annapolis."], "6": ["Keep right to take I 895 South toward Annapolis."], "7": ["Keep right to take exit 62 onto I 895 South toward Annapolis."]}}, "keep_to_stay_on": {"phrases": {"0": "<PERSON><PERSON><PERSON><PERSON> <RELATIVE_DIRECTION>, pozostając na: <STREET_NAMES>.", "1": "<PERSON><PERSON><PERSON><PERSON> <RELATIVE_DIRECTION>, aby s<PERSON> ze zjazdu <NUMBER_SIGN>, pozostając na: <STREET_NAMES>.", "2": "<PERSON><PERSON><PERSON><PERSON> <RELATIVE_DIRECTION>, pozostając na: <STREET_NAMES>, k<PERSON>unek: <TOWARD_SIGN>.", "3": "<PERSON><PERSON><PERSON><PERSON> <RELATIVE_DIRECTION>, aby s<PERSON> ze zjazdu <NUMBER_SIGN>, pozostając na: <STREET_NAMES>, kierunek: <TOWARD_SIGN>."}, "empty_street_name_labels": ["chodnik", "ścieżka rowerowa", "górski szlak rowerowy", "prz<PERSON><PERSON><PERSON> dla pieszych", "schody", "most", "tunel"], "relative_directions": ["<PERSON><PERSON><PERSON> strony", "środka drogi", "prawej strony"], "example_phrases": {"0": ["Keep left to stay on I 95 South.", "Keep straight to stay on I 95 South.", "Keep right to stay on I 95 South."], "1": ["Keep left to take exit 62 to stay on I 95 South."], "2": ["Keep left to stay on I 95 South toward Baltimore."], "3": ["Keep left to take exit 62 to stay on I 95 South toward Baltimore."]}}, "keep_to_stay_on_verbal": {"phrases": {"0": "<PERSON><PERSON><PERSON><PERSON> <RELATIVE_DIRECTION>, pozostając na: <STREET_NAMES>.", "1": "<PERSON><PERSON><PERSON><PERSON> <RELATIVE_DIRECTION>, aby s<PERSON> ze zjazdu <NUMBER_SIGN>, pozostając na: <STREET_NAMES>.", "2": "<PERSON><PERSON><PERSON><PERSON> <RELATIVE_DIRECTION>, pozostając na: <STREET_NAMES>, k<PERSON>unek: <TOWARD_SIGN>.", "3": "<PERSON><PERSON><PERSON><PERSON> <RELATIVE_DIRECTION>, aby s<PERSON> ze zjazdu <NUMBER_SIGN>, pozostając na: <STREET_NAMES>, kierunek: <TOWARD_SIGN>."}, "empty_street_name_labels": ["chodnik", "ścieżka rowerowa", "górski szlak rowerowy", "prz<PERSON><PERSON><PERSON> dla pieszych", "schody", "most", "tunel"], "relative_directions": ["<PERSON><PERSON><PERSON> strony", "środka drogi", "prawej strony"], "example_phrases": {"0": ["Keep left to stay on Interstate 95 South.", "Keep straight to stay on Interstate 95 South.", "Keep right to stay on Interstate 95 South."], "1": ["Keep left to take exit 62 to stay on Interstate 95 South."], "2": ["Keep left to stay on I 95 South toward Baltimore."], "3": ["Keep left to take exit 62 to stay on Interstate 95 South toward Baltimore."]}}, "keep_verbal": {"phrases": {"0": "Na rozwidleniu trzymaj się <RELATIVE_DIRECTION>.", "1": "<PERSON><PERSON><PERSON><PERSON> <RELATIVE_DIRECTION>, aby s<PERSON> ze zjazdu <NUMBER_SIGN>.", "2": "<PERSON><PERSON><PERSON><PERSON> <RELATIVE_DIRECTION>, aby w<PERSON><PERSON><PERSON> w: <STREET_NAMES>.", "3": "<PERSON><PERSON><PERSON><PERSON> <RELATIVE_DIRECTION>, aby s<PERSON> ze zjazdu <NUMBER_SIGN> w: <STREET_NAMES>.", "4": "<PERSON><PERSON><PERSON><PERSON> <RELATIVE_DIRECTION>, k<PERSON><PERSON><PERSON>: <TOWARD_SIGN>.", "5": "<PERSON><PERSON><PERSON><PERSON> <RELATIVE_DIRECTION>, aby s<PERSON> ze zjazdu <NUMBER_SIGN>, k<PERSON><PERSON>k: <TOWARD_SIGN>.", "6": "<PERSON><PERSON><PERSON><PERSON> <RELATIVE_DIRECTION>, aby w<PERSON><PERSON><PERSON> w: <STREET_NAMES>, k<PERSON><PERSON><PERSON>: <TOWARD_SIGN>.", "7": "<PERSON><PERSON><PERSON><PERSON> <RELATIVE_DIRECTION>, aby s<PERSON><PERSON> ze zjazdu <NUMBER_SIGN> w: <STREET_NAMES>, k<PERSON><PERSON><PERSON>: <TOWARD_SIGN>."}, "empty_street_name_labels": ["chodnik", "ścieżka rowerowa", "górski szlak rowerowy", "prz<PERSON><PERSON><PERSON> dla pieszych", "schody", "most", "tunel"], "relative_directions": ["<PERSON><PERSON><PERSON> strony", "środka drogi", "prawej strony"], "example_phrases": {"0": ["Keep left at the fork.", "Keep straight at the fork.", "Keep right at the fork."], "1": ["Keep right to take exit 62."], "2": ["Keep right to take Interstate 8 95 South."], "3": ["Keep right to take exit 62 onto Interstate 8 95 South."], "4": ["Keep right toward Annapolis."], "5": ["Keep right to take exit 62 toward Annapolis."], "6": ["Keep right to take Interstate 8 95 South toward Annapolis."], "7": ["Keep right to take exit 62 onto Interstate 8 95 South toward Annapolis."]}}, "merge": {"phrases": {"0": "Wjed<PERSON>.", "1": "<PERSON><PERSON><PERSON><PERSON> <RELATIVE_DIRECTION>.", "2": "Wjedź na: <STREET_NAMES>.", "3": "W<PERSON><PERSON><PERSON> <RELATIVE_DIRECTION> na: <STREET_NAMES>.", "4": "<PERSON><PERSON><PERSON><PERSON>, kierunek: <TOWARD_SIGN>.", "5": "<PERSON><PERSON><PERSON><PERSON> <RELATIVE_DIRECTION>, k<PERSON><PERSON><PERSON>: <TOWARD_SIGN>."}, "relative_directions": ["w lewo", "w prawo"], "empty_street_name_labels": ["chodnik", "ścieżka rowerowa", "górski szlak rowerowy", "prz<PERSON><PERSON><PERSON> dla pieszych", "schody", "most", "tunel"], "example_phrases": {"0": ["Merge."], "1": ["<PERSON><PERSON> left."], "2": ["Merge onto I 76 West/Pennsylvania Turnpike."], "3": ["Merge right onto I 83 South."], "4": ["Merge toward Baltimore."], "5": ["Merge right toward Baltimore."]}}, "merge_verbal": {"phrases": {"0": "Wjed<PERSON>.", "1": "<PERSON><PERSON><PERSON><PERSON> <RELATIVE_DIRECTION>.", "2": "Wjedź na: <STREET_NAMES>.", "3": "W<PERSON><PERSON><PERSON> <RELATIVE_DIRECTION> na: <STREET_NAMES>.", "4": "<PERSON><PERSON><PERSON><PERSON>, kierunek: <TOWARD_SIGN>.", "5": "<PERSON><PERSON><PERSON><PERSON> <RELATIVE_DIRECTION>, k<PERSON><PERSON><PERSON>: <TOWARD_SIGN>."}, "relative_directions": ["w lewo", "w prawo"], "empty_street_name_labels": ["chodnik", "ścieżka rowerowa", "górski szlak rowerowy", "prz<PERSON><PERSON><PERSON> dla pieszych", "schody", "most", "tunel"], "example_phrases": {"0": ["Merge."], "1": ["<PERSON><PERSON> left."], "2": ["Merge onto I 76 West/Pennsylvania Turnpike."], "3": ["Merge right onto I 83 South."], "4": ["Merge toward Baltimore."], "5": ["Merge right toward Baltimore."]}}, "post_transition_transit_verbal": {"phrases": {"0": "<PERSON><PERSON><PERSON>ą<PERSON><PERSON> na <TRANSIT_STOP_COUNT>. <TRANSIT_STOP_COUNT_LABEL>."}, "transit_stop_count_labels": {"one": "przystan<PERSON>", "other": "przystan<PERSON>"}, "example_phrases": {"0": ["Travel 1 stop.", "Travel 3 stops."]}}, "post_transition_verbal": {"phrases": {"0": "Podążaj prosto przez <LENGTH>.", "1": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> wzd<PERSON><PERSON><PERSON>: <STREET_NAMES> przez <LENGTH>."}, "empty_street_name_labels": ["chodnik", "ścieżka rowerowa", "górski szlak rowerowy", "prz<PERSON><PERSON><PERSON> dla pieszych", "schody", "most", "tunel"], "metric_lengths": ["<KILOMETERS> km", "1 km", "<METERS> m", "mniej niż 10 metrów"], "us_customary_lengths": ["<MILES> mi", "1 milę", "pół mili", "<PERSON><PERSON><PERSON><PERSON> mili", "<FEET> stóp", "mniej niż 10 stóp"], "example_phrases": {"0": ["Continue for 300 feet.", "Continue for 9 miles."], "1": ["Continue on Pennsylvania 7 43 for 6.2 miles.", "Continue on Main Street, Vermont 30 for 1 tenth of a mile."]}}, "ramp": {"phrases": {"0": "<PERSON><PERSON><PERSON><PERSON> zjazd <RELATIVE_DIRECTION>.", "1": "<PERSON><PERSON><PERSON><PERSON> zjazd <BRANCH_SIGN> <RELATIVE_DIRECTION>.", "2": "<PERSON><PERSON><PERSON><PERSON> zjazd <RELATIVE_DIRECTION>, k<PERSON><PERSON>k: <TOWARD_SIGN>.", "3": "<PERSON><PERSON><PERSON><PERSON> zjazd <BRANCH_SIGN> <RELATIVE_DIRECTION>, k<PERSON><PERSON>k: <TOWARD_SIGN>.", "4": "<PERSON><PERSON><PERSON><PERSON> zjazd <NAME_SIGN> <RELATIVE_DIRECTION>.", "5": "<PERSON><PERSON><PERSON><PERSON><PERSON> <RELATIVE_DIRECTION>, aby s<PERSON> ze zjazdu.", "6": "<PERSON><PERSON><PERSON><PERSON><PERSON> <RELATIVE_DIRECTION>, aby s<PERSON><PERSON> ze zjazdu <BRANCH_SIGN>.", "7": "<PERSON><PERSON><PERSON><PERSON><PERSON> <RELATIVE_DIRECTION>, aby s<PERSON> ze zjazdu, kierunek: <TOWARD_SIGN>.", "8": "<PERSON><PERSON><PERSON><PERSON><PERSON> <RELATIVE_DIRECTION>, aby s<PERSON> ze zjazdu <BRANCH_SIGN>, k<PERSON><PERSON><PERSON>: <TOWARD_SIGN>.", "9": "<PERSON><PERSON><PERSON><PERSON><PERSON> <RELATIVE_DIRECTION>, aby s<PERSON><PERSON> ze zjazdu <NAME_SIGN>.", "10": "Skorzystaj ze zjazdu.", "11": "<PERSON><PERSON><PERSON><PERSON> zjazd <BRANCH_SIGN>.", "12": "Skorzystaj ze zjazdu, kierunek: <TOWARD_SIGN>.", "13": "<PERSON><PERSON><PERSON><PERSON> z<PERSON>zd <BRANCH_SIGN>, k<PERSON><PERSON><PERSON>: <TOWARD_SIGN>.", "14": "<PERSON><PERSON><PERSON><PERSON> zjazd <NAME_SIGN>."}, "relative_directions": ["w lewo", "w prawo"], "example_phrases": {"0": ["Take the ramp on the left.", "Take the ramp on the right."], "1": ["Take the I 95 ramp on the right."], "2": ["Take the ramp on the left toward JFK."], "3": ["Take the South Conduit Avenue ramp on the left toward JFK."], "4": ["Take the Gettysburg Pike ramp on the right."], "5": ["Turn left to take the ramp.", "Turn right to take the ramp."], "6": ["Turn left to take the PA 283 West ramp."], "7": ["Turn left to take the ramp toward Harrisburg/Harrisburg International Airport."], "8": ["Turn left to take the PA 283 West ramp toward Harrisburg/Harrisburg International Airport."], "9": ["Turn right to take the Gettysburg Pike ramp."], "10": ["Take the ramp."], "11": ["Take the I 95 ramp."], "12": ["Take the ramp toward JFK."], "13": ["Take the Soutch Conduit Avenue ramp toward JFK."], "14": ["Take the Gettysburg ramp."]}}, "ramp_straight": {"phrases": {"0": "<PERSON><PERSON> prosto, aby s<PERSON>zy<PERSON>ć ze zjazdu.", "1": "<PERSON><PERSON> prosto, aby s<PERSON> ze zjazdu <BRANCH_SIGN>.", "2": "<PERSON><PERSON> prosto, a<PERSON> s<PERSON> ze zjazdu, k<PERSON><PERSON><PERSON>: <TOWARD_SIGN>.", "3": "<PERSON><PERSON> prosto, a<PERSON> s<PERSON> ze zjazdu <BRANCH_SIGN>, k<PERSON><PERSON><PERSON>: <TOWARD_SIGN>.", "4": "<PERSON><PERSON> prosto, aby s<PERSON> ze zjazdu <NAME_SIGN>."}, "example_phrases": {"0": ["Stay straight to take the ramp."], "1": ["Stay straight to take the US 322 East ramp."], "2": ["Stay straight to take the ramp toward Hershey."], "3": ["Stay straight to take the US 322 East/US 422 East/US 522 East/US 622 East ramp toward Hershey/Palmdale/Palmyra/Campbelltown."], "4": ["Stay straight to take the Gettysburg Pike ramp."]}}, "ramp_straight_verbal": {"phrases": {"0": "<PERSON><PERSON> prosto, aby s<PERSON>zy<PERSON>ć ze zjazdu.", "1": "<PERSON><PERSON> prosto, aby s<PERSON> ze zjazdu <BRANCH_SIGN>.", "2": "<PERSON><PERSON> prosto, a<PERSON> s<PERSON> ze zjazdu, k<PERSON><PERSON><PERSON>: <TOWARD_SIGN>.", "3": "<PERSON><PERSON> prosto, a<PERSON> s<PERSON> ze zjazdu <BRANCH_SIGN>, k<PERSON><PERSON><PERSON>: <TOWARD_SIGN>.", "4": "<PERSON><PERSON> prosto, aby s<PERSON> ze zjazdu <NAME_SIGN>."}, "example_phrases": {"0": ["Stay straight to take the ramp."], "1": ["Stay straight to take the U.S. 3 22 East ramp."], "2": ["Stay straight to take the ramp toward Hershey."], "3": ["Stay straight to take the U.S. 3 22 East, U.S. 4 22 East ramp toward Hershey, Palmdale."], "4": ["Stay straight to take the Gettysburg Pike ramp."]}}, "ramp_verbal": {"phrases": {"0": "<PERSON><PERSON><PERSON><PERSON> zjazd <RELATIVE_DIRECTION>.", "1": "<PERSON><PERSON><PERSON><PERSON> zjazd <BRANCH_SIGN> <RELATIVE_DIRECTION>.", "2": "<PERSON><PERSON><PERSON><PERSON> zjazd <RELATIVE_DIRECTION>, k<PERSON><PERSON>k: <TOWARD_SIGN>.", "3": "<PERSON><PERSON><PERSON><PERSON> zjazd <BRANCH_SIGN> <RELATIVE_DIRECTION>, k<PERSON><PERSON>k: <TOWARD_SIGN>.", "4": "<PERSON><PERSON><PERSON><PERSON> zjazd <NAME_SIGN> <RELATIVE_DIRECTION>.", "5": "<PERSON><PERSON><PERSON><PERSON><PERSON> <RELATIVE_DIRECTION>, aby s<PERSON> ze zjazdu.", "6": "<PERSON><PERSON><PERSON><PERSON><PERSON> <RELATIVE_DIRECTION>, aby s<PERSON><PERSON> ze zjazdu <BRANCH_SIGN>.", "7": "<PERSON><PERSON><PERSON><PERSON><PERSON> <RELATIVE_DIRECTION>, aby s<PERSON> ze zjazdu, kierunek: <TOWARD_SIGN>.", "8": "<PERSON><PERSON><PERSON><PERSON><PERSON> <RELATIVE_DIRECTION>, aby s<PERSON> ze zjazdu <BRANCH_SIGN>, k<PERSON><PERSON><PERSON>: <TOWARD_SIGN>.", "9": "<PERSON><PERSON><PERSON><PERSON><PERSON> <RELATIVE_DIRECTION>, aby s<PERSON><PERSON> ze zjazdu <NAME_SIGN>.", "10": "Skorzystaj ze zjazdu.", "11": "<PERSON><PERSON><PERSON><PERSON> zjazd <BRANCH_SIGN>.", "12": "Skorzystaj ze zjazdu, kierunek: <TOWARD_SIGN>.", "13": "<PERSON><PERSON><PERSON><PERSON> z<PERSON>zd <BRANCH_SIGN>, k<PERSON><PERSON><PERSON>: <TOWARD_SIGN>.", "14": "<PERSON><PERSON><PERSON><PERSON> zjazd <NAME_SIGN>."}, "relative_directions": ["w lewo", "w prawo"], "example_phrases": {"0": ["Take the ramp on the left.", "Take the ramp on the right."], "1": ["Take the Interstate 95 ramp on the right."], "2": ["Take the ramp on the left toward JFK."], "3": ["Take the South Conduit Avenue ramp on the left toward JFK."], "4": ["Take the Gettysburg Pike ramp on the right."], "5": ["Turn left to take the ramp.", "Turn right to take the ramp."], "6": ["Turn left to take the Pennsylvania 2 83 West ramp."], "7": ["Turn left to take the ramp toward Harrisburg/Harrisburg International Airport."], "8": ["Turn left to take the Pennsylvania 2 83 West ramp toward Harrisburg, Harrisburg International Airport."], "9": ["Turn right to take the Gettysburg Pike ramp."], "10": ["Take the ramp."], "11": ["Take the Interstate 95 ramp."], "12": ["Take the ramp toward JFK."], "13": ["Take the South Conduit Avenue ramp toward JFK."], "14": ["Take the Gettysburg Pike ramp."]}}, "sharp": {"phrases": {"0": "<PERSON><PERSON><PERSON><PERSON><PERSON> ostro <RELATIVE_DIRECTION>.", "1": "<PERSON><PERSON><PERSON><PERSON><PERSON> ostro <RELATIVE_DIRECTION> w stronę: <STREET_NAMES>.", "2": "<PERSON><PERSON><PERSON><PERSON><PERSON> ostro <RELATIVE_DIRECTION> w stronę: <BEGIN_STREET_NAMES>. Ko<PERSON><PERSON><PERSON><PERSON><PERSON> w<PERSON><PERSON><PERSON><PERSON><PERSON>: <STREET_NAMES>.", "3": "<PERSON><PERSON><PERSON><PERSON><PERSON> ostro <RELATIVE_DIRECTION>, pozostając na: <STREET_NAMES>.", "4": "<PERSON><PERSON><PERSON><PERSON><PERSON> ostro <RELATIVE_DIRECTION> na <JUNCTION_NAME>.", "5": "<PERSON><PERSON><PERSON><PERSON><PERSON> ostro <RELATIVE_DIRECTION>, k<PERSON><PERSON>k: <TOWARD_SIGN>."}, "empty_street_name_labels": ["chodnik", "ścieżka rowerowa", "górski szlak rowerowy", "prz<PERSON><PERSON><PERSON> dla pieszych", "schody", "most", "tunel"], "relative_directions": ["w lewo", "w prawo"], "example_phrases": {"0": ["Make a sharp left."], "1": ["Make a sharp right onto Flatbush Avenue."], "2": ["Make a sharp left onto North Bond Street/US 1 Business/MD 924. Continue on MD 924."], "3": ["Make a sharp right to stay on Sunstone Drive."], "4": ["Make a sharp right at Mannenbashi East."], "5": ["Make a sharp left toward Baltimore."]}}, "sharp_verbal": {"phrases": {"0": "<PERSON><PERSON><PERSON><PERSON><PERSON> ostro <RELATIVE_DIRECTION>.", "1": "<PERSON><PERSON><PERSON><PERSON><PERSON> ostro <RELATIVE_DIRECTION> w stronę: <STREET_NAMES>.", "2": "<PERSON><PERSON><PERSON><PERSON><PERSON> ostro <RELATIVE_DIRECTION> w stronę: <BEGIN_STREET_NAMES>.", "3": "<PERSON><PERSON><PERSON><PERSON><PERSON> ostro <RELATIVE_DIRECTION>, pozostając na: <STREET_NAMES>.", "4": "<PERSON><PERSON><PERSON><PERSON><PERSON> ostro <RELATIVE_DIRECTION> na <JUNCTION_NAME>.", "5": "<PERSON><PERSON><PERSON><PERSON><PERSON> ostro <RELATIVE_DIRECTION>, k<PERSON><PERSON>k: <TOWARD_SIGN>."}, "empty_street_name_labels": ["chodnik", "ścieżka rowerowa", "górski szlak rowerowy", "prz<PERSON><PERSON><PERSON> dla pieszych", "schody", "most", "tunel"], "relative_directions": ["w lewo", "w prawo"], "example_phrases": {"0": ["Make a sharp left."], "1": ["Make a sharp right onto Flatbush Avenue."], "2": ["Make a sharp left onto North Bond Street, U.S. 1 Business."], "3": ["Make a sharp right to stay on Sunstone Drive."], "4": ["Make a sharp right at Mannenbashi East."], "5": ["Make a sharp left toward Baltimore."]}}, "start": {"phrases": {"0": "<PERSON><PERSON><PERSON><PERSON> <CARDINAL_DIRECTION>.", "1": "<PERSON><PERSON><PERSON><PERSON> <PERSON> <CARDINAL_DIRECTION> w stronę: <STREET_NAMES>.", "2": "<PERSON><PERSON><PERSON><PERSON> <PERSON>ę <CARDINAL_DIRECTION> w stronę: <BEGIN_STREET_NAMES>. Ko<PERSON><PERSON><PERSON><PERSON><PERSON> w<PERSON><PERSON><PERSON><PERSON><PERSON>: <STREET_NAMES>.", "4": "<PERSON><PERSON> <CARDINAL_DIRECTION>.", "5": "<PERSON><PERSON> <CARDINAL_DIRECTION> w stronę: <STREET_NAMES>.", "6": "<PERSON><PERSON> <CARDINAL_DIRECTION> w stronę: <BEGIN_STREET_NAMES>. Kontynu<PERSON>j wz<PERSON><PERSON><PERSON><PERSON>: <STREET_NAMES>.", "8": "Id<PERSON> <CARDINAL_DIRECTION>.", "9": "I<PERSON><PERSON> <CARDINAL_DIRECTION> w stronę: <STREET_NAMES>.", "10": "Id<PERSON> <CARDINAL_DIRECTION> w stronę: <BEGIN_STREET_NAMES>. Kontynu<PERSON>j wz<PERSON><PERSON><PERSON><PERSON>: <STREET_NAMES>.", "16": "<PERSON><PERSON> <CARDINAL_DIRECTION>.", "17": "<PERSON><PERSON> <CARDINAL_DIRECTION> w stronę: <STREET_NAMES>.", "18": "<PERSON><PERSON> <CARDINAL_DIRECTION> w stronę: <BEGIN_STREET_NAMES>. Kontynu<PERSON>j wz<PERSON><PERSON><PERSON><PERSON>: <STREET_NAMES>."}, "cardinal_directions": ["na północ", "na północny wschód", "na wschód", "na południowy wschód", "na południe", "na południowy zachód", "na zachód", "na północny zachód"], "empty_street_name_labels": ["chodnik", "ścieżka rowerowa", "górski szlak rowerowy", "prz<PERSON><PERSON><PERSON> dla pieszych", "schody", "most", "tunel"], "example_phrases": {"0": ["Head east.", "Head north."], "1": ["Head southwest on 5th Avenue.", "Head west on the walkway", "Head east on the cycleway", "Head north on the mountain bike trail"], "2": ["Head south on North Prince Street/US 222/PA 272. Continue on US 222/PA 272."], "4": ["Drive east.", "Drive north."], "5": ["Drive southwest on 5th Avenue."], "6": ["Drive south on North Prince Street/US 222/PA 272. Continue on US 222/PA 272."], "8": ["Walk east.", "Walk north."], "9": ["Walk southwest on 5th Avenue.", "Walk west on the walkway"], "10": ["Walk south on North Prince Street/US 222/PA 272. Continue on US 222/PA 272."], "16": ["Bike east.", "Bike north."], "17": ["Bike southwest on 5th Avenue.", "Bike east on the cycleway", "Bike north on the mountain bike trail"], "18": ["Bike south on North Prince Street/US 222/PA 272. Continue on US 222/PA 272."]}}, "start_verbal": {"phrases": {"0": "<PERSON><PERSON><PERSON><PERSON> <CARDINAL_DIRECTION>.", "1": "<PERSON><PERSON><PERSON><PERSON> <CARDINAL_DIRECTION> przez <LENGTH>.", "2": "<PERSON><PERSON><PERSON><PERSON> <PERSON> <CARDINAL_DIRECTION> w stronę: <STREET_NAMES>.", "3": "<PERSON><PERSON><PERSON><PERSON> <PERSON> <CARDINAL_DIRECTION> w stronę: <STREET_NAMES> przez <LENGTH>.", "4": "<PERSON><PERSON><PERSON><PERSON> <PERSON> <CARDINAL_DIRECTION> w stronę: <BEGIN_STREET_NAMES>.", "5": "<PERSON><PERSON> <CARDINAL_DIRECTION>.", "6": "<PERSON><PERSON> <CARDINAL_DIRECTION> przez <LENGTH>.", "7": "<PERSON><PERSON> <CARDINAL_DIRECTION> w stronę: <STREET_NAMES>.", "8": "<PERSON><PERSON> <CARDINAL_DIRECTION> w stronę: <STREET_NAMES> przez <LENGTH>.", "9": "<PERSON><PERSON> <CARDINAL_DIRECTION> w stronę: <BEGIN_STREET_NAMES>.", "10": "Id<PERSON> <CARDINAL_DIRECTION>.", "11": "<PERSON><PERSON><PERSON> <CARDINAL_DIRECTION> przez <LENGTH>.", "12": "I<PERSON><PERSON> <CARDINAL_DIRECTION> w stronę: <STREET_NAMES>.", "13": "<PERSON><PERSON><PERSON> <CARDINAL_DIRECTION> w stronę: <STREET_NAMES> przez <LENGTH>.", "14": "Id<PERSON> <CARDINAL_DIRECTION> w stronę: <BEGIN_STREET_NAMES>.", "15": "<PERSON><PERSON> <CARDINAL_DIRECTION>.", "16": "<PERSON><PERSON> <CARDINAL_DIRECTION> przez <LENGTH>.", "17": "<PERSON><PERSON> <CARDINAL_DIRECTION> w stronę: <STREET_NAMES>.", "18": "<PERSON><PERSON> <CARDINAL_DIRECTION> w stronę: <STREET_NAMES> przez <LENGTH>.", "19": "<PERSON><PERSON> <CARDINAL_DIRECTION> w stronę: <BEGIN_STREET_NAMES>."}, "cardinal_directions": ["na północ", "na północny wschód", "na wschód", "na południowy wschód", "na południe", "na południowy zachód", "na zachód", "na północny zachód"], "empty_street_name_labels": ["chodnik", "ścieżka rowerowa", "górski szlak rowerowy", "prz<PERSON><PERSON><PERSON> dla pieszych", "schody", "most", "tunel"], "metric_lengths": ["<KILOMETERS> km", "1 km", "<METERS> m", "mniej niż 10 metrów"], "us_customary_lengths": ["<MILES> mi", "1 milę", "pół mili", "<PERSON><PERSON><PERSON><PERSON> mili", "<FEET> stóp", "mniej niż 10 stóp"], "example_phrases": {"0": ["Head east.", "Head north."], "1": ["Head east for a half mile.", "Head north for 1 kilometer."], "2": ["Head southwest on 5th Avenue."], "3": ["Head southwest on 5th Avenue for 1 tenth of a mile."], "4": ["Head south on North Prince Street, U.S. 2 22."], "5": ["Drive east.", "Drive north."], "6": ["Drive east for a half mile.", "Drive north for 1 kilometer."], "7": ["Drive southwest on 5th Avenue."], "8": ["Drive southwest on 5th Avenue for 1 tenth of a mile."], "9": ["Drive south on North Prince Street, U.S. 2 22."], "10": ["Walk east.", "Walk north."], "11": ["Walk east for a half mile.", "Walk north for 1 kilometer."], "12": ["Walk southwest on 5th Avenue."], "13": ["Walk southwest on 5th Avenue for 1 tenth of a mile."], "14": ["Walk south on North Prince Street, U.S. 2 22."], "15": ["Bike east.", "Bike north."], "16": ["Bike east for a half mile.", "Bike north for 1 kilometer."], "17": ["Bike southwest on 5th Avenue."], "18": ["Bike southwest on 5th Avenue for 1 tenth of a mile."], "19": ["Bike south on North Prince Street, U.S. 2 22."]}}, "transit": {"phrases": {"0": "W<PERSON><PERSON><PERSON> <TRANSIT_NAME>. (<TRANSIT_STOP_COUNT_LABEL>: <TRANSIT_STOP_COUNT>)", "1": "<PERSON><PERSON><PERSON><PERSON> <TRANSIT_NAME>, k<PERSON><PERSON><PERSON>: <TRANSIT_HEADSIGN>. (<TRANSIT_STOP_COUNT_LABEL>: <TRANSIT_STOP_COUNT>)"}, "empty_transit_name_labels": ["tram<PERSON>j", "metro", "pociąg", "autobus", "prom", "kolejkę linową", "gondolę", "kolejkę linowo-terenową"], "transit_stop_count_labels": {"one": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "other": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "example_phrases": {"0": ["Take the New Haven. (1 stop)", "Take the metro. (2 stops)", "Take the bus. (12 stops)"], "1": ["Take the F toward JAMAICA - 179 ST. (10 stops)", "Take the ferry toward Staten Island. (1 stop)"]}}, "transit_connection_destination": {"phrases": {"0": "<PERSON><PERSON><PERSON><PERSON> stację.", "1": "<PERSON><PERSON><PERSON><PERSON> <TRANSIT_STOP>.", "2": "<PERSON><PERSON><PERSON><PERSON> <STATION_LABEL> <TRANSIT_STOP>."}, "station_label": "sta<PERSON><PERSON><PERSON>", "example_phrases": {"0": ["Exit the station."], "1": ["Exit the Embarcadero Station."], "2": ["Exit the 8 St - NYU Station."]}}, "transit_connection_destination_verbal": {"phrases": {"0": "<PERSON><PERSON><PERSON><PERSON> stację.", "1": "<PERSON><PERSON><PERSON><PERSON> <TRANSIT_STOP>.", "2": "<PERSON><PERSON><PERSON><PERSON> <STATION_LABEL> <TRANSIT_STOP>."}, "station_label": "sta<PERSON><PERSON><PERSON>", "example_phrases": {"0": ["Exit the station."], "1": ["Exit the Embarcadero Station."], "2": ["Exit the 8 St - NYU Station."]}}, "transit_connection_start": {"phrases": {"0": "Wejdź na stację.", "1": "Wejdź na <TRANSIT_STOP>.", "2": "Wejdź na <STATION_LABEL> <TRANSIT_STOP>."}, "station_label": "sta<PERSON><PERSON><PERSON>", "example_phrases": {"0": ["Enter the station."], "1": ["Enter the Embarcadero Station."], "2": ["Enter the 8 St - NYU Station."]}}, "transit_connection_start_verbal": {"phrases": {"0": "Wejdź na stację.", "1": "Wejdź na <TRANSIT_STOP>.", "2": "Wejdź na <STATION_LABEL> <TRANSIT_STOP>."}, "station_label": "sta<PERSON><PERSON><PERSON>", "example_phrases": {"0": ["Enter the station."], "1": ["Enter the Embarcadero Station."], "2": ["Enter the 8 St - NYU Station."]}}, "transit_connection_transfer": {"phrases": {"0": "Przesiądź się na stacji.", "1": "Przesiądź się na <TRANSIT_STOP>.", "2": "Przesi<PERSON>dź się na <STATION_LABEL> <TRANSIT_STOP>."}, "station_label": "sta<PERSON><PERSON>", "example_phrases": {"0": ["Transfer at the station."], "1": ["Transfer at the Embarcadero Station."], "2": ["Transfer at the 8 St - NYU Station."]}}, "transit_connection_transfer_verbal": {"phrases": {"0": "Przesiądź się na stacji.", "1": "Przesiądź się na <TRANSIT_STOP>.", "2": "Przesi<PERSON>dź się na <STATION_LABEL> <TRANSIT_STOP>."}, "station_label": "sta<PERSON><PERSON>", "example_phrases": {"0": ["Transfer at the station."], "1": ["Transfer at the Embarcadero Station."], "2": ["Transfer at the 8 St - NYU Station."]}}, "transit_remain_on": {"phrases": {"0": "Pozostań <TRANSIT_NAME>. (<TRANSIT_STOP_COUNT_LABEL>: <TRANSIT_STOP_COUNT>)", "1": "Pozostań <TRANSIT_NAME>, k<PERSON><PERSON>k: <TRANSIT_HEADSIGN>. (<TRANSIT_STOP_COUNT_LABEL>: <TRANSIT_STOP_COUNT>)"}, "empty_transit_name_labels": ["w tramwaju", "w metrze", "w pociągu", "w autobusie", "na promie", "w kolejce linowej", "w gondoli", "w kolejce linowo-terenowej"], "transit_stop_count_labels": {"one": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "other": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "example_phrases": {"0": ["Remain on the New Haven. (1 stop)", "<PERSON><PERSON><PERSON> on the train. (3 stops)"], "1": ["Remain on the F toward JAMAICA - 179 ST. (10 stops)"]}}, "transit_remain_on_verbal": {"phrases": {"0": "Pozostań <TRANSIT_NAME>.", "1": "Pozostań <TRANSIT_NAME>, kierunek: <TRANSIT_HEADSIGN>."}, "empty_transit_name_labels": ["w tramwaju", "w metrze", "w pociągu", "w autobusie", "na promie", "w kolejce linowej", "w gondoli", "w kolejce linowo-terenowej"], "example_phrases": {"0": ["<PERSON><PERSON><PERSON> on the New Haven."], "1": ["Remain on the F toward JAMAICA - 179 ST."]}}, "transit_transfer": {"phrases": {"0": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> się i wybierz <TRANSIT_NAME>. (<TRANSIT_STOP_COUNT_LABEL>: <TRANSIT_STOP_COUNT>)", "1": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> się i wybierz <TRANSIT_NAME>, k<PERSON><PERSON>k: <TRANSIT_HEADSIGN>. (<TRANSIT_STOP_COUNT_LABEL>: <TRANSIT_STOP_COUNT>)"}, "empty_transit_name_labels": ["tram<PERSON>j", "metro", "pociąg", "autobus", "prom", "kolejkę linową", "gondolę", "kolejkę linowo-terenową"], "transit_stop_count_labels": {"one": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "other": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "example_phrases": {"0": ["Transfer to take the New Haven. (1 stop)", "Transfer to take the tram. (4 stops)"], "1": ["Transfer to take the F toward JAMAICA - 179 ST. (10 stops)"]}}, "transit_transfer_verbal": {"phrases": {"0": "Przesiądź się i wybierz <TRANSIT_NAME>.", "1": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> się i wybierz <TRANSIT_NAME>, k<PERSON>unek: <TRANSIT_HEADSIGN>."}, "empty_transit_name_labels": ["tram<PERSON>j", "metro", "pociąg", "autobus", "prom", "kolejkę linową", "gondolę", "kolejkę linowo-terenową"], "example_phrases": {"0": ["Transfer to take the New Haven."], "1": ["Transfer to take the F toward JAMAICA - 179 ST."]}}, "transit_verbal": {"phrases": {"0": "<PERSON><PERSON><PERSON><PERSON> <TRANSIT_NAME>.", "1": "<PERSON><PERSON><PERSON><PERSON> <TRANSIT_NAME>, k<PERSON><PERSON><PERSON>: <TRANSIT_HEADSIGN>."}, "empty_transit_name_labels": ["tram<PERSON>j", "metro", "pociąg", "autobus", "prom", "kolejkę linową", "gondolę", "kolejkę linowo-terenową"], "example_phrases": {"0": ["Take the New Haven."], "1": ["Take the F toward JAMAICA - 179 ST."]}}, "turn": {"phrases": {"0": "<PERSON><PERSON><PERSON><PERSON><PERSON> <RELATIVE_DIRECTION>.", "1": "<PERSON><PERSON><PERSON><PERSON><PERSON> <RELATIVE_DIRECTION> w <STREET_NAMES>.", "2": "<PERSON><PERSON><PERSON><PERSON><PERSON> <RELATIVE_DIRECTION> w <BEGIN_STREET_NAMES>. Kontynu<PERSON>j wz<PERSON><PERSON><PERSON><PERSON>: <STREET_NAMES>.", "3": "<PERSON><PERSON><PERSON><PERSON><PERSON> <RELATIVE_DIRECTION>, pozostając na: <STREET_NAMES>.", "4": "<PERSON><PERSON><PERSON><PERSON>ć <RELATIVE_DIRECTION> na <JUNCTION_NAME>.", "5": "<PERSON><PERSON><PERSON><PERSON><PERSON> <RELATIVE_DIRECTION>, k<PERSON><PERSON><PERSON>: <TOWARD_SIGN>."}, "empty_street_name_labels": ["chodnik", "ścieżka rowerowa", "górski szlak rowerowy", "prz<PERSON><PERSON><PERSON> dla pieszych", "schody", "most", "tunel"], "relative_directions": ["w lewo", "w prawo"], "example_phrases": {"0": ["Turn left."], "1": ["Turn right onto Flatbush Avenue."], "2": ["Turn left onto North Bond Street/US 1 Business/MD 924. Continue on MD 924."], "3": ["Turn right to stay on Sunstone Drive."], "4": ["Turn right at Mannenbashi East."], "5": ["Turn left toward Baltimore."]}}, "turn_verbal": {"phrases": {"0": "<PERSON><PERSON><PERSON><PERSON><PERSON> <RELATIVE_DIRECTION>.", "1": "<PERSON><PERSON><PERSON><PERSON><PERSON> <RELATIVE_DIRECTION> w <STREET_NAMES>.", "2": "<PERSON><PERSON><PERSON><PERSON><PERSON> <RELATIVE_DIRECTION> w <BEGIN_STREET_NAMES>.", "3": "<PERSON><PERSON><PERSON><PERSON><PERSON> <RELATIVE_DIRECTION>, pozostając na: <STREET_NAMES>.", "4": "<PERSON><PERSON><PERSON><PERSON>ć <RELATIVE_DIRECTION> na <JUNCTION_NAME>.", "5": "<PERSON><PERSON><PERSON><PERSON><PERSON> <RELATIVE_DIRECTION>, k<PERSON><PERSON><PERSON>: <TOWARD_SIGN>."}, "empty_street_name_labels": ["chodnik", "ścieżka rowerowa", "górski szlak rowerowy", "prz<PERSON><PERSON><PERSON> dla pieszych", "schody", "most", "tunel"], "relative_directions": ["w lewo", "w prawo"], "example_phrases": {"0": ["Turn left."], "1": ["Turn right onto Flatbush Avenue."], "2": ["Turn left onto North Bond Street, U.S. 1 Business."], "3": ["Turn right to stay on Sunstone Drive."], "4": ["Turn right at Mannenbashi East."], "5": ["Turn left toward Baltimore."]}}, "uturn": {"phrases": {"0": "<PERSON><PERSON><PERSON><PERSON><PERSON> <RELATIVE_DIRECTION>.", "1": "<PERSON><PERSON><PERSON><PERSON><PERSON> <RELATIVE_DIRECTION> w <STREET_NAMES>.", "2": "<PERSON><PERSON><PERSON><PERSON><PERSON> <RELATIVE_DIRECTION>, pozostając na: <STREET_NAMES>.", "3": "<PERSON><PERSON><PERSON><PERSON>ć <RELATIVE_DIRECTION> na: <CROSS_STREET_NAMES>.", "4": "<PERSON><PERSON><PERSON><PERSON><PERSON> <RELATIVE_DIRECTION> na: <CROSS_STREET_NAMES> w stronę: <STREET_NAMES>.", "5": "<PERSON><PERSON><PERSON><PERSON>ć <RELATIVE_DIRECTION> na: <CROSS_STREET_NAMES>, pozostając na: <STREET_NAMES>.", "6": "<PERSON><PERSON><PERSON><PERSON>ć <RELATIVE_DIRECTION> na <JUNCTION_NAME>.", "7": "<PERSON><PERSON><PERSON><PERSON><PERSON> <RELATIVE_DIRECTION>, k<PERSON><PERSON><PERSON>: <TOWARD_SIGN>."}, "empty_street_name_labels": ["chodnik", "ścieżka rowerowa", "górski szlak rowerowy", "prz<PERSON><PERSON><PERSON> dla pieszych", "schody", "most", "tunel"], "relative_directions": ["w lewo", "w prawo"], "example_phrases": {"0": ["Make a left U-turn."], "1": ["Make a right U-turn onto Bunker Hill Road."], "2": ["Make a left U-turn to stay on Bunker Hill Road."], "3": ["Make a left U-turn at Devonshire Road."], "4": ["Make a left U-turn at Devonshire Road onto Jonestown Road/US 22."], "5": ["Make a left U-turn at Devonshire Road to stay on Jonestown Road/US 22."], "6": ["Make a right U-turn at Mannenbashi East."], "7": ["Make a left U-turn toward Baltimore."]}}, "uturn_verbal": {"phrases": {"0": "<PERSON><PERSON><PERSON><PERSON><PERSON> <RELATIVE_DIRECTION>.", "1": "<PERSON><PERSON><PERSON><PERSON><PERSON> <RELATIVE_DIRECTION> w <STREET_NAMES>.", "2": "<PERSON><PERSON><PERSON><PERSON><PERSON> <RELATIVE_DIRECTION>, pozostając na: <STREET_NAMES>.", "3": "<PERSON><PERSON><PERSON><PERSON>ć <RELATIVE_DIRECTION> na: <CROSS_STREET_NAMES>.", "4": "<PERSON><PERSON><PERSON><PERSON><PERSON> <RELATIVE_DIRECTION> na: <CROSS_STREET_NAMES> w stronę: <STREET_NAMES>.", "5": "<PERSON><PERSON><PERSON><PERSON>ć <RELATIVE_DIRECTION> na: <CROSS_STREET_NAMES>, pozostając na: <STREET_NAMES>.", "6": "<PERSON><PERSON><PERSON><PERSON>ć <RELATIVE_DIRECTION> na <JUNCTION_NAME>.", "7": "<PERSON><PERSON><PERSON><PERSON><PERSON> <RELATIVE_DIRECTION>, k<PERSON><PERSON><PERSON>: <TOWARD_SIGN>."}, "empty_street_name_labels": ["chodnik", "ścieżka rowerowa", "górski szlak rowerowy", "prz<PERSON><PERSON><PERSON> dla pieszych", "schody", "most", "tunel"], "relative_directions": ["w lewo", "w prawo"], "example_phrases": {"0": ["Make a left U-turn."], "1": ["Make a right U-turn onto Bunker Hill Road."], "2": ["Make a left U-turn to stay on Bunker Hill Road."], "3": ["Make a left U-turn at Devonshire Road."], "4": ["Make a left U-turn at Devonshire Road onto Jonestown Road, U.S. 22."], "5": ["Make a left U-turn at Devonshire Road to stay on Jonestown Road, U.S. 22."], "6": ["Make a right U-turn at Mannenbashi East."], "7": ["Make a left U-turn toward Baltimore."]}}, "verbal_multi_cue": {"phrases": {"0": "<CURRENT_VERBAL_CUE> Na<PERSON><PERSON><PERSON><PERSON>: <NEXT_VERBAL_CUE>", "1": "<CURRENT_VERBAL_CUE> Następnie za <LENGTH>: <NEXT_VERBAL_CUE>"}, "metric_lengths": ["<KILOMETERS> km", "1 km", "<METERS> m", "mniej niż 10 metrów"], "us_customary_lengths": ["<MILES> mi", "1 milę", "pół mili", "<PERSON><PERSON><PERSON><PERSON> mili", "<FEET> stóp", "mniej niż 10 stóp"], "example_phrases": {"0": ["Bear right onto East Fayette Street. Then Turn right onto North Gay Street."], "1": ["Bear right onto East Fayette Street. Then, in 500 feet, Turn right onto North Gay Street."]}}, "approach_verbal_alert": {"phrases": {"0": "Za <LENGTH>: <CURRENT_VERBAL_CUE>"}, "metric_lengths": ["<KILOMETERS> km", "1 km", "<METERS> m", "mniej niż 10 metrów"], "us_customary_lengths": ["<MILES> mil", "1 milę", "pół mili", "<PERSON><PERSON><PERSON><PERSON> mili", "<FEET> stóp", "mniej niż 10 stóp"], "example_phrases": {"0": ["In a quarter mile, Turn right onto North Gay Street."]}}, "pass": {"phrases": {"0": "<PERSON><PERSON><PERSON><PERSON><PERSON> <OBJECT_LABEL>.", "1": "Przejdź na światłach <OBJECT_LABEL>."}, "object_labels": ["bram<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "skrzyżowanie dróg"], "example_phrases": {"0": ["Pass the gate."]}}, "elevator": {"phrases": {"0": "Skorzystaj z windy.", "1": "Skorzystaj z windy na <LEVEL>."}, "example_phrases": {"0": ["Wsiądź do windy."], "1": ["Wsiądź do windy i jedź na poziom 1."]}}, "steps": {"phrases": {"0": "Użyj schodów.", "1": "Skorzystaj ze schodów na <LEVEL>."}, "example_phrases": {"0": ["Wejdź na schody."], "1": ["Przejdź schodami na poziom 2."]}}, "escalator": {"phrases": {"0": "Skorzystaj z ruchomych schodów.", "1": "Skorzystaj z ruchomych schodów na <LEVEL>."}, "example_phrases": {"0": ["Take the escalator."], "1": ["Take the escalator to Level 3."]}}, "enter_building": {"phrases": {"0": "Wejdź do budynku.", "1": "Wejdź do budynku i idź dalej na <STREET_NAMES>."}, "empty_street_name_labels": ["chodnik", "ścieżka rowerowa", "górski szlak rowerowy", "prz<PERSON><PERSON><PERSON> dla pieszych"], "example_phrases": {"0": ["Enter the building."], "1": ["Enter the building, and continue on the walkway."]}}, "exit_building": {"phrases": {"0": "<PERSON><PERSON><PERSON><PERSON> budynek.", "1": "Wyjdź z budynku i idź dalej na <STREET_NAMES>."}, "empty_street_name_labels": ["chodnik", "ścieżka rowerowa", "górski szlak rowerowy", "prz<PERSON><PERSON><PERSON> dla pieszych"], "example_phrases": {"0": ["Exit the building."], "1": ["Exit the building, and continue on the walkway."]}}}}
{"posix_locale": "ru_RU.UTF-8", "aliases": ["ru"], "instructions": {"arrive": {"phrases": {"0": "Прибытие: <TIME>.", "1": "Прибытие: <TIME> в <TRANSIT_STOP>."}, "example_phrases": {"0": ["Arrive: 8:02 AM."], "1": ["Arrive: 8:02 AM at 8 St - NYU."]}}, "arrive_verbal": {"phrases": {"0": "Прибытие к <TIME>.", "1": "Прибытие к <TIME> в <TRANSIT_STOP>."}, "example_phrases": {"0": ["Arrive at 8:02 AM."], "1": ["Arrive at 8:02 AM at 8 St - NYU."]}}, "bear": {"phrases": {"0": "Поверните <RELATIVE_DIRECTION>.", "1": "Поверните <RELATIVE_DIRECTION> на <STREET_NAMES>.", "2": "Поверните <RELATIVE_DIRECTION> на <BEGIN_STREET_NAMES>. Затем двигайтесь по <STREET_NAMES>.", "3": "Поверните <RELATIVE_DIRECTION>, оставаясь на <STREET_NAMES>.", "4": "Поверните <RELATIVE_DIRECTION> на <JUNCTION_NAME>.", "5": "Поверните <RELATIVE_DIRECTION> к <TOWARD_SIGN>."}, "empty_street_name_labels": ["пешеходную дорожку", "велосипедную дорожку", "дорожку для горных велосипедов", "пешеходный переход", "the stairs", "the bridge", "the tunnel"], "relative_directions": ["налево", "направо"], "example_phrases": {"0": ["Bear right."], "1": ["<PERSON> left onto Arlen Road."], "2": ["Bear right onto Belair Road/US 1 Business. Continue on US 1 Business."], "3": ["<PERSON> left to stay on US 15 South."], "4": ["Bear right at Mannenbashi East."], "5": ["Bear left toward Baltimore."]}}, "bear_verbal": {"phrases": {"0": "Поверните <RELATIVE_DIRECTION>.", "1": "Поверните <RELATIVE_DIRECTION> на <STREET_NAMES>.", "2": "Поверните <RELATIVE_DIRECTION> на <BEGIN_STREET_NAMES>.", "3": "Поверните <RELATIVE_DIRECTION>, оставаясь на <STREET_NAMES>.", "4": "Поверните <RELATIVE_DIRECTION> на <JUNCTION_NAME>.", "5": "Поверните <RELATIVE_DIRECTION> к <TOWARD_SIGN>."}, "empty_street_name_labels": ["пешеходную дорожку", "велосипедную дорожку", "дорожку для горных велосипедов", "пешеходный переход", "the stairs", "the bridge", "the tunnel"], "relative_directions": ["налево", "направо"], "example_phrases": {"0": ["Bear right."], "1": ["<PERSON> left onto Arlen Road."], "2": ["Bear right onto Belair Road, U.S. 1 Business."], "3": ["<PERSON> left to stay on U.S. 15 South."], "4": ["Bear right at Mannenbashi East."], "5": ["Bear left toward Baltimore."]}}, "becomes": {"phrases": {"0": "<PREVIOUS_STREET_NAMES> переходит в <STREET_NAMES>."}, "example_phrases": {"0": ["Vine Street becomes Middletown Road."]}}, "becomes_verbal": {"phrases": {"0": "<PREVIOUS_STREET_NAMES> переходит в <STREET_NAMES>."}, "example_phrases": {"0": ["Vine Street becomes Middletown Road."]}}, "continue": {"phrases": {"0": "Продолжайте движение.", "1": "Продолжайте движение по <STREET_NAMES>.", "2": "Продолжайте движение через <JUNCTION_NAME>.", "3": "Продолжайте движение на <TOWARD_SIGN>."}, "empty_street_name_labels": ["пешеходной дорожке", "велосипедной дорожке", "дорожке для горных велосипедов", "пешеходный переход", "the stairs", "the bridge", "the tunnel"], "example_phrases": {"0": ["Continue."], "1": ["Continue on 10th Avenue."], "2": ["Continue at Mannenbashi East."], "3": ["Continue toward Baltimore."]}}, "continue_verbal": {"phrases": {"0": "Продолжайте движение.", "1": "Продолжайте движение еще <LENGTH>.", "2": "Продолжайте движение по <STREET_NAMES>.", "3": "Продолжайте движение по <STREET_NAMES> еще <LENGTH>.", "4": "Продолжайте движение через <JUNCTION_NAME>.", "5": "Продолжайте движение через <JUNCTION_NAME> еще <LENGTH>.", "6": "Продолжайте движение <TOWARD_SIGN>.", "7": "Продолжайте движение <TOWARD_SIGN> еще <LENGTH>."}, "empty_street_name_labels": ["пешеходной дорожке", "велосипедной дорожке", "дорожке для горных велосипедов", "пешеходному переходу", "the stairs", "the bridge", "the tunnel"], "metric_lengths": ["<KILOMETERS> ки<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "один километр", "<METERS> ме<PERSON><PERSON><PERSON>", "менее десяти метров"], "us_customary_lengths": ["<MILES> миль", "одну милю", "полмили", "четверть мили", "<FEET> футов", "менее десяти футов"], "example_phrases": {"0": ["Continue."], "1": ["Continue for 300 feet."], "2": ["Continue on 10th Avenue."], "3": ["Continue on 10th Avenue for 3 miles."], "4": ["Continue at Mannenbashi East."], "5": ["Continue at Mannenbashi East for 2 miles."], "6": ["Continue toward Baltimore."], "7": ["Continue toward Baltimore for 5 miles."]}}, "continue_verbal_alert": {"phrases": {"0": "Продолжайте движение.", "1": "Продолжайте движение по <STREET_NAMES>.", "2": "Продолжайте движение через <JUNCTION_NAME>.", "3": "Продолжайте движение <TOWARD_SIGN>."}, "empty_street_name_labels": ["пешеходная дорожка", "велосипедная дорожка", "дорожка для горных велосипедов", "пешеходный переход", "the stairs", "the bridge", "the tunnel"], "example_phrases": {"0": ["Continue."], "1": ["Continue on 10th Avenue."], "2": ["Continue at Mannenbashi East."], "3": ["Continue toward Baltimore."]}}, "depart": {"phrases": {"0": "Отправление: <TIME>.", "1": "Отправление: <TIME> из <TRANSIT_STOP>."}, "example_phrases": {"0": ["Depart: 8:02 AM."], "1": ["Depart: 8:02 AM from 8 St - NYU."]}}, "depart_verbal": {"phrases": {"0": "Отправление в <TIME>.", "1": "Отправление в <TIME> из <TRANSIT_STOP>."}, "example_phrases": {"0": ["Depart at 8:02 AM."], "1": ["Depart at 8:02 AM from 8 St - NYU."]}}, "destination": {"phrases": {"0": "Вы прибыли в пункт назначения.", "1": "Вы прибыли в <DESTINATION>.", "2": "Пункт назначения <RELATIVE_DIRECTION>.", "3": "<DESTINATION> <RELATIVE_DIRECTION>."}, "relative_directions": ["слева", "справа"], "example_phrases": {"0": ["You have arrived at your destination."], "1": ["You have arrived at 3206 Powelton Avenue."], "2": ["Your destination is on the left.", "Your destination is on the right."], "3": ["Lancaster Brewing Company is on the left."]}}, "destination_verbal": {"phrases": {"0": "Вы прибыли в пункт назначения.", "1": "Вы прибыли в <DESTINATION>.", "2": "Пункт назначения <RELATIVE_DIRECTION>.", "3": "<DESTINATION> <RELATIVE_DIRECTION>."}, "relative_directions": ["слева", "справа"], "example_phrases": {"0": ["You have arrived at your destination."], "1": ["You have arrived at 32 o6 Powelton Avenue."], "2": ["Your destination is on the left.", "Your destination is on the right."], "3": ["Lancaster Brewing Company is on the left."]}}, "destination_verbal_alert": {"phrases": {"0": "Вы прибудете в пункт назначения.", "1": "Вы прибудете в <DESTINATION>.", "2": "Пункт назначения будет <RELATIVE_DIRECTION>.", "3": "<DESTINATION> будет <RELATIVE_DIRECTION>."}, "relative_directions": ["слева", "справа"], "example_phrases": {"0": ["You will arrive at your destination."], "1": ["You will arrive at 32 o6 Powelton Avenue."], "2": ["Your destination will be on the left.", "Your destination will be on the right."], "3": ["Lancaster Brewing Company will be on the left."]}}, "enter_ferry": {"phrases": {"0": "Садитесь на паром.", "1": "Двигайтесь по <STREET_NAMES>.", "2": "Двигайтесь по <STREET_NAMES> <FERRY_LABEL>.", "3": "Садитесь на паром в направлении <TOWARD_SIGN>."}, "empty_street_name_labels": ["пешеходной дорожке", "велосипедной дорожке", "дорожке для горных велосипедов", "пешеходному переходу", "the stairs", "the bridge", "the tunnel"], "ferry_label": "Паром", "example_phrases": {"0": ["Take the Ferry."], "1": ["Take the Millersburg Ferry."], "2": ["Take the Bridgeport - Port Jefferson Ferry."], "3": ["Take the ferry toward Cape May."]}}, "enter_ferry_verbal": {"phrases": {"0": "Садитесь на паром.", "1": "Двигайтесь по <STREET_NAMES>.", "2": "Двигайтесь по <STREET_NAMES> <FERRY_LABEL>.", "3": "Садитесь на паром в направлении <TOWARD_SIGN>."}, "empty_street_name_labels": ["пешеходной дорожке", "велосипедной дорожке", "дорожке для горных велосипедов", "пешеходному переходу", "the stairs", "the bridge", "the tunnel"], "ferry_label": "Паром", "example_phrases": {"0": ["Take the Ferry."], "1": ["Take the Millersburg Ferry."], "2": ["Take the Bridgeport - Port Jefferson Ferry."], "3": ["Take the ferry toward Cape May."]}}, "enter_roundabout": {"phrases": {"0": "Выезжайте на кольцевую развязку.", "1": "На кольцевой развязке сверните на <ORDINAL_VALUE> съезде.", "2": "На кольцевой развязке сверните на <ORDINAL_VALUE> съезде на <ROUNDABOUT_EXIT_STREET_NAMES>.", "3": "На кольцевой развязке сверните на <ORDINAL_VALUE> съезде на <ROUNDABOUT_EXIT_BEGIN_STREET_NAMES>. Продолжите движение по <ROUNDABOUT_EXIT_STREET_NAMES>.", "4": "На кольцевой развязке сверните на <ORDINAL_VALUE> съезде в направлении <TOWARD_SIGN>.", "5": "На кольцевой развязке сверните на <ROUNDABOUT_EXIT_STREET_NAMES>.", "6": "На кольцевой развязке сверните на <ROUNDABOUT_EXIT_BEGIN_STREET_NAMES>. Продолжите движение по <ROUNDABOUT_EXIT_STREET_NAMES>.", "7": "На кольцевой развязке сверните в направлении <TOWARD_SIGN>.", "8": "Сверните на <STREET_NAMES>", "9": "Сверните на <STREET_NAMES> и сверните на <ORDINAL_VALUE> съезде.", "10": "Сверните на <STREET_NAMES> и сверните на <ORDINAL_VALUE> съезде на <ROUNDABOUT_EXIT_STREET_NAMES>.", "11": "Сверните на <STREET_NAMES> и сверните на <ORDINAL_VALUE> съезде на <ROUNDABOUT_EXIT_BEGIN_STREET_NAMES>. Продолжайте движение по <ROUNDABOUT_EXIT_STREET_NAMES>.", "12": "Сверните на <STREET_NAMES> и сверните на <ORDINAL_VALUE> съезде в сторону <TOWARD_SIGN>.", "13": "Сверните на <STREET_NAMES> и сверните на <ROUNDABOUT_EXIT_STREET_NAMES>.", "14": "Сверните на <STREET_NAMES> и сверните на <ROUNDABOUT_EXIT_BEGIN_STREET_NAMES>. Продолжите движение по <ROUNDABOUT_EXIT_STREET_NAMES>.", "15": "Сверните на <STREET_NAMES> и сверните в направлении<TOWARD_SIGN>."}, "ordinal_values": ["первом", "втором", "третьем", "четвёртом", "пятом", "шестом", "седьмом", "восьмом", "девятом", "десятом"], "empty_street_name_labels": ["пешеходную дорожку", "велосипедную дорожку", "дорожку для горных велосипедов", "пешеходный переход", "the stairs", "the bridge", "the tunnel"], "example_phrases": {"0": ["Enter the roundabout."], "1": ["Enter the roundabout and take the 1st exit.", "Enter the roundabout and take the 2nd exit.", "Enter the roundabout and take the 3rd exit.", "Enter the roundabout and take the 4th exit.", "Enter the roundabout and take the 5th exit.", "Enter the roundabout and take the 6th exit.", "Enter the roundabout and take the 7th exit.", "Enter the roundabout and take the 8th exit.", "Enter the roundabout and take the 9th exit.", "Enter the roundabout and take the 10th exit."], "2": ["Enter the roundabout and take the 3rd exit onto Main Street."], "3": ["Enter the roundabout and take the 3rd exit onto US 322/Main Street. Continue on US 322."], "4": ["Enter the roundabout and take the 3rd exit toward Baltimore."], "5": ["Enter the roundabout and take the exit onto Main Street."], "6": ["Enter the roundabout and take the exit onto US 322/Main Street. Continue on US 322."], "7": ["Enter the roundabout and take the exit toward Baltimore."], "8": ["Enter Dupont Circle."], "9": ["Enter Dupont Circle and take the 1st exit."], "10": ["Enter Dupont Circle and take the 3rd exit onto Main Street."], "11": ["Enter Dupont Circle and take the 3rd exit onto US 322/Main Street. Continue on US 322."], "12": ["Enter Dupont Circle and take the 3rd exit toward Baltimore."], "13": ["Enter Dupont Circle and take the exit onto Main Street."], "14": ["Enter Dupont Circle and take the exit onto US 322/Main Street. Continue on US 322."], "15": ["Enter Dupont Circle and take the exit toward Baltimore."]}}, "enter_roundabout_verbal": {"phrases": {"0": "Выезжайте на кольцевую развязку.", "1": "На кольцевой развязке сверните на <ORDINAL_VALUE> съезде.", "2": "На кольцевой развязке сверните на <ORDINAL_VALUE> съезде на <ROUNDABOUT_EXIT_STREET_NAMES>.", "3": "На кольцевой развязке сверните на <ORDINAL_VALUE> съезде на <ROUNDABOUT_EXIT_BEGIN_STREET_NAMES>.", "4": "На кольцевой развязке сверните на <ORDINAL_VALUE> съезде <TOWARD_SIGN>.", "5": "На кольцевой развязке сверните на <ROUNDABOUT_EXIT_STREET_NAMES>.", "6": "На кольцевой развязке сверните на <ROUNDABOUT_EXIT_BEGIN_STREET_NAMES>.", "7": "На кольцевой развязке сверните <TOWARD_SIGN>.", "8": "Сверните на <STREET_NAMES>", "9": "Сверните на <STREET_NAMES> и сверните на <ORDINAL_VALUE> съезде.", "10": "Сверните на <STREET_NAMES> и сверните на <ORDINAL_VALUE> съезде на <ROUNDABOUT_EXIT_STREET_NAMES>.", "11": "Сверните на <STREET_NAMES> и сверните на <ORDINAL_VALUE> съезде на <ROUNDABOUT_EXIT_BEGIN_STREET_NAMES>.", "12": "Сверните на <STREET_NAMES> и сверните на <ORDINAL_VALUE> съезде <TOWARD_SIGN>.", "13": "Сверните на <STREET_NAMES> и сверните на <ROUNDABOUT_EXIT_STREET_NAMES>.", "14": "Сверните на <STREET_NAMES> и сверните на <ROUNDABOUT_EXIT_BEGIN_STREET_NAMES>.", "15": "Сверните на <STREET_NAMES> и сверните <TOWARD_SIGN>."}, "ordinal_values": ["первом", "втором", "третьем", "четвёртом", "пятом", "шестом", "седьмом", "восьмом", "девятом", "десятом"], "empty_street_name_labels": ["пешеходную дорожку", "велосипедную дорожку", "дорожку для горных велосипедов", "пешеходный переход", "the stairs", "the bridge", "the tunnel"], "example_phrases": {"0": ["Enter the roundabout."], "1": ["Enter the roundabout and take the 1st exit.", "Enter the roundabout and take the 2nd exit.", "Enter the roundabout and take the 3rd exit.", "Enter the roundabout and take the 4th exit.", "Enter the roundabout and take the 5th exit.", "Enter the roundabout and take the 6th exit.", "Enter the roundabout and take the 7th exit.", "Enter the roundabout and take the 8th exit.", "Enter the roundabout and take the 9th exit.", "Enter the roundabout and take the 10th exit."], "2": ["Enter the roundabout and take the 3rd exit onto Main Street."], "3": ["Enter the roundabout and take the 3rd exit onto U.S. 3 22/Main Street."], "4": ["Enter the roundabout and take the 3rd exit toward Baltimore."], "5": ["Enter the roundabout and take the exit onto Main Street."], "6": ["Enter the roundabout and take the exit onto U.S. 3 22/Main Street."], "7": ["Enter the roundabout and take the exit toward Baltimore."], "8": ["Enter Dupont Circle."], "9": ["Enter Dupont Circle and take the 1st exit."], "10": ["Enter Dupont Circle and take the 3rd exit onto Main Street."], "11": ["Enter Dupont Circle and take the 3rd exit onto U.S. 3 22/Main Street."], "12": ["Enter Dupont Circle and take the 3rd exit toward Baltimore."], "13": ["Enter Dupont Circle and take the exit onto Main Street."], "14": ["Enter Dupont Circle and take the exit onto U.S. 3 22/Main Street."], "15": ["Enter Dupont Circle and take the exit toward Baltimore."]}}, "exit": {"phrases": {"0": "Сверните на съезде <RELATIVE_DIRECTION>.", "1": "Сверните на съезде <NUMBER_SIGN> <RELATIVE_DIRECTION>.", "2": "Сверните на съезде на <BRANCH_SIGN> <RELATIVE_DIRECTION>.", "3": "Сверните на съезде <NUMBER_SIGN> <RELATIVE_DIRECTION> на <BRANCH_SIGN>.", "4": "Сверните на съезде <RELATIVE_DIRECTION> к <TOWARD_SIGN>.", "5": "Сверните на съезде <NUMBER_SIGN> <RELATIVE_DIRECTION> к <TOWARD_SIGN>.", "6": "Сверните на съезде на <BRANCH_SIGN> <RELATIVE_DIRECTION> к <TOWARD_SIGN>.", "7": "Сверните на съезде <NUMBER_SIGN> <RELATIVE_DIRECTION> на <BRANCH_SIGN> к <TOWARD_SIGN>.", "8": "Сверните на съезде на <NAME_SIGN> <RELATIVE_DIRECTION>.", "10": "Сверните на съезде на <NAME_SIGN> <RELATIVE_DIRECTION> на <BRANCH_SIGN>.", "12": "Сверните на съезде на <NAME_SIGN> <RELATIVE_DIRECTION> к <TOWARD_SIGN>.", "14": "Сверните на съезде на <NAME_SIGN> <RELATIVE_DIRECTION> на <BRANCH_SIGN> к <TOWARD_SIGN>.", "15": "Сверните на съезде.", "16": "Сверните на съезде <NUMBER_SIGN>.", "17": "Сверните на съезде на <BRANCH_SIGN>.", "18": "Сверните на съезде <NUMBER_SIGN> к <BRANCH_SIGN>.", "19": "Сверните на съезде к <TOWARD_SIGN>.", "20": "Сверните на съезде <NUMBER_SIGN> к <TOWARD_SIGN>.", "21": "Сверните на съезде <BRANCH_SIGN> к <TOWARD_SIGN>.", "22": "Сверните на съезде <NUMBER_SIGN> на <BRANCH_SIGN> к <TOWARD_SIGN>.", "23": "Сверните на съезде <NAME_SIGN>.", "25": "Сверните на съезде <NAME_SIGN> к <BRANCH_SIGN>.", "27": "Сверните на съезде <NAME_SIGN> к <TOWARD_SIGN>.", "29": "Сверните на съезде <NAME_SIGN> на <BRANCH_SIGN> к <TOWARD_SIGN>."}, "relative_directions": ["слева", "справа"], "example_phrases": {"0": ["Take the exit on the left.", "Take the exit on the right."], "1": ["Take exit 67 B-A on the right."], "2": ["Take the US 322 West exit on the right."], "3": ["Take exit 67 B-A on the right onto US 322 West."], "4": ["Take the exit on the right toward Lewistown."], "5": ["Take exit 67 B-A on the right toward Lewistown."], "6": ["Take the US 322 West exit on the right toward Lewistown."], "7": ["Take exit 67 B-A on the right onto US 322 West toward Lewistown/State College."], "8": ["Take the White Marsh Boulevard exit on the left."], "10": ["Take the White Marsh Boulevard exit on the left onto MD 43 East."], "12": ["Take the White Marsh Boulevard exit on the left toward White Marsh."], "14": ["Take the White Marsh Boulevard exit on the left onto MD 43 East toward White Marsh."], "15": ["Take the exit."], "16": ["Take exit 67 B-A."], "17": ["Take the US 322 West exit."], "18": ["Take exit 67 B-A onto US 322 West."], "19": ["Take the exit toward Lewistown."], "20": ["Take exit 67 B-A toward Lewistown."], "21": ["Take the US 322 West exit toward Lewistown."], "22": ["Take exit 67 B-A onto US 322 West toward Lewistown/State College."], "23": ["Take the White Marsh Boulevard exit."], "25": ["Take the White Marsh Boulevard exit onto MD 43 East."], "27": ["Take the White Marsh Boulevard exit toward White Marsh."], "29": ["Take the White Marsh Boulevard exit onto MD 43 East toward White Marsh."]}}, "exit_roundabout": {"phrases": {"0": "Сверните с кольцевой развязки.", "1": "Сверните с кольцевой развязки на <STREET_NAMES>.", "2": "Сверните с кольцевой развязки на <BEGIN_STREET_NAMES>. Продолжите движение по <STREET_NAMES>.", "3": "Сверните с кольцевой развязки к <TOWARD_SIGN>."}, "empty_street_name_labels": ["пешеходную дорожку", "велосипедную дорожку", "дорожку для горных велосипедов", "пешеходный переход", "the stairs", "the bridge", "the tunnel"], "example_phrases": {"0": ["Exit the roundabout."], "1": ["Exit the roundabout onto Philadelphia Road/MD 7."], "2": ["Exit the roundabout onto Catoctin Mountain Highway/US 15. Continue on US 15."], "3": ["Exit the roundabout toward Baltimore."]}}, "exit_roundabout_verbal": {"phrases": {"0": "Сверните с кольцевой развязки.", "1": "Сверните с кольцевой развязки на <STREET_NAMES>.", "2": "Сверните с кольцевой развязки на <BEGIN_STREET_NAMES>.", "3": "Сверните с кольцевой развязки к <TOWARD_SIGN>."}, "empty_street_name_labels": ["пешеходную дорожку", "велосипедную дорожку", "дорожку для горных велосипедов", "пешеходный переход", "the stairs", "the bridge", "the tunnel"], "example_phrases": {"0": ["Exit the roundabout."], "1": ["Exit the roundabout onto Philadelphia Road, Maryland 7."], "2": ["Exit the roundabout onto Catoctin Mountain Highway, U.S. 15."], "3": ["Exit the roundabout toward Baltimore."]}}, "exit_verbal": {"phrases": {"0": "Сверните на съезд <RELATIVE_DIRECTION>.", "1": "Сверните на съезд <NUMBER_SIGN> <RELATIVE_DIRECTION>.", "2": "Сверните на съезд <BRANCH_SIGN> <RELATIVE_DIRECTION>.", "3": "Сверните на съезд <NUMBER_SIGN> <RELATIVE_DIRECTION> на <BRANCH_SIGN>.", "4": "Сверните на съезд <RELATIVE_DIRECTION> к <TOWARD_SIGN>.", "5": "Сверните на съезд <NUMBER_SIGN> <RELATIVE_DIRECTION> к <TOWARD_SIGN>.", "6": "Сверните на съезд на <BRANCH_SIGN> <RELATIVE_DIRECTION> к <TOWARD_SIGN>.", "7": "Сверните на съезд <NUMBER_SIGN> <RELATIVE_DIRECTION> на <BRANCH_SIGN> к <TOWARD_SIGN>.", "8": "Сверните на съезд на <NAME_SIGN> <RELATIVE_DIRECTION>.", "10": "Сверните на съезд на <NAME_SIGN> <RELATIVE_DIRECTION> на <BRANCH_SIGN>.", "12": "Сверните на съезд на <NAME_SIGN> <RELATIVE_DIRECTION> к <TOWARD_SIGN>.", "14": "Сверните на съезд на <NAME_SIGN> <RELATIVE_DIRECTION> на <BRANCH_SIGN> к <TOWARD_SIGN>.", "15": "Сверните на съезд.", "16": "Сверните на съезд <NUMBER_SIGN>.", "17": "Сверните на съезд на <BRANCH_SIGN>.", "18": "Сверните на съезд <NUMBER_SIGN> к <BRANCH_SIGN>.", "19": "Сверните на съезд к <TOWARD_SIGN>.", "20": "Сверните на съезд <NUMBER_SIGN> к <TOWARD_SIGN>.", "21": "Сверните на съезд <BRANCH_SIGN> к <TOWARD_SIGN>.", "22": "Сверните на съезд <NUMBER_SIGN> на <BRANCH_SIGN> к <TOWARD_SIGN>.", "23": "Сверните на съезд на <NAME_SIGN>.", "25": "Сверните на съезд на <NAME_SIGN> к <BRANCH_SIGN>.", "27": "Сверните на съезд на <NAME_SIGN> к <TOWARD_SIGN>.", "29": "Сверните на съезд на <NAME_SIGN> на <BRANCH_SIGN> к <TOWARD_SIGN>."}, "relative_directions": ["слева", "справа"], "example_phrases": {"0": ["Take the exit on the left.", "Take the exit on the right."], "1": ["Take exit 67 B-A on the right."], "2": ["Take the U.S. 3 22 West exit on the right."], "3": ["Take exit 67 B-A on the right onto U.S. 3 22 West."], "4": ["Take the exit on the right toward Lewistown."], "5": ["Take exit 67 B-A on the right toward Lewistown."], "6": ["Take the U.S. 3 22 West exit on the right toward Lewistown."], "7": ["Take exit 67 B-A on the right onto U.S. 3 22 West toward Lewistown, State College."], "8": ["Take the White Marsh Boulevard exit on the left."], "10": ["Take the White Marsh Boulevard exit on the left onto Maryland 43 East."], "12": ["Take the White Marsh Boulevard exit on the left toward White Marsh."], "14": ["Take the White Marsh Boulevard exit on the left onto Maryland 43 East toward White Marsh."], "15": ["Take the exit."], "16": ["Take exit 67 B-A."], "17": ["Take the US 322 West exit."], "18": ["Take exit 67 B-A onto US 322 West."], "19": ["Take the exit toward Lewistown."], "20": ["Take exit 67 B-A toward Lewistown."], "21": ["Take the US 322 West exit toward Lewistown."], "22": ["Take exit 67 B-A onto US 322 West toward Lewistown/State College."], "23": ["Take the White Marsh Boulevard exit."], "25": ["Take the White Marsh Boulevard exit onto MD 43 East."], "27": ["Take the White Marsh Boulevard exit toward White Marsh."], "29": ["Take the White Marsh Boulevard exit onto MD 43 East toward White Marsh."]}}, "exit_visual": {"phrases": {"0": "Сверните на съезд <EXIT_NUMBERS>"}, "example_phrases": {"0": ["Exit 1A"]}}, "keep": {"phrases": {"0": "Держитесь <RELATIVE_DIRECTION> на развилке.", "1": "Держитесь <RELATIVE_DIRECTION>, чтобы свернуть на съезд <NUMBER_SIGN>.", "2": "Дер<PERSON><PERSON>тесь <RELATIVE_DIRECTION>, чтобы свернуть на <STREET_NAMES>.", "3": "Держитесь <RELATIVE_DIRECTION>, чтобы свернуть на съезд <NUMBER_SIGN> на <STREET_NAMES>.", "4": "Держитесь <RELATIVE_DIRECTION> к <TOWARD_SIGN>.", "5": "Держитесь <RELATIVE_DIRECTION>, чтобы свернуть на съезд <NUMBER_SIGN> к <TOWARD_SIGN>.", "6": "Дер<PERSON>итесь <RELATIVE_DIRECTION>, чтобы свернуть на <STREET_NAMES> к <TOWARD_SIGN>.", "7": "Держитесь <RELATIVE_DIRECTION>, чтобы свернуть на съезд <NUMBER_SIGN> на <STREET_NAMES> к <TOWARD_SIGN>."}, "empty_street_name_labels": ["пешеходную дорожку", "велосипедную дорожку", "дорожку для горных велосипедов", "пешеходный переход", "the stairs", "the bridge", "the tunnel"], "relative_directions": ["левее", "прямо", "правее"], "example_phrases": {"0": ["Keep left at the fork.", "Keep straight at the fork.", "Keep right at the fork."], "1": ["Keep right to take exit 62."], "2": ["Keep right to take I 895 South."], "3": ["Keep right to take exit 62 onto I 895 South."], "4": ["Keep right toward Annapolis."], "5": ["Keep right to take exit 62 toward Annapolis."], "6": ["Keep right to take I 895 South toward Annapolis."], "7": ["Keep right to take exit 62 onto I 895 South toward Annapolis."]}}, "keep_to_stay_on": {"phrases": {"0": "Дер<PERSON>итесь <RELATIVE_DIRECTION>, чтобы остаться на <STREET_NAMES>.", "1": "Держитесь <RELATIVE_DIRECTION>, чтобы свернуть на съезд <NUMBER_SIGN> и остаться на <STREET_NAMES>.", "2": "Держитесь <RELATIVE_DIRECTION>, чтобы остаться на <STREET_NAMES> к <TOWARD_SIGN>.", "3": "Дер<PERSON>итесь <RELATIVE_DIRECTION>, чтобы свернуть на съезд <NUMBER_SIGN> и остаться на <STREET_NAMES> к <TOWARD_SIGN>."}, "empty_street_name_labels": ["пешеходную дорожку", "велосипедную дорожку", "дорожку для горных велосипедов", "пешеходный переход", "the stairs", "the bridge", "the tunnel"], "relative_directions": ["левее", "прямо", "правее"], "example_phrases": {"0": ["Keep left to stay on I 95 South.", "Keep straight to stay on I 95 South.", "Keep right to stay on I 95 South."], "1": ["Keep left to take exit 62 to stay on I 95 South."], "2": ["Keep left to stay on I 95 South toward Baltimore."], "3": ["Keep left to take exit 62 to stay on I 95 South toward Baltimore."]}}, "keep_to_stay_on_verbal": {"phrases": {"0": "Дер<PERSON>итесь <RELATIVE_DIRECTION>, чтобы остаться на <STREET_NAMES>.", "1": "Держитесь <RELATIVE_DIRECTION>, чтобы свернуть на съезд <NUMBER_SIGN> и остаться на <STREET_NAMES>.", "2": "Держитесь <RELATIVE_DIRECTION>, чтобы остаться на <STREET_NAMES> к <TOWARD_SIGN>.", "3": "Дер<PERSON>итесь <RELATIVE_DIRECTION>, чтобы свернуть на съезд <NUMBER_SIGN> и остаться на <STREET_NAMES> к <TOWARD_SIGN>."}, "empty_street_name_labels": ["пешеходную дорожку", "велосипедную дорожку", "дорожку для горных велосипедов", "пешеходный переход", "the stairs", "the bridge", "the tunnel"], "relative_directions": ["левее", "прямо", "правее"], "example_phrases": {"0": ["Keep left to stay on Interstate 95 South.", "Keep straight to stay on Interstate 95 South.", "Keep right to stay on Interstate 95 South."], "1": ["Keep left to take exit 62 to stay on Interstate 95 South."], "2": ["Keep left to stay on I 95 South toward Baltimore."], "3": ["Keep left to take exit 62 to stay on Interstate 95 South toward Baltimore."]}}, "keep_verbal": {"phrases": {"0": "Держитесь <RELATIVE_DIRECTION> на развилке.", "1": "Держитесь <RELATIVE_DIRECTION>, чтобы свернуть на съезд <NUMBER_SIGN>.", "2": "Дер<PERSON><PERSON>тесь <RELATIVE_DIRECTION>, чтобы свернуть на <STREET_NAMES>.", "3": "Держитесь <RELATIVE_DIRECTION>, чтобы свернуть на съезд <NUMBER_SIGN> на <STREET_NAMES>.", "4": "Держитесь <RELATIVE_DIRECTION> к <TOWARD_SIGN>.", "5": "Держитесь <RELATIVE_DIRECTION>, чтобы свернуть на съезд <NUMBER_SIGN> к <TOWARD_SIGN>.", "6": "Дер<PERSON>итесь <RELATIVE_DIRECTION>, чтобы свернуть на <STREET_NAMES> к <TOWARD_SIGN>.", "7": "Держитесь <RELATIVE_DIRECTION>, чтобы свернуть на съезд <NUMBER_SIGN> на <STREET_NAMES> к <TOWARD_SIGN>."}, "empty_street_name_labels": ["пешеходную дорожку", "велосипедную дорожку", "дорожку для горных велосипедов", "пешеходный переход", "the stairs", "the bridge", "the tunnel"], "relative_directions": ["левой стороны", "прямо", "правой стороны"], "example_phrases": {"0": ["Keep left at the fork.", "Keep straight at the fork.", "Keep right at the fork."], "1": ["Keep right to take exit 62."], "2": ["Keep right to take Interstate 8 95 South."], "3": ["Keep right to take exit 62 onto Interstate 8 95 South."], "4": ["Keep right toward Annapolis."], "5": ["Keep right to take exit 62 toward Annapolis."], "6": ["Keep right to take Interstate 8 95 South toward Annapolis."], "7": ["Keep right to take exit 62 onto Interstate 8 95 South toward Annapolis."]}}, "merge": {"phrases": {"0": "Выезжайте.", "1": "Выезжайте <RELATIVE_DIRECTION>.", "2": "Выезжайте на <STREET_NAMES>.", "3": "Выезжайте <RELATIVE_DIRECTION> на <STREET_NAMES>.", "4": "Выезжайте к <TOWARD_SIGN>.", "5": "Выезжайте <RELATIVE_DIRECTION> к <TOWARD_SIGN>."}, "relative_directions": ["налево", "направо"], "empty_street_name_labels": ["пешеходную дорожку", "велосипедную дорожку", "дорожку для горных велосипедов", "пешеходный переход", "the stairs", "the bridge", "the tunnel"], "example_phrases": {"0": ["Merge."], "1": ["<PERSON><PERSON> left."], "2": ["Merge onto I 76 West/Pennsylvania Turnpike."], "3": ["Merge right onto I 83 South."], "4": ["Merge toward Baltimore."], "5": ["Merge right toward Baltimore."]}}, "merge_verbal": {"phrases": {"0": "Выезжайте.", "1": "Выезжайте <RELATIVE_DIRECTION>.", "2": "Выезжайте на <STREET_NAMES>.", "3": "Выезжайте <RELATIVE_DIRECTION> на <STREET_NAMES>.", "4": "Выезжайте к <TOWARD_SIGN>.", "5": "Выезжайте <RELATIVE_DIRECTION> к <TOWARD_SIGN>."}, "relative_directions": ["налево", "направо"], "empty_street_name_labels": ["пешеходную дорожку", "велосипедную дорожку", "дорожку для горных велосипедов", "пешеходный переход", "the stairs", "the bridge", "the tunnel"], "example_phrases": {"0": ["Merge."], "1": ["<PERSON><PERSON> left."], "2": ["Merge onto I 76 West/Pennsylvania Turnpike."], "3": ["Merge right onto I 83 South."], "4": ["Merge toward Baltimore."], "5": ["Merge right toward Baltimore."]}}, "post_transition_transit_verbal": {"phrases": {"0": "Проезжайте <TRANSIT_STOP_COUNT> <TRANSIT_STOP_COUNT_LABEL>."}, "transit_stop_count_labels": {"one": "остановку", "other": "остановок"}, "example_phrases": {"0": ["Travel 1 stop.", "Travel 3 stops."]}}, "post_transition_verbal": {"phrases": {"0": "Продолжайте движение еще <LENGTH>.", "1": "Продолжайте движение по <STREET_NAMES> еще <LENGTH>."}, "empty_street_name_labels": ["пешеходной дорожке", "велосипедной дорожке", "дорожке для горных велосипедов", "пешеходному переходу", "the stairs", "the bridge", "the tunnel"], "metric_lengths": ["<KILOMETERS> ки<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "один километр", "<METERS> ме<PERSON><PERSON><PERSON>", "менее десяти метров"], "us_customary_lengths": ["<MILES> миль", "одну милю", "полмили", "четверть мили", "<FEET> футов", "менее десяти футов"], "example_phrases": {"0": ["Continue for 300 feet.", "Continue for 9 miles."], "1": ["Continue on Pennsylvania 7 43 for 6.2 miles.", "Continue on Main Street, Vermont 30 for 1 tenth of a mile."]}}, "ramp": {"phrases": {"0": "Сверните на въезд <RELATIVE_DIRECTION>.", "1": "Сверните на въезд на <BRANCH_SIGN> <RELATIVE_DIRECTION>.", "2": "Сверните на въезд <RELATIVE_DIRECTION> к <TOWARD_SIGN>.", "3": "Сверните на въезд на <BRANCH_SIGN> <RELATIVE_DIRECTION> к <TOWARD_SIGN>.", "4": "Сверните на въезд на <NAME_SIGN> <RELATIVE_DIRECTION>.", "5": "Сверните <RELATIVE_DIRECTION> на въезд.", "6": "Сверните <RELATIVE_DIRECTION> на въезд на <BRANCH_SIGN>.", "7": "Сверните <RELATIVE_DIRECTION> на въезд к <TOWARD_SIGN>.", "8": "Сверните <RELATIVE_DIRECTION> на въезд <BRANCH_SIGN> к <TOWARD_SIGN>.", "9": "Сверните <RELATIVE_DIRECTION> на въезд на <NAME_SIGN>.", "10": "Сверните на въезд.", "11": "Сверните на въезд на <BRANCH_SIGN>.", "12": "Сверните на въезд к <TOWARD_SIGN>.", "13": "Сверните на въезд на <BRANCH_SIGN> к <TOWARD_SIGN>.", "14": "Сверните на въезд на <NAME_SIGN>."}, "relative_directions": ["слева", "справа"], "example_phrases": {"0": ["Take the ramp on the left.", "Take the ramp on the right."], "1": ["Take the I 95 ramp on the right."], "2": ["Take the ramp on the left toward JFK."], "3": ["Take the South Conduit Avenue ramp on the left toward JFK."], "4": ["Take the Gettysburg Pike ramp on the right."], "5": ["Turn left to take the ramp.", "Turn right to take the ramp."], "6": ["Turn left to take the PA 283 West ramp."], "7": ["Turn left to take the ramp toward Harrisburg/Harrisburg International Airport."], "8": ["Turn left to take the PA 283 West ramp toward Harrisburg/Harrisburg International Airport."], "9": ["Turn right to take the Gettysburg Pike ramp."], "10": ["Take the ramp."], "11": ["Take the I 95 ramp."], "12": ["Take the ramp toward JFK."], "13": ["Take the Soutch Conduit Avenue ramp toward JFK."], "14": ["Take the Gettysburg ramp."]}}, "ramp_straight": {"phrases": {"0": "Держитесь прямо, чтобы заехать на въезд.", "1": "Держитесь прямо, чтобы заехать на въезд на <BRANCH_SIGN>.", "2": "Держитесь прямо, чтобы заехать на въезд к <TOWARD_SIGN>.", "3": "Держитесь прямо, чтобы заехать на въезд на <BRANCH_SIGN> к <TOWARD_SIGN>.", "4": "Держитесь прямо, чтобы заехать на въезд на <NAME_SIGN>."}, "example_phrases": {"0": ["Stay straight to take the ramp."], "1": ["Stay straight to take the US 322 East ramp."], "2": ["Stay straight to take the ramp toward Hershey."], "3": ["Stay straight to take the US 322 East/US 422 East/US 522 East/US 622 East ramp toward Hershey/Palmdale/Palmyra/Campbelltown."], "4": ["Stay straight to take the Gettysburg Pike ramp."]}}, "ramp_straight_verbal": {"phrases": {"0": "Держитесь прямо, чтобы заехать на въезд.", "1": "Держитесь прямо, чтобы заехать на въезд на <BRANCH_SIGN>.", "2": "Держитесь прямо, чтобы заехать на въезд к <TOWARD_SIGN>.", "3": "Держитесь прямо, чтобы заехать на въезд на <BRANCH_SIGN> к <TOWARD_SIGN>.", "4": "Держитесь прямо, чтобы заехать на въезд на <NAME_SIGN>."}, "example_phrases": {"0": ["Stay straight to take the ramp."], "1": ["Stay straight to take the U.S. 3 22 East ramp."], "2": ["Stay straight to take the ramp toward Hershey."], "3": ["Stay straight to take the U.S. 3 22 East, U.S. 4 22 East ramp toward Hershey, Palmdale."], "4": ["Stay straight to take the Gettysburg Pike ramp."]}}, "ramp_verbal": {"phrases": {"0": "Сверните на въезд <RELATIVE_DIRECTION>.", "1": "Сверните на въезд на <BRANCH_SIGN> <RELATIVE_DIRECTION>.", "2": "Сверните на въезд <RELATIVE_DIRECTION> к <TOWARD_SIGN>.", "3": "Сверните на въезд на <BRANCH_SIGN> <RELATIVE_DIRECTION> к <TOWARD_SIGN>.", "4": "Сверните на въезд на <NAME_SIGN> <RELATIVE_DIRECTION>.", "5": "Сверните <RELATIVE_DIRECTION> на въезд.", "6": "Сверните <RELATIVE_DIRECTION> на въезд на <BRANCH_SIGN>.", "7": "Сверните <RELATIVE_DIRECTION> на въезд к <TOWARD_SIGN>.", "8": "Сверните <RELATIVE_DIRECTION> на въезд <BRANCH_SIGN> к <TOWARD_SIGN>.", "9": "Сверните <RELATIVE_DIRECTION> на въезд на <NAME_SIGN>.", "10": "Сверните на въезд.", "11": "Сверните на въезд на <BRANCH_SIGN>.", "12": "Сверните на въезд к <TOWARD_SIGN>.", "13": "Сверните на въезд на <BRANCH_SIGN> к <TOWARD_SIGN>.", "14": "Сверните на въезд на <NAME_SIGN>."}, "relative_directions": ["слева", "справа"], "example_phrases": {"0": ["Take the ramp on the left.", "Take the ramp on the right."], "1": ["Take the Interstate 95 ramp on the right."], "2": ["Take the ramp on the left toward JFK."], "3": ["Take the South Conduit Avenue ramp on the left toward JFK."], "4": ["Take the Gettysburg Pike ramp on the right."], "5": ["Turn left to take the ramp.", "Turn right to take the ramp."], "6": ["Turn left to take the Pennsylvania 2 83 West ramp."], "7": ["Turn left to take the ramp toward Harrisburg/Harrisburg International Airport."], "8": ["Turn left to take the Pennsylvania 2 83 West ramp toward Harrisburg, Harrisburg International Airport."], "9": ["Turn right to take the Gettysburg Pike ramp."], "10": ["Take the ramp."], "11": ["Take the Interstate 95 ramp."], "12": ["Take the ramp toward JFK."], "13": ["Take the South Conduit Avenue ramp toward JFK."], "14": ["Take the Gettysburg Pike ramp."]}}, "sharp": {"phrases": {"0": "Круто поверните <RELATIVE_DIRECTION>.", "1": "Круто поверните <RELATIVE_DIRECTION> на <STREET_NAMES>.", "2": "Круто поверните <RELATIVE_DIRECTION> на <BEGIN_STREET_NAMES>. Следуйте далее по <STREET_NAMES>.", "3": "Круто поверните <RELATIVE_DIRECTION>, оставаясь на <STREET_NAMES>.", "4": "Круто поверните <RELATIVE_DIRECTION> через <JUNCTION_NAME>.", "5": "Круто поверните <RELATIVE_DIRECTION> к <TOWARD_SIGN>."}, "empty_street_name_labels": ["пешеходной дорожке", "велосипедной дорожке", "дорожке для горных велосипедов", "пешеходному переходу", "the stairs", "the bridge", "the tunnel"], "relative_directions": ["налево", "направо"], "example_phrases": {"0": ["Make a sharp left."], "1": ["Make a sharp right onto Flatbush Avenue."], "2": ["Make a sharp left onto North Bond Street/US 1 Business/MD 924. Continue on MD 924."], "3": ["Make a sharp right to stay on Sunstone Drive."], "4": ["Make a sharp right at Mannenbashi East."], "5": ["Make a sharp left toward Baltimore."]}}, "sharp_verbal": {"phrases": {"0": "Круто поверните <RELATIVE_DIRECTION>.", "1": "Круто поверните <RELATIVE_DIRECTION> на <STREET_NAMES>.", "2": "Круто поверните <RELATIVE_DIRECTION> на <BEGIN_STREET_NAMES>.", "3": "Круто поверните <RELATIVE_DIRECTION>, оставаясь на <STREET_NAMES>.", "4": "Круто поверните <RELATIVE_DIRECTION> через <JUNCTION_NAME>.", "5": "Круто поверните <RELATIVE_DIRECTION> к <TOWARD_SIGN>."}, "empty_street_name_labels": ["пешеходной дорожке", "велосипедной дорожке", "дорожке для горных велосипедов", "пешеходному переходу", "the stairs", "the bridge", "the tunnel"], "relative_directions": ["налево", "направо"], "example_phrases": {"0": ["Make a sharp left."], "1": ["Make a sharp right onto Flatbush Avenue."], "2": ["Make a sharp left onto North Bond Street, U.S. 1 Business."], "3": ["Make a sharp right to stay on Sunstone Drive."], "4": ["Make a sharp right at Mannenbashi East."], "5": ["Make a sharp left toward Baltimore."]}}, "start": {"phrases": {"0": "Двигайтесь на <CARDINAL_DIRECTION>.", "1": "Двигайтесь на <CARDINAL_DIRECTION> по <STREET_NAMES>.", "2": "Двигайтесь на <CARDINAL_DIRECTION> по <BEGIN_STREET_NAMES>. Следуйте далее по <STREET_NAMES>.", "4": "Двигайтесь на <CARDINAL_DIRECTION>.", "5": "Двигайтесь на <CARDINAL_DIRECTION> по <STREET_NAMES>.", "6": "Двигайтесь на <CARDINAL_DIRECTION> по <BEGIN_STREET_NAMES>. Следуйте далее по <STREET_NAMES>.", "8": "Идите <CARDINAL_DIRECTION>.", "9": "Идите <CARDINAL_DIRECTION> по <STREET_NAMES>.", "10": "Идите <CARDINAL_DIRECTION> по <BEGIN_STREET_NAMES>. Следуйте далее по <STREET_NAMES>.", "16": "Двигайтесь по <CARDINAL_DIRECTION>.", "17": "Двигайтесь по <CARDINAL_DIRECTION> по <STREET_NAMES>.", "18": "Двигайтесь по <CARDINAL_DIRECTION> по <BEGIN_STREET_NAMES>. Следуйте далее по <STREET_NAMES>."}, "cardinal_directions": ["север", "северо-восток", "восток", "юго-восток", "юг", "юго-запад", "запад", "северо-запад"], "empty_street_name_labels": ["пешеходной дорожке", "велосипедной дорожке", "дорожке для горных велосипедов", "пешеходный переход", "the stairs", "the bridge", "the tunnel"], "example_phrases": {"0": ["Head east.", "Head north."], "1": ["Head southwest on 5th Avenue.", "Head west on the walkway", "Head east on the cycleway", "Head north on the mountain bike trail"], "2": ["Head south on North Prince Street/US 222/PA 272. Continue on US 222/PA 272."], "4": ["Drive east.", "Drive north."], "5": ["Drive southwest on 5th Avenue."], "6": ["Drive south on North Prince Street/US 222/PA 272. Continue on US 222/PA 272."], "8": ["Walk east.", "Walk north."], "9": ["Walk southwest on 5th Avenue.", "Walk west on the walkway"], "10": ["Walk south on North Prince Street/US 222/PA 272. Continue on US 222/PA 272."], "16": ["Bike east.", "Bike north."], "17": ["Bike southwest on 5th Avenue.", "Bike east on the cycleway", "Bike north on the mountain bike trail"], "18": ["Bike south on North Prince Street/US 222/PA 272. Continue on US 222/PA 272."]}}, "start_verbal": {"phrases": {"0": "Двигайтесь на <CARDINAL_DIRECTION>.", "1": "Двигайтесь на <CARDINAL_DIRECTION> <LENGTH>.", "2": "Двигайтесь на <CARDINAL_DIRECTION> по <STREET_NAMES>.", "3": "Двигайтесь на <CARDINAL_DIRECTION> по <STREET_NAMES> <LENGTH>.", "4": "Двигайтесь на <CARDINAL_DIRECTION> по <BEGIN_STREET_NAMES>.", "5": "Двигайтесь на <CARDINAL_DIRECTION>.", "6": "Двигайтесь на <CARDINAL_DIRECTION> <LENGTH>.", "7": "Двигайтесь на <CARDINAL_DIRECTION> по <STREET_NAMES>.", "8": "Двигайтесь на <CARDINAL_DIRECTION> по <STREET_NAMES> <LENGTH>.", "9": "Двигайтесь на <CARDINAL_DIRECTION> по <BEGIN_STREET_NAMES>.", "10": "Идите на <CARDINAL_DIRECTION>.", "11": "Идите на <CARDINAL_DIRECTION> <LENGTH>.", "12": "Идите на <CARDINAL_DIRECTION> по <STREET_NAMES>.", "13": "Идите на <CARDINAL_DIRECTION> по <STREET_NAMES> <LENGTH>.", "14": "Идите на <CARDINAL_DIRECTION> по <BEGIN_STREET_NAMES>.", "15": "Двигайтесь на <CARDINAL_DIRECTION>.", "16": "Двигайтесь на <CARDINAL_DIRECTION> <LENGTH>.", "17": "Двигайтесь на <CARDINAL_DIRECTION> по <STREET_NAMES>.", "18": "Двигайтесь на <CARDINAL_DIRECTION> по <STREET_NAMES> <LENGTH>.", "19": "Двигайтесь на <CARDINAL_DIRECTION> по <BEGIN_STREET_NAMES>."}, "cardinal_directions": ["север", "северо-запад", "восток", "юго-восток", "юг", "юго-запад", "запад", "северо-запад"], "empty_street_name_labels": ["пешеходной дорожке", "велосипедной дорожке", "дорожке для горных велосипедов", "пешеходный переход", "the stairs", "the bridge", "the tunnel"], "metric_lengths": ["<KILOMETERS> ки<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "один километр", "<METERS> ме<PERSON><PERSON><PERSON>", "менее десяти метров"], "us_customary_lengths": ["<MILES> миль", "одну милю", "полмили", "четверть мили", "<FEET> футов", "менее десяти футов"], "example_phrases": {"0": ["Head east.", "Head north."], "1": ["Head east for a half mile.", "Head north for 1 kilometer."], "2": ["Head southwest on 5th Avenue."], "3": ["Head southwest on 5th Avenue for 1 tenth of a mile."], "4": ["Head south on North Prince Street, U.S. 2 22."], "5": ["Drive east.", "Drive north."], "6": ["Drive east for a half mile.", "Drive north for 1 kilometer."], "7": ["Drive southwest on 5th Avenue."], "8": ["Drive southwest on 5th Avenue for 1 tenth of a mile."], "9": ["Drive south on North Prince Street, U.S. 2 22."], "10": ["Walk east.", "Walk north."], "11": ["Walk east for a half mile.", "Walk north for 1 kilometer."], "12": ["Walk southwest on 5th Avenue."], "13": ["Walk southwest on 5th Avenue for 1 tenth of a mile."], "14": ["Walk south on North Prince Street, U.S. 2 22."], "15": ["Bike east.", "Bike north."], "16": ["Bike east for a half mile.", "Bike north for 1 kilometer."], "17": ["Bike southwest on 5th Avenue."], "18": ["Bike southwest on 5th Avenue for 1 tenth of a mile."], "19": ["Bike south on North Prince Street, U.S. 2 22."]}}, "transit": {"phrases": {"0": "Садитесь на <TRANSIT_NAME>. (<TRANSIT_STOP_COUNT> <TRANSIT_STOP_COUNT_LABEL>)", "1": "Садитесь на <TRANSIT_NAME> по направлению к <TRANSIT_HEADSIGN>. (<TRANSIT_STOP_COUNT> <TRANSIT_STOP_COUNT_LABEL>)"}, "empty_transit_name_labels": ["трамвай", "метро", "поезд", "автобус", "паром", "канатную дорогу", "гондолу", "фуникулер"], "transit_stop_count_labels": {"one": "остановка", "other": "остановки"}, "example_phrases": {"0": ["Take the New Haven. (1 stop)", "Take the metro. (2 stops)", "Take the bus. (12 stops)"], "1": ["Take the F toward JAMAICA - 179 ST. (10 stops)", "Take the ferry toward Staten Island. (1 stop)"]}}, "transit_connection_destination": {"phrases": {"0": "Сойдите со станции.", "1": "Сойдите со станции <TRANSIT_STOP>.", "2": "Сойдите со станции <TRANSIT_STOP> <STATION_LABEL>."}, "station_label": "Станция", "example_phrases": {"0": ["Exit the station."], "1": ["Exit the Embarcadero Station."], "2": ["Exit the 8 St - NYU Station."]}}, "transit_connection_destination_verbal": {"phrases": {"0": "Сойдите со станции.", "1": "Сойдите со станции <TRANSIT_STOP>.", "2": "Сойдите со станции <TRANSIT_STOP> <STATION_LABEL>."}, "station_label": "Станция", "example_phrases": {"0": ["Exit the station."], "1": ["Exit the Embarcadero Station."], "2": ["Exit the 8 St - NYU Station."]}}, "transit_connection_start": {"phrases": {"0": "Войдите на станцию.", "1": "Войдите на станцию <TRANSIT_STOP>.", "2": "Войдите на станцию <TRANSIT_STOP> <STATION_LABEL>."}, "station_label": "Станция", "example_phrases": {"0": ["Enter the station."], "1": ["Enter the Embarcadero Station."], "2": ["Enter the 8 St - NYU Station."]}}, "transit_connection_start_verbal": {"phrases": {"0": "Войдите на станцию.", "1": "Войдите на станцию <TRANSIT_STOP>.", "2": "Войдите на станцию <TRANSIT_STOP> <STATION_LABEL>."}, "station_label": "Станция", "example_phrases": {"0": ["Enter the station."], "1": ["Enter the Embarcadero Station."], "2": ["Enter the 8 St - NYU Station."]}}, "transit_connection_transfer": {"phrases": {"0": "Сделайте пересадку на станции", "1": "Сделайте пересадку на станции <TRANSIT_STOP>.", "2": "Сделайте пересадку на станции <TRANSIT_STOP> <STATION_LABEL>."}, "station_label": "Станция", "example_phrases": {"0": ["Transfer at the station."], "1": ["Transfer at the Embarcadero Station."], "2": ["Transfer at the 8 St - NYU Station."]}}, "transit_connection_transfer_verbal": {"phrases": {"0": "Сделайте пересадку на станции.", "1": "Сделайте пересадку на станции <TRANSIT_STOP>.", "2": "Сделайте пересадку на станции <TRANSIT_STOP> <STATION_LABEL>."}, "station_label": "Станция", "example_phrases": {"0": ["Transfer at the station."], "1": ["Transfer at the Embarcadero Station."], "2": ["Transfer at the 8 St - NYU Station."]}}, "transit_remain_on": {"phrases": {"0": "Оставайтесь на <TRANSIT_NAME>. (<TRANSIT_STOP_COUNT> <TRANSIT_STOP_COUNT_LABEL>)", "1": "Оставайтесь на <TRANSIT_NAME> по направлению к <TRANSIT_HEADSIGN>. (<TRANSIT_STOP_COUNT> <TRANSIT_STOP_COUNT_LABEL>)"}, "empty_transit_name_labels": ["трамвае", "метро", "поезде", "автобусе", "пароме", "канатной дороге", "гондоле", "фуникулере"], "transit_stop_count_labels": {"one": "остановка", "other": "остановок"}, "example_phrases": {"0": ["Remain on the New Haven. (1 stop)", "<PERSON><PERSON><PERSON> on the train. (3 stops)"], "1": ["Remain on the F toward JAMAICA - 179 ST. (10 stops)"]}}, "transit_remain_on_verbal": {"phrases": {"0": "Оставайтесь на <TRANSIT_NAME>.", "1": "Оставайтесь на <TRANSIT_NAME> по направлению к <TRANSIT_HEADSIGN>."}, "empty_transit_name_labels": ["трамвае", "метро", "поезде", "автобусе", "пароме", "канатной дороге", "гондоле", "фуникулере"], "example_phrases": {"0": ["<PERSON><PERSON><PERSON> on the New Haven."], "1": ["Remain on the F toward JAMAICA - 179 ST."]}}, "transit_transfer": {"phrases": {"0": "Пересаж<PERSON><PERSON>айтесь на <TRANSIT_NAME>. (<TRANSIT_STOP_COUNT> <TRANSIT_STOP_COUNT_LABEL>)", "1": "Переса<PERSON><PERSON><PERSON><PERSON>йтесь на <TRANSIT_NAME> по направлению к <TRANSIT_HEADSIGN>. (<TRANSIT_STOP_COUNT> <TRANSIT_STOP_COUNT_LABEL>)"}, "empty_transit_name_labels": ["трамвай", "метро", "поезд", "автобус", "паром", "канатную дорогу", "гондолу", "фуникулер"], "transit_stop_count_labels": {"one": "остановка", "other": "остановки"}, "example_phrases": {"0": ["Transfer to take the New Haven. (1 stop)", "Transfer to take the tram. (4 stops)"], "1": ["Transfer to take the F toward JAMAICA - 179 ST. (10 stops)"]}}, "transit_transfer_verbal": {"phrases": {"0": "Пересаживайтесь на <TRANSIT_NAME>.", "1": "Переса<PERSON><PERSON><PERSON><PERSON>йтесь на <TRANSIT_NAME> по направлению к <TRANSIT_HEADSIGN>."}, "empty_transit_name_labels": ["трамвай", "метро", "поезд", "автобус", "паром", "канатную дорогу", "гондолу", "фуникулер"], "example_phrases": {"0": ["Transfer to take the New Haven."], "1": ["Transfer to take the F toward JAMAICA - 179 ST."]}}, "transit_verbal": {"phrases": {"0": "Садитесь на <TRANSIT_NAME>.", "1": "Садитесь на <TRANSIT_NAME> по направлению к <TRANSIT_HEADSIGN>."}, "empty_transit_name_labels": ["трамвай", "метро", "поезд", "автобус", "паром", "канатную дорогу", "гондолу", "фуникулер"], "example_phrases": {"0": ["Take the New Haven."], "1": ["Take the F toward JAMAICA - 179 ST."]}}, "turn": {"phrases": {"0": "Поверните <RELATIVE_DIRECTION>.", "1": "Поверните <RELATIVE_DIRECTION> на <STREET_NAMES>.", "2": "Поверните <RELATIVE_DIRECTION> на <BEGIN_STREET_NAMES>. Продолжайте движение по <STREET_NAMES>.", "3": "Поверните <RELATIVE_DIRECTION>, чтобы остаться на <STREET_NAMES>.", "4": "Поверните <RELATIVE_DIRECTION> на <JUNCTION_NAME>.", "5": "Поверните <RELATIVE_DIRECTION> к <TOWARD_SIGN>."}, "empty_street_name_labels": ["пешеходную дорожку", "велосипедную дорожку", "дорожку для горных велосипедов", "пешеходный переход", "the stairs", "the bridge", "the tunnel"], "relative_directions": ["налево", "направо"], "example_phrases": {"0": ["Turn left."], "1": ["Turn right onto Flatbush Avenue."], "2": ["Turn left onto North Bond Street/US 1 Business/MD 924. Continue on MD 924."], "3": ["Turn right to stay on Sunstone Drive."], "4": ["Turn right at Mannenbashi East."], "5": ["Turn left toward Baltimore."]}}, "turn_verbal": {"phrases": {"0": "Поверните <RELATIVE_DIRECTION>.", "1": "Поверните <RELATIVE_DIRECTION> на <STREET_NAMES>.", "2": "Поверните <RELATIVE_DIRECTION> на <BEGIN_STREET_NAMES>.", "3": "Поверните <RELATIVE_DIRECTION>, чтобы остаться на <STREET_NAMES>.", "4": "Поверните <RELATIVE_DIRECTION> на <JUNCTION_NAME>.", "5": "Поверните <RELATIVE_DIRECTION> к <TOWARD_SIGN>."}, "empty_street_name_labels": ["пешеходную дорожку", "велосипедную дорожку", "дорожку для горных велосипедов", "пешеходный переход", "the stairs", "the bridge", "the tunnel"], "relative_directions": ["налево", "направо"], "example_phrases": {"0": ["Turn left."], "1": ["Turn right onto Flatbush Avenue."], "2": ["Turn left onto North Bond Street, U.S. 1 Business."], "3": ["Turn right to stay on Sunstone Drive."], "4": ["Turn right at Mannenbashi East."], "5": ["Turn left toward Baltimore."]}}, "uturn": {"phrases": {"0": "Развернитесь <RELATIVE_DIRECTION>.", "1": "Развернитесь <RELATIVE_DIRECTION> на <STREET_NAMES>.", "2": "Развернитесь <RELATIVE_DIRECTION>, чтобы остаться на <STREET_NAMES>.", "3": "Развернитесь <RELATIVE_DIRECTION> на <CROSS_STREET_NAMES>.", "4": "Развернитесь <RELATIVE_DIRECTION> на <CROSS_STREET_NAMES> на <STREET_NAMES>.", "5": "Развернитесь <RELATIVE_DIRECTION> на <CROSS_STREET_NAMES>, чтобы остаться на <STREET_NAMES>.", "6": "Развернитесь <RELATIVE_DIRECTION> на <JUNCTION_NAME>.", "7": "Развернитесь <RELATIVE_DIRECTION> в сторону <TOWARD_SIGN>."}, "empty_street_name_labels": ["пешеходной дорожке", "велосипедной дорожке", "пешеходном переходе", "дорожке для горных велосипедов", "the stairs", "the bridge", "the tunnel"], "relative_directions": ["налево", "направо"], "example_phrases": {"0": ["Make a left U-turn."], "1": ["Make a right U-turn onto Bunker Hill Road."], "2": ["Make a left U-turn to stay on Bunker Hill Road."], "3": ["Make a left U-turn at Devonshire Road."], "4": ["Make a left U-turn at Devonshire Road onto Jonestown Road/US 22."], "5": ["Make a left U-turn at Devonshire Road to stay on Jonestown Road/US 22."], "6": ["Make a right U-turn at Mannenbashi East."], "7": ["Make a left U-turn toward Baltimore."]}}, "uturn_verbal": {"phrases": {"0": "Развернитесь <RELATIVE_DIRECTION>.", "1": "Развернитесь <RELATIVE_DIRECTION> на <STREET_NAMES>.", "2": "Развернитесь <RELATIVE_DIRECTION>, чтобы остаться на <STREET_NAMES>.", "3": "Развернитесь <RELATIVE_DIRECTION> на <CROSS_STREET_NAMES>.", "4": "Развернитесь <RELATIVE_DIRECTION> на <CROSS_STREET_NAMES> на <STREET_NAMES>.", "5": "Развернитесь <RELATIVE_DIRECTION> на <CROSS_STREET_NAMES>, чтобы остаться на <STREET_NAMES>.", "6": "Развернитесь <RELATIVE_DIRECTION> на <JUNCTION_NAME>.", "7": "Развернитесь <RELATIVE_DIRECTION> в сторону <TOWARD_SIGN>."}, "empty_street_name_labels": ["пешеходной дорожке", "велосипедной дорожке", "дорожке для горных велосипедов", "пешеходном переходе", "the stairs", "the bridge", "the tunnel"], "relative_directions": ["налево", "направо"], "example_phrases": {"0": ["Make a left U-turn."], "1": ["Make a right U-turn onto Bunker Hill Road."], "2": ["Make a left U-turn to stay on Bunker Hill Road."], "3": ["Make a left U-turn at Devonshire Road."], "4": ["Make a left U-turn at Devonshire Road onto Jonestown Road, U.S. 22."], "5": ["Make a left U-turn at Devonshire Road to stay on Jonestown Road, U.S. 22."], "6": ["Make a right U-turn at Mannenbashi East."], "7": ["Make a left U-turn toward Baltimore."]}}, "verbal_multi_cue": {"phrases": {"0": "<CURRENT_VERBAL_CUE>, затем <NEXT_VERBAL_CUE>", "1": "<CURRENT_VERBAL_CUE>, затем через <LENGTH>, <NEXT_VERBAL_CUE>"}, "metric_lengths": ["<KILOMETERS> ки<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "один километр", "<METERS> ме<PERSON><PERSON><PERSON>", "менее десяти метров"], "us_customary_lengths": ["<MILES> миль", "одну милю", "полмили", "четверть мили", "<FEET> футов", "менее десяти футов"], "example_phrases": {"0": ["Bear right onto East Fayette Street. Then Turn right onto North Gay Street."], "1": ["Bear right onto East Fayette Street. Then, in 500 feet, Turn right onto North Gay Street."]}}, "approach_verbal_alert": {"phrases": {"0": "Через <LENGTH>, <CURRENT_VERBAL_CUE>"}, "metric_lengths": ["<KILOMETERS> ки<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "один километр", "<METERS> ме<PERSON><PERSON><PERSON>", "менее десяти метров"], "us_customary_lengths": ["<MILES> миль", "одну милю", "полмили", "четверть мили", "<FEET> футов", "менее десяти футов"], "example_phrases": {"0": ["In a quarter mile, Turn right onto North Gay Street."]}}, "pass": {"phrases": {"0": "Pass <OBJECT_LABEL>.", "1": "Pass traffic signals on <OBJECT_LABEL>."}, "object_labels": ["the gate", "the bollards", "ways intersection"], "example_phrases": {"0": ["Pass the gate."]}}, "elevator": {"phrases": {"0": "Воспользуйтесь лифтом.", "1": "Воспользуйтесь лифтом до <LEVEL>."}, "example_phrases": {"0": ["Take the elevator."], "1": ["Take the elevator to Level 1."]}}, "steps": {"phrases": {"0": "Воспользуйтесь лестницей.", "1": "Воспользуйтесь лестницей до <LEVEL>."}, "example_phrases": {"0": ["Take the stairs."], "1": ["Take the stairs to Level 2."]}}, "escalator": {"phrases": {"0": "Воспользуйтесь эскалатором.", "1": "Воспользуйтесь эскалатором до <LEVEL>."}, "example_phrases": {"0": ["Take the escalator."], "1": ["Take the escalator to Level 3."]}}, "enter_building": {"phrases": {"0": "Войдите в здание.", "1": "Войдите в здание, и продолжайте движение до <STREET_NAMES>."}, "empty_street_name_labels": ["пешеходной дорожке", "велосипедную дорожку", "дорожка для горных велосипедов", "пешеходный переход"], "example_phrases": {"0": ["Enter the building."], "1": ["Enter the building, and continue on the walkway."]}}, "exit_building": {"phrases": {"0": "Выйдите из здания.", "1": "Выйдите из здания и продолжайте по <STREET_NAMES>."}, "empty_street_name_labels": ["пешеходной дорожке", "велосипедной дорожке", "дорожке для горных велосипедов", "пешеходному переходу"], "example_phrases": {"0": ["Exit the building."], "1": ["Exit the building, and continue on the walkway."]}}}}
{"posix_locale": "es_ES.UTF-8", "aliases": ["es"], "instructions": {"arrive": {"phrases": {"0": "Llegada: <TIME>.", "1": "Llegada: <TIME> en <TRANSIT_STOP>."}, "example_phrases": {"0": ["Arrive: 8:02 AM."], "1": ["Arrive: 8:02 AM at 8 St - NYU."]}}, "arrive_verbal": {"phrases": {"0": "Llegada a las <TIME>.", "1": "Llegada a las <TIME> en <TRANSIT_STOP>."}, "example_phrases": {"0": ["Arrive at 8:02 AM."], "1": ["Arrive at 8:02 AM at 8 St - NYU."]}}, "bear": {"phrases": {"0": "Siga por la <RELATIVE_DIRECTION>.", "1": "Siga por la <RELATIVE_DIRECTION> en dirección a <STREET_NAMES>.", "2": "Siga por la <RELATIVE_DIRECTION> hacia <BEGIN_STREET_NAMES>. Continúe en <STREET_NAMES>.", "3": "Manténgase a la <RELATIVE_DIRECTION> para permanecer en <STREET_NAMES>.", "4": "Siga por la <RELATIVE_DIRECTION> en <JUNCTION_NAME>.", "5": "Siga por la <RELATIVE_DIRECTION> en dirección a <TOWARD_SIGN>."}, "empty_street_name_labels": ["la calzada", "el carril bici", "el sendero para bicicletas de montaña", "the crosswalk", "the stairs", "the bridge", "the tunnel"], "relative_directions": ["izquierda", "derecha"], "example_phrases": {"0": ["Bear right."], "1": ["<PERSON> left onto Arlen Road."], "2": ["Bear right onto Belair Road/US 1 Business. Continue on US 1 Business."], "3": ["<PERSON> left to stay on US 15 South."], "4": ["Bear right at Mannenbashi East."], "5": ["Bear left toward Baltimore."]}}, "bear_verbal": {"phrases": {"0": "Siga por la <RELATIVE_DIRECTION>.", "1": "Siga por la <RELATIVE_DIRECTION> en dirección a <STREET_NAMES>.", "2": "Siga por la <RELATIVE_DIRECTION> hacia <BEGIN_STREET_NAMES>.", "3": "Manténgase a la <RELATIVE_DIRECTION> para permanecer en <STREET_NAMES>.", "4": "Siga por la <RELATIVE_DIRECTION> en <JUNCTION_NAME>.", "5": "Siga por la <RELATIVE_DIRECTION> en dirección a <TOWARD_SIGN>."}, "empty_street_name_labels": ["la calzada", "el carril bici", "el sendero para bicicletas de montaña", "the crosswalk", "the stairs", "the bridge", "the tunnel"], "relative_directions": ["izquierda", "derecha"], "example_phrases": {"0": ["Bear right."], "1": ["<PERSON> left onto Arlen Road."], "2": ["Bear right onto Belair Road, U.S. 1 Business."], "3": ["<PERSON> left to stay on U.S. 15 South."], "4": ["Bear right at Mannenbashi East."], "5": ["Bear left toward Baltimore."]}}, "becomes": {"phrases": {"0": "<PREVIOUS_STREET_NAMES> se convierte en <STREET_NAMES>."}, "example_phrases": {"0": ["Vine Street becomes Middletown Road."]}}, "becomes_verbal": {"phrases": {"0": "<PREVIOUS_STREET_NAMES> se convierte en <STREET_NAMES>."}, "example_phrases": {"0": ["Vine Street becomes Middletown Road."]}}, "continue": {"phrases": {"0": "Continúe.", "1": "Continúe en <STREET_NAMES>.", "2": "Continúe en <JUNCTION_NAME>.", "3": "Continúe en dirección a <TOWARD_SIGN>."}, "empty_street_name_labels": ["la calzada", "el carril bici", "el sendero para bicicletas de montaña", "the crosswalk", "the stairs", "the bridge", "the tunnel"], "example_phrases": {"0": ["Continue."], "1": ["Continue on 10th Avenue."], "2": ["Continue at Mannenbashi East."], "3": ["Continue toward Baltimore."]}}, "continue_verbal": {"phrases": {"0": "Continúe.", "1": "Contin<PERSON>e durante <LENGTH>.", "2": "Continúe en <STREET_NAMES>.", "3": "Continúe en <STREET_NAMES> durante <LENGTH>.", "4": "Continúe en <JUNCTION_NAME>.", "5": "Continúe en <JUNCTION_NAME> durante <LENGTH>.", "6": "Continúe en dirección a <TOWARD_SIGN>.", "7": "Continúe dirección a <TOWARD_SIGN> durante <LENGTH>."}, "empty_street_name_labels": ["la calzada", "el carril bici", "el sendero para bicicletas de montaña", "the crosswalk", "the stairs", "the bridge", "the tunnel"], "metric_lengths": ["<KILOMETERS> kilómetros", "1 kilómetro", "<METERS> metros", "menos de 10 metros"], "us_customary_lengths": ["<MILES> millas", "1 milla", "media milla", "un cuarto de milla", "<FEET> pies", "menos de 10 pies"], "example_phrases": {"0": ["Continue."], "1": ["Continue for 300 feet."], "2": ["Continue on 10th Avenue."], "3": ["Continue on 10th Avenue for 3 miles."], "4": ["Continue at Mannenbashi East."], "5": ["Continue at Mannenbashi East for 2 miles."], "6": ["Continue toward Baltimore."], "7": ["Continue toward Baltimore for 5 miles."]}}, "continue_verbal_alert": {"phrases": {"0": "Continúe.", "1": "Continúe en <STREET_NAMES>.", "2": "Continúe en <JUNCTION_NAME>.", "3": "Continúe en dirección a <TOWARD_SIGN>."}, "empty_street_name_labels": ["la calzada", "el carril bici", "el sendero para bicicletas de montaña", "the crosswalk", "the stairs", "the bridge", "the tunnel"], "example_phrases": {"0": ["Continue."], "1": ["Continue on 10th Avenue."], "2": ["Continue at Mannenbashi East."], "3": ["Continue toward Baltimore."]}}, "depart": {"phrases": {"0": "Salida: <TIME>.", "1": "Salida: <TIME> desde <TRANSIT_STOP>."}, "example_phrases": {"0": ["Depart: 8:02 AM."], "1": ["Depart: 8:02 AM from 8 St - NYU."]}}, "depart_verbal": {"phrases": {"0": "Salida a las <TIME>.", "1": "Salida a las <TIME> desde <TRANSIT_STOP>."}, "example_phrases": {"0": ["Depart at 8:02 AM."], "1": ["Depart at 8:02 AM from 8 St - NYU."]}}, "destination": {"phrases": {"0": "Ha llegado a su destino.", "1": "Ha llegado a <DESTINATION>.", "2": "Su destino está a la <RELATIVE_DIRECTION>.", "3": "<DESTINATION> está a la <RELATIVE_DIRECTION>."}, "relative_directions": ["izquierda", "derecha"], "example_phrases": {"0": ["You have arrived at your destination."], "1": ["You have arrived at 3206 Powelton Avenue."], "2": ["Your destination is on the left.", "Your destination is on the right."], "3": ["Lancaster Brewing Company is on the left."]}}, "destination_verbal": {"phrases": {"0": "Ha llegado a su destino.", "1": "Ha llegado a <DESTINATION>.", "2": "Su destino está a la <RELATIVE_DIRECTION>.", "3": "<DESTINATION> está a la <RELATIVE_DIRECTION>."}, "relative_directions": ["izquierda", "derecha"], "example_phrases": {"0": ["You have arrived at your destination."], "1": ["You have arrived at 32 o6 Powelton Avenue."], "2": ["Your destination is on the left.", "Your destination is on the right."], "3": ["Lancaster Brewing Company is on the left."]}}, "destination_verbal_alert": {"phrases": {"0": "Llegará a su destino.", "1": "Llegará a <DESTINATION>.", "2": "Su destino estará a la <RELATIVE_DIRECTION>.", "3": "<DESTINATION> estará a la <RELATIVE_DIRECTION>."}, "relative_directions": ["izquierda", "derecha"], "example_phrases": {"0": ["You will arrive at your destination."], "1": ["You will arrive at 32 o6 Powelton Avenue."], "2": ["Your destination will be on the left.", "Your destination will be on the right."], "3": ["Lancaster Brewing Company will be on the left."]}}, "enter_ferry": {"phrases": {"0": "Tome el ferri.", "1": "<PERSON><PERSON> la <STREET_NAMES>.", "2": "<PERSON><PERSON> la <STREET_NAMES> <FERRY_LABEL>.", "3": "Tome el ferri en dirección a <TOWARD_SIGN>."}, "empty_street_name_labels": ["la calzada", "el carril bici", "el sendero para bicicletas de montaña", "the crosswalk", "the stairs", "the bridge", "the tunnel"], "ferry_label": "<PERSON><PERSON>", "example_phrases": {"0": ["Take the Ferry."], "1": ["Take the Millersburg Ferry."], "2": ["Take the Bridgeport - Port Jefferson Ferry."], "3": ["Take the ferry toward Cape May."]}}, "enter_ferry_verbal": {"phrases": {"0": "Tome el ferri.", "1": "<PERSON><PERSON> la <STREET_NAMES>.", "2": "<PERSON><PERSON> la <STREET_NAMES> <FERRY_LABEL>.", "3": "Tome el ferri en dirección a <TOWARD_SIGN>."}, "empty_street_name_labels": ["la calzada", "el carril bici", "el sendero para bicicletas de montaña", "the crosswalk", "the stairs", "the bridge", "the tunnel"], "ferry_label": "<PERSON><PERSON>", "example_phrases": {"0": ["Take the Ferry."], "1": ["Take the Millersburg Ferry."], "2": ["Take the Bridgeport - Port Jefferson Ferry."], "3": ["Take the ferry toward Cape May."]}}, "enter_roundabout": {"phrases": {"0": "Haga la rotonda.", "1": "Haga la rotonda y tome la salida <ORDINAL_VALUE>.", "2": "Haga la rotonda y tome la salida <ORDINAL_VALUE> hacia <ROUNDABOUT_EXIT_STREET_NAMES>.", "3": "Haga la rotonda y tome la salida <ORDINAL_VALUE> hacia <ROUNDABOUT_EXIT_BEGIN_STREET_NAMES>. Continúe en <ROUNDABOUT_EXIT_STREET_NAMES>.", "4": "Haga la rotonda y tome la salida <ORDINAL_VALUE> en dirección a <TOWARD_SIGN>.", "5": "Haga la rotonda y tome la salida hacia <ROUNDABOUT_EXIT_STREET_NAMES>.", "6": "Haga la rotonda y tome la salida hacia <ROUNDABOUT_EXIT_BEGIN_STREET_NAMES>. Continúe en <ROUNDABOUT_EXIT_STREET_NAMES>.", "7": "Haga la rotonda y tome la salida en dirección a <TOWARD_SIGN>.", "8": "Incorpórese a <STREET_NAMES>", "9": "Incorpórese a <STREET_NAMES> y tome la salida <ORDINAL_VALUE>.", "10": "Incorpórese a <STREET_NAMES> y tome la salida <ORDINAL_VALUE> hacia <ROUNDABOUT_EXIT_STREET_NAMES>.", "11": "Incorpórese a <STREET_NAMES> y tome la salida <ORDINAL_VALUE> hacia <ROUNDABOUT_EXIT_BEGIN_STREET_NAMES>. Continúe en <ROUNDABOUT_EXIT_STREET_NAMES>.", "12": "Incorpórese a <STREET_NAMES> y tome la salida <ORDINAL_VALUE> en dirección a <TOWARD_SIGN>.", "13": "Incorpórese a <STREET_NAMES> y tome la salida hacia <ROUNDABOUT_EXIT_STREET_NAMES>.", "14": "Incorpórese a <STREET_NAMES> y tome la salida hacia <ROUNDABOUT_EXIT_BEGIN_STREET_NAMES>. Continúe en <ROUNDABOUT_EXIT_STREET_NAMES>.", "15": "Incorpórese a <STREET_NAMES> y tome la salida en dirección a <TOWARD_SIGN>."}, "ordinal_values": ["1.º", "2.º", "3.º", "4.º", "5.º", "6.º", "7.º", "8.º", "9.º", "10.º"], "empty_street_name_labels": ["la calzada", "el carril bici", "el sendero para bicicletas de montaña", "the crosswalk", "the stairs", "the bridge", "the tunnel"], "example_phrases": {"0": ["Enter the roundabout."], "1": ["Enter the roundabout and take the 1st exit.", "Enter the roundabout and take the 2nd exit.", "Enter the roundabout and take the 3rd exit.", "Enter the roundabout and take the 4th exit.", "Enter the roundabout and take the 5th exit.", "Enter the roundabout and take the 6th exit.", "Enter the roundabout and take the 7th exit.", "Enter the roundabout and take the 8th exit.", "Enter the roundabout and take the 9th exit.", "Enter the roundabout and take the 10th exit."], "2": ["Enter the roundabout and take the 3rd exit onto Main Street."], "3": ["Enter the roundabout and take the 3rd exit onto US 322/Main Street. Continue on US 322."], "4": ["Enter the roundabout and take the 3rd exit toward Baltimore."], "5": ["Enter the roundabout and take the exit onto Main Street."], "6": ["Enter the roundabout and take the exit onto US 322/Main Street. Continue on US 322."], "7": ["Enter the roundabout and take the exit toward Baltimore."], "8": ["Enter Dupont Circle."], "9": ["Enter Dupont Circle and take the 1st exit."], "10": ["Enter Dupont Circle and take the 3rd exit onto Main Street."], "11": ["Enter Dupont Circle and take the 3rd exit onto US 322/Main Street. Continue on US 322."], "12": ["Enter Dupont Circle and take the 3rd exit toward Baltimore."], "13": ["Enter Dupont Circle and take the exit onto Main Street."], "14": ["Enter Dupont Circle and take the exit onto US 322/Main Street. Continue on US 322."], "15": ["Enter Dupont Circle and take the exit toward Baltimore."]}}, "enter_roundabout_verbal": {"phrases": {"0": "Haga la rotonda.", "1": "Haga la rotonda y tome la salida <ORDINAL_VALUE>.", "2": "Haga la rotonda y tome la salida <ORDINAL_VALUE> hacia <ROUNDABOUT_EXIT_STREET_NAMES>.", "3": "Haga la rotonda y tome la salida <ORDINAL_VALUE> hacia <ROUNDABOUT_EXIT_BEGIN_STREET_NAMES>.", "4": "Haga la rotonda y tome la salida <ORDINAL_VALUE> en dirección a <TOWARD_SIGN>.", "5": "Haga la rotonda y tome la salida hacia <ROUNDABOUT_EXIT_STREET_NAMES>.", "6": "Haga la rotonda y tome la salida hacia <ROUNDABOUT_EXIT_BEGIN_STREET_NAMES>.", "7": "Haga la rotonda y tome la salida en dirección a <TOWARD_SIGN>.", "8": "Incorpórese a <STREET_NAMES>", "9": "Incorpórese a <STREET_NAMES> y tome la salida <ORDINAL_VALUE>.", "10": "Incorpórese a <STREET_NAMES> y tome la salida <ORDINAL_VALUE> hacia <ROUNDABOUT_EXIT_STREET_NAMES>.", "11": "Incorpórese a <STREET_NAMES> y tome la salida <ORDINAL_VALUE> hacia <ROUNDABOUT_EXIT_BEGIN_STREET_NAMES>.", "12": "Incorpórese a <STREET_NAMES> y tome la salida <ORDINAL_VALUE> en dirección a <TOWARD_SIGN>.", "13": "Incorpórese a <STREET_NAMES> y tome la salida hacia <ROUNDABOUT_EXIT_STREET_NAMES>.", "14": "Incorpórese a <STREET_NAMES> y tome la salida hacia <ROUNDABOUT_EXIT_BEGIN_STREET_NAMES>.", "15": "Incorpórese a <STREET_NAMES> y tome la salida en dirección a <TOWARD_SIGN>."}, "ordinal_values": ["1.º", "2.º", "3.º", "4.º", "5.º", "6.º", "7.º", "8.º", "9.º", "10.º"], "empty_street_name_labels": ["la calzada", "el carril bici", "el sendero para bicicletas de montaña", "the crosswalk", "the stairs", "the bridge", "the tunnel"], "example_phrases": {"0": ["Enter the roundabout."], "1": ["Enter the roundabout and take the 1st exit.", "Enter the roundabout and take the 2nd exit.", "Enter the roundabout and take the 3rd exit.", "Enter the roundabout and take the 4th exit.", "Enter the roundabout and take the 5th exit.", "Enter the roundabout and take the 6th exit.", "Enter the roundabout and take the 7th exit.", "Enter the roundabout and take the 8th exit.", "Enter the roundabout and take the 9th exit.", "Enter the roundabout and take the 10th exit."], "2": ["Enter the roundabout and take the 3rd exit onto Main Street."], "3": ["Enter the roundabout and take the 3rd exit onto U.S. 3 22/Main Street."], "4": ["Enter the roundabout and take the 3rd exit toward Baltimore."], "5": ["Enter the roundabout and take the exit onto Main Street."], "6": ["Enter the roundabout and take the exit onto U.S. 3 22/Main Street."], "7": ["Enter the roundabout and take the exit toward Baltimore."], "8": ["Enter Dupont Circle."], "9": ["Enter Dupont Circle and take the 1st exit."], "10": ["Enter Dupont Circle and take the 3rd exit onto Main Street."], "11": ["Enter Dupont Circle and take the 3rd exit onto U.S. 3 22/Main Street."], "12": ["Enter Dupont Circle and take the 3rd exit toward Baltimore."], "13": ["Enter Dupont Circle and take the exit onto Main Street."], "14": ["Enter Dupont Circle and take the exit onto U.S. 3 22/Main Street."], "15": ["Enter Dupont Circle and take the exit toward Baltimore."]}}, "exit": {"phrases": {"0": "Tome la salida a la <RELATIVE_DIRECTION>.", "1": "Tome la salida <NUMBER_SIGN> a la <RELATIVE_DIRECTION>.", "2": "<PERSON><PERSON> la salida <BRANCH_SIGN> a la <RELATIVE_DIRECTION>.", "3": "Tom<PERSON> la salida <NUMBER_SIGN> a la <RELATIVE_DIRECTION> hacia <BRANCH_SIGN>.", "4": "Tome la salida a la <RELATIVE_DIRECTION> en dirección a <TOWARD_SIGN>.", "5": "Tom<PERSON> la salida <NUMBER_SIGN> a la <RELATIVE_DIRECTION> en dirección a <TOWARD_SIGN>.", "6": "<PERSON><PERSON> la salida <BRANCH_SIGN> a la <RELATIVE_DIRECTION> en dirección a <TOWARD_SIGN>.", "7": "Tom<PERSON> la salida <NUMBER_SIGN> a la <RELATIVE_DIRECTION> hacia <BRANCH_SIGN> en dirección a <TOWARD_SIGN>.", "8": "Tome la salida <NAME_SIGN> a la <RELATIVE_DIRECTION>.", "10": "<PERSON><PERSON> la salida <NAME_SIGN> a la <RELATIVE_DIRECTION> hacia <BRANCH_SIGN>.", "12": "Tome la salida <NAME_SIGN> a la <RELATIVE_DIRECTION> en dirección a <TOWARD_SIGN>.", "14": "<PERSON><PERSON> la salida <NAME_SIGN> a la <RELATIVE_DIRECTION> hacia <BRANCH_SIGN> en dirección a <TOWARD_SIGN>.", "15": "Tome la salida.", "16": "<PERSON><PERSON> la salida <NUMBER_SIGN>.", "17": "<PERSON><PERSON> la salida <BRANCH_SIGN>.", "18": "<PERSON><PERSON> la salida <NUMBER_SIGN> hacia <BRANCH_SIGN>.", "19": "Tome la salida en dirección a <TOWARD_SIGN>.", "20": "<PERSON><PERSON> la salida <NUMBER_SIGN> en dirección a <TOWARD_SIGN>.", "21": "<PERSON><PERSON> la salida <BRANCH_SIGN> en dirección a <TOWARD_SIGN>.", "22": "<PERSON><PERSON> la salida <NUMBER_SIGN> hacia <BRANCH_SIGN> en dirección a <TOWARD_SIGN>.", "23": "<PERSON><PERSON> la salida <NAME_SIGN>.", "25": "<PERSON><PERSON> la salida <NAME_SIGN> hacia <BRANCH_SIGN>.", "27": "<PERSON>e la salida <NAME_SIGN> en dirección a <TOWARD_SIGN>.", "29": "<PERSON><PERSON> la salida <NAME_SIGN> hacia <BRANCH_SIGN> en dirección a <TOWARD_SIGN>."}, "relative_directions": ["izquierda", "derecha"], "example_phrases": {"0": ["Take the exit on the left.", "Take the exit on the right."], "1": ["Take exit 67 B-A on the right."], "2": ["Take the US 322 West exit on the right."], "3": ["Take exit 67 B-A on the right onto US 322 West."], "4": ["Take the exit on the right toward Lewistown."], "5": ["Take exit 67 B-A on the right toward Lewistown."], "6": ["Take the US 322 West exit on the right toward Lewistown."], "7": ["Take exit 67 B-A on the right onto US 322 West toward Lewistown/State College."], "8": ["Take the White Marsh Boulevard exit on the left."], "10": ["Take the White Marsh Boulevard exit on the left onto MD 43 East."], "12": ["Take the White Marsh Boulevard exit on the left toward White Marsh."], "14": ["Take the White Marsh Boulevard exit on the left onto MD 43 East toward White Marsh."], "15": ["Take the exit."], "16": ["Take exit 67 B-A."], "17": ["Take the US 322 West exit."], "18": ["Take exit 67 B-A onto US 322 West."], "19": ["Take the exit toward Lewistown."], "20": ["Take exit 67 B-A toward Lewistown."], "21": ["Take the US 322 West exit toward Lewistown."], "22": ["Take exit 67 B-A onto US 322 West toward Lewistown/State College."], "23": ["Take the White Marsh Boulevard exit."], "25": ["Take the White Marsh Boulevard exit onto MD 43 East."], "27": ["Take the White Marsh Boulevard exit toward White Marsh."], "29": ["Take the White Marsh Boulevard exit onto MD 43 East toward White Marsh."]}}, "exit_roundabout": {"phrases": {"0": "Salga de la rotonda.", "1": "Salga de la rotonda hacia <STREET_NAMES>.", "2": "Salga de la rotonda hacia <BEGIN_STREET_NAMES>. Continúe en <STREET_NAMES>.", "3": "Salga de la rotonda en dirección a <TOWARD_SIGN>."}, "empty_street_name_labels": ["la calzada", "el carril bici", "el sendero para bicicletas de montaña", "the crosswalk", "the stairs", "the bridge", "the tunnel"], "example_phrases": {"0": ["Exit the roundabout."], "1": ["Exit the roundabout onto Philadelphia Road/MD 7."], "2": ["Exit the roundabout onto Catoctin Mountain Highway/US 15. Continue on US 15."], "3": ["Exit the roundabout toward Baltimore."]}}, "exit_roundabout_verbal": {"phrases": {"0": "Salga de la rotonda.", "1": "Salga de la rotonda hacia <STREET_NAMES>.", "2": "Salga de la rotonda hacia <BEGIN_STREET_NAMES>.", "3": "Salga de la rotonda en dirección a <TOWARD_SIGN>."}, "empty_street_name_labels": ["la calzada", "el carril bici", "el sendero para bicicletas de montaña", "the crosswalk", "the stairs", "the bridge", "the tunnel"], "example_phrases": {"0": ["Exit the roundabout."], "1": ["Exit the roundabout onto Philadelphia Road, Maryland 7."], "2": ["Exit the roundabout onto Catoctin Mountain Highway, U.S. 15."], "3": ["Exit the roundabout toward Baltimore."]}}, "exit_verbal": {"phrases": {"0": "Tome la salida a la <RELATIVE_DIRECTION>.", "1": "Tome la salida <NUMBER_SIGN> a la <RELATIVE_DIRECTION>.", "2": "<PERSON><PERSON> la salida <BRANCH_SIGN> a la <RELATIVE_DIRECTION>.", "3": "Tom<PERSON> la salida <NUMBER_SIGN> a la <RELATIVE_DIRECTION> hacia <BRANCH_SIGN>.", "4": "Tome la salida a la <RELATIVE_DIRECTION> en dirección a <TOWARD_SIGN>.", "5": "Tom<PERSON> la salida <NUMBER_SIGN> a la <RELATIVE_DIRECTION> en dirección a <TOWARD_SIGN>.", "6": "<PERSON><PERSON> la salida <BRANCH_SIGN> a la <RELATIVE_DIRECTION> en dirección a <TOWARD_SIGN>.", "7": "Toma la salida <NUMBER_SIGN> a la <RELATIVE_DIRECTION> hacia <BRANCH_SIGN> en dirección a <TOWARD_SIGN>.", "8": "Tome la salida <NAME_SIGN> a la <RELATIVE_DIRECTION>.", "10": "<PERSON><PERSON> la salida <NAME_SIGN> a la <RELATIVE_DIRECTION> hacia <BRANCH_SIGN>.", "12": "Tome la salida <NAME_SIGN> a la <RELATIVE_DIRECTION> en dirección a <TOWARD_SIGN>.", "14": "<PERSON><PERSON> la salida <NAME_SIGN> a la <RELATIVE_DIRECTION> hacia <BRANCH_SIGN> en dirección a <TOWARD_SIGN>.", "15": "Tome la salida.", "16": "<PERSON><PERSON> la salida <NUMBER_SIGN>.", "17": "<PERSON><PERSON> la salida <BRANCH_SIGN>.", "18": "<PERSON><PERSON> la salida <NUMBER_SIGN> hacia <BRANCH_SIGN>.", "19": "Tome la salida en dirección a <TOWARD_SIGN>.", "20": "<PERSON><PERSON> la salida <NUMBER_SIGN> en dirección a <TOWARD_SIGN>.", "21": "<PERSON><PERSON> la salida <BRANCH_SIGN> en dirección a <TOWARD_SIGN>.", "22": "<PERSON><PERSON> la salida <NUMBER_SIGN> hacia <BRANCH_SIGN> en dirección a <TOWARD_SIGN>.", "23": "<PERSON><PERSON> la salida <NAME_SIGN>.", "25": "<PERSON><PERSON> la salida <NAME_SIGN> hacia <BRANCH_SIGN>.", "27": "<PERSON>e la salida <NAME_SIGN> en dirección a <TOWARD_SIGN>.", "29": "<PERSON><PERSON> la salida <NAME_SIGN> hacia <BRANCH_SIGN> en dirección a <TOWARD_SIGN>."}, "relative_directions": ["izquierda", "derecha"], "example_phrases": {"0": ["Take the exit on the left.", "Take the exit on the right."], "1": ["Take exit 67 B-A on the right."], "2": ["Take the U.S. 3 22 West exit on the right."], "3": ["Take exit 67 B-A on the right onto U.S. 3 22 West."], "4": ["Take the exit on the right toward Lewistown."], "5": ["Take exit 67 B-A on the right toward Lewistown."], "6": ["Take the U.S. 3 22 West exit on the right toward Lewistown."], "7": ["Take exit 67 B-A on the right onto U.S. 3 22 West toward Lewistown, State College."], "8": ["Take the White Marsh Boulevard exit on the left."], "10": ["Take the White Marsh Boulevard exit on the left onto Maryland 43 East."], "12": ["Take the White Marsh Boulevard exit on the left toward White Marsh."], "14": ["Take the White Marsh Boulevard exit on the left onto Maryland 43 East toward White Marsh."], "15": ["Take the exit."], "16": ["Take exit 67 B-A."], "17": ["Take the US 322 West exit."], "18": ["Take exit 67 B-A onto US 322 West."], "19": ["Take the exit toward Lewistown."], "20": ["Take exit 67 B-A toward Lewistown."], "21": ["Take the US 322 West exit toward Lewistown."], "22": ["Take exit 67 B-A onto US 322 West toward Lewistown/State College."], "23": ["Take the White Marsh Boulevard exit."], "25": ["Take the White Marsh Boulevard exit onto MD 43 East."], "27": ["Take the White Marsh Boulevard exit toward White Marsh."], "29": ["Take the White Marsh Boulevard exit onto MD 43 East toward White Marsh."]}}, "exit_visual": {"phrases": {"0": "Salida <EXIT_NUMBERS>"}, "example_phrases": {"0": ["Exit 1A"]}}, "keep": {"phrases": {"0": "Manténgase a la <RELATIVE_DIRECTION> en la bifurcación.", "1": "Manténgase a la <RELATIVE_DIRECTION> para tomar la salida <NUMBER_SIGN>.", "2": "Manténgase a la <RELATIVE_DIRECTION> para tomar <STREET_NAMES>.", "3": "Manténgase a la <RELATIVE_DIRECTION> para tomar la salida <NUMBER_SIGN> hacia <STREET_NAMES>.", "4": "Manténgase a la <RELATIVE_DIRECTION> en dirección a <TOWARD_SIGN>.", "5": "Manténgase a la <RELATIVE_DIRECTION> para tomar la salida <NUMBER_SIGN> en dirección a <TOWARD_SIGN>.", "6": "Manténgase a la <RELATIVE_DIRECTION> para tomar la <STREET_NAMES> en dirección a <TOWARD_SIGN>.", "7": "Manténgase a la <RELATIVE_DIRECTION> para tomar la salida <NUMBER_SIGN> hacia <STREET_NAMES> en dirección a <TOWARD_SIGN>."}, "empty_street_name_labels": ["la calzada", "el carril bici", "el sendero para bicicletas de montaña", "the crosswalk", "the stairs", "the bridge", "the tunnel"], "relative_directions": ["izquierda", "recto", "derecha"], "example_phrases": {"0": ["Keep left at the fork.", "Keep straight at the fork.", "Keep right at the fork."], "1": ["Keep right to take exit 62."], "2": ["Keep right to take I 895 South."], "3": ["Keep right to take exit 62 onto I 895 South."], "4": ["Keep right toward Annapolis."], "5": ["Keep right to take exit 62 toward Annapolis."], "6": ["Keep right to take I 895 South toward Annapolis."], "7": ["Keep right to take exit 62 onto I 895 South toward Annapolis."]}}, "keep_to_stay_on": {"phrases": {"0": "Manténgase a la <RELATIVE_DIRECTION> para permanecer en <STREET_NAMES>.", "1": "Manténgase a la <RELATIVE_DIRECTION> para tomar la salida <NUMBER_SIGN> y permanecer en <STREET_NAMES>.", "2": "Manténgase a la <RELATIVE_DIRECTION> para permanecer en <STREET_NAMES> en dirección a <TOWARD_SIGN>.", "3": "Manténgase a la <RELATIVE_DIRECTION> para tomar la salida <NUMBER_SIGN> y permanecer en <STREET_NAMES> en dirección a <TOWARD_SIGN>."}, "empty_street_name_labels": ["la calzada", "el carril bici", "el sendero para bicicletas de montaña", "the crosswalk", "the stairs", "the bridge", "the tunnel"], "relative_directions": ["izquierda", "recto", "derecha"], "example_phrases": {"0": ["Keep left to stay on I 95 South.", "Keep straight to stay on I 95 South.", "Keep right to stay on I 95 South."], "1": ["Keep left to take exit 62 to stay on I 95 South."], "2": ["Keep left to stay on I 95 South toward Baltimore."], "3": ["Keep left to take exit 62 to stay on I 95 South toward Baltimore."]}}, "keep_to_stay_on_verbal": {"phrases": {"0": "Manténgase a la <RELATIVE_DIRECTION> para permanecer en <STREET_NAMES>.", "1": "Manténgase a la <RELATIVE_DIRECTION> para tomar la salida <NUMBER_SIGN> y permanecer en <STREET_NAMES>.", "2": "Manténgase a la <RELATIVE_DIRECTION> para permanecer en <STREET_NAMES> en dirección a <TOWARD_SIGN>.", "3": "Manténgase a la <RELATIVE_DIRECTION> para tomar la salida <NUMBER_SIGN> y permanecer en <STREET_NAMES> en dirección a <TOWARD_SIGN>."}, "empty_street_name_labels": ["la calzada", "el carril bici", "el sendero para bicicletas de montaña", "the crosswalk", "the stairs", "the bridge", "the tunnel"], "relative_directions": ["izquierda", "recto", "derecha"], "example_phrases": {"0": ["Keep left to stay on Interstate 95 South.", "Keep straight to stay on Interstate 95 South.", "Keep right to stay on Interstate 95 South."], "1": ["Keep left to take exit 62 to stay on Interstate 95 South."], "2": ["Keep left to stay on I 95 South toward Baltimore."], "3": ["Keep left to take exit 62 to stay on Interstate 95 South toward Baltimore."]}}, "keep_verbal": {"phrases": {"0": "Manténgase a la <RELATIVE_DIRECTION> en la bifurcación.", "1": "Manténgase a la <RELATIVE_DIRECTION> para tomar la salida <NUMBER_SIGN>.", "2": "Manténgase a la <RELATIVE_DIRECTION> para tomar <STREET_NAMES>.", "3": "Manténgase a la <RELATIVE_DIRECTION> para tomar la salida <NUMBER_SIGN> hacia <STREET_NAMES>.", "4": "Manténgase a la <RELATIVE_DIRECTION> en dirección a <TOWARD_SIGN>.", "5": "Manténgase a la <RELATIVE_DIRECTION> para tomar la salida <NUMBER_SIGN> en dirección a <TOWARD_SIGN>.", "6": "Manténgase a la <RELATIVE_DIRECTION> para tomar <STREET_NAMES> en dirección a <TOWARD_SIGN>.", "7": "Manténgase a la <RELATIVE_DIRECTION> para tomar la salida <NUMBER_SIGN> hacia <STREET_NAMES> en dirección a <TOWARD_SIGN>."}, "empty_street_name_labels": ["la calzada", "el carril bici", "el sendero para bicicletas de montaña", "the crosswalk", "the stairs", "the bridge", "the tunnel"], "relative_directions": ["izquierda", "recto", "derecha"], "example_phrases": {"0": ["Keep left at the fork.", "Keep straight at the fork.", "Keep right at the fork."], "1": ["Keep right to take exit 62."], "2": ["Keep right to take Interstate 8 95 South."], "3": ["Keep right to take exit 62 onto Interstate 8 95 South."], "4": ["Keep right toward Annapolis."], "5": ["Keep right to take exit 62 toward Annapolis."], "6": ["Keep right to take Interstate 8 95 South toward Annapolis."], "7": ["Keep right to take exit 62 onto Interstate 8 95 South toward Annapolis."]}}, "merge": {"phrases": {"0": "Incorpórese.", "1": "Incorpórese a la <RELATIVE_DIRECTION>.", "2": "Incorpórese a <STREET_NAMES>.", "3": "Incorpórese a la <RELATIVE_DIRECTION> a <STREET_NAMES>.", "4": "Incorpórese en dirección a <TOWARD_SIGN>.", "5": "Incorpórese a la <RELATIVE_DIRECTION> en dirección a <TOWARD_SIGN>."}, "relative_directions": ["izquierda", "derecha"], "empty_street_name_labels": ["la calzada", "el carril bici", "el sendero para bicicletas de montaña", "the crosswalk", "the stairs", "the bridge", "the tunnel"], "example_phrases": {"0": ["Merge."], "1": ["<PERSON><PERSON> left."], "2": ["Merge onto I 76 West/Pennsylvania Turnpike."], "3": ["Merge right onto I 83 South."], "4": ["Merge toward Baltimore."], "5": ["Merge right toward Baltimore."]}}, "merge_verbal": {"phrases": {"0": "Incorpórese.", "1": "Incorpórese a la <RELATIVE_DIRECTION>.", "2": "Incorpórese a <STREET_NAMES>.", "3": "Incorpórese a la <RELATIVE_DIRECTION> a <STREET_NAMES>.", "4": "Incorpórese en dirección a <TOWARD_SIGN>.", "5": "Incorpórese a la <RELATIVE_DIRECTION> en dirección a <TOWARD_SIGN>."}, "relative_directions": ["izquierda", "derecha"], "empty_street_name_labels": ["la calzada", "el carril bici", "el sendero para bicicletas de montaña", "the crosswalk", "the stairs", "the bridge", "the tunnel"], "example_phrases": {"0": ["Merge."], "1": ["<PERSON><PERSON> left."], "2": ["Merge onto I 76 West/Pennsylvania Turnpike."], "3": ["Merge right onto I 83 South."], "4": ["Merge toward Baltimore."], "5": ["Merge right toward Baltimore."]}}, "post_transition_transit_verbal": {"phrases": {"0": "<PERSON><PERSON> <TRANSIT_STOP_COUNT> <TRANSIT_STOP_COUNT_LABEL>."}, "transit_stop_count_labels": {"one": "parada", "other": "paradas"}, "example_phrases": {"0": ["Travel 1 stop.", "Travel 3 stops."]}}, "post_transition_verbal": {"phrases": {"0": "Contin<PERSON>e durante <LENGTH>.", "1": "Continúe en <STREET_NAMES> durante <LENGTH>."}, "empty_street_name_labels": ["la calzada", "el carril bici", "el sendero para bicicletas de montaña", "the crosswalk", "the stairs", "the bridge", "the tunnel"], "metric_lengths": ["<KILOMETERS> kilómetros", "1 kilómetro", "<METERS> metros", "menos de 10 metros"], "us_customary_lengths": ["<MILES> millas", "1 milla", "media milla", "un cuarto de milla", "<FEET> pies", "menos de 10 pies"], "example_phrases": {"0": ["Continue for 300 feet.", "Continue for 9 miles."], "1": ["Continue on Pennsylvania 7 43 for 6.2 miles.", "Continue on Main Street, Vermont 30 for 1 tenth of a mile."]}}, "ramp": {"phrases": {"0": "Tome la vía de salida a la <RELATIVE_DIRECTION>.", "1": "Tome la vía de salida <BRANCH_SIGN> a la <RELATIVE_DIRECTION>.", "2": "Tome la vía de salida a la <RELATIVE_DIRECTION> en dirección a <TOWARD_SIGN>.", "3": "Tome la vía de salida <BRANCH_SIGN> a la <RELATIVE_DIRECTION> en dirección a <TOWARD_SIGN>.", "4": "Tome la vía de salida <NAME_SIGN> a la <RELATIVE_DIRECTION>.", "5": "Gire a la <RELATIVE_DIRECTION> para tomar la vía de salida.", "6": "Gire a la <RELATIVE_DIRECTION> para tomar la vía de salida <BRANCH_SIGN>.", "7": "Gire a la <RELATIVE_DIRECTION> para tomar la vía de salida en dirección a <TOWARD_SIGN>.", "8": "Gire a la <RELATIVE_DIRECTION> para tomar la vía de salida <BRANCH_SIGN> en dirección a <TOWARD_SIGN>.", "9": "Gire a la <RELATIVE_DIRECTION> para tomar la vía de salida <NAME_SIGN>.", "10": "Tome la vía de salida.", "11": "Tome la vía de salida <BRANCH_SIGN>.", "12": "Tome la vía de salida en dirección a <TOWARD_SIGN>.", "13": "Tome la vía de salida <BRANCH_SIGN> en dirección a <TOWARD_SIGN>.", "14": "Tome la vía de salida <NAME_SIGN>."}, "relative_directions": ["izquierda", "derecha"], "example_phrases": {"0": ["Take the ramp on the left.", "Take the ramp on the right."], "1": ["Take the I 95 ramp on the right."], "2": ["Take the ramp on the left toward JFK."], "3": ["Take the South Conduit Avenue ramp on the left toward JFK."], "4": ["Take the Gettysburg Pike ramp on the right."], "5": ["Turn left to take the ramp.", "Turn right to take the ramp."], "6": ["Turn left to take the PA 283 West ramp."], "7": ["Turn left to take the ramp toward Harrisburg/Harrisburg International Airport."], "8": ["Turn left to take the PA 283 West ramp toward Harrisburg/Harrisburg International Airport."], "9": ["Turn right to take the Gettysburg Pike ramp."], "10": ["Take the ramp."], "11": ["Take the I 95 ramp."], "12": ["Take the ramp toward JFK."], "13": ["Take the Soutch Conduit Avenue ramp toward JFK."], "14": ["Take the Gettysburg ramp."]}}, "ramp_straight": {"phrases": {"0": "Siga recto para tomar la vía de salida.", "1": "Siga recto para tomar la vía de salida <BRANCH_SIGN>.", "2": "Siga recto para tomar la vía de salida en dirección a <TOWARD_SIGN>.", "3": "Siga recto para tomar la vía de salida <BRANCH_SIGN> en dirección a <TOWARD_SIGN>.", "4": "Siga recto para tomar la vía de salida <NAME_SIGN>."}, "example_phrases": {"0": ["Stay straight to take the ramp."], "1": ["Stay straight to take the US 322 East ramp."], "2": ["Stay straight to take the ramp toward Hershey."], "3": ["Stay straight to take the US 322 East/US 422 East/US 522 East/US 622 East ramp toward Hershey/Palmdale/Palmyra/Campbelltown."], "4": ["Stay straight to take the Gettysburg Pike ramp."]}}, "ramp_straight_verbal": {"phrases": {"0": "Siga recto para tomar la vía de salida.", "1": "Siga recto para tomar la vía de salida <BRANCH_SIGN>.", "2": "Siga recto para tomar la vía de salida en dirección a <TOWARD_SIGN>.", "3": "Siga recto para tomar la vía de salida <BRANCH_SIGN> en dirección a <TOWARD_SIGN>.", "4": "Siga recto para tomar la vía de salida <NAME_SIGN>."}, "example_phrases": {"0": ["Stay straight to take the ramp."], "1": ["Stay straight to take the U.S. 3 22 East ramp."], "2": ["Stay straight to take the ramp toward Hershey."], "3": ["Stay straight to take the U.S. 3 22 East, U.S. 4 22 East ramp toward Hershey, Palmdale."], "4": ["Stay straight to take the Gettysburg Pike ramp."]}}, "ramp_verbal": {"phrases": {"0": "Tome la vía de salida a la <RELATIVE_DIRECTION>.", "1": "Tome la vía de salida <BRANCH_SIGN> a la <RELATIVE_DIRECTION>.", "2": "Tome la vía de salida a la <RELATIVE_DIRECTION> en dirección a <TOWARD_SIGN>.", "3": "Tome la vía de salida <BRANCH_SIGN> a la <RELATIVE_DIRECTION> en dirección a <TOWARD_SIGN>.", "4": "Tome la vía de salida <NAME_SIGN> a la <RELATIVE_DIRECTION>.", "5": "Gire a la <RELATIVE_DIRECTION> para tomar la vía de salida.", "6": "Gire a la <RELATIVE_DIRECTION> para tomar la vía de salida <BRANCH_SIGN>.", "7": "Gire a la <RELATIVE_DIRECTION> para tomar la vía de salida en dirección a <TOWARD_SIGN>.", "8": "Gire a la <RELATIVE_DIRECTION> para tomar la vía de salida <BRANCH_SIGN> en dirección a <TOWARD_SIGN>.", "9": "Gire a la <RELATIVE_DIRECTION> para tomar la vía de salida <NAME_SIGN>.", "10": "Tome la vía de salida.", "11": "Tome la vía de salida <BRANCH_SIGN>.", "12": "Tome la vía de salida en dirección a <TOWARD_SIGN>.", "13": "Tome la vía de salida <BRANCH_SIGN> en dirección a <TOWARD_SIGN>.", "14": "Tome la vía de salida <NAME_SIGN>."}, "relative_directions": ["izquierda", "derecha"], "example_phrases": {"0": ["Take the ramp on the left.", "Take the ramp on the right."], "1": ["Take the Interstate 95 ramp on the right."], "2": ["Take the ramp on the left toward JFK."], "3": ["Take the South Conduit Avenue ramp on the left toward JFK."], "4": ["Take the Gettysburg Pike ramp on the right."], "5": ["Turn left to take the ramp.", "Turn right to take the ramp."], "6": ["Turn left to take the Pennsylvania 2 83 West ramp."], "7": ["Turn left to take the ramp toward Harrisburg/Harrisburg International Airport."], "8": ["Turn left to take the Pennsylvania 2 83 West ramp toward Harrisburg, Harrisburg International Airport."], "9": ["Turn right to take the Gettysburg Pike ramp."], "10": ["Take the ramp."], "11": ["Take the Interstate 95 ramp."], "12": ["Take the ramp toward JFK."], "13": ["Take the South Conduit Avenue ramp toward JFK."], "14": ["Take the Gettysburg Pike ramp."]}}, "sharp": {"phrases": {"0": "Gire completamente a la <RELATIVE_DIRECTION>.", "1": "Gire completamente a la <RELATIVE_DIRECTION> hacia <STREET_NAMES>.", "2": "Gire completamente a la <RELATIVE_DIRECTION> hacia <BEGIN_STREET_NAMES>. Continúe en <STREET_NAMES>.", "3": "Gire completamente a la <RELATIVE_DIRECTION> para permanecer en <STREET_NAMES>.", "4": "Gire completamente a la <RELATIVE_DIRECTION> en <JUNCTION_NAME>.", "5": "Gire completamente a la <RELATIVE_DIRECTION> en dirección a <TOWARD_SIGN>."}, "empty_street_name_labels": ["la calzada", "el carril bici", "el sendero para bicicletas de montaña", "the crosswalk", "the stairs", "the bridge", "the tunnel"], "relative_directions": ["izquierda", "derecha"], "example_phrases": {"0": ["Make a sharp left."], "1": ["Make a sharp right onto Flatbush Avenue."], "2": ["Make a sharp left onto North Bond Street/US 1 Business/MD 924. Continue on MD 924."], "3": ["Make a sharp right to stay on Sunstone Drive."], "4": ["Make a sharp right at Mannenbashi East."], "5": ["Make a sharp left toward Baltimore."]}}, "sharp_verbal": {"phrases": {"0": "Gire completamente a la <RELATIVE_DIRECTION>.", "1": "Gire completamente a la <RELATIVE_DIRECTION> hacia <STREET_NAMES>.", "2": "Gire completamente a la <RELATIVE_DIRECTION> hacia <BEGIN_STREET_NAMES>.", "3": "Gire completamente a la <RELATIVE_DIRECTION> para permanecer en <STREET_NAMES>.", "4": "Gire completamente a la <RELATIVE_DIRECTION> en <JUNCTION_NAME>.", "5": "Gire completamente a la <RELATIVE_DIRECTION> en dirección a <TOWARD_SIGN>."}, "empty_street_name_labels": ["la calzada", "el carril bici", "el sendero para bicicletas de montaña", "the crosswalk", "the stairs", "the bridge", "the tunnel"], "relative_directions": ["izquierda", "derecha"], "example_phrases": {"0": ["Make a sharp left."], "1": ["Make a sharp right onto Flatbush Avenue."], "2": ["Make a sharp left onto North Bond Street, U.S. 1 Business."], "3": ["Make a sharp right to stay on Sunstone Drive."], "4": ["Make a sharp right at Mannenbashi East."], "5": ["Make a sharp left toward Baltimore."]}}, "start": {"phrases": {"0": "<PERSON><PERSON><PERSON><PERSON><PERSON> al <CARDINAL_DIRECTION>.", "1": "<PERSON><PERSON><PERSON><PERSON><PERSON> al <CARDINAL_DIRECTION> por <STREET_NAMES>.", "2": "<PERSON><PERSON><PERSON><PERSON><PERSON> al <CARDINAL_DIRECTION> por <BEGIN_STREET_NAMES>. Continúe en <STREET_NAMES>.", "4": "Conduzca hacia el <CARDINAL_DIRECTION>.", "5": "Conduzca hacia el <CARDINAL_DIRECTION> por <STREET_NAMES>.", "6": "Conduzca hacia el <CARDINAL_DIRECTION> por <BEGIN_STREET_NAMES>. Continúe en <STREET_NAMES>.", "8": "Camine hacia el <CARDINAL_DIRECTION>.", "9": "Camine hacia el <CARDINAL_DIRECTION> por <STREET_NAMES>.", "10": "Camine hacia el <CARDINAL_DIRECTION> por <BEGIN_STREET_NAMES>. Continúe en <STREET_NAMES>.", "16": "Vaya en bicicleta hacia el <CARDINAL_DIRECTION>.", "17": "Vaya en bicicleta hacia el <CARDINAL_DIRECTION> por <STREET_NAMES>.", "18": "Vaya en bicicleta hacia el <CARDINAL_DIRECTION> por <BEGIN_STREET_NAMES>. Continúe en <STREET_NAMES>."}, "cardinal_directions": ["norte", "noreste", "este", "sureste", "sur", "suroeste", "oeste", "noroeste"], "empty_street_name_labels": ["la calzada", "el carril bici", "el sendero para bicicletas de montaña", "the crosswalk", "the stairs", "the bridge", "the tunnel"], "example_phrases": {"0": ["Head east.", "Head north."], "1": ["Head southwest on 5th Avenue.", "Head west on the walkway", "Head east on the cycleway", "Head north on the mountain bike trail"], "2": ["Head south on North Prince Street/US 222/PA 272. Continue on US 222/PA 272."], "4": ["Drive east.", "Drive north."], "5": ["Drive southwest on 5th Avenue."], "6": ["Drive south on North Prince Street/US 222/PA 272. Continue on US 222/PA 272."], "8": ["Walk east.", "Walk north."], "9": ["Walk southwest on 5th Avenue.", "Walk west on the walkway"], "10": ["Walk south on North Prince Street/US 222/PA 272. Continue on US 222/PA 272."], "16": ["Bike east.", "Bike north."], "17": ["Bike southwest on 5th Avenue.", "Bike east on the cycleway", "Bike north on the mountain bike trail"], "18": ["Bike south on North Prince Street/US 222/PA 272. Continue on US 222/PA 272."]}}, "start_verbal": {"phrases": {"0": "<PERSON><PERSON><PERSON><PERSON><PERSON> hacia el <CARDINAL_DIRECTION>.", "1": "Di<PERSON><PERSON><PERSON><PERSON> hacia el <CARDINAL_DIRECTION> durante <LENGTH>.", "2": "<PERSON><PERSON><PERSON><PERSON><PERSON> hacia el <CARDINAL_DIRECTION> por <STREET_NAMES>.", "3": "<PERSON><PERSON><PERSON><PERSON><PERSON> hacia el <CARDINAL_DIRECTION> por <STREET_NAMES> durante <LENGTH>.", "4": "<PERSON><PERSON><PERSON><PERSON><PERSON> hacia el <CARDINAL_DIRECTION> por <BEGIN_STREET_NAMES>.", "5": "Conduzca hacia el <CARDINAL_DIRECTION>.", "6": "Conduzca hacia el <CARDINAL_DIRECTION> durante <LENGTH>.", "7": "Conduzca hacia el <CARDINAL_DIRECTION> por <STREET_NAMES>.", "8": "Conduzca hacia el <CARDINAL_DIRECTION> por <STREET_NAMES> durante <LENGTH>.", "9": "Conduzca hacia el <CARDINAL_DIRECTION> por <BEGIN_STREET_NAMES>.", "10": "Camine hacia el <CARDINAL_DIRECTION>.", "11": "Camine hacia el <CARDINAL_DIRECTION> durante <LENGTH>.", "12": "Camine hacia el <CARDINAL_DIRECTION> por <STREET_NAMES>.", "13": "Camine hacia el <CARDINAL_DIRECTION> por <STREET_NAMES> durante <LENGTH>.", "14": "Camine hacia el <CARDINAL_DIRECTION> por <BEGIN_STREET_NAMES>.", "15": "Vaya en bicicleta hacia el <CARDINAL_DIRECTION>.", "16": "Vaya en bicicleta hacia el <CARDINAL_DIRECTION> durante <LENGTH>.", "17": "Vaya en bicicleta hacia el <CARDINAL_DIRECTION> por <STREET_NAMES>.", "18": "Vaya en bicicleta hacia el <CARDINAL_DIRECTION> por <STREET_NAMES> durante <LENGTH>.", "19": "Vaya en bicicleta hacia el <CARDINAL_DIRECTION> por <BEGIN_STREET_NAMES>."}, "cardinal_directions": ["norte", "noreste", "este", "sureste", "sur", "suroeste", "oeste", "noroeste"], "empty_street_name_labels": ["la calzada", "el carril bici", "el sendero para bicicletas de montaña", "the crosswalk", "the stairs", "the bridge", "the tunnel"], "metric_lengths": ["<KILOMETERS> kilómetros", "1 kilómetro", "<METERS> metros", "menos de 10 metros"], "us_customary_lengths": ["<MILES> millas", "1 milla", "media milla", "un cuarto de milla", "<FEET> pies", "menos de 10 pies"], "example_phrases": {"0": ["Head east.", "Head north."], "1": ["Head east for a half mile.", "Head north for 1 kilometer."], "2": ["Head southwest on 5th Avenue."], "3": ["Head southwest on 5th Avenue for 1 tenth of a mile."], "4": ["Head south on North Prince Street, U.S. 2 22."], "5": ["Drive east.", "Drive north."], "6": ["Drive east for a half mile.", "Drive north for 1 kilometer."], "7": ["Drive southwest on 5th Avenue."], "8": ["Drive southwest on 5th Avenue for 1 tenth of a mile."], "9": ["Drive south on North Prince Street, U.S. 2 22."], "10": ["Walk east.", "Walk north."], "11": ["Walk east for a half mile.", "Walk north for 1 kilometer."], "12": ["Walk southwest on 5th Avenue."], "13": ["Walk southwest on 5th Avenue for 1 tenth of a mile."], "14": ["Walk south on North Prince Street, U.S. 2 22."], "15": ["Bike east.", "Bike north."], "16": ["Bike east for a half mile.", "Bike north for 1 kilometer."], "17": ["Bike southwest on 5th Avenue."], "18": ["Bike southwest on 5th Avenue for 1 tenth of a mile."], "19": ["Bike south on North Prince Street, U.S. 2 22."]}}, "transit": {"phrases": {"0": "<PERSON><PERSON> el <TRANSIT_NAME>. ( <TRANSIT_STOP_COUNT> <TRANSIT_STOP_COUNT_LABEL> )", "1": "Tom<PERSON> el <TRANSIT_NAME> en dirección a <TRANSIT_HEADSIGN>. ( <TRANSIT_STOP_COUNT> <TRANSIT_STOP_COUNT_LABEL> )"}, "empty_transit_name_labels": ["tranvía", "metro", "tren", "autobús", "ferri", "teleférico", "g<PERSON><PERSON><PERSON>", "funicular"], "transit_stop_count_labels": {"one": "parada", "other": "paradas"}, "example_phrases": {"0": ["Take the New Haven. (1 stop)", "Take the metro. (2 stops)", "Take the bus. (12 stops)"], "1": ["Take the F toward JAMAICA - 179 ST. (10 stops)", "Take the ferry toward Staten Island. (1 stop)"]}}, "transit_connection_destination": {"phrases": {"0": "Salga de la estación.", "1": "Baje en la <TRANSIT_STOP>.", "2": "Baje en la <TRANSIT_STOP> <STATION_LABEL>."}, "station_label": "Estación", "example_phrases": {"0": ["Exit the station."], "1": ["Exit the Embarcadero Station."], "2": ["Exit the 8 St - NYU Station."]}}, "transit_connection_destination_verbal": {"phrases": {"0": "Salga de la estación.", "1": "Baje en la <TRANSIT_STOP>.", "2": "Baje en la <TRANSIT_STOP> <STATION_LABEL>."}, "station_label": "Estación", "example_phrases": {"0": ["Exit the station."], "1": ["Exit the Embarcadero Station."], "2": ["Exit the 8 St - NYU Station."]}}, "transit_connection_start": {"phrases": {"0": "Entre en la estación.", "1": "Entre en la <TRANSIT_STOP>.", "2": "Entre en la <TRANSIT_STOP> <STATION_LABEL>."}, "station_label": "Estación", "example_phrases": {"0": ["Enter the station."], "1": ["Enter the Embarcadero Station."], "2": ["Enter the 8 St - NYU Station."]}}, "transit_connection_start_verbal": {"phrases": {"0": "Entre en la estación.", "1": "Entre en la <TRANSIT_STOP>.", "2": "Entre en la <TRANSIT_STOP> <STATION_LABEL>."}, "station_label": "Estación", "example_phrases": {"0": ["Enter the station."], "1": ["Enter the Embarcadero Station."], "2": ["Enter the 8 St - NYU Station."]}}, "transit_connection_transfer": {"phrases": {"0": "Haga transbordo en la estación.", "1": "Haga transbordo en la <TRANSIT_STOP>.", "2": "Haga transbordo en la <TRANSIT_STOP> <STATION_LABEL>."}, "station_label": "Estación", "example_phrases": {"0": ["Transfer at the station."], "1": ["Transfer at the Embarcadero Station."], "2": ["Transfer at the 8 St - NYU Station."]}}, "transit_connection_transfer_verbal": {"phrases": {"0": "Haga transbordo en la estación.", "1": "Haga transbordo en la <TRANSIT_STOP>.", "2": "Haga transbordo en la <TRANSIT_STOP> <STATION_LABEL>."}, "station_label": "Estación", "example_phrases": {"0": ["Transfer at the station."], "1": ["Transfer at the Embarcadero Station."], "2": ["Transfer at the 8 St - NYU Station."]}}, "transit_remain_on": {"phrases": {"0": "Permanezca en el <TRANSIT_NAME>. ( <TRANSIT_STOP_COUNT> <TRANSIT_STOP_COUNT_LABEL> )", "1": "Permanezca en el <TRANSIT_NAME> en dirección a <TRANSIT_HEADSIGN>. ( <TRANSIT_STOP_COUNT> <TRANSIT_STOP_COUNT_LABEL> )"}, "empty_transit_name_labels": ["tranvía", "metro", "tren", "autobús", "ferri", "teleférico", "g<PERSON><PERSON><PERSON>", "funicular"], "transit_stop_count_labels": {"one": "parada", "other": "paradas"}, "example_phrases": {"0": ["Remain on the New Haven. (1 stop)", "<PERSON><PERSON><PERSON> on the train. (3 stops)"], "1": ["Remain on the F toward JAMAICA - 179 ST. (10 stops)"]}}, "transit_remain_on_verbal": {"phrases": {"0": "Permanezca en el <TRANSIT_NAME>.", "1": "Permanezca en el <TRANSIT_NAME> en dirección a <TRANSIT_HEADSIGN>."}, "empty_transit_name_labels": ["tranvía", "metro", "tren", "autobús", "ferri", "teleférico", "g<PERSON><PERSON><PERSON>", "funicular"], "example_phrases": {"0": ["<PERSON><PERSON><PERSON> on the New Haven."], "1": ["Remain on the F toward JAMAICA - 179 ST."]}}, "transit_transfer": {"phrases": {"0": "Haga transbordo para tomar el <TRANSIT_NAME>. ( <TRANSIT_STOP_COUNT> <TRANSIT_STOP_COUNT_LABEL> )", "1": "Haga transbordo para tomar el <TRANSIT_NAME> en dirección a <TRANSIT_HEADSIGN>. ( <TRANSIT_STOP_COUNT> <TRANSIT_STOP_COUNT_LABEL> )"}, "empty_transit_name_labels": ["tranvía", "metro", "tren", "autobús", "ferri", "teleférico", "g<PERSON><PERSON><PERSON>", "funicular"], "transit_stop_count_labels": {"one": "parada", "other": "paradas"}, "example_phrases": {"0": ["Transfer to take the New Haven. (1 stop)", "Transfer to take the tram. (4 stops)"], "1": ["Transfer to take the F toward JAMAICA - 179 ST. (10 stops)"]}}, "transit_transfer_verbal": {"phrases": {"0": "Haga transbordo para tomar el <TRANSIT_NAME>.", "1": "Haga transbordo para tomar el <TRANSIT_NAME> en dirección a <TRANSIT_HEADSIGN>."}, "empty_transit_name_labels": ["tranvía", "metro", "tren", "autobús", "ferri", "teleférico", "g<PERSON><PERSON><PERSON>", "funicular"], "example_phrases": {"0": ["Transfer to take the New Haven."], "1": ["Transfer to take the F toward JAMAICA - 179 ST."]}}, "transit_verbal": {"phrases": {"0": "<PERSON><PERSON> el <TRANSIT_NAME>.", "1": "<PERSON><PERSON> el <TRANSIT_NAME> en dirección a <TRANSIT_HEADSIGN>."}, "empty_transit_name_labels": ["tranvía", "metro", "tren", "autobús", "ferri", "teleférico", "g<PERSON><PERSON><PERSON>", "funicular"], "example_phrases": {"0": ["Take the New Haven."], "1": ["Take the F toward JAMAICA - 179 ST."]}}, "turn": {"phrases": {"0": "Gire a la <RELATIVE_DIRECTION>.", "1": "Gire a la <RELATIVE_DIRECTION> hacia <STREET_NAMES>.", "2": "Gire a la <RELATIVE_DIRECTION> hacia <BEGIN_STREET_NAMES>. Continúe en <STREET_NAMES>.", "3": "Gire a la <RELATIVE_DIRECTION> para permanecer en <STREET_NAMES>.", "4": "Gire a la <RELATIVE_DIRECTION> en <JUNCTION_NAME>.", "5": "Gire a la <RELATIVE_DIRECTION> en dirección a <TOWARD_SIGN>."}, "empty_street_name_labels": ["la calzada", "el carril bici", "el sendero para bicicletas de montaña", "the crosswalk", "the stairs", "the bridge", "the tunnel"], "relative_directions": ["izquierda", "derecha"], "example_phrases": {"0": ["Turn left."], "1": ["Turn right onto Flatbush Avenue."], "2": ["Turn left onto North Bond Street/US 1 Business/MD 924. Continue on MD 924."], "3": ["Turn right to stay on Sunstone Drive."], "4": ["Turn right at Mannenbashi East."], "5": ["Turn left toward Baltimore."]}}, "turn_verbal": {"phrases": {"0": "Gire a la <RELATIVE_DIRECTION>.", "1": "Gire a la <RELATIVE_DIRECTION> hacia <STREET_NAMES>.", "2": "Gire a la <RELATIVE_DIRECTION> hacia <BEGIN_STREET_NAMES>.", "3": "Gire a la <RELATIVE_DIRECTION> para permanecer en <STREET_NAMES>.", "4": "Gire a la <RELATIVE_DIRECTION> en <JUNCTION_NAME>.", "5": "Gire a la <RELATIVE_DIRECTION> en dirección a <TOWARD_SIGN>."}, "empty_street_name_labels": ["la calzada", "el carril bici", "el sendero para bicicletas de montaña", "the crosswalk", "the stairs", "the bridge", "the tunnel"], "relative_directions": ["izquierda", "derecha"], "example_phrases": {"0": ["Turn left."], "1": ["Turn right onto Flatbush Avenue."], "2": ["Turn left onto North Bond Street, U.S. 1 Business."], "3": ["Turn right to stay on Sunstone Drive."], "4": ["Turn right at Mannenbashi East."], "5": ["Turn left toward Baltimore."]}}, "uturn": {"phrases": {"0": "Haga un cambio de sentido a la <RELATIVE_DIRECTION>.", "1": "Haga un cambio de sentido a la <RELATIVE_DIRECTION> hacia <STREET_NAMES>.", "2": "Haga un cambio de sentido a la <RELATIVE_DIRECTION> para permanecer en <STREET_NAMES>.", "3": "Haga un cambio de sentido a la <RELATIVE_DIRECTION> en <CROSS_STREET_NAMES>.", "4": "Haga un cambio de sentido a la <RELATIVE_DIRECTION> en <CROSS_STREET_NAMES> hacia <STREET_NAMES>.", "5": "Haga un cambio de sentido a la <RELATIVE_DIRECTION> en <CROSS_STREET_NAMES> para permanecer en <STREET_NAMES>.", "6": "Haga un cambio de sentido a la <RELATIVE_DIRECTION> en <JUNCTION_NAME>.", "7": "Haga un cambio de sentido a la <RELATIVE_DIRECTION> en dirección a <TOWARD_SIGN>."}, "empty_street_name_labels": ["la calzada", "el carril bici", "el sendero para bicicletas de montaña", "the crosswalk", "the stairs", "the bridge", "the tunnel"], "relative_directions": ["izquierda", "derecha"], "example_phrases": {"0": ["Make a left U-turn."], "1": ["Make a right U-turn onto Bunker Hill Road."], "2": ["Make a left U-turn to stay on Bunker Hill Road."], "3": ["Make a left U-turn at Devonshire Road."], "4": ["Make a left U-turn at Devonshire Road onto Jonestown Road/US 22."], "5": ["Make a left U-turn at Devonshire Road to stay on Jonestown Road/US 22."], "6": ["Make a right U-turn at Mannenbashi East."], "7": ["Make a left U-turn toward Baltimore."]}}, "uturn_verbal": {"phrases": {"0": "Haga un cambio de sentido a la <RELATIVE_DIRECTION>.", "1": "Haga un cambio de sentido a la <RELATIVE_DIRECTION> hacia <STREET_NAMES>.", "2": "Haga un cambio de sentido a la <RELATIVE_DIRECTION> para permanecer en <STREET_NAMES>.", "3": "Haga un cambio de sentido a la <RELATIVE_DIRECTION> en <CROSS_STREET_NAMES>.", "4": "Haga un cambio de sentido a la <RELATIVE_DIRECTION> en <CROSS_STREET_NAMES> hacia <STREET_NAMES>.", "5": "Haga un cambio de sentido a la <RELATIVE_DIRECTION> en <CROSS_STREET_NAMES> para permanecer en <STREET_NAMES>.", "6": "Haga un cambio de sentido a la <RELATIVE_DIRECTION> en <JUNCTION_NAME>.", "7": "Haga un cambio de sentido a la <RELATIVE_DIRECTION> en dirección a <TOWARD_SIGN>."}, "empty_street_name_labels": ["la calzada", "el carril bici", "el sendero para bicicletas de montaña", "the crosswalk", "the stairs", "the bridge", "the tunnel"], "relative_directions": ["izquierda", "derecha"], "example_phrases": {"0": ["Make a left U-turn."], "1": ["Make a right U-turn onto Bunker Hill Road."], "2": ["Make a left U-turn to stay on Bunker Hill Road."], "3": ["Make a left U-turn at Devonshire Road."], "4": ["Make a left U-turn at Devonshire Road onto Jonestown Road, U.S. 22."], "5": ["Make a left U-turn at Devonshire Road to stay on Jonestown Road, U.S. 22."], "6": ["Make a right U-turn at Mannenbashi East."], "7": ["Make a left U-turn toward Baltimore."]}}, "verbal_multi_cue": {"phrases": {"0": "<CURRENT_VERBAL_CUE> Después <NEXT_VERBAL_CUE>", "1": "<CURRENT_VERBAL_CUE> Despu<PERSON>, en <LENGTH>, <NEXT_VERBAL_CUE>"}, "metric_lengths": ["<KILOMETERS> kilómetros", "1 kilómetro", "<METERS> metros", "menos de 10 metros"], "us_customary_lengths": ["<MILES> millas", "1 milla", "media milla", "un cuarto de milla", "<FEET> pies", "menos de 10 pies"], "example_phrases": {"0": ["Bear right onto East Fayette Street. Then Turn right onto North Gay Street."], "1": ["Bear right onto East Fayette Street. Then, in 500 feet, Turn right onto North Gay Street."]}}, "approach_verbal_alert": {"phrases": {"0": "En <LENGTH>, <CURRENT_VERBAL_CUE>"}, "metric_lengths": ["<KILOMETERS> kilómetros", "1 kilómetro", "<METERS> metros", "menos de 10 metros"], "us_customary_lengths": ["<MILES> millas", "1 milla", "media milla", "un cuarto de milla", "<FEET> pies", "menos de 10 pies"], "example_phrases": {"0": ["In a quarter mile, Turn right onto North Gay Street."]}}, "pass": {"phrases": {"0": "Pass <OBJECT_LABEL>.", "1": "Pass traffic signals on <OBJECT_LABEL>."}, "object_labels": ["the gate", "the bollards", "ways intersection"], "example_phrases": {"0": ["Pass the gate."]}}, "elevator": {"phrases": {"0": "Tome el ascensor.", "1": "Tome el ascensor a <LEVEL>."}, "example_phrases": {"0": ["Take the elevator."], "1": ["Take the elevator to Level 1."]}}, "steps": {"phrases": {"0": "Tome las escaleras.", "1": "Tome las escaleras a <LEVEL>."}, "example_phrases": {"0": ["Take the stairs."], "1": ["Take the stairs to Level 2."]}}, "escalator": {"phrases": {"0": "Tome la escalera mecánica.", "1": "Tome la escalera mecánica a <LEVEL>."}, "example_phrases": {"0": ["Take the escalator."], "1": ["Take the escalator to Level 3."]}}, "enter_building": {"phrases": {"0": "Entre al edificio.", "1": "Entre al edificio, y continúe por <STREET_NAMES>."}, "empty_street_name_labels": ["la calzada", "el carril bici", "el sendero para bicicletas de montaña", "the crosswalk"], "example_phrases": {"0": ["Enter the building."], "1": ["Enter the building, and continue on the walkway."]}}, "exit_building": {"phrases": {"0": "Salga del edificio.", "1": "Salga del edificio, y continúe por <STREET_NAMES>."}, "empty_street_name_labels": ["la calzada", "el carril bici", "el sendero para bicicletas de montaña", "the crosswalk"], "example_phrases": {"0": ["Exit the building."], "1": ["Exit the building, and continue on the walkway."]}}}}
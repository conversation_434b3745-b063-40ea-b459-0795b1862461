{"posix_locale": "nl_NL.UTF-8", "aliases": ["nl"], "instructions": {"arrive": {"phrases": {"0": "Aankomst: <TIME>.", "1": "Aankomst: <TIME> bij <TRANSIT_STOP>."}, "example_phrases": {"0": ["Arrive: 8:02 AM."], "1": ["Arrive: 8:02 AM at 8 St - NYU."]}}, "arrive_verbal": {"phrases": {"0": "Aankomst om <TIME>.", "1": "Aankomst bij <TRANSIT_STOP> om <TIME>."}, "example_phrases": {"0": ["Arrive at 8:02 AM."], "1": ["Arrive at 8:02 AM at 8 St - NYU."]}}, "bear": {"phrases": {"0": "Sla <RELATIVE_DIRECTION>af.", "1": "Sla <RELATIVE_DIRECTION>af en ga <STREET_NAMES> op.", "2": "Sla <RELATIVE_DIRECTION>af en ga <BEGIN_STREET_NAMES> op. Ga verder op <STREET_NAMES>.", "3": "Sla <RELATIVE_DIRECTION>af om op <STREET_NAMES> te blijven.", "4": "Sla <RELATIVE_DIRECTION>af bij <JUNCTION_NAME>.", "5": "Sla <RELATIVE_DIRECTION>af naar <TOWARD_SIGN>."}, "empty_street_name_labels": ["het voetpad", "het fietspad", "het mountainbikepad", "the crosswalk", "the stairs", "the bridge", "the tunnel"], "relative_directions": ["links", "rechts"], "example_phrases": {"0": ["Bear right."], "1": ["<PERSON> left onto Arlen Road."], "2": ["Bear right onto Belair Road/US 1 Business. Continue on US 1 Business."], "3": ["<PERSON> left to stay on US 15 South."], "4": ["Bear right at Mannenbashi East."], "5": ["Bear left toward Baltimore."]}}, "bear_verbal": {"phrases": {"0": "<RELATIVE_DIRECTION> afslaan.", "1": "<RELATIVE_DIRECTION> afslaan en <STREET_NAMES> ingaan.", "2": "<RELATIVE_DIRECTION> afslaan en <BEGIN_STREET_NAMES> ingaan.", "3": "<RELATIVE_DIRECTION> afslaan om op <STREET_NAMES> te blijven.", "4": "<RELATIVE_DIRECTION> afslaan bij <JUNCTION_NAME>.", "5": "<RELATIVE_DIRECTION> afslaan richting <TOWARD_SIGN>."}, "empty_street_name_labels": ["het voetpad", "het fietspad", "het mountainbikepad", "the crosswalk", "the stairs", "the bridge", "the tunnel"], "relative_directions": ["Links", "<PERSON><PERSON><PERSON>"], "example_phrases": {"0": ["Bear right."], "1": ["<PERSON> left onto Arlen Road."], "2": ["Bear right onto Belair Road, U.S. 1 Business."], "3": ["<PERSON> left to stay on U.S. 15 South."], "4": ["Bear right at Mannenbashi East."], "5": ["Bear left toward Baltimore."]}}, "becomes": {"phrases": {"0": "<PREVIOUS_STREET_NAMES> gaat over in <STREET_NAMES>."}, "example_phrases": {"0": ["Vine Street becomes Middletown Road."]}}, "becomes_verbal": {"phrases": {"0": "<PREVIOUS_STREET_NAMES> gaat over in <STREET_NAMES>."}, "example_phrases": {"0": ["Vine Street becomes Middletown Road."]}}, "continue": {"phrases": {"0": "Ga verder.", "1": "Ga verder op <STREET_NAMES>.", "2": "Ga verder bij <JUNCTION_NAME>.", "3": "Ga verder richting <TOWARD_SIGN>."}, "empty_street_name_labels": ["het voetpad", "het fietspad", "het mountainbikepad", "the crosswalk", "the stairs", "the bridge", "the tunnel"], "example_phrases": {"0": ["Continue."], "1": ["Continue on 10th Avenue."], "2": ["Continue at Mannenbashi East."], "3": ["Continue toward Baltimore."]}}, "continue_verbal": {"phrases": {"0": "Doorgaan.", "1": "<LENGTH> doorgaan.", "2": "Doorgaan op <STREET_NAMES>.", "3": "<LENGTH> doorgaan op <STREET_NAMES>.", "4": "Doorgaan bij <JUNCTION_NAME>.", "5": "<LENGTH> doorgaan bij <JUNCTION_NAME>.", "6": "Doorgaan <TOWARD_SIGN>.", "7": "<LENGTH> doorgaan <TOWARD_SIGN>."}, "empty_street_name_labels": ["het voetpad", "het fietspad", "het mountainbikepad", "the crosswalk", "the stairs", "the bridge", "the tunnel"], "metric_lengths": ["<KILOMETERS> kilometer", "1 kilometer", "<METERS> meter", "minder dan 10 meter"], "us_customary_lengths": ["<MILES> mijl", "1 mijl", "een halve mijl", "een kwart mijl", "<FEET> voet", "minder dan 10 voet"], "example_phrases": {"0": ["Continue."], "1": ["Continue for 300 feet."], "2": ["Continue on 10th Avenue."], "3": ["Continue on 10th Avenue for 3 miles."], "4": ["Continue at Mannenbashi East."], "5": ["Continue at Mannenbashi East for 2 miles."], "6": ["Continue toward Baltimore."], "7": ["Continue toward Baltimore for 5 miles."]}}, "continue_verbal_alert": {"phrases": {"0": "Doorgaan.", "1": "Doorgaan op <STREET_NAMES>.", "2": "Doorgaan bij <JUNCTION_NAME>.", "3": "Doorgaan <TOWARD_SIGN>."}, "empty_street_name_labels": ["het voetpad", "het fietspad", "het mountainbikepad", "the crosswalk", "the stairs", "the bridge", "the tunnel"], "example_phrases": {"0": ["Continue."], "1": ["Continue on 10th Avenue."], "2": ["Continue at Mannenbashi East."], "3": ["Continue toward Baltimore."]}}, "depart": {"phrases": {"0": "Vertrek: <TIME>.", "1": "Vertrek: <TIME> vanaf <TRANSIT_STOP>."}, "example_phrases": {"0": ["Depart: 8:02 AM."], "1": ["Depart: 8:02 AM from 8 St - NYU."]}}, "depart_verbal": {"phrases": {"0": "Vertrekken om <TIME>.", "1": "Vertrekken om <TIME> vanaf <TRANSIT_STOP>."}, "example_phrases": {"0": ["Depart at 8:02 AM."], "1": ["Depart at 8:02 AM from 8 St - NYU."]}}, "destination": {"phrases": {"0": "Je bent op je bestemming aangekomen.", "1": "Je bent aangekomen op <DESTINATION>.", "2": "Je bestemming bevindt zich <RELATIVE_DIRECTION>.", "3": "<DESTINATION> bevindt zich <RELATIVE_DIRECTION>."}, "relative_directions": ["links", "rechts"], "example_phrases": {"0": ["You have arrived at your destination."], "1": ["You have arrived at 3206 Powelton Avenue."], "2": ["Your destination is on the left.", "Your destination is on the right."], "3": ["Lancaster Brewing Company is on the left."]}}, "destination_verbal": {"phrases": {"0": "Aangekomen op je bestemming.", "1": "Aangekomen op <DESTINATION>.", "2": "Je bestemming bevindt zich <RELATIVE_DIRECTION>.", "3": "<DESTINATION> bevindt zich <RELATIVE_DIRECTION>."}, "relative_directions": ["links", "rechts"], "example_phrases": {"0": ["You have arrived at your destination."], "1": ["You have arrived at 32 o6 Powelton Avenue."], "2": ["Your destination is on the left.", "Your destination is on the right."], "3": ["Lancaster Brewing Company is on the left."]}}, "destination_verbal_alert": {"phrases": {"0": "Je zult op je bestemming aan<PERSON>.", "1": "Je zult aankomen op <DESTINATION>.", "2": "Je bestemming bevindt zich <RELATIVE_DIRECTION>.", "3": "<DESTINATION> bevindt zich <RELATIVE_DIRECTION>."}, "relative_directions": ["links", "rechts"], "example_phrases": {"0": ["You will arrive at your destination."], "1": ["You will arrive at 32 o6 Powelton Avenue."], "2": ["Your destination will be on the left.", "Your destination will be on the right."], "3": ["Lancaster Brewing Company will be on the left."]}}, "enter_ferry": {"phrases": {"0": "<PERSON><PERSON><PERSON>.", "1": "<PERSON><PERSON><PERSON> de <STREET_NAMES>.", "2": "N<PERSON><PERSON> de <STREET_NAMES> <FERRY_LABEL>.", "3": "<PERSON><PERSON><PERSON> de veerboot richting <TOWARD_SIGN>."}, "empty_street_name_labels": ["het voetpad", "het fietspad", "het mountainbikepad", "the crosswalk", "the stairs", "the bridge", "the tunnel"], "ferry_label": "Veer<PERSON><PERSON>", "example_phrases": {"0": ["Take the Ferry."], "1": ["Take the Millersburg Ferry."], "2": ["Take the Bridgeport - Port Jefferson Ferry."], "3": ["Take the ferry toward Cape May."]}}, "enter_ferry_verbal": {"phrases": {"0": "<PERSON><PERSON><PERSON><PERSON> nemen.", "1": "<STREET_NAMES> nemen.", "2": "<STREET_NAMES> <FERRY_LABEL> nemen.", "3": "Veerboot nemen richting <TOWARD_SIGN>."}, "empty_street_name_labels": ["het voetpad", "het fietspad", "het mountainbikepad", "the crosswalk", "the stairs", "the bridge", "the tunnel"], "ferry_label": "Veer<PERSON><PERSON>", "example_phrases": {"0": ["Take the Ferry."], "1": ["Take the Millersburg Ferry."], "2": ["Take the Bridgeport - Port Jefferson Ferry."], "3": ["Take the ferry toward Cape May."]}}, "enter_roundabout": {"phrases": {"0": "Ga de rotonde op.", "1": "Ga de rotonde op en neem de <ORDINAL_VALUE> afslag.", "2": "Ga de rotonde op en neem de <ORDINAL_VALUE> afslag naar <ROUNDABOUT_EXIT_STREET_NAMES>.", "3": "Ga de rotonde op en neem de <ORDINAL_VALUE> afslag naar <ROUNDABOUT_EXIT_BEGIN_STREET_NAMES>. Ga verder op <ROUNDABOUT_EXIT_STREET_NAMES>.", "4": "Ga de rotonde op en neem de <ORDINAL_VALUE> afslag richting <TOWARD_SIGN>.", "5": "Ga de rotonde op en neem de afslag naar <ROUNDABOUT_EXIT_STREET_NAMES>.", "6": "Ga de rotonde op en neem de afslag naar <ROUNDABOUT_EXIT_BEGIN_STREET_NAMES>. Ga verder op <ROUNDABOUT_EXIT_STREET_NAMES>.", "7": "Ga de rotonde op en neem de afslag richting <TOWARD_SIGN>.", "8": "Ga <STREET_NAMES> op", "9": "Ga <STREET_NAMES> op en neem de <ORDINAL_VALUE> afslag.", "10": "Ga <STREET_NAMES> op en neem de <ORDINAL_VALUE> afslag naar <ROUNDABOUT_EXIT_STREET_NAMES>.", "11": "Ga <STREET_NAMES> op en neem de <ORDINAL_VALUE> afslag naar <ROUNDABOUT_EXIT_BEGIN_STREET_NAMES>. Ga verder op <ROUNDABOUT_EXIT_STREET_NAMES>.", "12": "Ga <STREET_NAMES> op en neem de <ORDINAL_VALUE> afslag richting <TOWARD_SIGN>.", "13": "Ga <STREET_NAMES> op en neem de afslag naar <ROUNDABOUT_EXIT_STREET_NAMES>.", "14": "Ga <STREET_NAMES> op en neem de afslag naar <ROUNDABOUT_EXIT_BEGIN_STREET_NAMES>. Ga verder op <ROUNDABOUT_EXIT_STREET_NAMES>.", "15": "Ga <STREET_NAMES> op en neem de afslag richting <TOWARD_SIGN>."}, "ordinal_values": ["1e", "2e", "3e", "4e", "5e", "6e", "7e", "8e", "9e", "10e"], "empty_street_name_labels": ["het voetpad", "het fietspad", "het mountainbikepad", "the crosswalk", "the stairs", "the bridge", "the tunnel"], "example_phrases": {"0": ["Enter the roundabout."], "1": ["Enter the roundabout and take the 1st exit.", "Enter the roundabout and take the 2nd exit.", "Enter the roundabout and take the 3rd exit.", "Enter the roundabout and take the 4th exit.", "Enter the roundabout and take the 5th exit.", "Enter the roundabout and take the 6th exit.", "Enter the roundabout and take the 7th exit.", "Enter the roundabout and take the 8th exit.", "Enter the roundabout and take the 9th exit.", "Enter the roundabout and take the 10th exit."], "2": ["Enter the roundabout and take the 3rd exit onto Main Street."], "3": ["Enter the roundabout and take the 3rd exit onto US 322/Main Street. Continue on US 322."], "4": ["Enter the roundabout and take the 3rd exit toward Baltimore."], "5": ["Enter the roundabout and take the exit onto Main Street."], "6": ["Enter the roundabout and take the exit onto US 322/Main Street. Continue on US 322."], "7": ["Enter the roundabout and take the exit toward Baltimore."], "8": ["Enter Dupont Circle."], "9": ["Enter Dupont Circle and take the 1st exit."], "10": ["Enter Dupont Circle and take the 3rd exit onto Main Street."], "11": ["Enter Dupont Circle and take the 3rd exit onto US 322/Main Street. Continue on US 322."], "12": ["Enter Dupont Circle and take the 3rd exit toward Baltimore."], "13": ["Enter Dupont Circle and take the exit onto Main Street."], "14": ["Enter Dupont Circle and take the exit onto US 322/Main Street. Continue on US 322."], "15": ["Enter Dupont Circle and take the exit toward Baltimore."]}}, "enter_roundabout_verbal": {"phrases": {"0": "Rotonde <PERSON>.", "1": "Rotonde opgaan en de <ORDINAL_VALUE> afslag nemen.", "2": "Rotonde opgaan en de <ORDINAL_VALUE> afslag nemen naar <ROUNDABOUT_EXIT_STREET_NAMES>.", "3": "Rotonde opgaan en de <ORDINAL_VALUE> afslag nemen naar <ROUNDABOUT_EXIT_BEGIN_STREET_NAMES>.", "4": "Rotonde opgaan en de <ORDINAL_VALUE> afslag nemen richting <TOWARD_SIGN>.", "5": "Rotonde opgaan en de afslag nemen naar <ROUNDABOUT_EXIT_STREET_NAMES>.", "6": "Rotonde opgaan en de afslag nemen naar <ROUNDABOUT_EXIT_BEGIN_STREET_NAMES>.", "7": "Rotonde opgaan en de afslag nemen richting <TOWARD_SIGN>.", "8": "<STREET_NAMES> opgaan", "9": "<STREET_NAMES> opgaan en de <ORDINAL_VALUE> afslag nemen.", "10": "<STREET_NAMES> opgaan en de <ORDINAL_VALUE> afslag nemen naar <ROUNDABOUT_EXIT_STREET_NAMES>.", "11": "<STREET_NAMES> opgaan en de <ORDINAL_VALUE> afslag nemen naar <ROUNDABOUT_EXIT_BEGIN_STREET_NAMES>.", "12": "<STREET_NAMES> opgaan en de <ORDINAL_VALUE> afslag nemen richting <TOWARD_SIGN>.", "13": "<STREET_NAMES> opgaan en de afslag nemen naar <ROUNDABOUT_EXIT_STREET_NAMES>.", "14": "<STREET_NAMES> opgaan en de afslag nemen naar <ROUNDABOUT_EXIT_BEGIN_STREET_NAMES>.", "15": "<STREET_NAMES> opgaan en de afslag nemen richting <TOWARD_SIGN>."}, "ordinal_values": ["1e", "2e", "3e", "4e", "5e", "6e", "7e", "8e", "9e", "10e"], "empty_street_name_labels": ["het voetpad", "het fietspad", "het mountainbikepad", "the crosswalk", "the stairs", "the bridge", "the tunnel"], "example_phrases": {"0": ["Enter the roundabout."], "1": ["Enter the roundabout and take the 1st exit.", "Enter the roundabout and take the 2nd exit.", "Enter the roundabout and take the 3rd exit.", "Enter the roundabout and take the 4th exit.", "Enter the roundabout and take the 5th exit.", "Enter the roundabout and take the 6th exit.", "Enter the roundabout and take the 7th exit.", "Enter the roundabout and take the 8th exit.", "Enter the roundabout and take the 9th exit.", "Enter the roundabout and take the 10th exit."], "2": ["Enter the roundabout and take the 3rd exit onto Main Street."], "3": ["Enter the roundabout and take the 3rd exit onto U.S. 3 22/Main Street."], "4": ["Enter the roundabout and take the 3rd exit toward Baltimore."], "5": ["Enter the roundabout and take the exit onto Main Street."], "6": ["Enter the roundabout and take the exit onto U.S. 3 22/Main Street."], "7": ["Enter the roundabout and take the exit toward Baltimore."], "8": ["Enter Dupont Circle."], "9": ["Enter Dupont Circle and take the 1st exit."], "10": ["Enter Dupont Circle and take the 3rd exit onto Main Street."], "11": ["Enter Dupont Circle and take the 3rd exit onto U.S. 3 22/Main Street."], "12": ["Enter Dupont Circle and take the 3rd exit toward Baltimore."], "13": ["Enter Dupont Circle and take the exit onto Main Street."], "14": ["Enter Dupont Circle and take the exit onto U.S. 3 22/Main Street."], "15": ["Enter Dupont Circle and take the exit toward Baltimore."]}}, "exit": {"phrases": {"0": "Neem <RELATIVE_DIRECTION> de afslag.", "1": "Neem <RELATIVE_DIRECTION> afslag <NUMBER_SIGN>.", "2": "Neem <RELATIVE_DIRECTION> de afslag <BRANCH_SIGN>.", "3": "Neem <RELATIVE_DIRECTION> afslag <NUMBER_SIGN> naar <BRANCH_SIGN>.", "4": "Neem <RELATIVE_DIRECTION> de afslag richting <TOWARD_SIGN>.", "5": "Neem <RELATIVE_DIRECTION> afslag <NUMBER_SIGN> richting <TOWARD_SIGN>.", "6": "Neem <RELATIVE_DIRECTION> de afslag <BRANCH_SIGN> richting <TOWARD_SIGN>.", "7": "Neem <RELATIVE_DIRECTION> afslag <NUMBER_SIGN> naar <BRANCH_SIGN> richting <TOWARD_SIGN>.", "8": "Neem <RELATIVE_DIRECTION> de afslag <NAME_SIGN>.", "10": "Neem <RELATIVE_DIRECTION> de afslag <NAME_SIGN> naar <BRANCH_SIGN>.", "12": "Neem <RELATIVE_DIRECTION> de afslag <NAME_SIGN> richting <TOWARD_SIGN>.", "14": "Neem <RELATIVE_DIRECTION> de afslag <NAME_SIGN> naar <BRANCH_SIGN> richting <TOWARD_SIGN>.", "15": "<PERSON><PERSON><PERSON>.", "16": "<PERSON><PERSON><PERSON> a<PERSON> <NUMBER_SIGN>.", "17": "<PERSON><PERSON><PERSON> <BRANCH_SIGN>.", "18": "<PERSON><PERSON><PERSON> <NUMBER_SIGN> naar <BRANCH_SIGN>.", "19": "<PERSON><PERSON><PERSON> de <PERSON> richting <TOWARD_SIGN>.", "20": "<PERSON><PERSON><PERSON> a<PERSON> <NUMBER_SIGN> richting <TOWARD_SIGN>.", "21": "<PERSON><PERSON><PERSON> <BRANCH_SIGN> richting <TOWARD_SIGN>.", "22": "<PERSON><PERSON><PERSON> a<PERSON> <NUMBER_SIGN> naar <BRANCH_SIGN> richting <TOWARD_SIGN>.", "23": "<PERSON><PERSON><PERSON> a<PERSON> <NAME_SIGN>.", "25": "<PERSON><PERSON><PERSON> <NAME_SIGN> naar <BRANCH_SIGN>.", "27": "<PERSON><PERSON><PERSON> a<PERSON> <NAME_SIGN> richting <TOWARD_SIGN>.", "29": "<PERSON><PERSON><PERSON> a<PERSON> <NAME_SIGN> naar <BRANCH_SIGN> richting <TOWARD_SIGN>."}, "relative_directions": ["links", "rechts"], "example_phrases": {"0": ["Take the exit on the left.", "Take the exit on the right."], "1": ["Take exit 67 B-A on the right."], "2": ["Take the US 322 West exit on the right."], "3": ["Take exit 67 B-A on the right onto US 322 West."], "4": ["Take the exit on the right toward Lewistown."], "5": ["Take exit 67 B-A on the right toward Lewistown."], "6": ["Take the US 322 West exit on the right toward Lewistown."], "7": ["Take exit 67 B-A on the right onto US 322 West toward Lewistown/State College."], "8": ["Take the White Marsh Boulevard exit on the left."], "10": ["Take the White Marsh Boulevard exit on the left onto MD 43 East."], "12": ["Take the White Marsh Boulevard exit on the left toward White Marsh."], "14": ["Take the White Marsh Boulevard exit on the left onto MD 43 East toward White Marsh."], "15": ["Take the exit."], "16": ["Take exit 67 B-A."], "17": ["Take the US 322 West exit."], "18": ["Take exit 67 B-A onto US 322 West."], "19": ["Take the exit toward Lewistown."], "20": ["Take exit 67 B-A toward Lewistown."], "21": ["Take the US 322 West exit toward Lewistown."], "22": ["Take exit 67 B-A onto US 322 West toward Lewistown/State College."], "23": ["Take the White Marsh Boulevard exit."], "25": ["Take the White Marsh Boulevard exit onto MD 43 East."], "27": ["Take the White Marsh Boulevard exit toward White Marsh."], "29": ["Take the White Marsh Boulevard exit onto MD 43 East toward White Marsh."]}}, "exit_roundabout": {"phrases": {"0": "Verlaat de rotonde.", "1": "Verlaat de rotonde naar <STREET_NAMES>.", "2": "Verlaat de rotonde naar <BEGIN_STREET_NAMES>. Ga verder op <STREET_NAMES>.", "3": "Verlaat de rotonde richting <TOWARD_SIGN>."}, "empty_street_name_labels": ["het voetpad", "het fietspad", "het mountainbikepad", "the crosswalk", "the stairs", "the bridge", "the tunnel"], "example_phrases": {"0": ["Exit the roundabout."], "1": ["Exit the roundabout onto Philadelphia Road/MD 7."], "2": ["Exit the roundabout onto Catoctin Mountain Highway/US 15. Continue on US 15."], "3": ["Exit the roundabout toward Baltimore."]}}, "exit_roundabout_verbal": {"phrases": {"0": "Rotonde verlaten.", "1": "Rotonde verlaten naar <STREET_NAMES>.", "2": "Rotonde verlaten naar <BEGIN_STREET_NAMES>.", "3": "Rotonde verlaten richting <TOWARD_SIGN>."}, "empty_street_name_labels": ["het voetpad", "het fietspad", "het mountainbikepad", "the crosswalk", "the stairs", "the bridge", "the tunnel"], "example_phrases": {"0": ["Exit the roundabout."], "1": ["Exit the roundabout onto Philadelphia Road, Maryland 7."], "2": ["Exit the roundabout onto Catoctin Mountain Highway, U.S. 15."], "3": ["Exit the roundabout toward Baltimore."]}}, "exit_verbal": {"phrases": {"0": "<RELATIVE_DIRECTION> de afslag nemen.", "1": "<RELATIVE_DIRECTION> afslag <NUMBER_SIGN> nemen.", "2": "<RELATIVE_DIRECTION> afslag <BRANCH_SIGN> nemen.", "3": "<RELATIVE_DIRECTION> afslag <NUMBER_SIGN> nemen naar <BRANCH_SIGN>.", "4": "<RELATIVE_DIRECTION> de afslag nemen richting <TOWARD_SIGN>.", "5": "<RELATIVE_DIRECTION> afslag <NUMBER_SIGN> nemen richting <TOWARD_SIGN>.", "6": "<RELATIVE_DIRECTION> afslag <BRANCH_SIGN> nemen richting <TOWARD_SIGN>.", "7": "<RELATIVE_DIRECTION> afslag <NUMBER_SIGN> nemen naar <BRANCH_SIGN> richting <TOWARD_SIGN>.", "8": "<RELATIVE_DIRECTION> afslag <NAME_SIGN> nemen.", "10": "<RELATIVE_DIRECTION> afslag <NAME_SIGN> nemen naar <BRANCH_SIGN>.", "12": "<RELATIVE_DIRECTION> afslag <NAME_SIGN> nemen richting <TOWARD_SIGN>.", "14": "<RELATIVE_DIRECTION> afslag <NAME_SIGN> nemen naar <BRANCH_SIGN> richting <TOWARD_SIGN>.", "15": "Afslag nemen.", "16": "Afslag <NUMBER_SIGN> nemen.", "17": "Afslag <BRANCH_SIGN> nemen.", "18": "Afslag <NUMBER_SIGN> nemen naar <BRANCH_SIGN>.", "19": "De afslag nemen richting <TOWARD_SIGN>.", "20": "Afslag <NUMBER_SIGN> nemen richting <TOWARD_SIGN>.", "21": "Afslag <BRANCH_SIGN> nemen richting <TOWARD_SIGN>.", "22": "Afslag <NUMBER_SIGN> nemen naar <BRANCH_SIGN> richting <TOWARD_SIGN>.", "23": "Afslag <NAME_SIGN> nemen.", "25": "Afslag <NAME_SIGN> nemen naar <BRANCH_SIGN>.", "27": "Afslag <NAME_SIGN> nemen richting <TOWARD_SIGN>.", "29": "A<PERSON>lag <NAME_SIGN> nemen naar <BRANCH_SIGN> richting <TOWARD_SIGN>."}, "relative_directions": ["Links", "<PERSON><PERSON><PERSON>"], "example_phrases": {"0": ["Take the exit on the left.", "Take the exit on the right."], "1": ["Take exit 67 B-A on the right."], "2": ["Take the U.S. 3 22 West exit on the right."], "3": ["Take exit 67 B-A on the right onto U.S. 3 22 West."], "4": ["Take the exit on the right toward Lewistown."], "5": ["Take exit 67 B-A on the right toward Lewistown."], "6": ["Take the U.S. 3 22 West exit on the right toward Lewistown."], "7": ["Take exit 67 B-A on the right onto U.S. 3 22 West toward Lewistown, State College."], "8": ["Take the White Marsh Boulevard exit on the left."], "10": ["Take the White Marsh Boulevard exit on the left onto Maryland 43 East."], "12": ["Take the White Marsh Boulevard exit on the left toward White Marsh."], "14": ["Take the White Marsh Boulevard exit on the left onto Maryland 43 East toward White Marsh."], "15": ["Take the exit."], "16": ["Take exit 67 B-A."], "17": ["Take the US 322 West exit."], "18": ["Take exit 67 B-A onto US 322 West."], "19": ["Take the exit toward Lewistown."], "20": ["Take exit 67 B-A toward Lewistown."], "21": ["Take the US 322 West exit toward Lewistown."], "22": ["Take exit 67 B-A onto US 322 West toward Lewistown/State College."], "23": ["Take the White Marsh Boulevard exit."], "25": ["Take the White Marsh Boulevard exit onto MD 43 East."], "27": ["Take the White Marsh Boulevard exit toward White Marsh."], "29": ["Take the White Marsh Boulevard exit onto MD 43 East toward White Marsh."]}}, "exit_visual": {"phrases": {"0": "Afslag <EXIT_NUMBERS>"}, "example_phrases": {"0": ["Exit 1A"]}}, "keep": {"phrases": {"0": "Houd bij de splitsing <RELATIVE_DIRECTION> aan.", "1": "Houd <RELATIVE_DIRECTION> aan om afslag <NUMBER_SIGN> te nemen.", "2": "Houd <RELATIVE_DIRECTION> aan om <STREET_NAMES> op te gaan.", "3": "Houd <RELATIVE_DIRECTION> aan om afslag <NUMBER_SIGN> te nemen naar <STREET_NAMES>.", "4": "Houd <RELATIVE_DIRECTION> aan richting <TOWARD_SIGN>.", "5": "Houd <RELATIVE_DIRECTION> aan om afslag <NUMBER_SIGN> te nemen richting <TOWARD_SIGN>.", "6": "Houd <RELATIVE_DIRECTION> aan om <STREET_NAMES> op te gaan richting <TOWARD_SIGN>.", "7": "Houd <RELATIVE_DIRECTION> aan om afslag <NUMBER_SIGN> te nemen naar <STREET_NAMES> richting <TOWARD_SIGN>."}, "empty_street_name_labels": ["het voetpad", "het fietspad", "het mountainbikepad", "the crosswalk", "the stairs", "the bridge", "the tunnel"], "relative_directions": ["links", "recht<PERSON>or", "rechts"], "example_phrases": {"0": ["Keep left at the fork.", "Keep straight at the fork.", "Keep right at the fork."], "1": ["Keep right to take exit 62."], "2": ["Keep right to take I 895 South."], "3": ["Keep right to take exit 62 onto I 895 South."], "4": ["Keep right toward Annapolis."], "5": ["Keep right to take exit 62 toward Annapolis."], "6": ["Keep right to take I 895 South toward Annapolis."], "7": ["Keep right to take exit 62 onto I 895 South toward Annapolis."]}}, "keep_to_stay_on": {"phrases": {"0": "Houd <RELATIVE_DIRECTION> aan om op <STREET_NAMES> te blijven.", "1": "Houd <RELATIVE_DIRECTION> aan om afslag <NUMBER_SIGN> te nemen om op <STREET_NAMES> te blijven.", "2": "Houd <RELATIVE_DIRECTION> aan om op <STREET_NAMES> te blijven richting <TOWARD_SIGN>.", "3": "Houd <RELATIVE_DIRECTION> aan om afslag <NUMBER_SIGN> te nemen om op <STREET_NAMES> te blijven richting <TOWARD_SIGN>."}, "empty_street_name_labels": ["het voetpad", "het fietspad", "het mountainbikepad", "the crosswalk", "the stairs", "the bridge", "the tunnel"], "relative_directions": ["links", "recht<PERSON>or", "rechts"], "example_phrases": {"0": ["Keep left to stay on I 95 South.", "Keep straight to stay on I 95 South.", "Keep right to stay on I 95 South."], "1": ["Keep left to take exit 62 to stay on I 95 South."], "2": ["Keep left to stay on I 95 South toward Baltimore."], "3": ["Keep left to take exit 62 to stay on I 95 South toward Baltimore."]}}, "keep_to_stay_on_verbal": {"phrases": {"0": "<RELATIVE_DIRECTION> aanhouden om op <STREET_NAMES> te blijven.", "1": "<RELATIVE_DIRECTION> aanhouden om afslag <NUMBER_SIGN> te nemen om op <STREET_NAMES> te blijven.", "2": "<RELATIVE_DIRECTION> aanhouden om op <STREET_NAMES> te blijven richting <TOWARD_SIGN>.", "3": "<RELATIVE_DIRECTION> aanhouden om afslag <NUMBER_SIGN> te nemen om op <STREET_NAMES> te blijven richting <TOWARD_SIGN>."}, "empty_street_name_labels": ["het voetpad", "het fietspad", "het mountainbikepad", "the crosswalk", "the stairs", "the bridge", "the tunnel"], "relative_directions": ["Links", "Rechtdoor", "<PERSON><PERSON><PERSON>"], "example_phrases": {"0": ["Keep left to stay on Interstate 95 South.", "Keep straight to stay on Interstate 95 South.", "Keep right to stay on Interstate 95 South."], "1": ["Keep left to take exit 62 to stay on Interstate 95 South."], "2": ["Keep left to stay on I 95 South toward Baltimore."], "3": ["Keep left to take exit 62 to stay on Interstate 95 South toward Baltimore."]}}, "keep_verbal": {"phrases": {"0": "<RELATIVE_DIRECTION> aanhouden bij de splitsing.", "1": "<RELATIVE_DIRECTION> aanhouden om afslag <NUMBER_SIGN> te nemen.", "2": "<RELATIVE_DIRECTION> aanhouden om <STREET_NAMES> op te gaan.", "3": "<RELATIVE_DIRECTION> aanhouden om afslag <NUMBER_SIGN> te nemen naar <STREET_NAMES>.", "4": "<RELATIVE_DIRECTION> a<PERSON><PERSON><PERSON> richting <TOWARD_SIGN>.", "5": "<RELATIVE_DIRECTION> aanhouden om afslag <NUMBER_SIGN> te nemen richting <TOWARD_SIGN>.", "6": "<RELATIVE_DIRECTION> aan<PERSON>den om <STREET_NAMES> op te gaan richting <TOWARD_SIGN>.", "7": "<RELATIVE_DIRECTION> aanhouden om afslag <NUMBER_SIGN> te nemen naar <STREET_NAMES> richting <TOWARD_SIGN>."}, "empty_street_name_labels": ["het voetpad", "het fietspad", "het mountainbikepad", "the crosswalk", "the stairs", "the bridge", "the tunnel"], "relative_directions": ["Links", "Rechtdoor", "<PERSON><PERSON><PERSON>"], "example_phrases": {"0": ["Keep left at the fork.", "Keep straight at the fork.", "Keep right at the fork."], "1": ["Keep right to take exit 62."], "2": ["Keep right to take Interstate 8 95 South."], "3": ["Keep right to take exit 62 onto Interstate 8 95 South."], "4": ["Keep right toward Annapolis."], "5": ["Keep right to take exit 62 toward Annapolis."], "6": ["Keep right to take Interstate 8 95 South toward Annapolis."], "7": ["Keep right to take exit 62 onto Interstate 8 95 South toward Annapolis."]}}, "merge": {"phrases": {"0": "<PERSON>oeg in.", "1": "Voeg <RELATIVE_DIRECTION> in.", "2": "Voeg in op <STREET_NAMES>.", "3": "Voeg <RELATIVE_DIRECTION> in op <STREET_NAMES>.", "4": "Voeg in richting <TOWARD_SIGN>.", "5": "Voeg <RELATIVE_DIRECTION> in richting <TOWARD_SIGN>."}, "relative_directions": ["links", "rechts"], "empty_street_name_labels": ["het voetpad", "het fietspad", "het mountainbikepad", "the crosswalk", "the stairs", "the bridge", "the tunnel"], "example_phrases": {"0": ["Merge."], "1": ["<PERSON><PERSON> left."], "2": ["Merge onto I 76 West/Pennsylvania Turnpike."], "3": ["Merge right onto I 83 South."], "4": ["Merge toward Baltimore."], "5": ["Merge right toward Baltimore."]}}, "merge_verbal": {"phrases": {"0": "Invoegen.", "1": "<RELATIVE_DIRECTION> invoegen.", "2": "Invoegen op <STREET_NAMES>.", "3": "<RELATIVE_DIRECTION> invoegen op <STREET_NAMES>.", "4": "Invoegen richting <TOWARD_SIGN>.", "5": "<RELATIVE_DIRECTION> invoegen richting <TOWARD_SIGN>."}, "relative_directions": ["Links", "<PERSON><PERSON><PERSON>"], "empty_street_name_labels": ["het voetpad", "het fietspad", "het mountainbikepad", "the crosswalk", "the stairs", "the bridge", "the tunnel"], "example_phrases": {"0": ["Merge."], "1": ["<PERSON><PERSON> left."], "2": ["Merge onto I 76 West/Pennsylvania Turnpike."], "3": ["Merge right onto I 83 South."], "4": ["Merge toward Baltimore."], "5": ["Merge right toward Baltimore."]}}, "post_transition_transit_verbal": {"phrases": {"0": "<PERSON><PERSON> <TRANSIT_STOP_COUNT> <TRANSIT_STOP_COUNT_LABEL>."}, "transit_stop_count_labels": {"one": "halte", "other": "haltes"}, "example_phrases": {"0": ["Travel 1 stop.", "Travel 3 stops."]}}, "post_transition_verbal": {"phrases": {"0": "<LENGTH> doorgaan.", "1": "<LENGTH> doorgaan op <STREET_NAMES>."}, "empty_street_name_labels": ["het voetpad", "het fietspad", "het mountainbikepad", "the crosswalk", "the stairs", "the bridge", "the tunnel"], "metric_lengths": ["<KILOMETERS> kilometer", "1 kilometer", "<METERS> meter", "minder dan 10 meter"], "us_customary_lengths": ["<MILES> mijl", "1 mijl", "een halve mijl", "een kwart mijl", "<FEET> voet", "minder dan 10 voet"], "example_phrases": {"0": ["Continue for 300 feet.", "Continue for 9 miles."], "1": ["Continue on Pennsylvania 7 43 for 6.2 miles.", "Continue on Main Street, Vermont 30 for 1 tenth of a mile."]}}, "ramp": {"phrases": {"0": "Neem <RELATIVE_DIRECTION> de op-/afrit.", "1": "Neem <RELATIVE_DIRECTION> de op-/afrit <BRANCH_SIGN>.", "2": "Neem <RELATIVE_DIRECTION> de op-/afrit richting <TOWARD_SIGN>.", "3": "Neem <RELATIVE_DIRECTION> de op-/afrit <BRANCH_SIGN> richting <TOWARD_SIGN>.", "4": "Neem <RELATIVE_DIRECTION> de op-/afrit <NAME_SIGN>.", "5": "Sla <RELATIVE_DIRECTION>af om de op-/afrit te nemen.", "6": "Sla <RELATIVE_DIRECTION>af om de op-/afrit <BRANCH_SIGN> te nemen.", "7": "Sla <RELATIVE_DIRECTION>af om de op-/afrit te nemen richting <TOWARD_SIGN>.", "8": "Sla <RELATIVE_DIRECTION>af om de op-/afrit <BRANCH_SIGN> te nemen richting <TOWARD_SIGN>.", "9": "Sla <RELATIVE_DIRECTION>af om de op-/afrit <NAME_SIGN> te nemen.", "10": "Op-/afrit nemen.", "11": "Neem de op-/afrit <BRANCH_SIGN>.", "12": "Neem de op-/afrit richting <TOWARD_SIGN>.", "13": "Neem de op-/afrit <BRANCH_SIGN> richting <TOWARD_SIGN>.", "14": "Neem de op-/afrit <NAME_SIGN>."}, "relative_directions": ["links", "rechts"], "example_phrases": {"0": ["Take the ramp on the left.", "Take the ramp on the right."], "1": ["Take the I 95 ramp on the right."], "2": ["Take the ramp on the left toward JFK."], "3": ["Take the South Conduit Avenue ramp on the left toward JFK."], "4": ["Take the Gettysburg Pike ramp on the right."], "5": ["Turn left to take the ramp.", "Turn right to take the ramp."], "6": ["Turn left to take the PA 283 West ramp."], "7": ["Turn left to take the ramp toward Harrisburg/Harrisburg International Airport."], "8": ["Turn left to take the PA 283 West ramp toward Harrisburg/Harrisburg International Airport."], "9": ["Turn right to take the Gettysburg Pike ramp."], "10": ["Take the ramp."], "11": ["Take the I 95 ramp."], "12": ["Take the ramp toward JFK."], "13": ["Take the Soutch Conduit Avenue ramp toward JFK."], "14": ["Take the Gettysburg ramp."]}}, "ramp_straight": {"phrases": {"0": "<PERSON><PERSON><PERSON><PERSON> recht<PERSON>or gaan om de op-/afrit te nemen.", "1": "<PERSON><PERSON><PERSON><PERSON> rechtdoor gaan om de op-/afrit <BRANCH_SIGN> te nemen.", "2": "<PERSON><PERSON><PERSON><PERSON> rechtdoor gaan om de op-/afrit te nemen richting <TOWARD_SIGN>.", "3": "<PERSON><PERSON><PERSON><PERSON> rechtdoor gaan om de op-/afrit <BRANCH_SIGN> te nemen richting <TOWARD_SIGN>.", "4": "<PERSON><PERSON><PERSON><PERSON> rechtdoor gaan om de op-/afrit <NAME_SIGN> te nemen."}, "example_phrases": {"0": ["Stay straight to take the ramp."], "1": ["Stay straight to take the US 322 East ramp."], "2": ["Stay straight to take the ramp toward Hershey."], "3": ["Stay straight to take the US 322 East/US 422 East/US 522 East/US 622 East ramp toward Hershey/Palmdale/Palmyra/Campbelltown."], "4": ["Stay straight to take the Gettysburg Pike ramp."]}}, "ramp_straight_verbal": {"phrases": {"0": "<PERSON><PERSON><PERSON><PERSON> blijven gaan om de op-/afrit te nemen.", "1": "<PERSON><PERSON><PERSON><PERSON> blijven gaan om de op-/afrit <BRANCH_SIGN> te nemen.", "2": "<PERSON><PERSON><PERSON><PERSON> blijven gaan om de op-/afrit te nemen richting <TOWARD_SIGN>.", "3": "<PERSON><PERSON><PERSON><PERSON> blijven gaan om de op-/afrit <BRANCH_SIGN> te nemen richting <TOWARD_SIGN>.", "4": "<PERSON><PERSON><PERSON><PERSON> blijven gaan om de op-/afrit <NAME_SIGN> te nemen."}, "example_phrases": {"0": ["Stay straight to take the ramp."], "1": ["Stay straight to take the U.S. 3 22 East ramp."], "2": ["Stay straight to take the ramp toward Hershey."], "3": ["Stay straight to take the U.S. 3 22 East, U.S. 4 22 East ramp toward Hershey, Palmdale."], "4": ["Stay straight to take the Gettysburg Pike ramp."]}}, "ramp_verbal": {"phrases": {"0": "<RELATIVE_DIRECTION> de op-/afrit nemen.", "1": "<RELATIVE_DIRECTION> de op-/afrit <BRANCH_SIGN> nemen.", "2": "<RELATIVE_DIRECTION> de op-/afrit nemen richting <TOWARD_SIGN>.", "3": "<RELATIVE_DIRECTION> de op-/afrit <BRANCH_SIGN> nemen richting <TOWARD_SIGN>.", "4": "<RELATIVE_DIRECTION> de op-/afrit <NAME_SIGN> nemen.", "5": "<RELATIVE_DIRECTION> afslaan om de op-/afrit te nemen.", "6": "<RELATIVE_DIRECTION> afslaan om de op-/afrit <BRANCH_SIGN> te nemen.", "7": "<RELATIVE_DIRECTION> afslaan om de op-/afrit te nemen richting <TOWARD_SIGN>.", "8": "<RELATIVE_DIRECTION> afslaan om de op-/afrit <BRANCH_SIGN> te nemen richting <TOWARD_SIGN>.", "9": "<RELATIVE_DIRECTION> afslaan om de op-/afrit <NAME_SIGN> te nemen.", "10": "Op-/afrit nemen.", "11": "Op-/afrit <BRANCH_SIGN> nemen.", "12": "Op-/afrit nemen richting <TOWARD_SIGN>.", "13": "Op-/afrit <BRANCH_SIGN> nemen richting <TOWARD_SIGN>.", "14": "Op-/afrit <NAME_SIGN> nemen."}, "relative_directions": ["Links", "<PERSON><PERSON><PERSON>"], "example_phrases": {"0": ["Take the ramp on the left.", "Take the ramp on the right."], "1": ["Take the Interstate 95 ramp on the right."], "2": ["Take the ramp on the left toward JFK."], "3": ["Take the South Conduit Avenue ramp on the left toward JFK."], "4": ["Take the Gettysburg Pike ramp on the right."], "5": ["Turn left to take the ramp.", "Turn right to take the ramp."], "6": ["Turn left to take the Pennsylvania 2 83 West ramp."], "7": ["Turn left to take the ramp toward Harrisburg/Harrisburg International Airport."], "8": ["Turn left to take the Pennsylvania 2 83 West ramp toward Harrisburg, Harrisburg International Airport."], "9": ["Turn right to take the Gettysburg Pike ramp."], "10": ["Take the ramp."], "11": ["Take the Interstate 95 ramp."], "12": ["Take the ramp toward JFK."], "13": ["Take the South Conduit Avenue ramp toward JFK."], "14": ["Take the Gettysburg Pike ramp."]}}, "sharp": {"phrases": {"0": "<PERSON><PERSON><PERSON> een scherpe bocht <RELATIVE_DIRECTION>.", "1": "<PERSON><PERSON>m een scherpe bocht <RELATIVE_DIRECTION> naar <STREET_NAMES>.", "2": "<PERSON><PERSON>m een scherpe bocht <RELATIVE_DIRECTION> naar <BEGIN_STREET_NAMES>. Ga verder op <STREET_NAMES>.", "3": "Neem een scherpe bocht <RELATIVE_DIRECTION> om op <STREET_NAMES> te blijven.", "4": "<PERSON><PERSON><PERSON> een scherpe bocht <RELATIVE_DIRECTION> bij <JUNCTION_NAME>.", "5": "<PERSON><PERSON>m een scherpe bocht <RELATIVE_DIRECTION> richting <TOWARD_SIGN>."}, "empty_street_name_labels": ["het voetpad", "het fietspad", "het mountainbikepad", "the crosswalk", "the stairs", "the bridge", "the tunnel"], "relative_directions": ["links", "rechts"], "example_phrases": {"0": ["Make a sharp left."], "1": ["Make a sharp right onto Flatbush Avenue."], "2": ["Make a sharp left onto North Bond Street/US 1 Business/MD 924. Continue on MD 924."], "3": ["Make a sharp right to stay on Sunstone Drive."], "4": ["Make a sharp right at Mannenbashi East."], "5": ["Make a sharp left toward Baltimore."]}}, "sharp_verbal": {"phrases": {"0": "<PERSON><PERSON><PERSON> bocht naar <RELATIVE_DIRECTION> nemen.", "1": "<PERSON><PERSON><PERSON> bocht naar <RELATIVE_DIRECTION> nemen naar <STREET_NAMES>.", "2": "<PERSON><PERSON><PERSON> bocht naar <RELATIVE_DIRECTION> nemen naar <BEGIN_STREET_NAMES>.", "3": "<PERSON><PERSON><PERSON> bocht naar <RELATIVE_DIRECTION> nemen om op <STREET_NAMES> te blijven.", "4": "<PERSON><PERSON><PERSON> bocht naar <RELATIVE_DIRECTION> nemen bij <JUNCTION_NAME>.", "5": "<PERSON><PERSON><PERSON> bocht naar <RELATIVE_DIRECTION> nemen richting <TOWARD_SIGN>."}, "empty_street_name_labels": ["het voetpad", "het fietspad", "het mountainbikepad", "the crosswalk", "the stairs", "the bridge", "the tunnel"], "relative_directions": ["links", "rechts"], "example_phrases": {"0": ["Make a sharp left."], "1": ["Make a sharp right onto Flatbush Avenue."], "2": ["Make a sharp left onto North Bond Street, U.S. 1 Business."], "3": ["Make a sharp right to stay on Sunstone Drive."], "4": ["Make a sharp right at Mannenbashi East."], "5": ["Make a sharp left toward Baltimore."]}}, "start": {"phrases": {"0": "Ga richting het <CARDINAL_DIRECTION>.", "1": "Ga richting het <CARDINAL_DIRECTION> op <STREET_NAMES>.", "2": "Ga richting het <CARDINAL_DIRECTION> op <BEGIN_STREET_NAMES>. Ga verder op <STREET_NAMES>.", "4": "<PERSON><PERSON><PERSON><PERSON> richting het <CARDINAL_DIRECTION>.", "5": "<PERSON>ijd richting het <CARDINAL_DIRECTION> op <STREET_NAMES>.", "6": "<PERSON>ijd richting het <CARDINAL_DIRECTION> op <BEGIN_STREET_NAMES>. Ga verder op <STREET_NAMES>.", "8": "Loop richting het <CARDINAL_DIRECTION>.", "9": "Loop richting het <CARDINAL_DIRECTION> op <STREET_NAMES>.", "10": "Loop richting het <CARDINAL_DIRECTION> op <BEGIN_STREET_NAMES>. Ga verder op <STREET_NAMES>.", "16": "Fiets richting het <CARDINAL_DIRECTION>.", "17": "Fiets richting het <CARDINAL_DIRECTION> op <STREET_NAMES>.", "18": "Fiets richting het <CARDINAL_DIRECTION> op <BEGIN_STREET_NAMES>. Ga verder op <STREET_NAMES>."}, "cardinal_directions": ["noorden", "<PERSON><PERSON><PERSON><PERSON>", "oosten", "<PERSON><PERSON><PERSON><PERSON>", "zu<PERSON>", "zuidwesten", "westen", "noordwesten"], "empty_street_name_labels": ["het voetpad", "het fietspad", "het mountainbikepad", "the crosswalk", "the stairs", "the bridge", "the tunnel"], "example_phrases": {"0": ["Head east.", "Head north."], "1": ["Head southwest on 5th Avenue.", "Head west on the walkway", "Head east on the cycleway", "Head north on the mountain bike trail"], "2": ["Head south on North Prince Street/US 222/PA 272. Continue on US 222/PA 272."], "4": ["Drive east.", "Drive north."], "5": ["Drive southwest on 5th Avenue."], "6": ["Drive south on North Prince Street/US 222/PA 272. Continue on US 222/PA 272."], "8": ["Walk east.", "Walk north."], "9": ["Walk southwest on 5th Avenue.", "Walk west on the walkway"], "10": ["Walk south on North Prince Street/US 222/PA 272. Continue on US 222/PA 272."], "16": ["Bike east.", "Bike north."], "17": ["Bike southwest on 5th Avenue.", "Bike east on the cycleway", "Bike north on the mountain bike trail"], "18": ["Bike south on North Prince Street/US 222/PA 272. Continue on US 222/PA 272."]}}, "start_verbal": {"phrases": {"0": "Richting het <CARDINAL_DIRECTION> gaan.", "1": "<LENGTH> richting het <CARDINAL_DIRECTION> gaan.", "2": "Richting het <CARDINAL_DIRECTION> gaan op <STREET_NAMES>.", "3": "<LENGTH> richting het <CARDINAL_DIRECTION> gaan op <STREET_NAMES>.", "4": "Richting het <CARDINAL_DIRECTION> gaan op <BEGIN_STREET_NAMES>.", "5": "Richting het <CARDINAL_DIRECTION> rijden.", "6": "<LENGTH> richting het <CARDINAL_DIRECTION> rijden.", "7": "Richting het <CARDINAL_DIRECTION> rijden op <STREET_NAMES>.", "8": "<LENGTH> richting het <CARDINAL_DIRECTION> rijden op <STREET_NAMES>.", "9": "Richting het <CARDINAL_DIRECTION> rijden op <BEGIN_STREET_NAMES>.", "10": "Richting het <CARDINAL_DIRECTION> lopen.", "11": "<LENGTH> richting het <CARDINAL_DIRECTION> lopen.", "12": "Richting het <CARDINAL_DIRECTION> lopen op <STREET_NAMES>.", "13": "<LENGTH> richting het <CARDINAL_DIRECTION> lopen op <STREET_NAMES>.", "14": "Richting het <CARDINAL_DIRECTION> lopen op <BEGIN_STREET_NAMES>.", "15": "Richting het <CARDINAL_DIRECTION> fi<PERSON><PERSON>.", "16": "<LENGTH> richting het <CARDINAL_DIRECTION> fi<PERSON><PERSON>.", "17": "Richting het <CARDINAL_DIRECTION> fietsen op <STREET_NAMES>.", "18": "<LENGTH> richting het <CARDINAL_DIRECTION> fietsen op <STREET_NAMES>.", "19": "Richting het <CARDINAL_DIRECTION> fietsen op <BEGIN_STREET_NAMES>."}, "cardinal_directions": ["noorden", "<PERSON><PERSON><PERSON><PERSON>", "oosten", "<PERSON><PERSON><PERSON><PERSON>", "zu<PERSON>", "zuidwesten", "westen", "noordwesten"], "empty_street_name_labels": ["het voetpad", "het fietspad", "het mountainbikepad", "the crosswalk", "the stairs", "the bridge", "the tunnel"], "metric_lengths": ["<KILOMETERS> kilometer", "1 kilometer", "<METERS> meter", "minder dan 10 meter"], "us_customary_lengths": ["<MILES> mijl", "1 mijl", "een halve mijl", "een kwart mijl", "<FEET> voet", "minder dan 10 voet"], "example_phrases": {"0": ["Head east.", "Head north."], "1": ["Head east for a half mile.", "Head north for 1 kilometer."], "2": ["Head southwest on 5th Avenue."], "3": ["Head southwest on 5th Avenue for 1 tenth of a mile."], "4": ["Head south on North Prince Street, U.S. 2 22."], "5": ["Drive east.", "Drive north."], "6": ["Drive east for a half mile.", "Drive north for 1 kilometer."], "7": ["Drive southwest on 5th Avenue."], "8": ["Drive southwest on 5th Avenue for 1 tenth of a mile."], "9": ["Drive south on North Prince Street, U.S. 2 22."], "10": ["Walk east.", "Walk north."], "11": ["Walk east for a half mile.", "Walk north for 1 kilometer."], "12": ["Walk southwest on 5th Avenue."], "13": ["Walk southwest on 5th Avenue for 1 tenth of a mile."], "14": ["Walk south on North Prince Street, U.S. 2 22."], "15": ["Bike east.", "Bike north."], "16": ["Bike east for a half mile.", "Bike north for 1 kilometer."], "17": ["Bike southwest on 5th Avenue."], "18": ["Bike southwest on 5th Avenue for 1 tenth of a mile."], "19": ["Bike south on North Prince Street, U.S. 2 22."]}}, "transit": {"phrases": {"0": "N<PERSON><PERSON> de <TRANSIT_NAME>. (<TRANSIT_STOP_COUNT> <TRANSIT_STOP_COUNT_LABEL>)", "1": "N<PERSON><PERSON> de <TRANSIT_NAME> richting <TRANSIT_HEADSIGN>. (<TRANSIT_STOP_COUNT> <TRANSIT_STOP_COUNT_LABEL>)"}, "empty_transit_name_labels": ["tram", "metro", "trein", "bus", "veerboot", "kabeltram", "gondel", "ka<PERSON><PERSON><PERSON>"], "transit_stop_count_labels": {"one": "halte", "other": "haltes"}, "example_phrases": {"0": ["Take the New Haven. (1 stop)", "Take the metro. (2 stops)", "Take the bus. (12 stops)"], "1": ["Take the F toward JAMAICA - 179 ST. (10 stops)", "Take the ferry toward Staten Island. (1 stop)"]}}, "transit_connection_destination": {"phrases": {"0": "Verlaat het station.", "1": "Verlaat de <TRANSIT_STOP>.", "2": "Verlaat de <TRANSIT_STOP> <STATION_LABEL>."}, "station_label": "Station", "example_phrases": {"0": ["Exit the station."], "1": ["Exit the Embarcadero Station."], "2": ["Exit the 8 St - NYU Station."]}}, "transit_connection_destination_verbal": {"phrases": {"0": "Het station verlaten.", "1": "De <TRANSIT_STOP> verlaten.", "2": "De <TRANSIT_STOP> <STATION_LABEL> verlaten."}, "station_label": "Station", "example_phrases": {"0": ["Exit the station."], "1": ["Exit the Embarcadero Station."], "2": ["Exit the 8 St - NYU Station."]}}, "transit_connection_start": {"phrases": {"0": "Ga het station in.", "1": "Ga de <TRANSIT_STOP> in.", "2": "Ga de <TRANSIT_STOP> <STATION_LABEL> in."}, "station_label": "Station", "example_phrases": {"0": ["Enter the station."], "1": ["Enter the Embarcadero Station."], "2": ["Enter the 8 St - NYU Station."]}}, "transit_connection_start_verbal": {"phrases": {"0": "Station ingaan.", "1": "<TRANSIT_STOP> ingaan.", "2": "<TRANSIT_STOP> <STATION_LABEL> ingaan."}, "station_label": "Station", "example_phrases": {"0": ["Enter the station."], "1": ["Enter the Embarcadero Station."], "2": ["Enter the 8 St - NYU Station."]}}, "transit_connection_transfer": {"phrases": {"0": "Stap over op het station.", "1": "Stap over bij de <TRANSIT_STOP>.", "2": "Stap over bij de <TRANSIT_STOP> <STATION_LABEL>."}, "station_label": "Station", "example_phrases": {"0": ["Transfer at the station."], "1": ["Transfer at the Embarcadero Station."], "2": ["Transfer at the 8 St - NYU Station."]}}, "transit_connection_transfer_verbal": {"phrases": {"0": "Overstappen op het station.", "1": "Overstappen bij de <TRANSIT_STOP>.", "2": "SOverstappen bij de <TRANSIT_STOP> <STATION_LABEL>."}, "station_label": "Station", "example_phrases": {"0": ["Transfer at the station."], "1": ["Transfer at the Embarcadero Station."], "2": ["Transfer at the 8 St - NYU Station."]}}, "transit_remain_on": {"phrases": {"0": "Blijf in de <TRANSIT_NAME>. (<TRANSIT_STOP_COUNT> <TRANSIT_STOP_COUNT_LABEL>)", "1": "Blijf in de <TRANSIT_NAME> richting <TRANSIT_HEADSIGN>. (<TRANSIT_STOP_COUNT> <TRANSIT_STOP_COUNT_LABEL>)"}, "empty_transit_name_labels": ["tram", "metro", "trein", "bus", "veerboot", "kabeltram", "gondel", "ka<PERSON><PERSON><PERSON>"], "transit_stop_count_labels": {"one": "halte", "other": "haltes"}, "example_phrases": {"0": ["Remain on the New Haven. (1 stop)", "<PERSON><PERSON><PERSON> on the train. (3 stops)"], "1": ["Remain on the F toward JAMAICA - 179 ST. (10 stops)"]}}, "transit_remain_on_verbal": {"phrases": {"0": "In de <TRANSIT_NAME> blijven.", "1": "In de <TRANSIT_NAME> blijven richting <TRANSIT_HEADSIGN>."}, "empty_transit_name_labels": ["tram", "metro", "trein", "bus", "veerboot", "kabeltram", "gondel", "ka<PERSON><PERSON><PERSON>"], "example_phrases": {"0": ["<PERSON><PERSON><PERSON> on the New Haven."], "1": ["Remain on the F toward JAMAICA - 179 ST."]}}, "transit_transfer": {"phrases": {"0": "Stap over om de <TRANSIT_NAME> te nemen. (<TRANSIT_STOP_COUNT> <TRANSIT_STOP_COUNT_LABEL>)", "1": "Stap over om de <TRANSIT_NAME> te nemen richting <TRANSIT_HEADSIGN>. (<TRANSIT_STOP_COUNT> <TRANSIT_STOP_COUNT_LABEL>)"}, "empty_transit_name_labels": ["tram", "metro", "trein", "bus", "veerboot", "kabeltram", "gondel", "ka<PERSON><PERSON><PERSON>"], "transit_stop_count_labels": {"one": "halte", "other": "haltes"}, "example_phrases": {"0": ["Transfer to take the New Haven. (1 stop)", "Transfer to take the tram. (4 stops)"], "1": ["Transfer to take the F toward JAMAICA - 179 ST. (10 stops)"]}}, "transit_transfer_verbal": {"phrases": {"0": "Overstappen om de <TRANSIT_NAME> te nemen.", "1": "Overstappen om de <TRANSIT_NAME> te nemen richting <TRANSIT_HEADSIGN>."}, "empty_transit_name_labels": ["tram", "metro", "trein", "bus", "veerboot", "kabeltram", "gondel", "ka<PERSON><PERSON><PERSON>"], "example_phrases": {"0": ["Transfer to take the New Haven."], "1": ["Transfer to take the F toward JAMAICA - 179 ST."]}}, "transit_verbal": {"phrases": {"0": "De <TRANSIT_NAME> nemen.", "1": "<TRANSIT_NAME> nemen richting <TRANSIT_HEADSIGN>."}, "empty_transit_name_labels": ["tram", "metro", "trein", "bus", "veerboot", "kabeltram", "gondel", "ka<PERSON><PERSON><PERSON>"], "example_phrases": {"0": ["Take the New Haven."], "1": ["Take the F toward JAMAICA - 179 ST."]}}, "turn": {"phrases": {"0": "Sla <RELATIVE_DIRECTION>af.", "1": "Sla <RELATIVE_DIRECTION>af naar <STREET_NAMES>.", "2": "Sla <RELATIVE_DIRECTION>af naar <BEGIN_STREET_NAMES>. Ga verder op <STREET_NAMES>.", "3": "Sla <RELATIVE_DIRECTION>af om op <STREET_NAMES> te blijven.", "4": "Sla <RELATIVE_DIRECTION>af bij <JUNCTION_NAME>.", "5": "Sla <RELATIVE_DIRECTION>af richting <TOWARD_SIGN>."}, "empty_street_name_labels": ["het voetpad", "het fietspad", "het mountainbikepad", "the crosswalk", "the stairs", "the bridge", "the tunnel"], "relative_directions": ["links", "rechts"], "example_phrases": {"0": ["Turn left."], "1": ["Turn right onto Flatbush Avenue."], "2": ["Turn left onto North Bond Street/US 1 Business/MD 924. Continue on MD 924."], "3": ["Turn right to stay on Sunstone Drive."], "4": ["Turn right at Mannenbashi East."], "5": ["Turn left toward Baltimore."]}}, "turn_verbal": {"phrases": {"0": "<RELATIVE_DIRECTION> afslaan.", "1": "<RELATIVE_DIRECTION> afslaan naar <STREET_NAMES>.", "2": "<RELATIVE_DIRECTION> afslaan naar <BEGIN_STREET_NAMES>.", "3": "<RELATIVE_DIRECTION> afslaan om op <STREET_NAMES> te blijven.", "4": "<RELATIVE_DIRECTION> afslaan bij <JUNCTION_NAME>.", "5": "<RELATIVE_DIRECTION> afslaan richting <TOWARD_SIGN>."}, "empty_street_name_labels": ["het voetpad", "het fietspad", "het mountainbikepad", "the crosswalk", "the stairs", "the bridge", "the tunnel"], "relative_directions": ["Links", "<PERSON><PERSON><PERSON>"], "example_phrases": {"0": ["Turn left."], "1": ["Turn right onto Flatbush Avenue."], "2": ["Turn left onto North Bond Street, U.S. 1 Business."], "3": ["Turn right to stay on Sunstone Drive."], "4": ["Turn right at Mannenbashi East."], "5": ["Turn left toward Baltimore."]}}, "uturn": {"phrases": {"0": "Keer <RELATIVE_DIRECTION>om.", "1": "<PERSON><PERSON> <RELATIVE_DIRECTION>om naar <STREET_NAMES>.", "2": "<PERSON>er <RELATIVE_DIRECTION>om om op <STREET_NAMES> te blijven.", "3": "<PERSON>er <RELATIVE_DIRECTION>om bij <CROSS_STREET_NAMES>.", "4": "Keer <RELATIVE_DIRECTION>om bij <CROSS_STREET_NAMES> naar <STREET_NAMES>.", "5": "<PERSON>er <RELATIVE_DIRECTION>om bij <CROSS_STREET_NAMES> om op <STREET_NAMES> te blijven.", "6": "<PERSON>er <RELATIVE_DIRECTION>om bij <JUNCTION_NAME>.", "7": "<PERSON><PERSON> <RELATIVE_DIRECTION>om richting <TOWARD_SIGN>."}, "empty_street_name_labels": ["het voetpad", "het fietspad", "het mountainbikepad", "the crosswalk", "the stairs", "the bridge", "the tunnel"], "relative_directions": ["links", "rechts"], "example_phrases": {"0": ["Make a left U-turn."], "1": ["Make a right U-turn onto Bunker Hill Road."], "2": ["Make a left U-turn to stay on Bunker Hill Road."], "3": ["Make a left U-turn at Devonshire Road."], "4": ["Make a left U-turn at Devonshire Road onto Jonestown Road/US 22."], "5": ["Make a left U-turn at Devonshire Road to stay on Jonestown Road/US 22."], "6": ["Make a right U-turn at Mannenbashi East."], "7": ["Make a left U-turn toward Baltimore."]}}, "uturn_verbal": {"phrases": {"0": "<RELATIVE_DIRECTION> omkeren.", "1": "<RELATIVE_DIRECTION> omkeren naar <STREET_NAMES>.", "2": "<RELATIVE_DIRECTION> omkeren om op <STREET_NAMES> te blijven.", "3": "<RELATIVE_DIRECTION> omkeren bij <CROSS_STREET_NAMES>.", "4": "<RELATIVE_DIRECTION> omkeren bij <CROSS_STREET_NAMES> naar <STREET_NAMES>.", "5": "<RELATIVE_DIRECTION> omkeren bij <CROSS_STREET_NAMES> om op <STREET_NAMES> te blijven.", "6": "<RELATIVE_DIRECTION> omkeren bij <JUNCTION_NAME>.", "7": "<RELATIVE_DIRECTION> om<PERSON>en richting <TOWARD_SIGN>."}, "empty_street_name_labels": ["het voetpad", "het fietspad", "het mountainbikepad", "the crosswalk", "the stairs", "the bridge", "the tunnel"], "relative_directions": ["Links", "<PERSON><PERSON><PERSON>"], "example_phrases": {"0": ["Make a left U-turn."], "1": ["Make a right U-turn onto Bunker Hill Road."], "2": ["Make a left U-turn to stay on Bunker Hill Road."], "3": ["Make a left U-turn at Devonshire Road."], "4": ["Make a left U-turn at Devonshire Road onto Jonestown Road, U.S. 22."], "5": ["Make a left U-turn at Devonshire Road to stay on Jonestown Road, U.S. 22."], "6": ["Make a right U-turn at Mannenbashi East."], "7": ["Make a left U-turn toward Baltimore."]}}, "verbal_multi_cue": {"phrases": {"0": "<CURRENT_VERBAL_CUE> <PERSON><PERSON><PERSON> <NEXT_VERBAL_CUE>", "1": "<CURRENT_VERBAL_CUE> <PERSON><PERSON><PERSON>, over <LENGTH>, <NEXT_VERBAL_CUE>"}, "metric_lengths": ["<KILOMETERS> kilometer", "1 kilometer", "<METERS> meter", "minder dan 10 meter"], "us_customary_lengths": ["<MILES> mijl", "1 mijl", "een halve mijl", "een kwart mijl", "<FEET> voet", "minder dan 10 voet"], "example_phrases": {"0": ["Bear right onto East Fayette Street. Then Turn right onto North Gay Street."], "1": ["Bear right onto East Fayette Street. Then, in 500 feet, Turn right onto North Gay Street."]}}, "approach_verbal_alert": {"phrases": {"0": "Over <LENGTH>, <CURRENT_VERBAL_CUE>"}, "metric_lengths": ["<KILOMETERS> kilometer", "1 kilometer", "<METERS> meter", "minder dan 10 meter"], "us_customary_lengths": ["<MILES> mijl", "1 mijl", "een halve mijl", "een kwart mijl", "<FEET> voet", "minder dan 10 voet"], "example_phrases": {"0": ["In a quarter mile, Turn right onto North Gay Street."]}}, "pass": {"phrases": {"0": "Pass <OBJECT_LABEL>.", "1": "Pass traffic signals on <OBJECT_LABEL>."}, "object_labels": ["the gate", "the bollards", "ways intersection"], "example_phrases": {"0": ["Pass the gate."]}}, "elevator": {"phrases": {"0": "<PERSON><PERSON><PERSON> de lift.", "1": "<PERSON><PERSON><PERSON> de <PERSON> naar <LEVEL>."}, "example_phrases": {"0": ["Take the elevator."], "1": ["Take the elevator to Level 1."]}}, "steps": {"phrases": {"0": "<PERSON><PERSON><PERSON> de <PERSON>.", "1": "<PERSON><PERSON><PERSON> de <PERSON> naar <LEVEL>."}, "example_phrases": {"0": ["Take the stairs."], "1": ["Take the stairs to Level 2."]}}, "escalator": {"phrases": {"0": "<PERSON><PERSON><PERSON>.", "1": "<PERSON><PERSON><PERSON> naar <LEVEL>."}, "example_phrases": {"0": ["Take the escalator."], "1": ["Take the escalator to Level 3."]}}, "enter_building": {"phrases": {"0": "Ga het gebouw in.", "1": "Ga het gebouw in en ga verder op <STREET_NAMES>."}, "empty_street_name_labels": ["het voetpad", "het fietspad", "het mountainbikepad", "the crosswalk"], "example_phrases": {"0": ["Enter the building."], "1": ["Enter the building, and continue on the walkway."]}}, "exit_building": {"phrases": {"0": "Ga het gebouw uit.", "1": "Ga het gebouw uit en ga verder op <STREET_NAMES>."}, "empty_street_name_labels": ["het voetpad", "het fietspad", "het mountainbikepad", "the crosswalk"], "example_phrases": {"0": ["Exit the building."], "1": ["Exit the building, and continue on the walkway."]}}}}
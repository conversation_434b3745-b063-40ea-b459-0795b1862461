{"posix_locale": "sv_SE.UTF-8", "aliases": ["sv"], "instructions": {"arrive": {"phrases": {"0": "<PERSON><PERSON><PERSON><PERSON>: <TIME>.", "1": "<PERSON><PERSON><PERSON><PERSON>: <TIME> till <TRANSIT_STOP>."}, "example_phrases": {"0": ["Arrive: 8:02 AM."], "1": ["Arrive: 8:02 AM at 8 St - NYU."]}}, "arrive_verbal": {"phrases": {"0": "<PERSON><PERSON><PERSON><PERSON> <TIME>.", "1": "<PERSON><PERSON><PERSON><PERSON> <TIME> till <TRANSIT_STOP>."}, "example_phrases": {"0": ["Arrive at 8:02 AM."], "1": ["Arrive at 8:02 AM at 8 St - NYU."]}}, "bear": {"phrases": {"0": "Sväng <RELATIVE_DIRECTION>.", "1": "Sväng <RELATIVE_DIRECTION> in på <STREET_NAMES>.", "2": "Sväng <RELATIVE_DIRECTION> in på <BEGIN_STREET_NAMES>. Fortsätt på <STREET_NAMES>.", "3": "Sväng <RELATIVE_DIRECTION> för att stanna kvar på <STREET_NAMES>.", "4": "Sväng <RELATIVE_DIRECTION> i <JUNCTION_NAME>.", "5": "Sväng <RELATIVE_DIRECTION> mot <TOWARD_SIGN>."}, "empty_street_name_labels": ["gångvägen", "cykelbanan", "terrängcykelbanan", "the crosswalk", "the stairs", "the bridge", "the tunnel"], "relative_directions": ["vänster", "<PERSON><PERSON><PERSON>"], "example_phrases": {"0": ["Bear right."], "1": ["<PERSON> left onto Arlen Road."], "2": ["Bear right onto Belair Road/US 1 Business. Continue on US 1 Business."], "3": ["<PERSON> left to stay on US 15 South."], "4": ["Bear right at Mannenbashi East."], "5": ["Bear left toward Baltimore."]}}, "bear_verbal": {"phrases": {"0": "Sväng <RELATIVE_DIRECTION>.", "1": "Sväng <RELATIVE_DIRECTION> in på <STREET_NAMES>.", "2": "Sväng <RELATIVE_DIRECTION> in på <BEGIN_STREET_NAMES>.", "3": "Sväng <RELATIVE_DIRECTION> för att stanna kvar på <STREET_NAMES>.", "4": "Sväng <RELATIVE_DIRECTION> i <JUNCTION_NAME>.", "5": "Sväng <RELATIVE_DIRECTION> mot <TOWARD_SIGN>."}, "empty_street_name_labels": ["gångvägen", "cykelbanan", "terrängcykelbanan", "the crosswalk", "the stairs", "the bridge", "the tunnel"], "relative_directions": ["vänster", "<PERSON><PERSON><PERSON>"], "example_phrases": {"0": ["Bear right."], "1": ["<PERSON> left onto Arlen Road."], "2": ["Bear right onto Belair Road, U.S. 1 Business."], "3": ["<PERSON> left to stay on U.S. 15 South."], "4": ["Bear right at Mannenbashi East."], "5": ["Bear left toward Baltimore."]}}, "becomes": {"phrases": {"0": "<PREVIOUS_STREET_NAMES> blir <STREET_NAMES>."}, "example_phrases": {"0": ["Vine Street becomes Middletown Road."]}}, "becomes_verbal": {"phrases": {"0": "<PREVIOUS_STREET_NAMES> blir <STREET_NAMES>."}, "example_phrases": {"0": ["Vine Street becomes Middletown Road."]}}, "continue": {"phrases": {"0": "Fortsätt.", "1": "Fortsätt på <STREET_NAMES>.", "2": "Fortsätt i <JUNCTION_NAME>.", "3": "Fortsätt mot <TOWARD_SIGN>."}, "empty_street_name_labels": ["gångvägen", "cykelbanan", "terrängcykelbanan", "the crosswalk", "the stairs", "the bridge", "the tunnel"], "example_phrases": {"0": ["Continue."], "1": ["Continue on 10th Avenue."], "2": ["Continue at Mannenbashi East."], "3": ["Continue toward Baltimore."]}}, "continue_verbal": {"phrases": {"0": "Fortsätt.", "1": "Fortsätt i <LENGTH>.", "2": "Fortsätt på <STREET_NAMES>.", "3": "Fortsätt på <STREET_NAMES> i <LENGTH>.", "4": "Fortsätt på <JUNCTION_NAME>.", "5": "Fortsätt på <JUNCTION_NAME> i <LENGTH>.", "6": "Fortsätt mot<TOWARD_SIGN>.", "7": "Fortsätt mot <TOWARD_SIGN> i <LENGTH>."}, "empty_street_name_labels": ["gångvägen", "cykelbanan", "terrängcykelbanan", "the crosswalk", "the stairs", "the bridge", "the tunnel"], "metric_lengths": ["<KILOMETERS> kilometer", "1 kilometer", "<METERS> meter", "<PERSON><PERSON> än 10 meter"], "us_customary_lengths": ["<MILES> engelska mil", "1 engelsk mil", "en halv engelsk mil", "en kvarts engelsk mil ", "<FEET> fot", "mindre än 10 fot"], "example_phrases": {"0": ["Continue."], "1": ["Continue for 300 feet."], "2": ["Continue on 10th Avenue."], "3": ["Continue on 10th Avenue for 3 miles."], "4": ["Continue at Mannenbashi East."], "5": ["Continue at Mannenbashi East for 2 miles."], "6": ["Continue toward Baltimore."], "7": ["Continue toward Baltimore for 5 miles."]}}, "continue_verbal_alert": {"phrases": {"0": "Fortsätt.", "1": "Fortsätt på <STREET_NAMES>.", "2": "Fortsätt i <JUNCTION_NAME>.", "3": "Fortsätt mot <TOWARD_SIGN>."}, "empty_street_name_labels": ["gångvägen", "cykelbanan", "terrängcykelbanan", "the crosswalk", "the stairs", "the bridge", "the tunnel"], "example_phrases": {"0": ["Continue."], "1": ["Continue on 10th Avenue."], "2": ["Continue at Mannenbashi East."], "3": ["Continue toward Baltimore."]}}, "depart": {"phrases": {"0": "Avres: <TIME>.", "1": "Avres: <TIME> f<PERSON><PERSON><PERSON> <TRANSIT_STOP>."}, "example_phrases": {"0": ["Depart: 8:02 AM."], "1": ["Depart: 8:02 AM from 8 St - NYU."]}}, "depart_verbal": {"phrases": {"0": "Avres <TIME>.", "1": "Avres <TIME> f<PERSON><PERSON><PERSON> <TRANSIT_STOP>."}, "example_phrases": {"0": ["Depart at 8:02 AM."], "1": ["Depart at 8:02 AM from 8 St - NYU."]}}, "destination": {"phrases": {"0": "Du har anlänt till din destination.", "1": "Du har anlänt till <DESTINATION>.", "2": "Din destination är till <RELATIVE_DIRECTION>.", "3": "<DESTINATION> är till <RELATIVE_DIRECTION>."}, "relative_directions": ["vänster", "<PERSON><PERSON><PERSON>"], "example_phrases": {"0": ["You have arrived at your destination."], "1": ["You have arrived at 3206 Powelton Avenue."], "2": ["Your destination is on the left.", "Your destination is on the right."], "3": ["Lancaster Brewing Company is on the left."]}}, "destination_verbal": {"phrases": {"0": "Du har anlänt till din destination.", "1": "Du har anlänt till <DESTINATION>.", "2": "Din destination är till <RELATIVE_DIRECTION>.", "3": "<DESTINATION> är till <RELATIVE_DIRECTION>."}, "relative_directions": ["vänster", "<PERSON><PERSON><PERSON>"], "example_phrases": {"0": ["You have arrived at your destination."], "1": ["You have arrived at 32 o6 Powelton Avenue."], "2": ["Your destination is on the left.", "Your destination is on the right."], "3": ["Lancaster Brewing Company is on the left."]}}, "destination_verbal_alert": {"phrases": {"0": "Du kommer att anlända till din destination.", "1": "Du kommer att anlända till <DESTINATION>.", "2": "Din destination kommer att vara till <RELATIVE_DIRECTION>.", "3": "<DESTINATION> kommer att vara till <RELATIVE_DIRECTION>."}, "relative_directions": ["vänster", "<PERSON><PERSON><PERSON>"], "example_phrases": {"0": ["You will arrive at your destination."], "1": ["You will arrive at 32 o6 Powelton Avenue."], "2": ["Your destination will be on the left.", "Your destination will be on the right."], "3": ["Lancaster Brewing Company will be on the left."]}}, "enter_ferry": {"phrases": {"0": "<PERSON> färjan.", "1": "Ta <STREET_NAMES>.", "2": "Ta <STREET_NAMES> <FERRY_LABEL>.", "3": "Ta färjan mot <TOWARD_SIGN>."}, "empty_street_name_labels": ["gångvägen", "cykelbanan", "terrängcykelbanan", "the crosswalk", "the stairs", "the bridge", "the tunnel"], "ferry_label": "<PERSON><PERSON><PERSON><PERSON>", "example_phrases": {"0": ["Take the Ferry."], "1": ["Take the Millersburg Ferry."], "2": ["Take the Bridgeport - Port Jefferson Ferry."], "3": ["Take the ferry toward Cape May."]}}, "enter_ferry_verbal": {"phrases": {"0": "<PERSON> färjan.", "1": "Ta <STREET_NAMES>.", "2": "Ta <STREET_NAMES> <FERRY_LABEL>.", "3": "Ta färjan mot <TOWARD_SIGN>."}, "empty_street_name_labels": ["gångvägen", "cykelbanan", "terrängcykelbanan", "the crosswalk", "the stairs", "the bridge", "the tunnel"], "ferry_label": "<PERSON><PERSON><PERSON><PERSON>", "example_phrases": {"0": ["Take the Ferry."], "1": ["Take the Millersburg Ferry."], "2": ["Take the Bridgeport - Port Jefferson Ferry."], "3": ["Take the ferry toward Cape May."]}}, "enter_roundabout": {"phrases": {"0": "<PERSON><PERSON><PERSON> in i rondellen.", "1": "<PERSON><PERSON><PERSON> in i rondellen och ta den <ORDINAL_VALUE> avfarten.", "2": "<PERSON><PERSON><PERSON> in i rondellen och ta den <ORDINAL_VALUE> a<PERSON><PERSON><PERSON> in på <ROUNDABOUT_EXIT_STREET_NAMES>.", "3": "<PERSON><PERSON><PERSON> in i rondellen och ta den <ORDINAL_VALUE> a<PERSON><PERSON><PERSON> in på <ROUNDABOUT_EXIT_BEGIN_STREET_NAMES>. Fortsätt på <ROUNDABOUT_EXIT_STREET_NAMES>.", "4": "<PERSON><PERSON><PERSON> in i rondellen och ta den <ORDINAL_VALUE> avfarten mot <TOWARD_SIGN>.", "5": "<PERSON><PERSON><PERSON> in i rondellen och ta avfarten in på <ROUNDABOUT_EXIT_STREET_NAMES>.", "6": "<PERSON><PERSON><PERSON> in i rondellen och ta avfarten in på <ROUNDABOUT_EXIT_BEGIN_STREET_NAMES>. Fortsätt på <ROUNDABOUT_EXIT_STREET_NAMES>.", "7": "<PERSON><PERSON><PERSON> in i rondellen och ta avfarten mot <TOWARD_SIGN>.", "8": "<PERSON><PERSON><PERSON> in på <STREET_NAMES>", "9": "<PERSON><PERSON><PERSON> in på <STREET_NAMES> och ta den <ORDINAL_VALUE> avfarten.", "10": "<PERSON><PERSON><PERSON> in på <STREET_NAMES> och ta den <ORDINAL_VALUE> av<PERSON><PERSON> in på <ROUNDABOUT_EXIT_STREET_NAMES>.", "11": "<PERSON><PERSON><PERSON> in på <STREET_NAMES> och ta den <ORDINAL_VALUE> av<PERSON><PERSON> in på <ROUNDABOUT_EXIT_BEGIN_STREET_NAMES>. Fortsätt på <ROUNDABOUT_EXIT_STREET_NAMES>.", "12": "<PERSON><PERSON><PERSON> in på <STREET_NAMES> och ta den <ORDINAL_VALUE> avfarten mot <TOWARD_SIGN>.", "13": "<PERSON><PERSON><PERSON> in på <STREET_NAMES> och ta avfarten mot <ROUNDABOUT_EXIT_STREET_NAMES>.", "14": "<PERSON><PERSON><PERSON> in på <STREET_NAMES> och ta avfarten mot <ROUNDABOUT_EXIT_BEGIN_STREET_NAMES>. Fortsätt på <ROUNDABOUT_EXIT_STREET_NAMES>.", "15": "<PERSON><PERSON><PERSON> in på <STREET_NAMES> och ta avfarten mot <TOWARD_SIGN>."}, "ordinal_values": ["1:a", "2:a", "3:e", "4:e", "5:e", "6:e", "7:e", "8:e", "9:e", "10:e"], "empty_street_name_labels": ["gångvägen", "cykelbanan", "terrängcykelbanan", "the crosswalk", "the stairs", "the bridge", "the tunnel"], "example_phrases": {"0": ["Enter the roundabout."], "1": ["Enter the roundabout and take the 1st exit.", "Enter the roundabout and take the 2nd exit.", "Enter the roundabout and take the 3rd exit.", "Enter the roundabout and take the 4th exit.", "Enter the roundabout and take the 5th exit.", "Enter the roundabout and take the 6th exit.", "Enter the roundabout and take the 7th exit.", "Enter the roundabout and take the 8th exit.", "Enter the roundabout and take the 9th exit.", "Enter the roundabout and take the 10th exit."], "2": ["Enter the roundabout and take the 3rd exit onto Main Street."], "3": ["Enter the roundabout and take the 3rd exit onto US 322/Main Street. Continue on US 322."], "4": ["Enter the roundabout and take the 3rd exit toward Baltimore."], "5": ["Enter the roundabout and take the exit onto Main Street."], "6": ["Enter the roundabout and take the exit onto US 322/Main Street. Continue on US 322."], "7": ["Enter the roundabout and take the exit toward Baltimore."], "8": ["Enter Dupont Circle."], "9": ["Enter Dupont Circle and take the 1st exit."], "10": ["Enter Dupont Circle and take the 3rd exit onto Main Street."], "11": ["Enter Dupont Circle and take the 3rd exit onto US 322/Main Street. Continue on US 322."], "12": ["Enter Dupont Circle and take the 3rd exit toward Baltimore."], "13": ["Enter Dupont Circle and take the exit onto Main Street."], "14": ["Enter Dupont Circle and take the exit onto US 322/Main Street. Continue on US 322."], "15": ["Enter Dupont Circle and take the exit toward Baltimore."]}}, "enter_roundabout_verbal": {"phrases": {"0": "<PERSON><PERSON><PERSON> in i rondellen.", "1": "<PERSON><PERSON><PERSON> in i rondellen och ta den <ORDINAL_VALUE> avfarten.", "2": "<PERSON><PERSON><PERSON> in i rondellen och ta den <ORDINAL_VALUE> a<PERSON><PERSON><PERSON> in på <ROUNDABOUT_EXIT_STREET_NAMES>.", "3": "<PERSON><PERSON><PERSON> in i rondellen och ta den <ORDINAL_VALUE> a<PERSON><PERSON><PERSON> in på <ROUNDABOUT_EXIT_BEGIN_STREET_NAMES>.", "4": "<PERSON><PERSON><PERSON> in i rondellen och ta den <ORDINAL_VALUE> avfarten mot <TOWARD_SIGN>.", "5": "<PERSON><PERSON><PERSON> in i rondellen och ta avfarten in på <ROUNDABOUT_EXIT_STREET_NAMES>.", "6": "<PERSON><PERSON><PERSON> in i rondellen och ta avfarten in på <ROUNDABOUT_EXIT_BEGIN_STREET_NAMES>.", "7": "<PERSON><PERSON><PERSON> in i rondellen och ta avfarten mot <TOWARD_SIGN>.", "8": "<PERSON><PERSON><PERSON> in på <STREET_NAMES>", "9": "<PERSON><PERSON><PERSON> in på <STREET_NAMES> och ta den <ORDINAL_VALUE> avfarten.", "10": "<PERSON><PERSON><PERSON> in på <STREET_NAMES> och ta den <ORDINAL_VALUE> av<PERSON><PERSON> in på <ROUNDABOUT_EXIT_STREET_NAMES>.", "11": "<PERSON><PERSON><PERSON> in på <STREET_NAMES> och ta den <ORDINAL_VALUE> av<PERSON><PERSON> in på <ROUNDABOUT_EXIT_BEGIN_STREET_NAMES>.", "12": "<PERSON><PERSON><PERSON> in på <STREET_NAMES> och ta den <ORDINAL_VALUE> avfarten mot <TOWARD_SIGN>.", "13": "<PERSON><PERSON><PERSON> in på <STREET_NAMES> och ta avfarten mot <ROUNDABOUT_EXIT_STREET_NAMES>.", "14": "<PERSON><PERSON><PERSON> in på <STREET_NAMES> och ta avfarten in på <ROUNDABOUT_EXIT_BEGIN_STREET_NAMES>.", "15": "<PERSON><PERSON><PERSON> in på <STREET_NAMES> och ta avfarten mot <TOWARD_SIGN>."}, "ordinal_values": ["1:a", "2:a", "3:e", "4:e", "5:e", "6:e", "7:e", "8:e", "9:e", "10:e"], "empty_street_name_labels": ["gångvägen", "cykelbanan", "terrängcykelbanan", "the crosswalk", "the stairs", "the bridge", "the tunnel"], "example_phrases": {"0": ["Enter the roundabout."], "1": ["Enter the roundabout and take the 1st exit.", "Enter the roundabout and take the 2nd exit.", "Enter the roundabout and take the 3rd exit.", "Enter the roundabout and take the 4th exit.", "Enter the roundabout and take the 5th exit.", "Enter the roundabout and take the 6th exit.", "Enter the roundabout and take the 7th exit.", "Enter the roundabout and take the 8th exit.", "Enter the roundabout and take the 9th exit.", "Enter the roundabout and take the 10th exit."], "2": ["Enter the roundabout and take the 3rd exit onto Main Street."], "3": ["Enter the roundabout and take the 3rd exit onto U.S. 3 22/Main Street."], "4": ["Enter the roundabout and take the 3rd exit toward Baltimore."], "5": ["Enter the roundabout and take the exit onto Main Street."], "6": ["Enter the roundabout and take the exit onto U.S. 3 22/Main Street."], "7": ["Enter the roundabout and take the exit toward Baltimore."], "8": ["Enter Dupont Circle."], "9": ["Enter Dupont Circle and take the 1st exit."], "10": ["Enter Dupont Circle and take the 3rd exit onto Main Street."], "11": ["Enter Dupont Circle and take the 3rd exit onto U.S. 3 22/Main Street."], "12": ["Enter Dupont Circle and take the 3rd exit toward Baltimore."], "13": ["Enter Dupont Circle and take the exit onto Main Street."], "14": ["Enter Dupont Circle and take the exit onto U.S. 3 22/Main Street."], "15": ["Enter Dupont Circle and take the exit toward Baltimore."]}}, "exit": {"phrases": {"0": "Ta avfarten till <RELATIVE_DIRECTION>.", "1": "Ta avfart <NUMBER_SIGN> till <RELATIVE_DIRECTION>.", "2": "Ta <BRANCH_SIGN> av<PERSON>ten till <RELATIVE_DIRECTION>.", "3": "Ta avfart <NUMBER_SIGN> till <RELATIVE_DIRECTION> in på <BRANCH_SIGN>.", "4": "Ta avfarten till <RELATIVE_DIRECTION> mot <TOWARD_SIGN>.", "5": "Ta avfart <NUMBER_SIGN> till <RELATIVE_DIRECTION> mot <TOWARD_SIGN>.", "6": "Ta <BRANCH_SIGN> av<PERSON>ten till <RELATIVE_DIRECTION> mot <TOWARD_SIGN>.", "7": "Ta avfart <NUMBER_SIGN> till <RELATIVE_DIRECTION> in på <BRANCH_SIGN> mot<TOWARD_SIGN>.", "8": "Ta <NAME_SIGN>avfarten till <RELATIVE_DIRECTION>.", "10": "Ta <NAME_SIGN>av<PERSON><PERSON> till <RELATIVE_DIRECTION> in på <BRANCH_SIGN>.", "12": "Ta <NAME_SIGN>av<PERSON>ten till <RELATIVE_DIRECTION> mot <TOWARD_SIGN>.", "14": "Ta <NAME_SIGN>av<PERSON><PERSON> till <RELATIVE_DIRECTION> in på <BRANCH_SIGN> mot<TOWARD_SIGN>.", "15": "<PERSON> avfarten.", "16": "Ta avfart <NUMBER_SIGN>.", "17": "Ta <BRANCH_SIGN> av<PERSON><PERSON>.", "18": "Ta avfart <NUMBER_SIGN> in på <BRANCH_SIGN>.", "19": "Ta avfarten mot <TOWARD_SIGN>.", "20": "Ta avfart <NUMBER_SIGN> mot <TOWARD_SIGN>.", "21": "Ta <BRANCH_SIGN> avfarten mot <TOWARD_SIGN>.", "22": "Ta avfart <NUMBER_SIGN> in på <BRANCH_SIGN> mot <TOWARD_SIGN>.", "23": "Ta <NAME_SIGN>av<PERSON><PERSON>.", "25": "Ta <NAME_SIGN>av<PERSON><PERSON> in på  <BRANCH_SIGN>.", "27": "Ta <NAME_SIGN>avfarten mot <TOWARD_SIGN>.", "29": "Ta <NAME_SIGN>av<PERSON><PERSON> in på <BRANCH_SIGN> mot <TOWARD_SIGN>."}, "relative_directions": ["vänster", "<PERSON><PERSON><PERSON>"], "example_phrases": {"0": ["Take the exit on the left.", "Take the exit on the right."], "1": ["Take exit 67 B-A on the right."], "2": ["Take the US 322 West exit on the right."], "3": ["Take exit 67 B-A on the right onto US 322 West."], "4": ["Take the exit on the right toward Lewistown."], "5": ["Take exit 67 B-A on the right toward Lewistown."], "6": ["Take the US 322 West exit on the right toward Lewistown."], "7": ["Take exit 67 B-A on the right onto US 322 West toward Lewistown/State College."], "8": ["Take the White Marsh Boulevard exit on the left."], "10": ["Take the White Marsh Boulevard exit on the left onto MD 43 East."], "12": ["Take the White Marsh Boulevard exit on the left toward White Marsh."], "14": ["Take the White Marsh Boulevard exit on the left onto MD 43 East toward White Marsh."], "15": ["Take the exit."], "16": ["Take exit 67 B-A."], "17": ["Take the US 322 West exit."], "18": ["Take exit 67 B-A onto US 322 West."], "19": ["Take the exit toward Lewistown."], "20": ["Take exit 67 B-A toward Lewistown."], "21": ["Take the US 322 West exit toward Lewistown."], "22": ["Take exit 67 B-A onto US 322 West toward Lewistown/State College."], "23": ["Take the White Marsh Boulevard exit."], "25": ["Take the White Marsh Boulevard exit onto MD 43 East."], "27": ["Take the White Marsh Boulevard exit toward White Marsh."], "29": ["Take the White Marsh Boulevard exit onto MD 43 East toward White Marsh."]}}, "exit_roundabout": {"phrases": {"0": "<PERSON><PERSON><PERSON> ut ur rondellen.", "1": "<PERSON><PERSON><PERSON> ut ur rondellen in på <STREET_NAMES>.", "2": "<PERSON><PERSON><PERSON> ut ur rondellen in på <BEGIN_STREET_NAMES>. Fortsätt på  <STREET_NAMES>.", "3": "<PERSON><PERSON><PERSON> ut ur rondellen mot <TOWARD_SIGN>."}, "empty_street_name_labels": ["gångvägen", "cykelbanan", "terrängcykelbanan", "the crosswalk", "the stairs", "the bridge", "the tunnel"], "example_phrases": {"0": ["Exit the roundabout."], "1": ["Exit the roundabout onto Philadelphia Road/MD 7."], "2": ["Exit the roundabout onto Catoctin Mountain Highway/US 15. Continue on US 15."], "3": ["Exit the roundabout toward Baltimore."]}}, "exit_roundabout_verbal": {"phrases": {"0": "<PERSON><PERSON><PERSON> ut ur rondellen.", "1": "<PERSON><PERSON><PERSON> ut ur rondellen in på <STREET_NAMES>.", "2": "<PERSON><PERSON><PERSON> ut ur rondellen in på <BEGIN_STREET_NAMES>.", "3": "<PERSON><PERSON><PERSON> ut ur rondellen mot <TOWARD_SIGN>."}, "empty_street_name_labels": ["gångvägen", "cykelbanan", "terrängcykelbanan", "the crosswalk", "the stairs", "the bridge", "the tunnel"], "example_phrases": {"0": ["Exit the roundabout."], "1": ["Exit the roundabout onto Philadelphia Road, Maryland 7."], "2": ["Exit the roundabout onto Catoctin Mountain Highway, U.S. 15."], "3": ["Exit the roundabout toward Baltimore."]}}, "exit_verbal": {"phrases": {"0": "Ta avfarten till <RELATIVE_DIRECTION>.", "1": "Ta avfart <NUMBER_SIGN> till <RELATIVE_DIRECTION>.", "2": "Ta <BRANCH_SIGN>av<PERSON>ten till <RELATIVE_DIRECTION>.", "3": "Ta avfart <NUMBER_SIGN> till <RELATIVE_DIRECTION> in på <BRANCH_SIGN>.", "4": "Ta avfarten till <RELATIVE_DIRECTION> mot <TOWARD_SIGN>.", "5": "Ta avfart <NUMBER_SIGN> till <RELATIVE_DIRECTION> mot <TOWARD_SIGN>.", "6": "Ta <BRANCH_SIGN>av<PERSON>ten till <RELATIVE_DIRECTION> mot <TOWARD_SIGN>.", "7": "Ta avfart <NUMBER_SIGN> till <RELATIVE_DIRECTION> in på <BRANCH_SIGN> mot <TOWARD_SIGN>.", "8": "Ta <NAME_SIGN>avfarten till <RELATIVE_DIRECTION>.", "10": "Ta <NAME_SIGN>av<PERSON><PERSON> till <RELATIVE_DIRECTION> in på <BRANCH_SIGN>.", "12": "Ta <NAME_SIGN> av<PERSON>ten till <RELATIVE_DIRECTION> mot <TOWARD_SIGN>.", "14": "Ta <NAME_SIGN>av<PERSON><PERSON> till <RELATIVE_DIRECTION> in på <BRANCH_SIGN> mot <TOWARD_SIGN>.", "15": "<PERSON> avfarten.", "16": "Ta avfart <NUMBER_SIGN>.", "17": "Ta <BRANCH_SIGN>av<PERSON><PERSON>.", "18": "Ta avfart <NUMBER_SIGN> in på <BRANCH_SIGN>.", "19": "Ta avfarten mot <TOWARD_SIGN>.", "20": "Ta avfart <NUMBER_SIGN> mot <TOWARD_SIGN>.", "21": "Ta <BRANCH_SIGN>avfarten mot <TOWARD_SIGN>.", "22": "Ta avfart <NUMBER_SIGN> in på <BRANCH_SIGN> mot <TOWARD_SIGN>.", "23": "Ta <NAME_SIGN>av<PERSON><PERSON>.", "25": "Ta <NAME_SIGN>av<PERSON><PERSON> in på <BRANCH_SIGN>.", "27": "Ta <NAME_SIGN>avfarten mot <TOWARD_SIGN>.", "29": "Ta <NAME_SIGN>av<PERSON><PERSON> in på <BRANCH_SIGN> mot <TOWARD_SIGN>."}, "relative_directions": ["vänster ", "<PERSON><PERSON><PERSON>"], "example_phrases": {"0": ["Take the exit on the left.", "Take the exit on the right."], "1": ["Take exit 67 B-A on the right."], "2": ["Take the U.S. 3 22 West exit on the right."], "3": ["Take exit 67 B-A on the right onto U.S. 3 22 West."], "4": ["Take the exit on the right toward Lewistown."], "5": ["Take exit 67 B-A on the right toward Lewistown."], "6": ["Take the U.S. 3 22 West exit on the right toward Lewistown."], "7": ["Take exit 67 B-A on the right onto U.S. 3 22 West toward Lewistown, State College."], "8": ["Take the White Marsh Boulevard exit on the left."], "10": ["Take the White Marsh Boulevard exit on the left onto Maryland 43 East."], "12": ["Take the White Marsh Boulevard exit on the left toward White Marsh."], "14": ["Take the White Marsh Boulevard exit on the left onto Maryland 43 East toward White Marsh."], "15": ["Take the exit."], "16": ["Take exit 67 B-A."], "17": ["Take the US 322 West exit."], "18": ["Take exit 67 B-A onto US 322 West."], "19": ["Take the exit toward Lewistown."], "20": ["Take exit 67 B-A toward Lewistown."], "21": ["Take the US 322 West exit toward Lewistown."], "22": ["Take exit 67 B-A onto US 322 West toward Lewistown/State College."], "23": ["Take the White Marsh Boulevard exit."], "25": ["Take the White Marsh Boulevard exit onto MD 43 East."], "27": ["Take the White Marsh Boulevard exit toward White Marsh."], "29": ["Take the White Marsh Boulevard exit onto MD 43 East toward White Marsh."]}}, "exit_visual": {"phrases": {"0": "Avfart <EXIT_NUMBERS>"}, "example_phrases": {"0": ["Exit 1A"]}}, "keep": {"phrases": {"0": "H<PERSON>ll <RELATIVE_DIRECTION> i korsningen.", "1": "<PERSON><PERSON><PERSON> <RELATIVE_DIRECTION> för att ta avfart <NUMBER_SIGN>.", "2": "H<PERSON>ll <RELATIVE_DIRECTION> för att ta <STREET_NAMES>.", "3": "<PERSON><PERSON>ll <RELATIVE_DIRECTION> för att ta avfart <NUMBER_SIGN> in på <STREET_NAMES>.", "4": "H<PERSON>ll <RELATIVE_DIRECTION> mot <TOWARD_SIGN>.", "5": "<PERSON><PERSON><PERSON> <RELATIVE_DIRECTION> för att ta avfart <NUMBER_SIGN> mot <TOWARD_SIGN>.", "6": "H<PERSON>ll <RELATIVE_DIRECTION>för att ta <STREET_NAMES> mot <TOWARD_SIGN>.", "7": "<PERSON><PERSON><PERSON> <RELATIVE_DIRECTION> för att ta avfart <NUMBER_SIGN> in på <STREET_NAMES> mot <TOWARD_SIGN>."}, "empty_street_name_labels": ["gångvägen", "cykelbanan", "terrängcykelbanan", "the crosswalk", "the stairs", "the bridge", "the tunnel"], "relative_directions": ["vänster", "rakt fram", "<PERSON><PERSON><PERSON>"], "example_phrases": {"0": ["Keep left at the fork.", "Keep straight at the fork.", "Keep right at the fork."], "1": ["Keep right to take exit 62."], "2": ["Keep right to take I 895 South."], "3": ["Keep right to take exit 62 onto I 895 South."], "4": ["Keep right toward Annapolis."], "5": ["Keep right to take exit 62 toward Annapolis."], "6": ["Keep right to take I 895 South toward Annapolis."], "7": ["Keep right to take exit 62 onto I 895 South toward Annapolis."]}}, "keep_to_stay_on": {"phrases": {"0": "<PERSON><PERSON><PERSON> <RELATIVE_DIRECTION> för att stanna kvar på  <STREET_NAMES>.", "1": "<PERSON><PERSON>ll <RELATIVE_DIRECTION> för att ta avfart <NUMBER_SIGN> för att stanna kvar på <STREET_NAMES>.", "2": "<PERSON><PERSON><PERSON> <RELATIVE_DIRECTION> för att stanna kvar på <STREET_NAMES> mot <TOWARD_SIGN>.", "3": "<PERSON><PERSON>ll <RELATIVE_DIRECTION> för att ta avfart <NUMBER_SIGN> för att stanna kvar på <STREET_NAMES> mot <TOWARD_SIGN>."}, "empty_street_name_labels": ["gångvägen", "cykelbanan", "terrängcykelbanan", "the crosswalk", "the stairs", "the bridge", "the tunnel"], "relative_directions": ["vänster", "rakt fram", "<PERSON><PERSON><PERSON>"], "example_phrases": {"0": ["Keep left to stay on I 95 South.", "Keep straight to stay on I 95 South.", "Keep right to stay on I 95 South."], "1": ["Keep left to take exit 62 to stay on I 95 South."], "2": ["Keep left to stay on I 95 South toward Baltimore."], "3": ["Keep left to take exit 62 to stay on I 95 South toward Baltimore."]}}, "keep_to_stay_on_verbal": {"phrases": {"0": "<PERSON><PERSON><PERSON> <RELATIVE_DIRECTION> för att stanna kvar på <STREET_NAMES>.", "1": "<PERSON><PERSON>ll <RELATIVE_DIRECTION> för att ta avfart <NUMBER_SIGN> för att stanna kvar på <STREET_NAMES>.", "2": "<PERSON><PERSON><PERSON> <RELATIVE_DIRECTION> för att stanna kvar på <STREET_NAMES> mot <TOWARD_SIGN>.", "3": "<PERSON><PERSON>ll <RELATIVE_DIRECTION> för att ta avfart <NUMBER_SIGN> för att stanna kvar på <STREET_NAMES> mot <TOWARD_SIGN>."}, "empty_street_name_labels": ["gångvägen", "cykelbanan", "terrängcykelbanan", "the crosswalk", "the stairs", "the bridge", "the tunnel"], "relative_directions": ["vänster", "rakt fram", "<PERSON><PERSON><PERSON>"], "example_phrases": {"0": ["Keep left to stay on Interstate 95 South.", "Keep straight to stay on Interstate 95 South.", "Keep right to stay on Interstate 95 South."], "1": ["Keep left to take exit 62 to stay on Interstate 95 South."], "2": ["Keep left to stay on I 95 South toward Baltimore."], "3": ["Keep left to take exit 62 to stay on Interstate 95 South toward Baltimore."]}}, "keep_verbal": {"phrases": {"0": "H<PERSON>ll <RELATIVE_DIRECTION> i korsningen.", "1": "<PERSON><PERSON><PERSON> <RELATIVE_DIRECTION> för att ta avfart <NUMBER_SIGN>.", "2": "H<PERSON>ll <RELATIVE_DIRECTION> för att ta <STREET_NAMES>.", "3": "<PERSON><PERSON>ll <RELATIVE_DIRECTION> för att ta avfart <NUMBER_SIGN> in på  <STREET_NAMES>.", "4": "H<PERSON>ll <RELATIVE_DIRECTION> mot <TOWARD_SIGN>.", "5": "<PERSON><PERSON><PERSON> <RELATIVE_DIRECTION> för att ta avfart <NUMBER_SIGN> mot <TOWARD_SIGN>.", "6": "H<PERSON>ll <RELATIVE_DIRECTION> för att ta <STREET_NAMES> mot <TOWARD_SIGN>.", "7": "<PERSON><PERSON><PERSON> <RELATIVE_DIRECTION> för att ta avfart <NUMBER_SIGN> in på <STREET_NAMES> mot <TOWARD_SIGN>."}, "empty_street_name_labels": ["gångvägen", "cykelbanan", "terrängcykelbanan", "the crosswalk", "the stairs", "the bridge", "the tunnel"], "relative_directions": ["vänster", "rakt fram", "<PERSON><PERSON><PERSON>"], "example_phrases": {"0": ["Keep left at the fork.", "Keep straight at the fork.", "Keep right at the fork."], "1": ["Keep right to take exit 62."], "2": ["Keep right to take Interstate 8 95 South."], "3": ["Keep right to take exit 62 onto Interstate 8 95 South."], "4": ["Keep right toward Annapolis."], "5": ["Keep right to take exit 62 toward Annapolis."], "6": ["Keep right to take Interstate 8 95 South toward Annapolis."], "7": ["Keep right to take exit 62 onto Interstate 8 95 South toward Annapolis."]}}, "merge": {"phrases": {"0": "<PERSON><PERSON><PERSON> in.", "1": "<PERSON><PERSON><PERSON> in till <RELATIVE_DIRECTION>.", "2": "<PERSON><PERSON><PERSON> in på <STREET_NAMES>.", "3": "<PERSON><PERSON><PERSON> in till <RELATIVE_DIRECTION> in på <STREET_NAMES>.", "4": "<PERSON><PERSON><PERSON> in mot <TOWARD_SIGN>.", "5": "<PERSON><PERSON><PERSON> till <RELATIVE_DIRECTION> mot <TOWARD_SIGN>."}, "relative_directions": ["vänster", "<PERSON><PERSON><PERSON>"], "empty_street_name_labels": ["gångvägen", "cykelbanan", "terrängcykelbanan", "the crosswalk", "the stairs", "the bridge", "the tunnel"], "example_phrases": {"0": ["Merge."], "1": ["<PERSON><PERSON> left."], "2": ["Merge onto I 76 West/Pennsylvania Turnpike."], "3": ["Merge right onto I 83 South."], "4": ["Merge toward Baltimore."], "5": ["Merge right toward Baltimore."]}}, "merge_verbal": {"phrases": {"0": "<PERSON><PERSON><PERSON> in.", "1": "<PERSON><PERSON><PERSON> in till <RELATIVE_DIRECTION>.", "2": "<PERSON><PERSON><PERSON> in på <STREET_NAMES>.", "3": "<PERSON><PERSON><PERSON> in till <RELATIVE_DIRECTION> <PERSON><PERSON><PERSON> in på <STREET_NAMES>.", "4": "<PERSON><PERSON><PERSON> in mot <TOWARD_SIGN>.", "5": "<PERSON><PERSON><PERSON> in till <RELATIVE_DIRECTION> mot <TOWARD_SIGN>."}, "relative_directions": ["vänster", "<PERSON><PERSON><PERSON>"], "empty_street_name_labels": ["gångvägen", "cykelbanan", "terrängcykelbanan", "the crosswalk", "the stairs", "the bridge", "the tunnel"], "example_phrases": {"0": ["Merge."], "1": ["<PERSON><PERSON> left."], "2": ["Merge onto I 76 West/Pennsylvania Turnpike."], "3": ["Merge right onto I 83 South."], "4": ["Merge toward Baltimore."], "5": ["Merge right toward Baltimore."]}}, "post_transition_transit_verbal": {"phrases": {"0": "<PERSON>sa<TRANSIT_STOP_COUNT> <TRANSIT_STOP_COUNT_LABEL>."}, "transit_stop_count_labels": {"one": "stopp", "other": "stopp"}, "example_phrases": {"0": ["Travel 1 stop.", "Travel 3 stops."]}}, "post_transition_verbal": {"phrases": {"0": "Fortsätt i <LENGTH>.", "1": "Fortsätt på <STREET_NAMES> i <LENGTH>."}, "empty_street_name_labels": ["gångvägen", "cykelbanan", "terrängcykelbanan", "the crosswalk", "the stairs", "the bridge", "the tunnel"], "metric_lengths": ["<KILOMETERS> kilometer", "1 kilometer", "<METERS> meter", "<PERSON><PERSON> än 10 meter"], "us_customary_lengths": ["<MILES> engelska mil", "1 engelsk mil", "en halv engelsk mil", "en kvarts engelsk mit", "<FEET> fot", "mindre än 10 fot"], "example_phrases": {"0": ["Continue for 300 feet.", "Continue for 9 miles."], "1": ["Continue on Pennsylvania 7 43 for 6.2 miles.", "Continue on Main Street, Vermont 30 for 1 tenth of a mile."]}}, "ramp": {"phrases": {"0": "Ta påfarten till <RELATIVE_DIRECTION>.", "1": "Ta <BRANCH_SIGN>p<PERSON><PERSON><PERSON> till <RELATIVE_DIRECTION>.", "2": "Ta påfarten till <RELATIVE_DIRECTION> mot <TOWARD_SIGN>.", "3": "Ta <BRANCH_SIGN>p<PERSON><PERSON><PERSON> till <RELATIVE_DIRECTION> mot <TOWARD_SIGN>.", "4": "Ta <NAME_SIGN>p<PERSON><PERSON><PERSON> till <RELATIVE_DIRECTION>.", "5": "Sväng <RELATIVE_DIRECTION> för att ta påfarten.", "6": "Sväng <RELATIVE_DIRECTION> för att ta <BRANCH_SIGN>p<PERSON><PERSON><PERSON>.", "7": "Sväng <RELATIVE_DIRECTION> för att ta påfarten mot <TOWARD_SIGN>.", "8": "Sväng <RELATIVE_DIRECTION> för att ta <BRANCH_SIGN>på<PERSON><PERSON> mot <TOWARD_SIGN>.", "9": "Sväng <RELATIVE_DIRECTION> för att ta <NAME_SIGN>p<PERSON><PERSON><PERSON>.", "10": "<PERSON> p<PERSON>.", "11": "Ta <BRANCH_SIGN>p<PERSON><PERSON><PERSON>.", "12": "Ta påfarten mot <TOWARD_SIGN>.", "13": "Ta <BRANCH_SIGN>p<PERSON><PERSON><PERSON> mot <TOWARD_SIGN>.", "14": "Ta <NAME_SIGN>p<PERSON><PERSON><PERSON>."}, "relative_directions": ["vänster", "<PERSON><PERSON><PERSON>"], "example_phrases": {"0": ["Take the ramp on the left.", "Take the ramp on the right."], "1": ["Take the I 95 ramp on the right."], "2": ["Take the ramp on the left toward JFK."], "3": ["Take the South Conduit Avenue ramp on the left toward JFK."], "4": ["Take the Gettysburg Pike ramp on the right."], "5": ["Turn left to take the ramp.", "Turn right to take the ramp."], "6": ["Turn left to take the PA 283 West ramp."], "7": ["Turn left to take the ramp toward Harrisburg/Harrisburg International Airport."], "8": ["Turn left to take the PA 283 West ramp toward Harrisburg/Harrisburg International Airport."], "9": ["Turn right to take the Gettysburg Pike ramp."], "10": ["Take the ramp."], "11": ["Take the I 95 ramp."], "12": ["Take the ramp toward JFK."], "13": ["Take the Soutch Conduit Avenue ramp toward JFK."], "14": ["Take the Gettysburg ramp."]}}, "ramp_straight": {"phrases": {"0": "<PERSON><PERSON><PERSON> rakt fram för att ta påfarten.", "1": "<PERSON><PERSON><PERSON> rakt fram för att ta <BRANCH_SIGN>p<PERSON><PERSON><PERSON>.", "2": "<PERSON><PERSON><PERSON> rakt fram för att ta påfarten mot <TOWARD_SIGN>.", "3": "<PERSON><PERSON><PERSON> rakt fram för att ta <BRANCH_SIGN>på<PERSON><PERSON> mot <TOWARD_SIGN>.", "4": "<PERSON><PERSON><PERSON> rakt fram för att ta <NAME_SIGN>p<PERSON><PERSON><PERSON>."}, "example_phrases": {"0": ["Stay straight to take the ramp."], "1": ["Stay straight to take the US 322 East ramp."], "2": ["Stay straight to take the ramp toward Hershey."], "3": ["Stay straight to take the US 322 East/US 422 East/US 522 East/US 622 East ramp toward Hershey/Palmdale/Palmyra/Campbelltown."], "4": ["Stay straight to take the Gettysburg Pike ramp."]}}, "ramp_straight_verbal": {"phrases": {"0": "<PERSON><PERSON><PERSON> rakt fram för att ta påfarten.", "1": "<PERSON><PERSON><PERSON> rakt fram för att ta<BRANCH_SIGN> p<PERSON><PERSON><PERSON>.", "2": "<PERSON><PERSON><PERSON> rakt fram för att ta påfarten mot <TOWARD_SIGN>.", "3": "<PERSON><PERSON><PERSON> rakt fram för att ta<BRANCH_SIGN> på<PERSON><PERSON> mot <TOWARD_SIGN>.", "4": "<PERSON><PERSON><PERSON> rakt fram för att ta <NAME_SIGN>p<PERSON><PERSON><PERSON>."}, "example_phrases": {"0": ["Stay straight to take the ramp."], "1": ["Stay straight to take the U.S. 3 22 East ramp."], "2": ["Stay straight to take the ramp toward Hershey."], "3": ["Stay straight to take the U.S. 3 22 East, U.S. 4 22 East ramp toward Hershey, Palmdale."], "4": ["Stay straight to take the Gettysburg Pike ramp."]}}, "ramp_verbal": {"phrases": {"0": "Ta påfarten till <RELATIVE_DIRECTION>.", "1": "Ta <BRANCH_SIGN>p<PERSON><PERSON><PERSON> till <RELATIVE_DIRECTION>.", "2": "Ta påfarten till <RELATIVE_DIRECTION> mot <TOWARD_SIGN>.", "3": "Ta <BRANCH_SIGN>p<PERSON><PERSON><PERSON> till <RELATIVE_DIRECTION> mot <TOWARD_SIGN>.", "4": "Ta <NAME_SIGN>p<PERSON><PERSON><PERSON> till <RELATIVE_DIRECTION>.", "5": "Sväng <RELATIVE_DIRECTION> för att ta påfarten.", "6": "Sväng <RELATIVE_DIRECTION> för att ta <BRANCH_SIGN>p<PERSON><PERSON><PERSON>.", "7": "Sväng <RELATIVE_DIRECTION> för att ta påfarten mot <TOWARD_SIGN>.", "8": "Sväng <RELATIVE_DIRECTION> för att ta <BRANCH_SIGN>på<PERSON><PERSON> mot <TOWARD_SIGN>.", "9": "Sväng <RELATIVE_DIRECTION> för att ta <NAME_SIGN>p<PERSON><PERSON><PERSON>.", "10": "<PERSON> p<PERSON>.", "11": "Ta <BRANCH_SIGN>p<PERSON><PERSON><PERSON>.", "12": "Ta påfarten mot <TOWARD_SIGN>.", "13": "Ta <BRANCH_SIGN>p<PERSON><PERSON><PERSON> mot <TOWARD_SIGN>.", "14": "Ta <NAME_SIGN>p<PERSON><PERSON><PERSON>."}, "relative_directions": ["vänster", "<PERSON><PERSON><PERSON>"], "example_phrases": {"0": ["Take the ramp on the left.", "Take the ramp on the right."], "1": ["Take the Interstate 95 ramp on the right."], "2": ["Take the ramp on the left toward JFK."], "3": ["Take the South Conduit Avenue ramp on the left toward JFK."], "4": ["Take the Gettysburg Pike ramp on the right."], "5": ["Turn left to take the ramp.", "Turn right to take the ramp."], "6": ["Turn left to take the Pennsylvania 2 83 West ramp."], "7": ["Turn left to take the ramp toward Harrisburg/Harrisburg International Airport."], "8": ["Turn left to take the Pennsylvania 2 83 West ramp toward Harrisburg, Harrisburg International Airport."], "9": ["Turn right to take the Gettysburg Pike ramp."], "10": ["Take the ramp."], "11": ["Take the Interstate 95 ramp."], "12": ["Take the ramp toward JFK."], "13": ["Take the South Conduit Avenue ramp toward JFK."], "14": ["Take the Gettysburg Pike ramp."]}}, "sharp": {"phrases": {"0": "<PERSON><PERSON><PERSON> en skarp sväng till <RELATIVE_DIRECTION>.", "1": "<PERSON><PERSON><PERSON> en skarp sväng till <RELATIVE_DIRECTION> in på <STREET_NAMES>.", "2": "<PERSON><PERSON><PERSON> en skarp sväng till <RELATIVE_DIRECTION> in på <BEGIN_STREET_NAMES>. Fortsätt på <STREET_NAMES>.", "3": "<PERSON><PERSON><PERSON> en skarp sväng till <RELATIVE_DIRECTION> för att stanna kvar på  <STREET_NAMES>.", "4": "<PERSON><PERSON><PERSON> en skarp sväng till <RELATIVE_DIRECTION> i <JUNCTION_NAME>.", "5": "<PERSON><PERSON><PERSON> en skarp sväng till <RELATIVE_DIRECTION> mot <TOWARD_SIGN>."}, "empty_street_name_labels": ["gångvägen", "cykelbanan", "terrängcykelbanan", "the crosswalk", "the stairs", "the bridge", "the tunnel"], "relative_directions": ["vänster", "<PERSON><PERSON><PERSON>"], "example_phrases": {"0": ["Make a sharp left."], "1": ["Make a sharp right onto Flatbush Avenue."], "2": ["Make a sharp left onto North Bond Street/US 1 Business/MD 924. Continue on MD 924."], "3": ["Make a sharp right to stay on Sunstone Drive."], "4": ["Make a sharp right at Mannenbashi East."], "5": ["Make a sharp left toward Baltimore."]}}, "sharp_verbal": {"phrases": {"0": "<PERSON><PERSON><PERSON> en skarp sväng till <RELATIVE_DIRECTION>.", "1": "<PERSON><PERSON><PERSON> en skarp sväng till <RELATIVE_DIRECTION> in på <STREET_NAMES>.", "2": "<PERSON><PERSON><PERSON> en skarp sväng till <RELATIVE_DIRECTION> in på <BEGIN_STREET_NAMES>.", "3": "<PERSON><PERSON><PERSON> en skarp sväng till <RELATIVE_DIRECTION> för att stanna kvar på  <STREET_NAMES>.", "4": "<PERSON><PERSON><PERSON> en skarp sväng till <RELATIVE_DIRECTION> i <JUNCTION_NAME>.", "5": "<PERSON><PERSON><PERSON> en skarp sväng till <RELATIVE_DIRECTION> mot <TOWARD_SIGN>."}, "empty_street_name_labels": ["gångvägen", "cykelbanan", "terrängcykelbanan", "the crosswalk", "the stairs", "the bridge", "the tunnel"], "relative_directions": ["vänster", "<PERSON><PERSON><PERSON>"], "example_phrases": {"0": ["Make a sharp left."], "1": ["Make a sharp right onto Flatbush Avenue."], "2": ["Make a sharp left onto North Bond Street, U.S. 1 Business."], "3": ["Make a sharp right to stay on Sunstone Drive."], "4": ["Make a sharp right at Mannenbashi East."], "5": ["Make a sharp left toward Baltimore."]}}, "start": {"phrases": {"0": "<PERSON><PERSON><PERSON> <CARDINAL_DIRECTION>.", "1": "<PERSON><PERSON><PERSON> <CARDINAL_DIRECTION> på <STREET_NAMES>.", "2": "<PERSON><PERSON><PERSON> <CARDINAL_DIRECTION> på <BEGIN_STREET_NAMES>. Fortsätt på <STREET_NAMES>.", "4": "<PERSON><PERSON><PERSON> <CARDINAL_DIRECTION>.", "5": "<PERSON><PERSON><PERSON> <CARDINAL_DIRECTION> på <STREET_NAMES>.", "6": "<PERSON><PERSON><PERSON> <CARDINAL_DIRECTION> on <BEGIN_STREET_NAMES>. Fortsätt på <STREET_NAMES>.", "8": "Gå <CARDINAL_DIRECTION>.", "9": "Gå <CARDINAL_DIRECTION> på <STREET_NAMES>.", "10": "Gå <CARDINAL_DIRECTION> på <BEGIN_STREET_NAMES>. Fortsätt på <STREET_NAMES>.", "16": "Cycla <CARDINAL_DIRECTION>.", "17": "Cy<PERSON>la <CARDINAL_DIRECTION> på <STREET_NAMES>.", "18": "Cy<PERSON>la <CARDINAL_DIRECTION> på <BEGIN_STREET_NAMES>. Fortsätt <STREET_NAMES>."}, "cardinal_directions": ["<PERSON><PERSON><PERSON>", "mot nordöst", "österut", "mot sydöst", "<PERSON><PERSON><PERSON><PERSON>", "mot sydväst", "västerut", "mot nordväst"], "empty_street_name_labels": ["gångvägen", "cykelbanan", "terrängcykelbanan", "the crosswalk", "the stairs", "the bridge", "the tunnel"], "example_phrases": {"0": ["Head east.", "Head north."], "1": ["Head southwest on 5th Avenue.", "Head west on the walkway", "Head east on the cycleway", "Head north on the mountain bike trail"], "2": ["Head south on North Prince Street/US 222/PA 272. Continue on US 222/PA 272."], "4": ["Drive east.", "Drive north."], "5": ["Drive southwest on 5th Avenue."], "6": ["Drive south on North Prince Street/US 222/PA 272. Continue on US 222/PA 272."], "8": ["Walk east.", "Walk north."], "9": ["Walk southwest on 5th Avenue.", "Walk west on the walkway"], "10": ["Walk south on North Prince Street/US 222/PA 272. Continue on US 222/PA 272."], "16": ["Bike east.", "Bike north."], "17": ["Bike southwest on 5th Avenue.", "Bike east on the cycleway", "Bike north on the mountain bike trail"], "18": ["Bike south on North Prince Street/US 222/PA 272. Continue on US 222/PA 272."]}}, "start_verbal": {"phrases": {"0": "<PERSON><PERSON><PERSON> <CARDINAL_DIRECTION>.", "1": "<PERSON><PERSON><PERSON> <CARDINAL_DIRECTION> i <LENGTH>.", "2": "<PERSON><PERSON><PERSON> <CARDINAL_DIRECTION> på <STREET_NAMES>.", "3": "<PERSON><PERSON><PERSON> <CARDINAL_DIRECTION> på <STREET_NAMES> i <LENGTH>.", "4": "<PERSON><PERSON><PERSON> <CARDINAL_DIRECTION> på <BEGIN_STREET_NAMES>.", "5": "<PERSON><PERSON><PERSON> <CARDINAL_DIRECTION>.", "6": "<PERSON><PERSON><PERSON> <CARDINAL_DIRECTION> i <LENGTH>.", "7": "<PERSON><PERSON><PERSON> <CARDINAL_DIRECTION>  <STREET_NAMES>.", "8": "<PERSON><PERSON><PERSON> <CARDINAL_DIRECTION> på <STREET_NAMES> i <LENGTH>.", "9": "<PERSON><PERSON><PERSON> <CARDINAL_DIRECTION> på <BEGIN_STREET_NAMES>.", "10": "Gå <CARDINAL_DIRECTION>.", "11": "Gå <CARDINAL_DIRECTION> i <LENGTH>.", "12": "Gå <CARDINAL_DIRECTION> på <STREET_NAMES>.", "13": "Gå <CARDINAL_DIRECTION> på <STREET_NAMES> i <LENGTH>.", "14": "Gå<CARDINAL_DIRECTION> på <BEGIN_STREET_NAMES>.", "15": "Cykla <CARDINAL_DIRECTION>.", "16": "Cykla <CARDINAL_DIRECTION> i <LENGTH>.", "17": "Cykla <CARDINAL_DIRECTION> på <STREET_NAMES>.", "18": "Cy<PERSON> <CARDINAL_DIRECTION> på <STREET_NAMES> i <LENGTH>.", "19": "Cy<PERSON> <CARDINAL_DIRECTION> på <BEGIN_STREET_NAMES>."}, "cardinal_directions": ["<PERSON><PERSON><PERSON>", "mot nordöst", "österut", "mot sydöst", "<PERSON><PERSON><PERSON><PERSON>", "mot sydväst", "västerut", "mot nordväst"], "empty_street_name_labels": ["gångvägen", "cykelbanan", "terrängcykelbanan", "the crosswalk", "the stairs", "the bridge", "the tunnel"], "metric_lengths": ["<KILOMETERS> kilometer", "1 kilometer", "<METERS> meter", "<PERSON><PERSON> än 10 meter"], "us_customary_lengths": ["<MILES> engelska mil", "1 engelsk mil", "en halv engelsk mil", "en kvarts engelsk mil", "<FEET> fot", "mindre än 10 fot"], "example_phrases": {"0": ["Head east.", "Head north."], "1": ["Head east for a half mile.", "Head north for 1 kilometer."], "2": ["Head southwest on 5th Avenue."], "3": ["Head southwest on 5th Avenue for 1 tenth of a mile."], "4": ["Head south on North Prince Street, U.S. 2 22."], "5": ["Drive east.", "Drive north."], "6": ["Drive east for a half mile.", "Drive north for 1 kilometer."], "7": ["Drive southwest on 5th Avenue."], "8": ["Drive southwest on 5th Avenue for 1 tenth of a mile."], "9": ["Drive south on North Prince Street, U.S. 2 22."], "10": ["Walk east.", "Walk north."], "11": ["Walk east for a half mile.", "Walk north for 1 kilometer."], "12": ["Walk southwest on 5th Avenue."], "13": ["Walk southwest on 5th Avenue for 1 tenth of a mile."], "14": ["Walk south on North Prince Street, U.S. 2 22."], "15": ["Bike east.", "Bike north."], "16": ["Bike east for a half mile.", "Bike north for 1 kilometer."], "17": ["Bike southwest on 5th Avenue."], "18": ["Bike southwest on 5th Avenue for 1 tenth of a mile."], "19": ["Bike south on North Prince Street, U.S. 2 22."]}}, "transit": {"phrases": {"0": "Ta <TRANSIT_NAME>. (<TRANSIT_STOP_COUNT> <TRANSIT_STOP_COUNT_LABEL>)", "1": "Ta <TRANSIT_NAME> mot <TRANSIT_HEADSIGN>. (<TRANSIT_STOP_COUNT> <TRANSIT_STOP_COUNT_LABEL>)"}, "empty_transit_name_labels": ["spårvagnen", "tunnelbanan", "t<PERSON><PERSON>", "bussen", "<PERSON><PERSON><PERSON><PERSON>", "linbanan", "gondolen", "bergbanan"], "transit_stop_count_labels": {"one": "stopp", "other": "stopp"}, "example_phrases": {"0": ["Take the New Haven. (1 stop)", "Take the metro. (2 stops)", "Take the bus. (12 stops)"], "1": ["Take the F toward JAMAICA - 179 ST. (10 stops)", "Take the ferry toward Staten Island. (1 stop)"]}}, "transit_connection_destination": {"phrases": {"0": "Lämna stationen.", "1": "Lämna <TRANSIT_STOP>.", "2": "Lämna <TRANSIT_STOP> <STATION_LABEL>."}, "station_label": "Station", "example_phrases": {"0": ["Exit the station."], "1": ["Exit the Embarcadero Station."], "2": ["Exit the 8 St - NYU Station."]}}, "transit_connection_destination_verbal": {"phrases": {"0": "Lämna stationen.", "1": "Lämna <TRANSIT_STOP>.", "2": "Lämna <TRANSIT_STOP> <STATION_LABEL>."}, "station_label": "Station", "example_phrases": {"0": ["Exit the station."], "1": ["Exit the Embarcadero Station."], "2": ["Exit the 8 St - NYU Station."]}}, "transit_connection_start": {"phrases": {"0": "<PERSON><PERSON> in på stationen.", "1": "Gå in på <TRANSIT_STOP>.", "2": "<PERSON><PERSON> in på <TRANSIT_STOP> <STATION_LABEL>."}, "station_label": "Station", "example_phrases": {"0": ["Enter the station."], "1": ["Enter the Embarcadero Station."], "2": ["Enter the 8 St - NYU Station."]}}, "transit_connection_start_verbal": {"phrases": {"0": "<PERSON><PERSON> in på stationen.", "1": "Gå in på <TRANSIT_STOP>.", "2": "<PERSON><PERSON> in på <TRANSIT_STOP> <STATION_LABEL>."}, "station_label": "Station", "example_phrases": {"0": ["Enter the station."], "1": ["Enter the Embarcadero Station."], "2": ["Enter the 8 St - NYU Station."]}}, "transit_connection_transfer": {"phrases": {"0": "Byt på stationen.", "1": "Byt på <TRANSIT_STOP>.", "2": "Byt på  <TRANSIT_STOP> <STATION_LABEL>."}, "station_label": "Station", "example_phrases": {"0": ["Transfer at the station."], "1": ["Transfer at the Embarcadero Station."], "2": ["Transfer at the 8 St - NYU Station."]}}, "transit_connection_transfer_verbal": {"phrases": {"0": "Byt på stationen.", "1": "Byt på <TRANSIT_STOP>.", "2": "Byt på <TRANSIT_STOP> <STATION_LABEL>."}, "station_label": "Station", "example_phrases": {"0": ["Transfer at the station."], "1": ["Transfer at the Embarcadero Station."], "2": ["Transfer at the 8 St - NYU Station."]}}, "transit_remain_on": {"phrases": {"0": "<PERSON><PERSON> p<PERSON>  <TRANSIT_NAME>. (<TRANSIT_STOP_COUNT> <TRANSIT_STOP_COUNT_LABEL>)", "1": "<PERSON><PERSON> <TRANSIT_NAME> mot <TRANSIT_HEADSIGN>. (<TRANSIT_STOP_COUNT> <TRANSIT_STOP_COUNT_LABEL>)"}, "empty_transit_name_labels": ["spårvagnen ", "tunnelbanan", "t<PERSON><PERSON>", "bussen", "<PERSON><PERSON><PERSON><PERSON>", "linbanan", "gondolen", "bergbanan"], "transit_stop_count_labels": {"one": "stopp", "other": "stopp"}, "example_phrases": {"0": ["Remain on the New Haven. (1 stop)", "<PERSON><PERSON><PERSON> on the train. (3 stops)"], "1": ["Remain on the F toward JAMAICA - 179 ST. (10 stops)"]}}, "transit_remain_on_verbal": {"phrases": {"0": "<PERSON><PERSON> <TRANSIT_NAME>.", "1": "<PERSON><PERSON> <TRANSIT_NAME> mot <TRANSIT_HEADSIGN>."}, "empty_transit_name_labels": ["spårvagnen", "tunnelbanan", "t<PERSON><PERSON>", "bussen", "<PERSON><PERSON><PERSON><PERSON>", "linbanan", "gondolen", "bergbanan"], "example_phrases": {"0": ["<PERSON><PERSON><PERSON> on the New Haven."], "1": ["Remain on the F toward JAMAICA - 179 ST."]}}, "transit_transfer": {"phrases": {"0": "Byt för att ta <TRANSIT_NAME>. (<TRANSIT_STOP_COUNT> <TRANSIT_STOP_COUNT_LABEL>)", "1": "Byt för att ta <TRANSIT_NAME> mot <TRANSIT_HEADSIGN>. (<TRANSIT_STOP_COUNT> <TRANSIT_STOP_COUNT_LABEL>)"}, "empty_transit_name_labels": ["spårvagnen", "tunnelbanan", "t<PERSON><PERSON>", "bussen", "<PERSON><PERSON><PERSON><PERSON>", "linbanan", "gondolen", "bergbanan"], "transit_stop_count_labels": {"one": "stopp", "other": "stopp"}, "example_phrases": {"0": ["Transfer to take the New Haven. (1 stop)", "Transfer to take the tram. (4 stops)"], "1": ["Transfer to take the F toward JAMAICA - 179 ST. (10 stops)"]}}, "transit_transfer_verbal": {"phrases": {"0": "Byt för att ta <TRANSIT_NAME>.", "1": "Byt för att ta <TRANSIT_NAME> mot <TRANSIT_HEADSIGN>."}, "empty_transit_name_labels": ["spårvagnen", "tunnelbanan", "t<PERSON><PERSON>", "bussen", "<PERSON><PERSON><PERSON><PERSON>", "linbanan", "gondolen", "bergbanan"], "example_phrases": {"0": ["Transfer to take the New Haven."], "1": ["Transfer to take the F toward JAMAICA - 179 ST."]}}, "transit_verbal": {"phrases": {"0": "Ta <TRANSIT_NAME>.", "1": "Ta <TRANSIT_NAME> mot <TRANSIT_HEADSIGN>."}, "empty_transit_name_labels": ["spårvagnen", "tunnelbanan", "t<PERSON><PERSON>", "bussen", "<PERSON><PERSON><PERSON><PERSON>", "linbanan", "gondolen", "bergbanan"], "example_phrases": {"0": ["Take the New Haven."], "1": ["Take the F toward JAMAICA - 179 ST."]}}, "turn": {"phrases": {"0": "Sväng <RELATIVE_DIRECTION>.", "1": "Sväng <RELATIVE_DIRECTION> in på <STREET_NAMES>.", "2": "Sväng <RELATIVE_DIRECTION> in på <BEGIN_STREET_NAMES>. Fortsätt på <STREET_NAMES>.", "3": "Sväng <RELATIVE_DIRECTION> för att stanna kvar på <STREET_NAMES>.", "4": "Sväng <RELATIVE_DIRECTION> vid <JUNCTION_NAME>.", "5": "Sväng <RELATIVE_DIRECTION> mot <TOWARD_SIGN>."}, "empty_street_name_labels": ["gångvägen", "cykelbanan", "terrängcykelbanan", "the crosswalk", "the stairs", "the bridge", "the tunnel"], "relative_directions": ["vänster", "<PERSON><PERSON><PERSON>"], "example_phrases": {"0": ["Turn left."], "1": ["Turn right onto Flatbush Avenue."], "2": ["Turn left onto North Bond Street/US 1 Business/MD 924. Continue on MD 924."], "3": ["Turn right to stay on Sunstone Drive."], "4": ["Turn right at Mannenbashi East."], "5": ["Turn left toward Baltimore."]}}, "turn_verbal": {"phrases": {"0": "Sväng <RELATIVE_DIRECTION>.", "1": "Sväng <RELATIVE_DIRECTION> in på <STREET_NAMES>.", "2": "Sväng <RELATIVE_DIRECTION> in på <BEGIN_STREET_NAMES>.", "3": "Sväng <RELATIVE_DIRECTION> för att stanna kvar på <STREET_NAMES>.", "4": "Sväng <RELATIVE_DIRECTION> i <JUNCTION_NAME>.", "5": "Sväng <RELATIVE_DIRECTION> mot <TOWARD_SIGN>."}, "empty_street_name_labels": ["gångvägen", "cykelbanan", "terrängcykelbanan", "the crosswalk", "the stairs", "the bridge", "the tunnel"], "relative_directions": ["vänster", "<PERSON><PERSON><PERSON>"], "example_phrases": {"0": ["Turn left."], "1": ["Turn right onto Flatbush Avenue."], "2": ["Turn left onto North Bond Street, U.S. 1 Business."], "3": ["Turn right to stay on Sunstone Drive."], "4": ["Turn right at Mannenbashi East."], "5": ["Turn left toward Baltimore."]}}, "uturn": {"phrases": {"0": "G<PERSON><PERSON> en <RELATIVE_DIRECTION> U-sväng.", "1": "G<PERSON><PERSON> en <RELATIVE_DIRECTION> U-sväng in på <STREET_NAMES>.", "2": "G<PERSON>r en <RELATIVE_DIRECTION> U-sväng för att stanna kvar på <STREET_NAMES>.", "3": "G<PERSON><PERSON> en <RELATIVE_DIRECTION> U-sväng vid <CROSS_STREET_NAMES>.", "4": "G<PERSON><PERSON> en <RELATIVE_DIRECTION> U-sväng vid <CROSS_STREET_NAMES> in på <STREET_NAMES>.", "5": "Gör en <RELATIVE_DIRECTION> U-sväng vid <CROSS_STREET_NAMES> för att stanna kvar på <STREET_NAMES>.", "6": "G<PERSON><PERSON> en <RELATIVE_DIRECTION> U-sväng vid <JUNCTION_NAME>.", "7": "<PERSON><PERSON><PERSON> en <RELATIVE_DIRECTION> U-sväng mot <TOWARD_SIGN>."}, "empty_street_name_labels": ["gångvägen", "cykelbanan", "terrängcykelbanan", "the crosswalk", "the stairs", "the bridge", "the tunnel"], "relative_directions": ["vänster", "<PERSON><PERSON><PERSON>"], "example_phrases": {"0": ["Make a left U-turn."], "1": ["Make a right U-turn onto Bunker Hill Road."], "2": ["Make a left U-turn to stay on Bunker Hill Road."], "3": ["Make a left U-turn at Devonshire Road."], "4": ["Make a left U-turn at Devonshire Road onto Jonestown Road/US 22."], "5": ["Make a left U-turn at Devonshire Road to stay on Jonestown Road/US 22."], "6": ["Make a right U-turn at Mannenbashi East."], "7": ["Make a left U-turn toward Baltimore."]}}, "uturn_verbal": {"phrases": {"0": "G<PERSON><PERSON> en <RELATIVE_DIRECTION> U-sväng.", "1": "G<PERSON><PERSON> en <RELATIVE_DIRECTION> U-sväng in på <STREET_NAMES>.", "2": "G<PERSON>r en <RELATIVE_DIRECTION> U-sväng för att stanna kvar på <STREET_NAMES>.", "3": "G<PERSON><PERSON> en <RELATIVE_DIRECTION> U-sväng vid <CROSS_STREET_NAMES>.", "4": "G<PERSON><PERSON> en <RELATIVE_DIRECTION> U-sväng vid <CROSS_STREET_NAMES> in på <STREET_NAMES>.", "5": "Gör en <RELATIVE_DIRECTION> U-sväng vid <CROSS_STREET_NAMES> för att stanna kvar på <STREET_NAMES>.", "6": "G<PERSON><PERSON> en <RELATIVE_DIRECTION> U-sväng vid <JUNCTION_NAME>.", "7": "<PERSON><PERSON><PERSON> en <RELATIVE_DIRECTION> U-sväng mot <TOWARD_SIGN>."}, "empty_street_name_labels": ["gångvägen", "cykelbanan", "terrängcykelbanan", "the crosswalk", "the stairs", "the bridge", "the tunnel"], "relative_directions": ["vänster", "<PERSON><PERSON><PERSON>"], "example_phrases": {"0": ["Make a left U-turn."], "1": ["Make a right U-turn onto Bunker Hill Road."], "2": ["Make a left U-turn to stay on Bunker Hill Road."], "3": ["Make a left U-turn at Devonshire Road."], "4": ["Make a left U-turn at Devonshire Road onto Jonestown Road, U.S. 22."], "5": ["Make a left U-turn at Devonshire Road to stay on Jonestown Road, U.S. 22."], "6": ["Make a right U-turn at Mannenbashi East."], "7": ["Make a left U-turn toward Baltimore."]}}, "verbal_multi_cue": {"phrases": {"0": "<CURRENT_VERBAL_CUE> Därefter <NEXT_VERBAL_CUE>", "1": "<CURRENT_VERBAL_CUE> <PERSON><PERSON><PERSON><PERSON>, i <LENGTH>, <NEXT_VERBAL_CUE>"}, "metric_lengths": ["<KILOMETERS> kilometer", "1 kilometer", "<METERS> meter", "<PERSON><PERSON> än 10 meter"], "us_customary_lengths": ["<MILES> engelska mil", "1 engelsk mil", "en halv engelsk mil", "en kvarts engelsk mil", "<FEET> fot", "mindre än 10 fot"], "example_phrases": {"0": ["Bear right onto East Fayette Street. Then Turn right onto North Gay Street."], "1": ["Bear right onto East Fayette Street. Then, in 500 feet, Turn right onto North Gay Street."]}}, "approach_verbal_alert": {"phrases": {"0": "Om <LENGTH>, <CURRENT_VERBAL_CUE>"}, "metric_lengths": ["<KILOMETERS> kilometer", "1 kilometer", "<METERS> meter", "<PERSON><PERSON> än 10 meter"], "us_customary_lengths": ["<MILES> engelska mil", "1 engelsk mil", "en halv engelsk mil", "en kvarts engelsk mil", "<FEET> fot", "<PERSON><PERSON> än 10 meter"], "example_phrases": {"0": ["In a quarter mile, Turn right onto North Gay Street."]}}, "pass": {"phrases": {"0": "Pass <OBJECT_LABEL>.", "1": "Pass traffic signals on <OBJECT_LABEL>."}, "object_labels": ["the gate", "the bollards", "ways intersection"], "example_phrases": {"0": ["Pass the gate."]}}, "elevator": {"phrases": {"0": "Take the elevator.", "1": "Take the elevator to <LEVEL>."}, "example_phrases": {"0": ["Take the elevator."], "1": ["Take the elevator to Level 1."]}}, "steps": {"phrases": {"0": "Take the stairs.", "1": "Take the stairs to <LEVEL>."}, "example_phrases": {"0": ["Take the stairs."], "1": ["Take the stairs to Level 2."]}}, "escalator": {"phrases": {"0": "Take the escalator.", "1": "Take the escalator to <LEVEL>."}, "example_phrases": {"0": ["Take the escalator."], "1": ["Take the escalator to Level 3."]}}, "enter_building": {"phrases": {"0": "Enter the building.", "1": "Enter the building, and continue on <STREET_NAMES>."}, "empty_street_name_labels": ["gångvägen", "cykelbanan", "terrängcykelbanan", "the crosswalk"], "example_phrases": {"0": ["Enter the building."], "1": ["Enter the building, and continue on the walkway."]}}, "exit_building": {"phrases": {"0": "Exit the building.", "1": "Exit the building, and continue on <STREET_NAMES>."}, "empty_street_name_labels": ["gångvägen", "cykelbanan", "terrängcykelbanan", "the crosswalk"], "example_phrases": {"0": ["Exit the building."], "1": ["Exit the building, and continue on the walkway."]}}}}
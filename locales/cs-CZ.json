{"posix_locale": "cs_CZ.UTF-8", "aliases": ["cs"], "instructions": {"arrive": {"phrases": {"0": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>: <TIME>.", "1": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>: <TIME> do <TRANSIT_STOP>."}, "example_phrases": {"0": ["Arrive: 8:02 AM."], "1": ["Arrive: 8:02 AM at 8 St - NYU."]}}, "arrive_verbal": {"phrases": {"0": "Přijeďte v <TIME>.", "1": "Přijeďte v <TIME> do <TRANSIT_STOP>."}, "example_phrases": {"0": ["Arrive at 8:02 AM."], "1": ["Arrive at 8:02 AM at 8 St - NYU."]}}, "bear": {"phrases": {"0": "<PERSON><PERSON><PERSON> se <RELATIVE_DIRECTION>.", "1": "Dr<PERSON>te se <RELATIVE_DIRECTION> na <STREET_NAMES>.", "2": "Dr<PERSON><PERSON> se <RELATIVE_DIRECTION> na <BEGIN_STREET_NAMES>. Pokračujte na <STREET_NAMES>.", "3": "<PERSON><PERSON><PERSON> se <RELATIVE_DIRECTION>, abyste zůstali na <STREET_NAMES>.", "4": "Dr<PERSON>te se <RELATIVE_DIRECTION> na <JUNCTION_NAME>.", "5": "<PERSON><PERSON><PERSON> se <RELATIVE_DIRECTION> směrem na <TOWARD_SIGN>."}, "empty_street_name_labels": ["chodník", "cyklostezka", "stezka pro horská kola", "the crosswalk", "the stairs", "the bridge", "the tunnel"], "relative_directions": ["vlevo", "vpravo"], "example_phrases": {"0": ["Bear right."], "1": ["<PERSON> left onto Arlen Road."], "2": ["Bear right onto Belair Road/US 1 Business. Continue on US 1 Business."], "3": ["<PERSON> left to stay on US 15 South."], "4": ["Bear right at Mannenbashi East."], "5": ["Bear left toward Baltimore."]}}, "bear_verbal": {"phrases": {"0": "<PERSON><PERSON><PERSON> se <RELATIVE_DIRECTION>.", "1": "Dr<PERSON>te se <RELATIVE_DIRECTION> na <STREET_NAMES>.", "2": "Dr<PERSON><PERSON> se <RELATIVE_DIRECTION> na <BEGIN_STREET_NAMES>.", "3": "<PERSON><PERSON><PERSON> se <RELATIVE_DIRECTION>, abyste zůstali na <STREET_NAMES>.", "4": "Dr<PERSON>te se <RELATIVE_DIRECTION> na <JUNCTION_NAME>.", "5": "<PERSON><PERSON><PERSON> se <RELATIVE_DIRECTION> směrem na <TOWARD_SIGN>."}, "empty_street_name_labels": ["chodník", "cyklostezka", "stezka pro horská kola", "the crosswalk", "the stairs", "the bridge", "the tunnel"], "relative_directions": ["vlevo", "vpravo"], "example_phrases": {"0": ["Bear right."], "1": ["<PERSON> left onto Arlen Road."], "2": ["Bear right onto Belair Road, U.S. 1 Business."], "3": ["<PERSON> left to stay on U.S. 15 South."], "4": ["Bear right at Mannenbashi East."], "5": ["Bear left toward Baltimore."]}}, "becomes": {"phrases": {"0": "Z <PREVIOUS_STREET_NAMES> se stává <STREET_NAMES>."}, "example_phrases": {"0": ["Vine Street becomes Middletown Road."]}}, "becomes_verbal": {"phrases": {"0": "Z <PREVIOUS_STREET_NAMES> se stává <STREET_NAMES>."}, "example_phrases": {"0": ["Vine Street becomes Middletown Road."]}}, "continue": {"phrases": {"0": "Pokračujte.", "1": "Pokračujte na <STREET_NAMES>.", "2": "Pokračujte na <JUNCTION_NAME>.", "3": "Pokračujte směrem na <TOWARD_SIGN>."}, "empty_street_name_labels": ["chodník", "cyklostezka", "stezka pro horská kola", "the crosswalk", "the stairs", "the bridge", "the tunnel"], "example_phrases": {"0": ["Continue."], "1": ["Continue on 10th Avenue."], "2": ["Continue at Mannenbashi East."], "3": ["Continue toward Baltimore."]}}, "continue_verbal": {"phrases": {"0": "Pokračujte.", "1": "Pokračujte <LENGTH>.", "2": "Pokračujte na <STREET_NAMES>.", "3": "Pokračujte <LENGTH> na <STREET_NAMES> .", "4": "Pokračujte na <JUNCTION_NAME>.", "5": "Pokračujte <LENGTH> na <JUNCTION_NAME>.", "6": "Pokračujte směrem na <TOWARD_SIGN>.", "7": "Pokračujte <LENGTH> směrem na <TOWARD_SIGN>."}, "empty_street_name_labels": ["chodník", "cyklostezka", "stezka pro horská kola", "the crosswalk", "the stairs", "the bridge", "the tunnel"], "metric_lengths": ["<KILOMETERS> kilometrů", "1 kilometr", "<METERS> metrů", "méně než 10 metrů"], "us_customary_lengths": ["<MILES> mil", "1 míli", "p<PERSON><PERSON>", "čtvrt míle", "<FEET> stop", "méně než 10 stop"], "example_phrases": {"0": ["Continue."], "1": ["Continue for 300 feet."], "2": ["Continue on 10th Avenue."], "3": ["Continue on 10th Avenue for 3 miles."], "4": ["Continue at Mannenbashi East."], "5": ["Continue at Mannenbashi East for 2 miles."], "6": ["Continue toward Baltimore."], "7": ["Continue toward Baltimore for 5 miles."]}}, "continue_verbal_alert": {"phrases": {"0": "Pokračujte.", "1": "Pokračujte na <STREET_NAMES>.", "2": "Pokračujte na <JUNCTION_NAME>.", "3": "Pokračujte na <TOWARD_SIGN>."}, "empty_street_name_labels": ["chodník", "cyklostezka", "stezka pro horská kola", "the crosswalk", "the stairs", "the bridge", "the tunnel"], "example_phrases": {"0": ["Continue."], "1": ["Continue on 10th Avenue."], "2": ["Continue at Mannenbashi East."], "3": ["Continue toward Baltimore."]}}, "depart": {"phrases": {"0": "Vyjeďte: <TIME>.", "1": "<PERSON>yjeďte: <TIME> z <TRANSIT_STOP>."}, "example_phrases": {"0": ["Depart: 8:02 AM."], "1": ["Depart: 8:02 AM from 8 St - NYU."]}}, "depart_verbal": {"phrases": {"0": "Vyjeďte v <TIME>.", "1": "Vyjeďte v <TIME> z <TRANSIT_STOP>."}, "example_phrases": {"0": ["Depart at 8:02 AM."], "1": ["Depart at 8:02 AM from 8 St - NYU."]}}, "destination": {"phrases": {"0": "Dorazili jste do svého cíle.", "1": "Dorazili jste do <DESTINATION>.", "2": "<PERSON><PERSON><PERSON> c<PERSON> je <RELATIVE_DIRECTION>.", "3": "<DESTINATION> je <RELATIVE_DIRECTION>."}, "relative_directions": ["vlevo", "vpravo"], "example_phrases": {"0": ["You have arrived at your destination."], "1": ["You have arrived at 3206 Powelton Avenue."], "2": ["Your destination is on the left.", "Your destination is on the right."], "3": ["Lancaster Brewing Company is on the left."]}}, "destination_verbal": {"phrases": {"0": "Dorazili jste do svého cíle.", "1": "Dorazili jste do <DESTINATION>.", "2": "<PERSON><PERSON><PERSON> c<PERSON> je <RELATIVE_DIRECTION>.", "3": "<DESTINATION> je <RELATIVE_DIRECTION>."}, "relative_directions": ["vlevo", "vpravo"], "example_phrases": {"0": ["You have arrived at your destination."], "1": ["You have arrived at 32 o6 Powelton Avenue."], "2": ["Your destination is on the left.", "Your destination is on the right."], "3": ["Lancaster Brewing Company is on the left."]}}, "destination_verbal_alert": {"phrases": {"0": "Dorazíte do svého cíle.", "1": "Dorazíte do <DESTINATION>.", "2": "<PERSON><PERSON><PERSON> c<PERSON>l bude <RELATIVE_DIRECTION>.", "3": "<DESTINATION> bude <RELATIVE_DIRECTION>."}, "relative_directions": ["vlevo", "vpravo"], "example_phrases": {"0": ["You will arrive at your destination."], "1": ["You will arrive at 32 o6 Powelton Avenue."], "2": ["Your destination will be on the left.", "Your destination will be on the right."], "3": ["Lancaster Brewing Company will be on the left."]}}, "enter_ferry": {"phrases": {"0": "<PERSON><PERSON><PERSON> t<PERSON>.", "1": "<PERSON><PERSON><PERSON> po <STREET_NAMES>.", "2": "<PERSON><PERSON><PERSON> <STREET_NAMES> <FERRY_LABEL>.", "3": "<PERSON><PERSON><PERSON> trajektem k <TOWARD_SIGN>."}, "empty_street_name_labels": ["chodník", "tcyklostezka", "stezka pro horská kola", "the crosswalk", "the stairs", "the bridge", "the tunnel"], "ferry_label": "Trajekt", "example_phrases": {"0": ["Take the Ferry."], "1": ["Take the Millersburg Ferry."], "2": ["Take the Bridgeport - Port Jefferson Ferry."], "3": ["Take the ferry toward Cape May."]}}, "enter_ferry_verbal": {"phrases": {"0": "<PERSON><PERSON><PERSON> t<PERSON>.", "1": "<PERSON><PERSON><PERSON> po <STREET_NAMES>.", "2": "<PERSON><PERSON><PERSON> <STREET_NAMES> <FERRY_LABEL>.", "3": "<PERSON><PERSON><PERSON> trajektem k <TOWARD_SIGN>."}, "empty_street_name_labels": ["chodník", "cyklostezka", "stezka pro horská kola", "the crosswalk", "the stairs", "the bridge", "the tunnel"], "ferry_label": "Trajekt", "example_phrases": {"0": ["Take the Ferry."], "1": ["Take the Millersburg Ferry."], "2": ["Take the Bridgeport - Port Jefferson Ferry."], "3": ["Take the ferry toward Cape May."]}}, "enter_roundabout": {"phrases": {"0": "Vjeďte na kruhový objezd.", "1": "Vjeďte na kruhový objezd a sjeďte na <ORDINAL_VALUE> výjezdu.", "2": "Vjeďte na kruhový objezd a sjeďte na <ORDINAL_VALUE> výjezdu na <ROUNDABOUT_EXIT_STREET_NAMES>.", "3": "Vjeďte na kruhový objezd a sjeďte na <ORDINAL_VALUE> výjezdu na <ROUNDABOUT_EXIT_BEGIN_STREET_NAMES>. Pokračujte na <ROUNDABOUT_EXIT_STREET_NAMES>.", "4": "Vjeďte na kruhový objezd a sjeďte na <ORDINAL_VALUE> výjezdu na <TOWARD_SIGN>.", "5": "Vjeďte na kruhový objezd a sjeďte na výjezdu na <ROUNDABOUT_EXIT_STREET_NAMES>.", "6": "Vjeďte na kruhový objezd a sjeďte na výjezdu na <ROUNDABOUT_EXIT_BEGIN_STREET_NAMES>. Pokračujte na <ROUNDABOUT_EXIT_STREET_NAMES>.", "7": "Vjeďte na kruhový objezd a sjeďte na výjezdu směrem na <TOWARD_SIGN>.", "8": "Vjeďte do <STREET_NAMES>", "9": "Vjeďte do <STREET_NAMES> a sjeďte na <ORDINAL_VALUE> výjezdu.", "10": "Vjeďte do <STREET_NAMES> a sjeďte na <ORDINAL_VALUE> výjezdu na <ROUNDABOUT_EXIT_STREET_NAMES>.", "11": "Vjeďte do <STREET_NAMES> a sje<PERSON>te na <ORDINAL_VALUE> výjezdu na <ROUNDABOUT_EXIT_BEGIN_STREET_NAMES>. Pokračujte na <ROUNDABOUT_EXIT_STREET_NAMES>.", "12": "Vjeďte do <STREET_NAMES> a sjeďte na <ORDINAL_VALUE> výjezdu na <TOWARD_SIGN>.", "13": "Vjeďte do <STREET_NAMES> a sjeďte na výjezdu na <ROUNDABOUT_EXIT_STREET_NAMES>.", "14": "Vjeďte do <STREET_NAMES> a sjeďte na výjezdu na <ROUNDABOUT_EXIT_BEGIN_STREET_NAMES>. Pokračujte na <ROUNDABOUT_EXIT_STREET_NAMES>.", "15": "Vjeďte do <STREET_NAMES> a sjeďte na výjezdu směrem na <TOWARD_SIGN>."}, "ordinal_values": ["1.", "2.", "3.", "4.", "5.", "6.", "7.", "8.", "9.", "10."], "empty_street_name_labels": ["chodník", "cyklostezka", "stezka pro horská kola", "the crosswalk", "the stairs", "the bridge", "the tunnel"], "example_phrases": {"0": ["Enter the roundabout."], "1": ["Enter the roundabout and take the 1st exit.", "Enter the roundabout and take the 2nd exit.", "Enter the roundabout and take the 3rd exit.", "Enter the roundabout and take the 4th exit.", "Enter the roundabout and take the 5th exit.", "Enter the roundabout and take the 6th exit.", "Enter the roundabout and take the 7th exit.", "Enter the roundabout and take the 8th exit.", "Enter the roundabout and take the 9th exit.", "Enter the roundabout and take the 10th exit."], "2": ["Enter the roundabout and take the 3rd exit onto Main Street."], "3": ["Enter the roundabout and take the 3rd exit onto US 322/Main Street. Continue on US 322."], "4": ["Enter the roundabout and take the 3rd exit toward Baltimore."], "5": ["Enter the roundabout and take the exit onto Main Street."], "6": ["Enter the roundabout and take the exit onto US 322/Main Street. Continue on US 322."], "7": ["Enter the roundabout and take the exit toward Baltimore."], "8": ["Enter Dupont Circle."], "9": ["Enter Dupont Circle and take the 1st exit."], "10": ["Enter Dupont Circle and take the 3rd exit onto Main Street."], "11": ["Enter Dupont Circle and take the 3rd exit onto US 322/Main Street. Continue on US 322."], "12": ["Enter Dupont Circle and take the 3rd exit toward Baltimore."], "13": ["Enter Dupont Circle and take the exit onto Main Street."], "14": ["Enter Dupont Circle and take the exit onto US 322/Main Street. Continue on US 322."], "15": ["Enter Dupont Circle and take the exit toward Baltimore."]}}, "enter_roundabout_verbal": {"phrases": {"0": "Vjeďte na kruhový objezd.", "1": "Vjeďte na kruhový objezd a sjeďte na <ORDINAL_VALUE> výjezdu.", "2": "Vjeďte na kruhový objezd a sjeďte na <ORDINAL_VALUE> výjezdu na <ROUNDABOUT_EXIT_STREET_NAMES>.", "3": "Vjeďte na kruhový objezd a sjeďte <ORDINAL_VALUE> výjezdu na <ROUNDABOUT_EXIT_BEGIN_STREET_NAMES>.", "4": "Vjeďte na kruhový objezd a sjeďte na <ORDINAL_VALUE> výjezdu směrem na <TOWARD_SIGN>.", "5": "Vjeďte na kruhový objezd a sjeďte na výjezdu na <ROUNDABOUT_EXIT_STREET_NAMES>.", "6": "Vjeďte na kruhový objezd a vyjeďte na <ROUNDABOUT_EXIT_BEGIN_STREET_NAMES>.", "7": "Vjeďte na kruhový objezd a sjeďte na výjezdu směrem na <TOWARD_SIGN>.", "8": "Vjeďte na <STREET_NAMES>", "9": "Vjeďte na <STREET_NAMES> a sjeďte na <ORDINAL_VALUE> výjezdu.", "10": "Vjeďte na <STREET_NAMES> a sjeďte na <ORDINAL_VALUE> výjezdu na <ROUNDABOUT_EXIT_STREET_NAMES>.", "11": "Vjeďte na <STREET_NAMES> a sjeďte na <ORDINAL_VALUE> výjezdu na <ROUNDABOUT_EXIT_BEGIN_STREET_NAMES>.", "12": "Vjeďte na <STREET_NAMES> a sjeďte na <ORDINAL_VALUE> výjezdu směrem na <TOWARD_SIGN>.", "13": "Vjeďte na <STREET_NAMES> a sjeďte na výjezdu na <ROUNDABOUT_EXIT_STREET_NAMES>.", "14": "Vjeďte na <STREET_NAMES> a sjeďte na výjezdu na <ROUNDABOUT_EXIT_BEGIN_STREET_NAMES>.", "15": "Vjeďtena <STREET_NAMES> a sjeďte na výjezdu směrem na <TOWARD_SIGN>."}, "ordinal_values": ["1.", "2.", "3.", "4.", "5.", "6.", "7.", "8.", "9.", "10."], "empty_street_name_labels": ["chodník", "cyklostezka", "stezka pro horská kola", "the crosswalk", "the stairs", "the bridge", "the tunnel"], "example_phrases": {"0": ["Enter the roundabout."], "1": ["Enter the roundabout and take the 1st exit.", "Enter the roundabout and take the 2nd exit.", "Enter the roundabout and take the 3rd exit.", "Enter the roundabout and take the 4th exit.", "Enter the roundabout and take the 5th exit.", "Enter the roundabout and take the 6th exit.", "Enter the roundabout and take the 7th exit.", "Enter the roundabout and take the 8th exit.", "Enter the roundabout and take the 9th exit.", "Enter the roundabout and take the 10th exit."], "2": ["Enter the roundabout and take the 3rd exit onto Main Street."], "3": ["Enter the roundabout and take the 3rd exit onto U.S. 3 22/Main Street."], "4": ["Enter the roundabout and take the 3rd exit toward Baltimore."], "5": ["Enter the roundabout and take the exit onto Main Street."], "6": ["Enter the roundabout and take the exit onto U.S. 3 22/Main Street."], "7": ["Enter the roundabout and take the exit toward Baltimore."], "8": ["Enter Dupont Circle."], "9": ["Enter Dupont Circle and take the 1st exit."], "10": ["Enter Dupont Circle and take the 3rd exit onto Main Street."], "11": ["Enter Dupont Circle and take the 3rd exit onto U.S. 3 22/Main Street."], "12": ["Enter Dupont Circle and take the 3rd exit toward Baltimore."], "13": ["Enter Dupont Circle and take the exit onto Main Street."], "14": ["Enter Dupont Circle and take the exit onto U.S. 3 22/Main Street."], "15": ["Enter Dupont Circle and take the exit toward Baltimore."]}}, "exit": {"phrases": {"0": "Sjeďte na výjezdu <RELATIVE_DIRECTION>.", "1": "Sjeďte na výjezdu <NUMBER_SIGN> <RELATIVE_DIRECTION>.", "2": "Sjeďte na výjezdu <BRANCH_SIGN> <RELATIVE_DIRECTION>.", "3": "Sjeďte na výjezdu <NUMBER_SIGN> <RELATIVE_DIRECTION> na <BRANCH_SIGN>.", "4": "Sjeďte na výjezdu <RELATIVE_DIRECTION> směrem na <TOWARD_SIGN>.", "5": "Sjeďte na výjezdu <NUMBER_SIGN> <RELATIVE_DIRECTION> směrem na <TOWARD_SIGN>.", "6": "Sjeďte na výjezdu <BRANCH_SIGN> <RELATIVE_DIRECTION> směrem na <TOWARD_SIGN>.", "7": "Sjeďte na výjezdu <NUMBER_SIGN> <RELATIVE_DIRECTION> na <BRANCH_SIGN> směrem na <TOWARD_SIGN>.", "8": "Sjeďte na výjezdu <NAME_SIGN><RELATIVE_DIRECTION>.", "10": "Sjeďte na výjezdu <NAME_SIGN> <RELATIVE_DIRECTION> na <BRANCH_SIGN>.", "12": "Sjeďte na výjezdu <NAME_SIGN> <RELATIVE_DIRECTION> směrem na <TOWARD_SIGN>.", "14": "Sjeďte na výjezdu <NAME_SIGN> <RELATIVE_DIRECTION> na <BRANCH_SIGN> směrem na <TOWARD_SIGN>.", "15": "Sjeďte na výjezdu.", "16": "Sjeďte na výjezdu <NUMBER_SIGN>.", "17": "Sjeďte na výjezdu <BRANCH_SIGN>.", "18": "Sjeďte na výjezdu <NUMBER_SIGN> na <BRANCH_SIGN>.", "19": "Sjeďte na výjezdu směrem na <TOWARD_SIGN>.", "20": "Sjeďte na výjezdu <NUMBER_SIGN> směrem na <TOWARD_SIGN>.", "21": "Sjeďte na výjezdu <BRANCH_SIGN> <TOWARD_SIGN>.", "22": "Sjeďte na výjezdu <NUMBER_SIGN> na <BRANCH_SIGN> směrem na <TOWARD_SIGN>.", "23": "Sjeďte na výjezdu <NAME_SIGN>.", "25": "Sjeďte na výjezdu <NAME_SIGN> na <BRANCH_SIGN>.", "27": "Sjeďte na výjezdu <NAME_SIGN> směrem na <TOWARD_SIGN>.", "29": "Sjeďte na výjezdu <NAME_SIGN> na <BRANCH_SIGN> směrem na <TOWARD_SIGN>."}, "relative_directions": ["vlevo", "vpravo"], "example_phrases": {"0": ["Take the exit on the left.", "Take the exit on the right."], "1": ["Take exit 67 B-A on the right."], "2": ["Take the US 322 West exit on the right."], "3": ["Take exit 67 B-A on the right onto US 322 West."], "4": ["Take the exit on the right toward Lewistown."], "5": ["Take exit 67 B-A on the right toward Lewistown."], "6": ["Take the US 322 West exit on the right toward Lewistown."], "7": ["Take exit 67 B-A on the right onto US 322 West toward Lewistown/State College."], "8": ["Take the White Marsh Boulevard exit on the left."], "10": ["Take the White Marsh Boulevard exit on the left onto MD 43 East."], "12": ["Take the White Marsh Boulevard exit on the left toward White Marsh."], "14": ["Take the White Marsh Boulevard exit on the left onto MD 43 East toward White Marsh."], "15": ["Take the exit."], "16": ["Take exit 67 B-A."], "17": ["Take the US 322 West exit."], "18": ["Take exit 67 B-A onto US 322 West."], "19": ["Take the exit toward Lewistown."], "20": ["Take exit 67 B-A toward Lewistown."], "21": ["Take the US 322 West exit toward Lewistown."], "22": ["Take exit 67 B-A onto US 322 West toward Lewistown/State College."], "23": ["Take the White Marsh Boulevard exit."], "25": ["Take the White Marsh Boulevard exit onto MD 43 East."], "27": ["Take the White Marsh Boulevard exit toward White Marsh."], "29": ["Take the White Marsh Boulevard exit onto MD 43 East toward White Marsh."]}}, "exit_roundabout": {"phrases": {"0": "Sjeďte z kruhového objezdu.", "1": "Sjeďte z kruhového objezdu na <STREET_NAMES>.", "2": "Sjeďte z kruhového objezdu na <BEGIN_STREET_NAMES>. Pokračujte na <STREET_NAMES>.", "3": "Sjeďte z kruhového objezdu směrem na <TOWARD_SIGN>."}, "empty_street_name_labels": ["chodník", "cyklostezka", "stezka pro horská kola", "the crosswalk", "the stairs", "the bridge", "the tunnel"], "example_phrases": {"0": ["Exit the roundabout."], "1": ["Exit the roundabout onto Philadelphia Road/MD 7."], "2": ["Exit the roundabout onto Catoctin Mountain Highway/US 15. Continue on US 15."], "3": ["Exit the roundabout toward Baltimore."]}}, "exit_roundabout_verbal": {"phrases": {"0": "Sjeďte z kruhového objezdu.", "1": "Sjeďte z kruhového objezdu na <STREET_NAMES>.", "2": "Sjeďte z kruhového objezdu na <BEGIN_STREET_NAMES>.", "3": "Sjeďte z kruhového objezdu směrem na <TOWARD_SIGN>."}, "empty_street_name_labels": ["chodník", "cyklostezka", "stezka pro horská kola", "the crosswalk", "the stairs", "the bridge", "the tunnel"], "example_phrases": {"0": ["Exit the roundabout."], "1": ["Exit the roundabout onto Philadelphia Road, Maryland 7."], "2": ["Exit the roundabout onto Catoctin Mountain Highway, U.S. 15."], "3": ["Exit the roundabout toward Baltimore."]}}, "exit_verbal": {"phrases": {"0": "Sjeďte na výjezdu <RELATIVE_DIRECTION>.", "1": "Sjeďte na výjezdu <NUMBER_SIGN> <RELATIVE_DIRECTION>.", "2": "Sjeďte na výjezdu <BRANCH_SIGN> <RELATIVE_DIRECTION>.", "3": "Sjeďte na výjezdu <NUMBER_SIGN> <RELATIVE_DIRECTION> na <BRANCH_SIGN>.", "4": "Sjeďte na výjezdu <RELATIVE_DIRECTION> směrem na <TOWARD_SIGN>.", "5": "Sjeďte na výjezdu <NUMBER_SIGN> <RELATIVE_DIRECTION> směrem na <TOWARD_SIGN>.", "6": "Sjeďte na výjezdu <BRANCH_SIGN> <RELATIVE_DIRECTION> směrem na <TOWARD_SIGN>.", "7": "Sjeďte na výjezdu <NUMBER_SIGN> <RELATIVE_DIRECTION> na <BRANCH_SIGN> směrem na <TOWARD_SIGN>.", "8": "Sjeďte na výjezdu <NAME_SIGN> <RELATIVE_DIRECTION>.", "10": "Sjeďte na výjezdu <NAME_SIGN> <RELATIVE_DIRECTION> na <BRANCH_SIGN>.", "12": "Sjeďte na výjezdu <NAME_SIGN> <RELATIVE_DIRECTION> směrem na <TOWARD_SIGN>.", "14": "Sjeďte na výjezdu <NAME_SIGN> <RELATIVE_DIRECTION> na <BRANCH_SIGN> směrem na <TOWARD_SIGN>.", "15": "Sjeďte na výjezdu.", "16": "Sjeďte na výjezdu <NUMBER_SIGN>.", "17": "Sjeďte na výjezdu <BRANCH_SIGN>.", "18": "Sjeďte na výjezdu <NUMBER_SIGN> na <BRANCH_SIGN>.", "19": "Sjeďte na výjezdu <TOWARD_SIGN>.", "20": "Sjeďte na výjezdu <NUMBER_SIGN> směrem na <TOWARD_SIGN>.", "21": "Sjeďte na výjezdu <BRANCH_SIGN> směrem na <TOWARD_SIGN>.", "22": "Sjeďte na výjezdu <NUMBER_SIGN> na <BRANCH_SIGN> směrem na <TOWARD_SIGN>.", "23": "Sjeďte na výjezdu <NAME_SIGN>.", "25": "Sjeďte na výjezdu <NAME_SIGN> na <BRANCH_SIGN>.", "27": "Sjeďte na výjezdu <NAME_SIGN> směrem na <TOWARD_SIGN>.", "29": "Sjeďte na výjezdu <NAME_SIGN> na <BRANCH_SIGN> směrem na <TOWARD_SIGN>."}, "relative_directions": ["vlevo", "vpravo"], "example_phrases": {"0": ["Take the exit on the left.", "Take the exit on the right."], "1": ["Take exit 67 B-A on the right."], "2": ["Take the U.S. 3 22 West exit on the right."], "3": ["Take exit 67 B-A on the right onto U.S. 3 22 West."], "4": ["Take the exit on the right toward Lewistown."], "5": ["Take exit 67 B-A on the right toward Lewistown."], "6": ["Take the U.S. 3 22 West exit on the right toward Lewistown."], "7": ["Take exit 67 B-A on the right onto U.S. 3 22 West toward Lewistown, State College."], "8": ["Take the White Marsh Boulevard exit on the left."], "10": ["Take the White Marsh Boulevard exit on the left onto Maryland 43 East."], "12": ["Take the White Marsh Boulevard exit on the left toward White Marsh."], "14": ["Take the White Marsh Boulevard exit on the left onto Maryland 43 East toward White Marsh."], "15": ["Take the exit."], "16": ["Take exit 67 B-A."], "17": ["Take the US 322 West exit."], "18": ["Take exit 67 B-A onto US 322 West."], "19": ["Take the exit toward Lewistown."], "20": ["Take exit 67 B-A toward Lewistown."], "21": ["Take the US 322 West exit toward Lewistown."], "22": ["Take exit 67 B-A onto US 322 West toward Lewistown/State College."], "23": ["Take the White Marsh Boulevard exit."], "25": ["Take the White Marsh Boulevard exit onto MD 43 East."], "27": ["Take the White Marsh Boulevard exit toward White Marsh."], "29": ["Take the White Marsh Boulevard exit onto MD 43 East toward White Marsh."]}}, "exit_visual": {"phrases": {"0": "Sjeďte <EXIT_NUMBERS>"}, "example_phrases": {"0": ["Exit 1A"]}}, "keep": {"phrases": {"0": "Na rozdvojení se držte <RELATIVE_DIRECTION>.", "1": "<PERSON><PERSON><PERSON> se <RELATIVE_DIRECTION>, abyste sjeli na výjezdu <NUMBER_SIGN>.", "2": "<PERSON><PERSON><PERSON> se <RELATIVE_DIRECTION>, abyste vjeli na <STREET_NAMES>.", "3": "<PERSON><PERSON><PERSON> se <RELATIVE_DIRECTION>, abyste sjeli na výjezdu <NUMBER_SIGN> na <STREET_NAMES>.", "4": "<PERSON><PERSON><PERSON> se <RELATIVE_DIRECTION> směrem na <TOWARD_SIGN>.", "5": "<PERSON><PERSON><PERSON> se <RELATIVE_DIRECTION>, abyste sjeli na výjezdu <NUMBER_SIGN> sm<PERSON>rem na <TOWARD_SIGN>.", "6": "<PERSON><PERSON><PERSON> se <RELATIVE_DIRECTION>, abyste vjeli na <STREET_NAMES> směrem na <TOWARD_SIGN>.", "7": "<PERSON><PERSON><PERSON> se <RELATIVE_DIRECTION>, abyste sjeli na výjezdu <NUMBER_SIGN> na <STREET_NAMES> směrem na <TOWARD_SIGN>."}, "empty_street_name_labels": ["chodník", "cyklostezka", "stezka pro horská kola", "the crosswalk", "the stairs", "the bridge", "the tunnel"], "relative_directions": ["vlevo", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "vpravo"], "example_phrases": {"0": ["Keep left at the fork.", "Keep straight at the fork.", "Keep right at the fork."], "1": ["Keep right to take exit 62."], "2": ["Keep right to take I 895 South."], "3": ["Keep right to take exit 62 onto I 895 South."], "4": ["Keep right toward Annapolis."], "5": ["Keep right to take exit 62 toward Annapolis."], "6": ["Keep right to take I 895 South toward Annapolis."], "7": ["Keep right to take exit 62 onto I 895 South toward Annapolis."]}}, "keep_to_stay_on": {"phrases": {"0": "<PERSON><PERSON><PERSON> se <RELATIVE_DIRECTION>, abyste zůstali na <STREET_NAMES>.", "1": "<PERSON><PERSON><PERSON> se <RELATIVE_DIRECTION>, abyste sjeli na výjezdu <NUMBER_SIGN> a zůstali na <STREET_NAMES>.", "2": "<PERSON><PERSON><PERSON> se <RELATIVE_DIRECTION>, abyste zůstali na <STREET_NAMES> směrem na <TOWARD_SIGN>.", "3": "<PERSON><PERSON><PERSON> se <RELATIVE_DIRECTION>, abyste sjeli na výjezdu <NUMBER_SIGN> a zůstal<PERSON> na <STREET_NAMES> sm<PERSON>rem na <TOWARD_SIGN>."}, "empty_street_name_labels": ["chodník", "cyklostezka", "stezka pro horská kola", "the crosswalk", "the stairs", "the bridge", "the tunnel"], "relative_directions": ["vlevo", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "vpravo"], "example_phrases": {"0": ["Keep left to stay on I 95 South.", "Keep straight to stay on I 95 South.", "Keep right to stay on I 95 South."], "1": ["Keep left to take exit 62 to stay on I 95 South."], "2": ["Keep left to stay on I 95 South toward Baltimore."], "3": ["Keep left to take exit 62 to stay on I 95 South toward Baltimore."]}}, "keep_to_stay_on_verbal": {"phrases": {"0": "<PERSON><PERSON><PERSON> se <RELATIVE_DIRECTION>, abyste zůstali na <STREET_NAMES>.", "1": "<PERSON><PERSON><PERSON> se <RELATIVE_DIRECTION>, abyste sjeli na výjezdu <NUMBER_SIGN> a zůstali na <STREET_NAMES>.", "2": "<PERSON><PERSON><PERSON> se <RELATIVE_DIRECTION>, abyste zůstali na <STREET_NAMES> směrem na <TOWARD_SIGN>.", "3": "<PERSON><PERSON><PERSON> se <RELATIVE_DIRECTION>, abyste sjeli na výjezdu <NUMBER_SIGN> a zůstal<PERSON> na <STREET_NAMES> sm<PERSON>rem na <TOWARD_SIGN>."}, "empty_street_name_labels": ["chodník", "cyklostezka", "stezka pro horská kola", "the crosswalk", "the stairs", "the bridge", "the tunnel"], "relative_directions": ["vlevo", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "vpravo"], "example_phrases": {"0": ["Keep left to stay on Interstate 95 South.", "Keep straight to stay on Interstate 95 South.", "Keep right to stay on Interstate 95 South."], "1": ["Keep left to take exit 62 to stay on Interstate 95 South."], "2": ["Keep left to stay on I 95 South toward Baltimore."], "3": ["Keep left to take exit 62 to stay on Interstate 95 South toward Baltimore."]}}, "keep_verbal": {"phrases": {"0": "Na rozdvojení se držte <RELATIVE_DIRECTION>.", "1": "<PERSON><PERSON><PERSON> se <RELATIVE_DIRECTION>, abyste sjeli na výjezdu <NUMBER_SIGN>.", "2": "<PERSON><PERSON><PERSON> se <RELATIVE_DIRECTION>, abyste sjeli na výjezdu <STREET_NAMES>.", "3": "<PERSON><PERSON><PERSON> se <RELATIVE_DIRECTION>, abyste sjeli na výjezdu <NUMBER_SIGN> na <STREET_NAMES>.", "4": "<PERSON><PERSON><PERSON> se <RELATIVE_DIRECTION> směrem na <TOWARD_SIGN>.", "5": "<PERSON><PERSON><PERSON> se <RELATIVE_DIRECTION>, abyste sjeli na výjezdu <NUMBER_SIGN> sm<PERSON>rem na <TOWARD_SIGN>.", "6": "<PERSON><PERSON><PERSON> se <RELATIVE_DIRECTION>, abyste vje<PERSON> do <STREET_NAMES> směrem na <TOWARD_SIGN>.", "7": "<PERSON><PERSON><PERSON> se <RELATIVE_DIRECTION>, abyste sjeli na výjezdu <NUMBER_SIGN> na <STREET_NAMES> směrem na <TOWARD_SIGN>."}, "empty_street_name_labels": ["chodník", "cyklostezka", "stezka pro horská kola", "the crosswalk", "the stairs", "the bridge", "the tunnel"], "relative_directions": ["vlevo", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "vpravo"], "example_phrases": {"0": ["Keep left at the fork.", "Keep straight at the fork.", "Keep right at the fork."], "1": ["Keep right to take exit 62."], "2": ["Keep right to take Interstate 8 95 South."], "3": ["Keep right to take exit 62 onto Interstate 8 95 South."], "4": ["Keep right toward Annapolis."], "5": ["Keep right to take exit 62 toward Annapolis."], "6": ["Keep right to take Interstate 8 95 South toward Annapolis."], "7": ["Keep right to take exit 62 onto Interstate 8 95 South toward Annapolis."]}}, "merge": {"phrases": {"0": "Zařaďte se.", "1": "Zařaďte se <RELATIVE_DIRECTION>.", "2": "Zařaďte se k <STREET_NAMES>.", "3": "Z<PERSON>řaďte se <RELATIVE_DIRECTION> k <STREET_NAMES>.", "4": "Zařaďte se směrem na <TOWARD_SIGN>.", "5": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> se <RELATIVE_DIRECTION> směrem na <TOWARD_SIGN>."}, "relative_directions": ["vlevo", "vpravo"], "empty_street_name_labels": ["chodník", "cyklostezka", "stezka pro horská kola", "the crosswalk", "the stairs", "the bridge", "the tunnel"], "example_phrases": {"0": ["Merge."], "1": ["<PERSON><PERSON> left."], "2": ["Merge onto I 76 West/Pennsylvania Turnpike."], "3": ["Merge right onto I 83 South."], "4": ["Merge toward Baltimore."], "5": ["Merge right toward Baltimore."]}}, "merge_verbal": {"phrases": {"0": "Zařaďte se.", "1": "Zařaďte se <RELATIVE_DIRECTION>.", "2": "Zařaďte se k <STREET_NAMES>.", "3": "Z<PERSON>řaďte se <RELATIVE_DIRECTION> k <STREET_NAMES>.", "4": "Zařaďte se směrem na <TOWARD_SIGN>.", "5": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> se <RELATIVE_DIRECTION> směrem na <TOWARD_SIGN>."}, "relative_directions": ["vlevo", "vpravo"], "empty_street_name_labels": ["chodník", "cyklostezka", "stezka pro horská kola", "the crosswalk", "the stairs", "the bridge", "the tunnel"], "example_phrases": {"0": ["Merge."], "1": ["<PERSON><PERSON> left."], "2": ["Merge onto I 76 West/Pennsylvania Turnpike."], "3": ["Merge right onto I 83 South."], "4": ["Merge toward Baltimore."], "5": ["Merge right toward Baltimore."]}}, "post_transition_transit_verbal": {"phrases": {"0": "Cestování <TRANSIT_STOP_COUNT> <TRANSIT_STOP_COUNT_LABEL>."}, "transit_stop_count_labels": {"one": "zastávka", "other": "zastávky"}, "example_phrases": {"0": ["Travel 1 stop.", "Travel 3 stops."]}}, "post_transition_verbal": {"phrases": {"0": "Pokračujte <LENGTH>.", "1": "Pokračujte <LENGTH> po <STREET_NAMES>."}, "empty_street_name_labels": ["chodník", "cyklostezka", "stezka pro horská kola", "the crosswalk", "the stairs", "the bridge", "the tunnel"], "metric_lengths": ["<KILOMETERS> kilometrů", "1 kilometr", "<METERS> metrů", "méně než 10 metrů"], "us_customary_lengths": ["<MILES> mil", "1 míli", "p<PERSON><PERSON>", "čtvrt míle", "<FEET> stop", "méně než 10 stop"], "example_phrases": {"0": ["Continue for 300 feet.", "Continue for 9 miles."], "1": ["Continue on Pennsylvania 7 43 for 6.2 miles.", "Continue on Main Street, Vermont 30 for 1 tenth of a mile."]}}, "ramp": {"phrases": {"0": "Vjeďte na nájezd <RELATIVE_DIRECTION>.", "1": "Vjeďte na nájezd <BRANCH_SIGN> <RELATIVE_DIRECTION>.", "2": "Vjeďte na nájezd <RELATIVE_DIRECTION> směrem na <TOWARD_SIGN>.", "3": "Vjeďte na nájezd <BRANCH_SIGN> <RELATIVE_DIRECTION> směrem na <TOWARD_SIGN>.", "4": "Vjeďte na nájezd <NAME_SIGN> <RELATIVE_DIRECTION>.", "5": "Odbočte <RELATIVE_DIRECTION>, abyste vjeli na nájezd.", "6": "Odbočte <RELATIVE_DIRECTION>, abyste vjeli na nájezd <BRANCH_SIGN>.", "7": "Odbočte <RELATIVE_DIRECTION>, abyste vjeli na nájezd směrem na <TOWARD_SIGN>.", "8": "Odbočte <RELATIVE_DIRECTION>, abyste vjeli na nájezd <BRANCH_SIGN> sm<PERSON>rem na <TOWARD_SIGN>.", "9": "Odbočte <RELATIVE_DIRECTION>, abyste vjeli na nájezd <NAME_SIGN>.", "10": "Vjeďte na nájezd.", "11": "Vjeďte na nájezd <BRANCH_SIGN>.", "12": "Vjeďte na nájezd směrem na <TOWARD_SIGN>.", "13": "Vjeďte na nájezd <BRANCH_SIGN> směrem na <TOWARD_SIGN>.", "14": "Vjeďte na nájezd <NAME_SIGN>."}, "relative_directions": ["vlevo", "vpravo"], "example_phrases": {"0": ["Take the ramp on the left.", "Take the ramp on the right."], "1": ["Take the I 95 ramp on the right."], "2": ["Take the ramp on the left toward JFK."], "3": ["Take the South Conduit Avenue ramp on the left toward JFK."], "4": ["Take the Gettysburg Pike ramp on the right."], "5": ["Turn left to take the ramp.", "Turn right to take the ramp."], "6": ["Turn left to take the PA 283 West ramp."], "7": ["Turn left to take the ramp toward Harrisburg/Harrisburg International Airport."], "8": ["Turn left to take the PA 283 West ramp toward Harrisburg/Harrisburg International Airport."], "9": ["Turn right to take the Gettysburg Pike ramp."], "10": ["Take the ramp."], "11": ["Take the I 95 ramp."], "12": ["Take the ramp toward JFK."], "13": ["Take the Soutch Conduit Avenue ramp toward JFK."], "14": ["Take the Gettysburg ramp."]}}, "ramp_straight": {"phrases": {"0": "Pokračujte rovně, abyste vjeli na nájezd.", "1": "Pokračujte rovně, abyste vjeli na nájezd <BRANCH_SIGN>.", "2": "Pokračujte rovně, abyste vjeli na nájezd <TOWARD_SIGN>.", "3": "Pokračujte rovně, abyste vjeli na nájezd <BRANCH_SIGN> směrem na <TOWARD_SIGN>.", "4": "Pokračujte rovně, abyste vjeli na nájezd <NAME_SIGN>."}, "example_phrases": {"0": ["Stay straight to take the ramp."], "1": ["Stay straight to take the US 322 East ramp."], "2": ["Stay straight to take the ramp toward Hershey."], "3": ["Stay straight to take the US 322 East/US 422 East/US 522 East/US 622 East ramp toward Hershey/Palmdale/Palmyra/Campbelltown."], "4": ["Stay straight to take the Gettysburg Pike ramp."]}}, "ramp_straight_verbal": {"phrases": {"0": "Pokračujte rovně, abyste vjeli na nájezd.", "1": "Pokračujte rovně, abyste vjeli na nájezd <BRANCH_SIGN>.", "2": "Pokračujte rovně, abyste vjeli na nájezd směrem na <TOWARD_SIGN>.", "3": "Pokračujte rovně, abyste vjeli na nájezd <BRANCH_SIGN> směrem na <TOWARD_SIGN>.", "4": "Pokračujte rovně, abyste vjeli na nájezd <NAME_SIGN>."}, "example_phrases": {"0": ["Stay straight to take the ramp."], "1": ["Stay straight to take the U.S. 3 22 East ramp."], "2": ["Stay straight to take the ramp toward Hershey."], "3": ["Stay straight to take the U.S. 3 22 East, U.S. 4 22 East ramp toward Hershey, Palmdale."], "4": ["Stay straight to take the Gettysburg Pike ramp."]}}, "ramp_verbal": {"phrases": {"0": "Vjeďte na nájezd <RELATIVE_DIRECTION>.", "1": "Vjeďte na nájezd <BRANCH_SIGN> <RELATIVE_DIRECTION>.", "2": "Vjeďte na nájezd <RELATIVE_DIRECTION> směrem na <TOWARD_SIGN>.", "3": "Vjeďte na nájezd <BRANCH_SIGN> <RELATIVE_DIRECTION> směrem na <TOWARD_SIGN>.", "4": "Vjeďte na nájezd <NAME_SIGN> <RELATIVE_DIRECTION>.", "5": "Odbočte <RELATIVE_DIRECTION>, abyste vjeli na nájezd.", "6": "Odbočte <RELATIVE_DIRECTION>, abyste vjeli na nájezd <BRANCH_SIGN>.", "7": "Odbočte <RELATIVE_DIRECTION>, abyste vjeli na nájezd směrem na <TOWARD_SIGN>.", "8": "Odbočte <RELATIVE_DIRECTION>, abyste vjeli na nájezd <BRANCH_SIGN> sm<PERSON>rem na <TOWARD_SIGN>.", "9": "Odbočte <RELATIVE_DIRECTION>, abyste vjeli na nájezd <NAME_SIGN>.", "10": "Vjeďte na nájezd.", "11": "Vjeďte na nájezd <BRANCH_SIGN>.", "12": "Vjeďte na nájezd směrem na <TOWARD_SIGN>.", "13": "Vjeďte na nájezd <BRANCH_SIGN> směrem na <TOWARD_SIGN>.", "14": "Vjeďte na nájezd <NAME_SIGN>."}, "relative_directions": ["vlevo", "vpravo"], "example_phrases": {"0": ["Take the ramp on the left.", "Take the ramp on the right."], "1": ["Take the Interstate 95 ramp on the right."], "2": ["Take the ramp on the left toward JFK."], "3": ["Take the South Conduit Avenue ramp on the left toward JFK."], "4": ["Take the Gettysburg Pike ramp on the right."], "5": ["Turn left to take the ramp.", "Turn right to take the ramp."], "6": ["Turn left to take the Pennsylvania 2 83 West ramp."], "7": ["Turn left to take the ramp toward Harrisburg/Harrisburg International Airport."], "8": ["Turn left to take the Pennsylvania 2 83 West ramp toward Harrisburg, Harrisburg International Airport."], "9": ["Turn right to take the Gettysburg Pike ramp."], "10": ["Take the ramp."], "11": ["Take the Interstate 95 ramp."], "12": ["Take the ramp toward JFK."], "13": ["Take the South Conduit Avenue ramp toward JFK."], "14": ["Take the Gettysburg Pike ramp."]}}, "sharp": {"phrases": {"0": "Odbočte ostře <RELATIVE_DIRECTION>.", "1": "Odbočte ostře <RELATIVE_DIRECTION> na <STREET_NAMES>.", "2": "Odbočte ostře <RELATIVE_DIRECTION> na <BEGIN_STREET_NAMES>. Pokračujte na <STREET_NAMES>.", "3": "Odbočte ostře <RELATIVE_DIRECTION>, abyste zůstali na <STREET_NAMES>.", "4": "Na <JUNCTION_NAME> odbočte ostře <RELATIVE_DIRECTION>.", "5": "Odbočte ostře <RELATIVE_DIRECTION> směrem na <TOWARD_SIGN>."}, "empty_street_name_labels": ["chodník", "cyklostezka", "stezka pro horská kola", "the crosswalk", "the stairs", "the bridge", "the tunnel"], "relative_directions": ["vlevo", "vpravo"], "example_phrases": {"0": ["Make a sharp left."], "1": ["Make a sharp right onto Flatbush Avenue."], "2": ["Make a sharp left onto North Bond Street/US 1 Business/MD 924. Continue on MD 924."], "3": ["Make a sharp right to stay on Sunstone Drive."], "4": ["Make a sharp right at Mannenbashi East."], "5": ["Make a sharp left toward Baltimore."]}}, "sharp_verbal": {"phrases": {"0": "Odbočte ostře <RELATIVE_DIRECTION>.", "1": "Odbočte ostře <RELATIVE_DIRECTION> na <STREET_NAMES>.", "2": "Odbočte ostře <RELATIVE_DIRECTION> na <BEGIN_STREET_NAMES>.", "3": "Odbočte ostře <RELATIVE_DIRECTION>, abyste zůstali na <STREET_NAMES>.", "4": "Na <JUNCTION_NAME> odbočte ostře <RELATIVE_DIRECTION>.", "5": "Odbočte ostře <RELATIVE_DIRECTION> směrem na <TOWARD_SIGN>."}, "empty_street_name_labels": ["chodník", "cyklostezka", "stezka pro horská kola", "the crosswalk", "the stairs", "the bridge", "the tunnel"], "relative_directions": ["vlevo", "vpravo"], "example_phrases": {"0": ["Make a sharp left."], "1": ["Make a sharp right onto Flatbush Avenue."], "2": ["Make a sharp left onto North Bond Street, U.S. 1 Business."], "3": ["Make a sharp right to stay on Sunstone Drive."], "4": ["Make a sharp right at Mannenbashi East."], "5": ["Make a sharp left toward Baltimore."]}}, "start": {"phrases": {"0": "Směř<PERSON>jte <CARDINAL_DIRECTION>.", "1": "Směř<PERSON>jte <CARDINAL_DIRECTION> na <STREET_NAMES>.", "2": "Směřujte <CARDINAL_DIRECTION> na <BEGIN_STREET_NAMES>. Pokračujte na <STREET_NAMES>.", "4": "Je<PERSON><PERSON> <CARDINAL_DIRECTION>.", "5": "Je<PERSON><PERSON> <CARDINAL_DIRECTION> na <STREET_NAMES>.", "6": "Je<PERSON><PERSON> <CARDINAL_DIRECTION> na <BEGIN_STREET_NAMES>. Pokračujte na <STREET_NAMES>.", "8": "Jděte <CARDINAL_DIRECTION>.", "9": "Jděte <CARDINAL_DIRECTION> na <STREET_NAMES>.", "10": "Jděte <CARDINAL_DIRECTION> na <BEGIN_STREET_NAMES>. Pokračujte na <STREET_NAMES>.", "16": "Je<PERSON><PERSON> <CARDINAL_DIRECTION>.", "17": "Je<PERSON><PERSON> <CARDINAL_DIRECTION> na <STREET_NAMES>.", "18": "Je<PERSON><PERSON> <CARDINAL_DIRECTION> na <BEGIN_STREET_NAMES>. Pokračujte na <STREET_NAMES>."}, "cardinal_directions": ["na sever", "na severovýchod", "na východ", "na jihovýchod", "na jih", "na jihozápad", "na západ", "na severozápad"], "empty_street_name_labels": ["chodník", "cyklostezka", "stezka pro horská kola", "the crosswalk", "the stairs", "the bridge", "the tunnel"], "example_phrases": {"0": ["Head east.", "Head north."], "1": ["Head southwest on 5th Avenue.", "Head west on the walkway", "Head east on the cycleway", "Head north on the mountain bike trail"], "2": ["Head south on North Prince Street/US 222/PA 272. Continue on US 222/PA 272."], "4": ["Drive east.", "Drive north."], "5": ["Drive southwest on 5th Avenue."], "6": ["Drive south on North Prince Street/US 222/PA 272. Continue on US 222/PA 272."], "8": ["Walk east.", "Walk north."], "9": ["Walk southwest on 5th Avenue.", "Walk west on the walkway"], "10": ["Walk south on North Prince Street/US 222/PA 272. Continue on US 222/PA 272."], "16": ["Bike east.", "Bike north."], "17": ["Bike southwest on 5th Avenue.", "Bike east on the cycleway", "Bike north on the mountain bike trail"], "18": ["Bike south on North Prince Street/US 222/PA 272. Continue on US 222/PA 272."]}}, "start_verbal": {"phrases": {"0": "Směř<PERSON>jte <CARDINAL_DIRECTION>.", "1": "Smě<PERSON><PERSON>jte <LENGTH> <CARDINAL_DIRECTION>.", "2": "Směř<PERSON>jte <CARDINAL_DIRECTION> na <STREET_NAMES>.", "3": "Smě<PERSON><PERSON>jte <LENGTH><CARDINAL_DIRECTION> na <STREET_NAMES>.", "4": "Smě<PERSON><PERSON>jte <CARDINAL_DIRECTION> na <BEGIN_STREET_NAMES>.", "5": "Je<PERSON><PERSON> <CARDINAL_DIRECTION>.", "6": "Je<PERSON><PERSON> <LENGTH> <CARDINAL_DIRECTION>.", "7": "Je<PERSON><PERSON> <CARDINAL_DIRECTION> na <STREET_NAMES>.", "8": "Je<PERSON>te <LENGTH> <CARDINAL_DIRECTION> na <STREET_NAMES>.", "9": "Je<PERSON><PERSON> <CARDINAL_DIRECTION> na <BEGIN_STREET_NAMES>.", "10": "Jděte <CARDINAL_DIRECTION>.", "11": "Jděte <LENGTH> <CARDINAL_DIRECTION>.", "12": "Jděte <CARDINAL_DIRECTION> na <STREET_NAMES>.", "13": "Jděte <LENGTH> <CARDINAL_DIRECTION> na <STREET_NAMES>.", "14": "Jděte <CARDINAL_DIRECTION> na <BEGIN_STREET_NAMES>.", "15": "Je<PERSON><PERSON> <CARDINAL_DIRECTION>.", "16": "Je<PERSON><PERSON> <LENGTH> <CARDINAL_DIRECTION>.", "17": "Je<PERSON><PERSON> <CARDINAL_DIRECTION> na <STREET_NAMES>.", "18": "Je<PERSON>te <LENGTH> <CARDINAL_DIRECTION> na <STREET_NAMES>.", "19": "Je<PERSON><PERSON> <CARDINAL_DIRECTION> na <BEGIN_STREET_NAMES>."}, "cardinal_directions": ["na sever", "na severovýchod", "na východ", "na jihovýchod", "na jih", "na jihozápad", "na západ", "na severozápad"], "empty_street_name_labels": ["chodník", "cyklostezka", "stezka pro horská kola", "the crosswalk", "the stairs", "the bridge", "the tunnel"], "metric_lengths": ["<KILOMETERS> kilometrů", "1 kilometr", "<METERS> metrů", "méně než 10 metrů"], "us_customary_lengths": ["<MILES> mil", "1 míli", "p<PERSON><PERSON>", "čtvrt míle", "<FEET> stop", "méně než 10 stop"], "example_phrases": {"0": ["Head east.", "Head north."], "1": ["Head east for a half mile.", "Head north for 1 kilometer."], "2": ["Head southwest on 5th Avenue."], "3": ["Head southwest on 5th Avenue for 1 tenth of a mile."], "4": ["Head south on North Prince Street, U.S. 2 22."], "5": ["Drive east.", "Drive north."], "6": ["Drive east for a half mile.", "Drive north for 1 kilometer."], "7": ["Drive southwest on 5th Avenue."], "8": ["Drive southwest on 5th Avenue for 1 tenth of a mile."], "9": ["Drive south on North Prince Street, U.S. 2 22."], "10": ["Walk east.", "Walk north."], "11": ["Walk east for a half mile.", "Walk north for 1 kilometer."], "12": ["Walk southwest on 5th Avenue."], "13": ["Walk southwest on 5th Avenue for 1 tenth of a mile."], "14": ["Walk south on North Prince Street, U.S. 2 22."], "15": ["Bike east.", "Bike north."], "16": ["Bike east for a half mile.", "Bike north for 1 kilometer."], "17": ["Bike southwest on 5th Avenue."], "18": ["Bike southwest on 5th Avenue for 1 tenth of a mile."], "19": ["Bike south on North Prince Street, U.S. 2 22."]}}, "transit": {"phrases": {"0": "Je<PERSON><PERSON> <TRANSIT_NAME>. (<TRANSIT_STOP_COUNT> <TRANSIT_STOP_COUNT_LABEL>)", "1": "<PERSON><PERSON><PERSON> <TRANSIT_NAME> směrem na <TRANSIT_HEADSIGN>. (<TRANSIT_STOP_COUNT> <TRANSIT_STOP_COUNT_LABEL>)"}, "empty_transit_name_labels": ["tramvají", "metrem", "vlakem", "autobusem", "trajektem", "<PERSON><PERSON><PERSON><PERSON>", "gondolou", "poz<PERSON><PERSON><PERSON>"], "transit_stop_count_labels": {"one": "zastávka", "other": "zastávky"}, "example_phrases": {"0": ["Take the New Haven. (1 stop)", "Take the metro. (2 stops)", "Take the bus. (12 stops)"], "1": ["Take the F toward JAMAICA - 179 ST. (10 stops)", "Take the ferry toward Staten Island. (1 stop)"]}}, "transit_connection_destination": {"phrases": {"0": "Odejděte ze zastávky.", "1": "Odejděte z <TRANSIT_STOP>.", "2": "Odejděte z <TRANSIT_STOP> <STATION_LABEL>."}, "station_label": "Zastávka", "example_phrases": {"0": ["Exit the station."], "1": ["Exit the Embarcadero Station."], "2": ["Exit the 8 St - NYU Station."]}}, "transit_connection_destination_verbal": {"phrases": {"0": "Odejděte ze zastávky.", "1": "Odejděte z <TRANSIT_STOP>.", "2": "Odejděte z <TRANSIT_STOP> <STATION_LABEL>."}, "station_label": "Zastávka", "example_phrases": {"0": ["Exit the station."], "1": ["Exit the Embarcadero Station."], "2": ["Exit the 8 St - NYU Station."]}}, "transit_connection_start": {"phrases": {"0": "Vstupte na zastávku.", "1": "Vstupte na <TRANSIT_STOP>.", "2": "Vstupte na <TRANSIT_STOP> <STATION_LABEL>."}, "station_label": "Zastávka", "example_phrases": {"0": ["Enter the station."], "1": ["Enter the Embarcadero Station."], "2": ["Enter the 8 St - NYU Station."]}}, "transit_connection_start_verbal": {"phrases": {"0": "Vstupte na zastávku.", "1": "Vstupte na <TRANSIT_STOP>.", "2": "Vstupte na <TRANSIT_STOP> <STATION_LABEL>."}, "station_label": "Zastávka", "example_phrases": {"0": ["Enter the station."], "1": ["Enter the Embarcadero Station."], "2": ["Enter the 8 St - NYU Station."]}}, "transit_connection_transfer": {"phrases": {"0": "Přestupte na zastávce.", "1": "Přestupte na <TRANSIT_STOP>.", "2": "Přestupte na <TRANSIT_STOP> <STATION_LABEL>."}, "station_label": "Zastávka", "example_phrases": {"0": ["Transfer at the station."], "1": ["Transfer at the Embarcadero Station."], "2": ["Transfer at the 8 St - NYU Station."]}}, "transit_connection_transfer_verbal": {"phrases": {"0": "Přestupte na zastávce.", "1": "Přestupte na <TRANSIT_STOP>.", "2": "Přestupte na <TRANSIT_STOP> <STATION_LABEL>."}, "station_label": "Zastávka", "example_phrases": {"0": ["Transfer at the station."], "1": ["Transfer at the Embarcadero Station."], "2": ["Transfer at the 8 St - NYU Station."]}}, "transit_remain_on": {"phrases": {"0": "Zů<PERSON>ňte <TRANSIT_NAME>. (<TRANSIT_STOP_COUNT> <TRANSIT_STOP_COUNT_LABEL>)", "1": "Zů<PERSON>ňte <TRANSIT_NAME> sm<PERSON>rem na <TRANSIT_HEADSIGN>. (<TRANSIT_STOP_COUNT> <TRANSIT_STOP_COUNT_LABEL>)"}, "empty_transit_name_labels": ["v tramvaji", "v metru", "ve vlaku", "v autobuse", "na trajektu", "v lanovce", "v gondole", "v pozemní lanovce"], "transit_stop_count_labels": {"one": "zastávka", "other": "zastávky"}, "example_phrases": {"0": ["Remain on the New Haven. (1 stop)", "<PERSON><PERSON><PERSON> on the train. (3 stops)"], "1": ["Remain on the F toward JAMAICA - 179 ST. (10 stops)"]}}, "transit_remain_on_verbal": {"phrases": {"0": "Zůstaňte <TRANSIT_NAME>.", "1": "Zůstaňte <TRANSIT_NAME> směrem na <TRANSIT_HEADSIGN>."}, "empty_transit_name_labels": ["v tramvaji", "v metru", "ve vlaku", "v autobuse", "na trajektu", "v lanovce", "v gondole", "v pozemní lanovce"], "example_phrases": {"0": ["<PERSON><PERSON><PERSON> on the New Haven."], "1": ["Remain on the F toward JAMAICA - 179 ST."]}}, "transit_transfer": {"phrases": {"0": "<PERSON><PERSON><PERSON><PERSON><PERSON>, abyste nastoupili <TRANSIT_NAME>. (<TRANSIT_STOP_COUNT> <TRANSIT_STOP_COUNT_LABEL>)", "1": "<PERSON><PERSON><PERSON><PERSON><PERSON>, abyste nastoupili <TRANSIT_NAME> směrem na <TRANSIT_HEADSIGN>. (<TRANSIT_STOP_COUNT> <TRANSIT_STOP_COUNT_LABEL>)"}, "empty_transit_name_labels": ["do tramvaje", "do metra", "do vlaku", "do autobusu", "na trajekt", "do <PERSON><PERSON><PERSON>", "na gondolu", "do poz<PERSON>ní <PERSON>"], "transit_stop_count_labels": {"one": "zastávka", "other": "zastávky"}, "example_phrases": {"0": ["Transfer to take the New Haven. (1 stop)", "Transfer to take the tram. (4 stops)"], "1": ["Transfer to take the F toward JAMAICA - 179 ST. (10 stops)"]}}, "transit_transfer_verbal": {"phrases": {"0": "<PERSON><PERSON><PERSON><PERSON><PERSON>, abyste nastoupili <TRANSIT_NAME>.", "1": "<PERSON><PERSON><PERSON><PERSON><PERSON>, abyste nastoupili <TRANSIT_NAME> směrem na <TRANSIT_HEADSIGN>."}, "empty_transit_name_labels": ["do tramvaje", "do metra", "do vlaku", "do autobusu", "na trajekt", "do <PERSON><PERSON><PERSON>", "na gondolu", "do poz<PERSON>ní <PERSON>"], "example_phrases": {"0": ["Transfer to take the New Haven."], "1": ["Transfer to take the F toward JAMAICA - 179 ST."]}}, "transit_verbal": {"phrases": {"0": "Je<PERSON><PERSON> <TRANSIT_NAME>.", "1": "<PERSON><PERSON><PERSON> <TRANSIT_NAME> sm<PERSON>rem na <TRANSIT_HEADSIGN>."}, "empty_transit_name_labels": ["tramvají", "metrem", "vlakem", "autobusem", "trajektem", "<PERSON><PERSON><PERSON><PERSON>", "gondolou", "poz<PERSON><PERSON><PERSON>"], "example_phrases": {"0": ["Take the New Haven."], "1": ["Take the F toward JAMAICA - 179 ST."]}}, "turn": {"phrases": {"0": "Odbočte <RELATIVE_DIRECTION>.", "1": "Odbočte <RELATIVE_DIRECTION> na <STREET_NAMES>.", "2": "Odbočte <RELATIVE_DIRECTION> na <BEGIN_STREET_NAMES>. Pokračujte na <STREET_NAMES>.", "3": "Odbočte <RELATIVE_DIRECTION>, abyste zůstali na <STREET_NAMES>.", "4": "Odbočte <RELATIVE_DIRECTION> na <JUNCTION_NAME>.", "5": "Odbočte <RELATIVE_DIRECTION> směrem na <TOWARD_SIGN>."}, "empty_street_name_labels": ["chodník", "cyklostezka", "stezka pro horská kola", "the crosswalk", "the stairs", "the bridge", "the tunnel"], "relative_directions": ["vlevo", "vpravo"], "example_phrases": {"0": ["Turn left."], "1": ["Turn right onto Flatbush Avenue."], "2": ["Turn left onto North Bond Street/US 1 Business/MD 924. Continue on MD 924."], "3": ["Turn right to stay on Sunstone Drive."], "4": ["Turn right at Mannenbashi East."], "5": ["Turn left toward Baltimore."]}}, "turn_verbal": {"phrases": {"0": "Odbočte <RELATIVE_DIRECTION>.", "1": "Odbočte <RELATIVE_DIRECTION> na <STREET_NAMES>.", "2": "Odbočte <RELATIVE_DIRECTION> na <BEGIN_STREET_NAMES>.", "3": "Odbočte <RELATIVE_DIRECTION>, abyste zůstali na <STREET_NAMES>.", "4": "Odbočte <RELATIVE_DIRECTION> na <JUNCTION_NAME>.", "5": "Odbočte <RELATIVE_DIRECTION> směrem na <TOWARD_SIGN>."}, "empty_street_name_labels": ["chodník", "cyklostezka", "stezka pro horská kola", "the crosswalk", "the stairs", "the bridge", "the tunnel"], "relative_directions": ["vlevo", "vpravo"], "example_phrases": {"0": ["Turn left."], "1": ["Turn right onto Flatbush Avenue."], "2": ["Turn left onto North Bond Street, U.S. 1 Business."], "3": ["Turn right to stay on Sunstone Drive."], "4": ["Turn right at Mannenbashi East."], "5": ["Turn left toward Baltimore."]}}, "uturn": {"phrases": {"0": "Otočte se o 180° <RELATIVE_DIRECTION>.", "1": "Otočte se o 180° <RELATIVE_DIRECTION> na <STREET_NAMES>.", "2": "Otočte se o 180° <RELATIVE_DIRECTION>, abyste zůstali na <STREET_NAMES>.", "3": "Na <CROSS_STREET_NAMES> se otočte o 180° <RELATIVE_DIRECTION> .", "4": "Na <CROSS_STREET_NAMES> se otočte o 180° <RELATIVE_DIRECTION> na <STREET_NAMES>.", "5": "Na <CROSS_STREET_NAMES> se otočte o 180° <RELATIVE_DIRECTION>, abyste zůstali na <STREET_NAMES>.", "6": "Na <JUNCTION_NAME> se otočte o 180° <RELATIVE_DIRECTION> .", "7": "Otočte se o 180° <RELATIVE_DIRECTION> směrem na <TOWARD_SIGN>."}, "empty_street_name_labels": ["chodník", "cyklostezka", "stezka pro horská kola", "the crosswalk", "the stairs", "the bridge", "the tunnel"], "relative_directions": ["vlevo", "vpravo"], "example_phrases": {"0": ["Make a left U-turn."], "1": ["Make a right U-turn onto Bunker Hill Road."], "2": ["Make a left U-turn to stay on Bunker Hill Road."], "3": ["Make a left U-turn at Devonshire Road."], "4": ["Make a left U-turn at Devonshire Road onto Jonestown Road/US 22."], "5": ["Make a left U-turn at Devonshire Road to stay on Jonestown Road/US 22."], "6": ["Make a right U-turn at Mannenbashi East."], "7": ["Make a left U-turn toward Baltimore."]}}, "uturn_verbal": {"phrases": {"0": "Otočte se o 180° <RELATIVE_DIRECTION>.", "1": "Otočte se o 180° <RELATIVE_DIRECTION> na <STREET_NAMES>.", "2": "Otočte se o 180° <RELATIVE_DIRECTION>, abyste zůstali na <STREET_NAMES>.", "3": "Na <CROSS_STREET_NAMES> se otočte o 180° <RELATIVE_DIRECTION>.", "4": "Na <CROSS_STREET_NAMES> se otočte o 180° <RELATIVE_DIRECTION> na <STREET_NAMES>.", "5": "Na <CROSS_STREET_NAMES> se otočte o 180°<RELATIVE_DIRECTION>, abyste zůstali na <STREET_NAMES>.", "6": "Na <JUNCTION_NAME> se otočte o 180 °<RELATIVE_DIRECTION>.", "7": "Otočte se o 180° <RELATIVE_DIRECTION> směrem na <TOWARD_SIGN>."}, "empty_street_name_labels": ["chodník", "cyklostezka", "stezka pro horská kola", "the crosswalk", "the stairs", "the bridge", "the tunnel"], "relative_directions": ["vlevo", "vpravo"], "example_phrases": {"0": ["Make a left U-turn."], "1": ["Make a right U-turn onto Bunker Hill Road."], "2": ["Make a left U-turn to stay on Bunker Hill Road."], "3": ["Make a left U-turn at Devonshire Road."], "4": ["Make a left U-turn at Devonshire Road onto Jonestown Road, U.S. 22."], "5": ["Make a left U-turn at Devonshire Road to stay on Jonestown Road, U.S. 22."], "6": ["Make a right U-turn at Mannenbashi East."], "7": ["Make a left U-turn toward Baltimore."]}}, "verbal_multi_cue": {"phrases": {"0": "<CURRENT_VERBAL_CUE> Poté <NEXT_VERBAL_CUE>", "1": "<CURRENT_VERBAL_CUE> Poté za <LENGTH>, <NEXT_VERBAL_CUE>"}, "metric_lengths": ["<KILOMETERS> kilometrů", "1 kilometr", "<METERS> metrů", "méně než 10 metrů"], "us_customary_lengths": ["<MILES> mil", "1 míli", "p<PERSON><PERSON>", "čtvrt míle", "<FEET> stop", "méně než 10 stop"], "example_phrases": {"0": ["Bear right onto East Fayette Street. Then Turn right onto North Gay Street."], "1": ["Bear right onto East Fayette Street. Then, in 500 feet, Turn right onto North Gay Street."]}}, "approach_verbal_alert": {"phrases": {"0": "Za <LENGTH>, <CURRENT_VERBAL_CUE>"}, "metric_lengths": ["<KILOMETERS> kilometrů", "1 kilometr", "<METERS> metrů", "méně než 10 metrů"], "us_customary_lengths": ["<MILES> mil", "1 míli", "p<PERSON><PERSON>", "čtvrt míle", "<FEET> stop", "méně než 10 stop"], "example_phrases": {"0": ["In a quarter mile, Turn right onto North Gay Street."]}}, "pass": {"phrases": {"0": "Pass <OBJECT_LABEL>.", "1": "Pass traffic signals on <OBJECT_LABEL>."}, "object_labels": ["the gate", "the bollards", "ways intersection"], "example_phrases": {"0": ["Pass the gate."]}}, "elevator": {"phrases": {"0": "Take the elevator.", "1": "Take the elevator to <LEVEL>."}, "example_phrases": {"0": ["Take the elevator."], "1": ["Take the elevator to Level 1."]}}, "steps": {"phrases": {"0": "Take the stairs.", "1": "Take the stairs to <LEVEL>."}, "example_phrases": {"0": ["Take the stairs."], "1": ["Take the stairs to Level 2."]}}, "escalator": {"phrases": {"0": "Take the escalator.", "1": "Take the escalator to <LEVEL>."}, "example_phrases": {"0": ["Take the escalator."], "1": ["Take the escalator to Level 3."]}}, "enter_building": {"phrases": {"0": "Enter the building.", "1": "Enter the building, and continue on <STREET_NAMES>."}, "empty_street_name_labels": ["chodník", "cyklostezka", "stezka pro horská kola", "the crosswalk"], "example_phrases": {"0": ["Enter the building."], "1": ["Enter the building, and continue on the walkway."]}}, "exit_building": {"phrases": {"0": "Exit the building.", "1": "Exit the building, and continue on <STREET_NAMES>."}, "empty_street_name_labels": ["chodník", "cyklostezka", "stezka pro horská kola", "the crosswalk"], "example_phrases": {"0": ["Exit the building."], "1": ["Exit the building, and continue on the walkway."]}}}}
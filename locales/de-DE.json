{"posix_locale": "de_DE.UTF-8", "aliases": ["de"], "instructions": {"arrive": {"phrases": {"0": "Ankunft: <TIME>.", "1": "Ankunft: <TIME> an <TRANSIT_STOP>."}, "example_phrases": {"0": ["Arrive: 8:02 AM."], "1": ["Arrive: 8:02 AM at 8 St - NYU."]}}, "arrive_verbal": {"phrases": {"0": "Um <TIME> an<PERSON><PERSON>n", "1": "Um <TIME> an <TRANSIT_STOP> ankommen."}, "example_phrases": {"0": ["Arrive at 8:02 AM."], "1": ["Arrive at 8:02 AM at 8 St - NYU."]}}, "bear": {"phrases": {"0": "<PERSON><PERSON>t nach <RELATIVE_DIRECTION> abbiegen.", "1": "<PERSON><PERSON>t nach <RELATIVE_DIRECTION> auf <STREET_NAMES> abbiegen.", "2": "<PERSON><PERSON>t nach <RELATIVE_DIRECTION> auf <BEGIN_STREET_NAMES> abbiegen. Dann weiter auf <STREET_NAMES>.", "3": "<PERSON><PERSON>t nach <RELATIVE_DIRECTION> abbiegen um auf <STREET_NAMES> zu bleiben.", "4": "An <JUNCTION_NAME> leicht nach <RELATIVE_DIRECTION> abbiegen.", "5": "<PERSON><PERSON>t nach <RELATIVE_DIRECTION> in Richtung <TOWARD_SIGN> abbiegen."}, "empty_street_name_labels": ["<PERSON><PERSON>we<PERSON>", "Radwe<PERSON>", "Mountainbike Strecke", "Fußgängerübergang", "the stairs", "the bridge", "the tunnel"], "relative_directions": ["links", "rechts"], "example_phrases": {"0": ["Bear right."], "1": ["<PERSON> left onto Arlen Road."], "2": ["Bear right onto Belair Road/US 1 Business. Continue on US 1 Business."], "3": ["<PERSON> left to stay on US 15 South."], "4": ["Bear right at Mannenbashi East."], "5": ["Bear left toward Baltimore."]}}, "bear_verbal": {"phrases": {"0": "<PERSON><PERSON>t nach <RELATIVE_DIRECTION> abbiegen.", "1": "<PERSON><PERSON>t nach <RELATIVE_DIRECTION> auf <STREET_NAMES> abbiegen.", "2": "<PERSON><PERSON>t nach <RELATIVE_DIRECTION> auf <BEGIN_STREET_NAMES> abbiegen. Dann weiter auf <STREET_NAMES>.", "3": "<PERSON><PERSON>t nach <RELATIVE_DIRECTION> abbiegen um auf <STREET_NAMES> zu bleiben.", "4": "An <JUNCTION_NAME> leicht nach <RELATIVE_DIRECTION> abbiegen.", "5": "<PERSON><PERSON>t nach <RELATIVE_DIRECTION> in Richtung <TOWARD_SIGN> abbiegen."}, "empty_street_name_labels": ["<PERSON><PERSON>we<PERSON>", "Radwe<PERSON>", "Mountainbike Strecke", "Fußgängerübergang", "the stairs", "the bridge", "the tunnel"], "relative_directions": ["links", "rechts"], "example_phrases": {"0": ["Bear right."], "1": ["<PERSON> left onto Arlen Road."], "2": ["Bear right onto Belair Road, U.S. 1 Business."], "3": ["<PERSON> left to stay on U.S. 15 South."], "4": ["Bear right at Mannenbashi East."], "5": ["Bear left toward Baltimore."]}}, "becomes": {"phrases": {"0": "<PREVIOUS_STREET_NAMES> wird zu <STREET_NAMES>."}, "example_phrases": {"0": ["Vine Street becomes Middletown Road."]}}, "becomes_verbal": {"phrases": {"0": "<PREVIOUS_STREET_NAMES> wird zu <STREET_NAMES>."}, "example_phrases": {"0": ["Vine Street becomes Middletown Road."]}}, "continue": {"phrases": {"0": "<PERSON><PERSON>.", "1": "<PERSON><PERSON> auf <STREET_NAMES>.", "2": "Weiter an <JUNCTION_NAME>.", "3": "<PERSON><PERSON> in Richtung <TOWARD_SIGN>."}, "empty_street_name_labels": ["<PERSON><PERSON>we<PERSON>", "Radwe<PERSON>", "Mountainbike Strecke", "Fußgängerübergang", "the stairs", "the bridge", "the tunnel"], "example_phrases": {"0": ["Continue."], "1": ["Continue on 10th Avenue."], "2": ["Continue at Mannenbashi East."], "3": ["Continue toward Baltimore."]}}, "continue_verbal": {"phrases": {"0": "<PERSON><PERSON>.", "1": "<LENGTH> weiter.", "2": "<PERSON><PERSON> auf <STREET_NAMES>.", "3": "<LENGTH> weiter auf <STREET_NAMES>.", "4": "Weiter an <JUNCTION_NAME>.", "5": "An <JUNCTION_NAME> <LENGTH> weiter.", "6": "<PERSON><PERSON> in Richtung <TOWARD_SIGN>.", "7": "<LENGTH> weiter in Richtung <TOWARD_SIGN>."}, "empty_street_name_labels": ["<PERSON><PERSON>we<PERSON>", "Radwe<PERSON>", "Mountainbike Strecke", "Fußgängerübergang", "the stairs", "the bridge", "the tunnel"], "metric_lengths": ["<KILOMETERS> Kilometer", "einen Kilometer", "<METERS> Meter", "weniger als 10 Meter"], "us_customary_lengths": ["<MILES> <PERSON><PERSON>", "eine <PERSON>", "eine halbe <PERSON>", "eine viertel Meile", "<FEET> <PERSON><PERSON>", "weniger als 10 Fuß"], "example_phrases": {"0": ["Continue."], "1": ["Continue for 300 feet."], "2": ["Continue on 10th Avenue."], "3": ["Continue on 10th Avenue for 3 miles."], "4": ["Continue at Mannenbashi East."], "5": ["Continue at Mannenbashi East for 2 miles."], "6": ["Continue toward Baltimore."], "7": ["Continue toward Baltimore for 5 miles."]}}, "continue_verbal_alert": {"phrases": {"0": "<PERSON><PERSON>.", "1": "<PERSON><PERSON> auf <STREET_NAMES>.", "2": "Weiter an <JUNCTION_NAME>.", "3": "<PERSON><PERSON> in Richtung <TOWARD_SIGN>."}, "empty_street_name_labels": ["<PERSON><PERSON>we<PERSON>", "Radwe<PERSON>", "Mountainbike Strecke", "Fußgängerübergang", "the stairs", "the bridge", "the tunnel"], "example_phrases": {"0": ["Continue."], "1": ["Continue on 10th Avenue."], "2": ["Continue at Mannenbashi East."], "3": ["Continue toward Baltimore."]}}, "depart": {"phrases": {"0": "Abfahrt: <TIME>.", "1": "Abfahrt: <TIME> an <TRANSIT_STOP>."}, "example_phrases": {"0": ["Depart: 8:02 AM."], "1": ["Depart: 8:02 AM from 8 St - NYU."]}}, "depart_verbal": {"phrases": {"0": "Um <TIME> abfahren.", "1": "Um <TIME> von <TRANSIT_STOP> abfahren."}, "example_phrases": {"0": ["Depart at 8:02 AM."], "1": ["Depart at 8:02 AM from 8 St - NYU."]}}, "destination": {"phrases": {"0": "<PERSON><PERSON> er<PERSON>.", "1": "<DESTINATION> erreicht.", "2": "<PERSON><PERSON> befindet sich auf der <RELATIVE_DIRECTION> Seite.", "3": "<DESTINATION> befindet sich auf der <RELATIVE_DIRECTION> Seite."}, "relative_directions": ["linken", "rechten"], "example_phrases": {"0": ["You have arrived at your destination."], "1": ["You have arrived at 3206 Powelton Avenue."], "2": ["Your destination is on the left.", "Your destination is on the right."], "3": ["Lancaster Brewing Company is on the left."]}}, "destination_verbal": {"phrases": {"0": "<PERSON><PERSON> er<PERSON>.", "1": "<DESTINATION> erreicht.", "2": "<PERSON><PERSON> befindet sich auf der <RELATIVE_DIRECTION> Seite.", "3": "<DESTINATION> befindet sich auf der <RELATIVE_DIRECTION> Seite."}, "relative_directions": ["linken", "rechten"], "example_phrases": {"0": ["You have arrived at your destination."], "1": ["You have arrived at 32 o6 Powelton Avenue."], "2": ["Your destination is on the left.", "Your destination is on the right."], "3": ["Lancaster Brewing Company is on the left."]}}, "destination_verbal_alert": {"phrases": {"0": "Das Ziel wird erreicht.", "1": "<DESTINATION> wird erreicht.", "2": "Das Ziel befindet sich auf der <RELATIVE_DIRECTION> Seite.", "3": "<DESTINATION> befindet sich auf der <RELATIVE_DIRECTION> Seite."}, "relative_directions": ["linken", "rechten"], "example_phrases": {"0": ["You will arrive at your destination."], "1": ["You will arrive at 32 o6 Powelton Avenue."], "2": ["Your destination will be on the left.", "Your destination will be on the right."], "3": ["Lancaster Brewing Company will be on the left."]}}, "enter_ferry": {"phrases": {"0": "Die Fähre nehmen.", "1": "Die <STREET_NAMES> nehmen.", "2": "Die <FERRY_LABEL> <STREET_NAMES> nehmen.", "3": "<PERSON> Fähre in Richtung <TOWARD_SIGN> nehmen."}, "empty_street_name_labels": ["<PERSON><PERSON>we<PERSON>", "Radwe<PERSON>", "Mountainbike Strecke", "Fußgängerübergang", "the stairs", "the bridge", "the tunnel"], "ferry_label": "<PERSON><PERSON><PERSON><PERSON>", "example_phrases": {"0": ["Take the Ferry."], "1": ["Take the Millersburg Ferry."], "2": ["Take the Bridgeport - Port Jefferson Ferry."], "3": ["Take the ferry toward Cape May."]}}, "enter_ferry_verbal": {"phrases": {"0": "Die Fähre nehmen.", "1": "Die <STREET_NAMES> nehmen.", "2": "Die <FERRY_LABEL> <STREET_NAMES> nehmen.", "3": "<PERSON> Fähre in Richtung <TOWARD_SIGN> nehmen."}, "empty_street_name_labels": ["<PERSON><PERSON>we<PERSON>", "Radwe<PERSON>", "Mountainbike Strecke", "Fußgängerübergang", "the stairs", "the bridge", "the tunnel"], "ferry_label": "<PERSON><PERSON><PERSON><PERSON>", "example_phrases": {"0": ["Take the Ferry."], "1": ["Take the Millersburg Ferry."], "2": ["Take the Bridgeport - Port Jefferson Ferry."], "3": ["Take the ferry toward Cape May."]}}, "enter_roundabout": {"phrases": {"0": "In den Kreisverkehr fahren.", "1": "In den Kreisverkehr fahren und die <ORDINAL_VALUE> Ausfahrt nehmen.", "2": "In den Kreisverkehr fahren und die <ORDINAL_VALUE> Ausfahrt auf <ROUNDABOUT_EXIT_STREET_NAMES> nehmen.", "3": "In den Kreisverkehr fahren und die <ORDINAL_VALUE> Ausfahrt auf <ROUNDABOUT_EXIT_BEGIN_STREET_NAMES> nehmen. <PERSON>ter auf <ROUNDABOUT_EXIT_STREET_NAMES>.", "4": "In den Kreisverkehr fahren und die <ORDINAL_VALUE> Ausfahrt in Richtung <TOWARD_SIGN> nehmen.", "5": "In den Kreisverkehr fahren und die Ausfahrt auf <ROUNDABOUT_EXIT_STREET_NAMES> nehmen.", "6": "In den Kreisverkehr fahren und die Ausfahrt auf <ROUNDABOUT_EXIT_BEGIN_STREET_NAMES> nehmen. <PERSON>ter auf <ROUNDABOUT_EXIT_STREET_NAMES>.", "7": "In den Kreisverkehr fahren und die Ausfahrt in Richtung <TOWARD_SIGN> nehmen.", "8": "Auf <STREET_NAMES> fahren.", "9": "Auf <STREET_NAMES> fahren und die <ORDINAL_VALUE> Ausfahrt nehmen.", "10": "Auf <STREET_NAMES> fahren und die <ORDINAL_VALUE> Ausfahrt auf <ROUNDABOUT_EXIT_STREET_NAMES> nehmen.", "11": "Auf <STREET_NAMES> fahren und die <ORDINAL_VALUE> Ausfahrt auf <ROUNDABOUT_EXIT_BEGIN_STREET_NAMES> nehmen. Weiter auf <ROUNDABOUT_EXIT_STREET_NAMES>.", "12": "Auf <STREET_NAMES> fahren und die <ORDINAL_VALUE> Ausfahrt in Richtung <TOWARD_SIGN> nehmen.", "13": "Auf <STREET_NAMES> fahren und die Ausfahrt auf <ROUNDABOUT_EXIT_STREET_NAMES> nehmen.", "14": "Auf <STREET_NAMES> fahren und die Ausfahrt auf <ROUNDABOUT_EXIT_BEGIN_STREET_NAMES> nehmen. Weiter auf <ROUNDABOUT_EXIT_STREET_NAMES>.", "15": "Auf <STREET_NAMES> fahren und die Ausfahrt in Richtung <TOWARD_SIGN> nehmen."}, "ordinal_values": ["1.", "2.", "3.", "4.", "5.", "6.", "7.", "8.", "9.", "10."], "empty_street_name_labels": ["<PERSON><PERSON>we<PERSON>", "Radwe<PERSON>", "Mountainbike Strecke", "Fußgängerübergang", "the stairs", "the bridge", "the tunnel"], "example_phrases": {"0": ["Enter the roundabout."], "1": ["Enter the roundabout and take the 1st exit.", "Enter the roundabout and take the 2nd exit.", "Enter the roundabout and take the 3rd exit.", "Enter the roundabout and take the 4th exit.", "Enter the roundabout and take the 5th exit.", "Enter the roundabout and take the 6th exit.", "Enter the roundabout and take the 7th exit.", "Enter the roundabout and take the 8th exit.", "Enter the roundabout and take the 9th exit.", "Enter the roundabout and take the 10th exit."], "2": ["Enter the roundabout and take the 3rd exit onto Main Street."], "3": ["Enter the roundabout and take the 3rd exit onto US 322/Main Street. Continue on US 322."], "4": ["Enter the roundabout and take the 3rd exit toward Baltimore."], "5": ["Enter the roundabout and take the exit onto Main Street."], "6": ["Enter the roundabout and take the exit onto US 322/Main Street. Continue on US 322."], "7": ["Enter the roundabout and take the exit toward Baltimore."], "8": ["Enter Dupont Circle."], "9": ["Enter Dupont Circle and take the 1st exit."], "10": ["Enter Dupont Circle and take the 3rd exit onto Main Street."], "11": ["Enter Dupont Circle and take the 3rd exit onto US 322/Main Street. Continue on US 322."], "12": ["Enter Dupont Circle and take the 3rd exit toward Baltimore."], "13": ["Enter Dupont Circle and take the exit onto Main Street."], "14": ["Enter Dupont Circle and take the exit onto US 322/Main Street. Continue on US 322."], "15": ["Enter Dupont Circle and take the exit toward Baltimore."]}}, "enter_roundabout_verbal": {"phrases": {"0": "In den Kreisverkehr fahren.", "1": "In den Kreisverkehr fahren und die <ORDINAL_VALUE> Ausfahrt nehmen.", "2": "In den Kreisverkehr fahren und die <ORDINAL_VALUE> Ausfahrt nehmen auf <ROUNDABOUT_EXIT_STREET_NAMES>.", "3": "In den Kreisverkehr fahren und die <ORDINAL_VALUE> Ausfahrt auf <ROUNDABOUT_EXIT_BEGIN_STREET_NAMES> nehmen.", "4": "In den Kreisverkehr fahren und die <ORDINAL_VALUE> Ausfahrt in Richtung <TOWARD_SIGN> nehmen.", "5": "In den Kreisverkehr fahren und die Ausfahrt auf <ROUNDABOUT_EXIT_STREET_NAMES> nehmen.", "6": "In den Kreisverkehr fahren und die Ausfahrt auf <ROUNDABOUT_EXIT_BEGIN_STREET_NAMES> nehmen.", "7": "In den Kreisverkehr fahren und die Ausfahrt in Richtung <TOWARD_SIGN> nehmen.", "8": "Auf <STREET_NAMES> fahren.", "9": "Auf <STREET_NAMES> fahren", "10": "Auf <STREET_NAMES> fahren und die <ORDINAL_VALUE> Ausfahrt auf <ROUNDABOUT_EXIT_STREET_NAMES> nehmen.", "11": "Auf <STREET_NAMES> fahren und die <ORDINAL_VALUE> Ausfahrt auf <ROUNDABOUT_EXIT_BEGIN_STREET_NAMES> nehmen.", "12": "Auf <STREET_NAMES> fahren und die <ORDINAL_VALUE> Ausfahrt in Richtung <TOWARD_SIGN> nehmen.", "13": "Auf <STREET_NAMES> fahren und die Ausfahrt auf <ROUNDABOUT_EXIT_STREET_NAMES> nehmen.", "14": "Auf <STREET_NAMES> fahren und die Ausfahrt auf <ROUNDABOUT_EXIT_BEGIN_STREET_NAMES> nehmen.", "15": "Auf <STREET_NAMES> fahren und die Ausfahrt in Richtung <TOWARD_SIGN> nehmen."}, "ordinal_values": ["erste", "zweite", "dritte", "vierte", "fünfte", "sechste", "siebte", "achte", "neunte", "zeh<PERSON>"], "empty_street_name_labels": ["<PERSON><PERSON>we<PERSON>", "Radwe<PERSON>", "Mountainbike Strecke", "Fußgängerübergang", "the stairs", "the bridge", "the tunnel"], "example_phrases": {"0": ["Enter the roundabout."], "1": ["Enter the roundabout and take the 1st exit.", "Enter the roundabout and take the 2nd exit.", "Enter the roundabout and take the 3rd exit.", "Enter the roundabout and take the 4th exit.", "Enter the roundabout and take the 5th exit.", "Enter the roundabout and take the 6th exit.", "Enter the roundabout and take the 7th exit.", "Enter the roundabout and take the 8th exit.", "Enter the roundabout and take the 9th exit.", "Enter the roundabout and take the 10th exit."], "2": ["Enter the roundabout and take the 3rd exit onto Main Street."], "3": ["Enter the roundabout and take the 3rd exit onto U.S. 3 22/Main Street."], "4": ["Enter the roundabout and take the 3rd exit toward Baltimore."], "5": ["Enter the roundabout and take the exit onto Main Street."], "6": ["Enter the roundabout and take the exit onto U.S. 3 22/Main Street."], "7": ["Enter the roundabout and take the exit toward Baltimore."], "8": ["Enter Dupont Circle."], "9": ["Enter Dupont Circle and take the 1st exit."], "10": ["Enter Dupont Circle and take the 3rd exit onto Main Street."], "11": ["Enter Dupont Circle and take the 3rd exit onto U.S. 3 22/Main Street."], "12": ["Enter Dupont Circle and take the 3rd exit toward Baltimore."], "13": ["Enter Dupont Circle and take the exit onto Main Street."], "14": ["Enter Dupont Circle and take the exit onto U.S. 3 22/Main Street."], "15": ["Enter Dupont Circle and take the exit toward Baltimore."]}}, "exit": {"phrases": {"0": "An der Ausfahrt <RELATIVE_DIRECTION> abfahren.", "1": "An der Ausfahrt <NUMBER_SIGN> <RELATIVE_DIRECTION> abfahren.", "2": "Auf <BRANCH_SIGN> über die Ausfahrt <RELATIVE_DIRECTION> abfahren.", "3": "Auf <BRANCH_SIGN> über die Ausfahrt <NUMBER_SIGN> <RELATIVE_DIRECTION> abfahren.", "4": "An der Ausfahrt <RELATIVE_DIRECTION> ab<PERSON><PERSON> Rich<PERSON>g <TOWARD_SIGN>.", "5": "An der Ausfahrt <NUMBER_SIGN> <RELATIVE_DIRECTION> abfahren Richtung <TOWARD_SIGN>.", "6": "Auf <BRANCH_SIGN> Richtung <TOWARD_SIGN> über die Ausfahrt <RELATIVE_DIRECTION> abfahren.", "7": "Auf <BRANCH_SIGN> Richtung <TOWARD_SIGN> über die Ausfahrt <NUMBER_SIGN> <RELATIVE_DIRECTION> abfahren.", "8": "An der Ausfahrt <NAME_SIGN> <RELATIVE_DIRECTION> abfahren.", "10": "Auf <BRANCH_SIGN> über die Ausfahrt <NAME_SIGN> <RELATIVE_DIRECTION> abfahren.", "12": "An der Ausfahrt <NAME_SIGN> <RELATIVE_DIRECTION> abfahren Richtung <TOWARD_SIGN>.", "14": "Auf <BRANCH_SIGN> Richtung <TOWARD_SIGN> über die Ausfahrt <NAME_SIGN> <RELATIVE_DIRECTION> abfahren.", "15": "An der Ausfahrt abfahren.", "16": "An der Ausfahrt <NUMBER_SIGN> abfahren.", "17": "An der Ausfahrt <BRANCH_SIGN> abfahren.", "18": "Auf <BRANCH_SIGN> über die Ausfahrt <NUMBER_SIGN>.", "19": "An der Ausfahrt abfahren Richtung <TOWARD_SIGN>.", "20": "An der Ausfahrt <NUMBER_SIGN> ab<PERSON><PERSON> Richtung <TOWARD_SIGN>.", "21": "Auf <BRANCH_SIGN> Richtung <TOWARD_SIGN> über die Ausfahrt abfahren.", "22": "Auf <BRANCH_SIGN> Richtung <TOWARD_SIGN> über die Ausfahrt <NUMBER_SIGN> abfahren.", "23": "An der Ausfahrt <NAME_SIGN> abfahren.", "25": "Auf <BRANCH_SIGN> über die Ausfahrt <NAME_SIGN> abfahren.", "27": "An der Ausfahrt <NAME_SIGN> ab<PERSON><PERSON> Richtung <TOWARD_SIGN>.", "29": "Auf <BRANCH_SIGN> Richtung <TOWARD_SIGN> über die Ausfahrt <NAME_SIGN> abfahren."}, "relative_directions": ["links", "rechts"], "example_phrases": {"0": ["Take the exit on the left.", "Take the exit on the right."], "1": ["Take exit 67 B-A on the right."], "2": ["Take the US 322 West exit on the right."], "3": ["Take exit 67 B-A on the right onto US 322 West."], "4": ["Take the exit on the right toward Lewistown."], "5": ["Take exit 67 B-A on the right toward Lewistown."], "6": ["Take the US 322 West exit on the right toward Lewistown."], "7": ["Take exit 67 B-A on the right onto US 322 West toward Lewistown/State College."], "8": ["Take the White Marsh Boulevard exit on the left."], "10": ["Take the White Marsh Boulevard exit on the left onto MD 43 East."], "12": ["Take the White Marsh Boulevard exit on the left toward White Marsh."], "14": ["Take the White Marsh Boulevard exit on the left onto MD 43 East toward White Marsh."], "15": ["Take the exit."], "16": ["Take exit 67 B-A."], "17": ["Take the US 322 West exit."], "18": ["Take exit 67 B-A onto US 322 West."], "19": ["Take the exit toward Lewistown."], "20": ["Take exit 67 B-A toward Lewistown."], "21": ["Take the US 322 West exit toward Lewistown."], "22": ["Take exit 67 B-A onto US 322 West toward Lewistown/State College."], "23": ["Take the White Marsh Boulevard exit."], "25": ["Take the White Marsh Boulevard exit onto MD 43 East."], "27": ["Take the White Marsh Boulevard exit toward White Marsh."], "29": ["Take the White Marsh Boulevard exit onto MD 43 East toward White Marsh."]}}, "exit_roundabout": {"phrases": {"0": "Aus dem Kreisverkehr ausfahren.", "1": "Aus dem Kreisverkehr auf <STREET_NAMES> ausfahren.", "2": "Aus dem Kreisverkehr auf <BEGIN_STREET_NAMES> ausfahren. Dann weiter auf <STREET_NAMES>.", "3": "Aus dem Kreisverkehr in Richtung <TOWARD_SIGN> ausfahren."}, "empty_street_name_labels": ["<PERSON><PERSON>we<PERSON>", "Radwe<PERSON>", "Mountainbike Strecke", "Fußgängerübergang", "the stairs", "the bridge", "the tunnel"], "example_phrases": {"0": ["Exit the roundabout."], "1": ["Exit the roundabout onto Philadelphia Road/MD 7."], "2": ["Exit the roundabout onto Catoctin Mountain Highway/US 15. Continue on US 15."], "3": ["Exit the roundabout toward Baltimore."]}}, "exit_roundabout_verbal": {"phrases": {"0": "Aus dem Kreisverkehr ausfahren.", "1": "Aus dem Kreisverkehr auf <STREET_NAMES> ausfahren.", "2": "Aus dem Kreisverkehr auf <BEGIN_STREET_NAMES> ausfahren.", "3": "Aus dem Kreisverkehr in Richtung <TOWARD_SIGN> ausfahren."}, "empty_street_name_labels": ["<PERSON><PERSON>we<PERSON>", "Radwe<PERSON>", "Mountainbike Strecke", "Fußgängerübergang", "the stairs", "the bridge", "the tunnel"], "example_phrases": {"0": ["Exit the roundabout."], "1": ["Exit the roundabout onto Philadelphia Road, Maryland 7."], "2": ["Exit the roundabout onto Catoctin Mountain Highway, U.S. 15."], "3": ["Exit the roundabout toward Baltimore."]}}, "exit_verbal": {"phrases": {"0": "An der Ausfahrt <RELATIVE_DIRECTION> abfahren.", "1": "An der Ausfahrt <NUMBER_SIGN> <RELATIVE_DIRECTION> abfahren.", "2": "Auf <BRANCH_SIGN> über die Ausfahrt <RELATIVE_DIRECTION> abfahren.", "3": "Auf <BRANCH_SIGN> über die Ausfahrt <NUMBER_SIGN> <RELATIVE_DIRECTION> abfahren.", "4": "An der Ausfahrt <RELATIVE_DIRECTION> ab<PERSON><PERSON> Rich<PERSON>g <TOWARD_SIGN>.", "5": "An der Ausfahrt <NUMBER_SIGN> <RELATIVE_DIRECTION> abfahren Richtung <TOWARD_SIGN>.", "6": "Auf <BRANCH_SIGN> Richtung <TOWARD_SIGN> über die Ausfahrt <RELATIVE_DIRECTION> abfahren.", "7": "Auf <BRANCH_SIGN> Richtung <TOWARD_SIGN> über die Ausfahrt <NUMBER_SIGN> <RELATIVE_DIRECTION> abfahren.", "8": "An der Ausfahrt <NAME_SIGN> <RELATIVE_DIRECTION> abfahren.", "10": "Auf <BRANCH_SIGN> über die Ausfahrt <NAME_SIGN> <RELATIVE_DIRECTION> abfahren.", "12": "An der Ausfahrt <NAME_SIGN> <RELATIVE_DIRECTION> abfahren Richtung <TOWARD_SIGN>.", "14": "Auf <BRANCH_SIGN> Richtung <TOWARD_SIGN> über die Ausfahrt <NAME_SIGN> <RELATIVE_DIRECTION> abfahren.", "15": "An der Ausfahrt abfahren.", "16": "An der Ausfahrt <NUMBER_SIGN> abfahren.", "17": "An der Ausfahrt <BRANCH_SIGN> abfahren.", "18": "Auf <BRANCH_SIGN> über die Ausfahrt <NUMBER_SIGN>.", "19": "An der Ausfahrt abfahren Richtung <TOWARD_SIGN>.", "20": "An der Ausfahrt <NUMBER_SIGN> ab<PERSON><PERSON> Richtung <TOWARD_SIGN>.", "21": "Auf <BRANCH_SIGN> Richtung <TOWARD_SIGN> über die Ausfahrt abfahren.", "22": "Auf <BRANCH_SIGN> Richtung <TOWARD_SIGN> über die Ausfahrt <NUMBER_SIGN> abfahren.", "23": "An der Ausfahrt <NAME_SIGN> abfahren.", "25": "Auf <BRANCH_SIGN> über die Ausfahrt <NAME_SIGN> abfahren.", "27": "An der Ausfahrt <NAME_SIGN> ab<PERSON><PERSON> Richtung <TOWARD_SIGN>.", "29": "Auf <BRANCH_SIGN> Richtung <TOWARD_SIGN> über die Ausfahrt <NAME_SIGN> abfahren."}, "relative_directions": ["links", "rechts"], "example_phrases": {"0": ["Take the exit on the left.", "Take the exit on the right."], "1": ["Take exit 67 B-A on the right."], "2": ["Take the U.S. 3 22 West exit on the right."], "3": ["Take exit 67 B-A on the right onto U.S. 3 22 West."], "4": ["Take the exit on the right toward Lewistown."], "5": ["Take exit 67 B-A on the right toward Lewistown."], "6": ["Take the U.S. 3 22 West exit on the right toward Lewistown."], "7": ["Take exit 67 B-A on the right onto U.S. 3 22 West toward Lewistown, State College."], "8": ["Take the White Marsh Boulevard exit on the left."], "10": ["Take the White Marsh Boulevard exit on the left onto Maryland 43 East."], "12": ["Take the White Marsh Boulevard exit on the left toward White Marsh."], "14": ["Take the White Marsh Boulevard exit on the left onto Maryland 43 East toward White Marsh."], "15": ["Take the exit."], "16": ["Take exit 67 B-A."], "17": ["Take the US 322 West exit."], "18": ["Take exit 67 B-A onto US 322 West."], "19": ["Take the exit toward Lewistown."], "20": ["Take exit 67 B-A toward Lewistown."], "21": ["Take the US 322 West exit toward Lewistown."], "22": ["Take exit 67 B-A onto US 322 West toward Lewistown/State College."], "23": ["Take the White Marsh Boulevard exit."], "25": ["Take the White Marsh Boulevard exit onto MD 43 East."], "27": ["Take the White Marsh Boulevard exit toward White Marsh."], "29": ["Take the White Marsh Boulevard exit onto MD 43 East toward White Marsh."]}}, "exit_visual": {"phrases": {"0": "Ausfahrt <EXIT_NUMBERS>"}, "example_phrases": {"0": ["Exit 1A"]}}, "keep": {"phrases": {"0": "<RELATIVE_DIRECTION> halten an der Gabelung.", "1": "<RELATIVE_DIRECTION> halten auf die Ausfahrt <NUMBER_SIGN>.", "2": "<RELATIVE_DIRECTION> halten auf <STREET_NAMES>.", "3": "<RELATIVE_DIRECTION> halten um die Ausfahrt <NUMBER_SIGN> auf <STREET_NAMES> zu nehmen.", "4": "<RELATIVE_DIRECTION> halten Richtung <TOWARD_SIGN>.", "5": "<RELATIVE_DIRECTION> halten um die Ausfahrt <NUMBER_SIGN> Richtung <TOWARD_SIGN> zu nehmen.", "6": "<RELATIVE_DIRECTION> halten auf <STREET_NAMES> Richtung <TOWARD_SIGN>.", "7": "<RELATIVE_DIRECTION> halten um die Ausfahrt <NUMBER_SIGN> auf <STREET_NAMES> Richtung <TOWARD_SIGN> zu nehmen."}, "empty_street_name_labels": ["<PERSON><PERSON>we<PERSON>", "Radwe<PERSON>", "Mountainbike Strecke", "Fußgängerübergang", "the stairs", "the bridge", "the tunnel"], "relative_directions": ["Links", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "example_phrases": {"0": ["Keep left at the fork.", "Keep straight at the fork.", "Keep right at the fork."], "1": ["Keep right to take exit 62."], "2": ["Keep right to take I 895 South."], "3": ["Keep right to take exit 62 onto I 895 South."], "4": ["Keep right toward Annapolis."], "5": ["Keep right to take exit 62 toward Annapolis."], "6": ["Keep right to take I 895 South toward Annapolis."], "7": ["Keep right to take exit 62 onto I 895 South toward Annapolis."]}}, "keep_to_stay_on": {"phrases": {"0": "Auf <STREET_NAMES> <RELATIVE_DIRECTION> halten.", "1": "Auf <STREET_NAMES> über die Ausfahrt <NUMBER_SIGN> <RELATIVE_DIRECTION> halten.", "2": "Auf <STREET_NAMES> über die Ausfahrt <NUMBER_SIGN> <RELATIVE_DIRECTION> halten.", "3": "Auf <STREET_NAMES> über die Ausfahrt <NUMBER_SIGN> <RELATIVE_DIRECTION> halten Richtung <TOWARD_SIGN>."}, "empty_street_name_labels": ["<PERSON><PERSON>we<PERSON>", "Radwe<PERSON>", "Mountainbike Strecke", "Fußgängerübergang", "the stairs", "the bridge", "the tunnel"], "relative_directions": ["links", "g<PERSON><PERSON><PERSON>", "rechts"], "example_phrases": {"0": ["Keep left to stay on I 95 South.", "Keep straight to stay on I 95 South.", "Keep right to stay on I 95 South."], "1": ["Keep left to take exit 62 to stay on I 95 South."], "2": ["Keep left to stay on I 95 South toward Baltimore."], "3": ["Keep left to take exit 62 to stay on I 95 South toward Baltimore."]}}, "keep_to_stay_on_verbal": {"phrases": {"0": "Auf <STREET_NAMES> <RELATIVE_DIRECTION> halten.", "1": "Auf <STREET_NAMES> über die Ausfahrt <NUMBER_SIGN> <RELATIVE_DIRECTION> halten.", "2": "Auf <STREET_NAMES> über die Ausfahrt <NUMBER_SIGN> <RELATIVE_DIRECTION> halten.", "3": "Auf <STREET_NAMES> über die Ausfahrt <NUMBER_SIGN> <RELATIVE_DIRECTION> halten Richtung <TOWARD_SIGN>."}, "empty_street_name_labels": ["<PERSON><PERSON>we<PERSON>", "Radwe<PERSON>", "Mountainbike Strecke", "Fußgängerübergang", "the stairs", "the bridge", "the tunnel"], "relative_directions": ["links", "g<PERSON><PERSON><PERSON>", "rechts"], "example_phrases": {"0": ["Keep left to stay on Interstate 95 South.", "Keep straight to stay on Interstate 95 South.", "Keep right to stay on Interstate 95 South."], "1": ["Keep left to take exit 62 to stay on Interstate 95 South."], "2": ["Keep left to stay on I 95 South toward Baltimore."], "3": ["Keep left to take exit 62 to stay on Interstate 95 South toward Baltimore."]}}, "keep_verbal": {"phrases": {"0": "<RELATIVE_DIRECTION> halten an der Gabelung.", "1": "<RELATIVE_DIRECTION> halten auf die Ausfahrt <NUMBER_SIGN>.", "2": "<RELATIVE_DIRECTION> halten auf <STREET_NAMES>.", "3": "<RELATIVE_DIRECTION> halten um die Ausfahrt <NUMBER_SIGN> auf <STREET_NAMES> zu nehmen.", "4": "<RELATIVE_DIRECTION> halten Richtung <TOWARD_SIGN>.", "5": "<RELATIVE_DIRECTION> halten um die Ausfahrt <NUMBER_SIGN> Richtung <TOWARD_SIGN> zu nehmen.", "6": "<RELATIVE_DIRECTION> halten auf <STREET_NAMES> Richtung <TOWARD_SIGN>.", "7": "<RELATIVE_DIRECTION> halten um die Ausfahrt <NUMBER_SIGN> auf <STREET_NAMES> Richtung <TOWARD_SIGN> zu nehmen."}, "empty_street_name_labels": ["<PERSON><PERSON>we<PERSON>", "Radwe<PERSON>", "Mountainbike Strecke", "Fußgängerübergang", "the stairs", "the bridge", "the tunnel"], "relative_directions": ["Links", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "example_phrases": {"0": ["Keep left at the fork.", "Keep straight at the fork.", "Keep right at the fork."], "1": ["Keep right to take exit 62."], "2": ["Keep right to take Interstate 8 95 South."], "3": ["Keep right to take exit 62 onto Interstate 8 95 South."], "4": ["Keep right toward Annapolis."], "5": ["Keep right to take exit 62 toward Annapolis."], "6": ["Keep right to take Interstate 8 95 South toward Annapolis."], "7": ["Keep right to take exit 62 onto Interstate 8 95 South toward Annapolis."]}}, "merge": {"phrases": {"0": "Einfädeln.", "1": "<RELATIVE_DIRECTION> einfädeln.", "2": "Auf <STREET_NAMES> einfädeln.", "3": "<RELATIVE_DIRECTION> auf <STREET_NAMES> einfädeln.", "4": "In Richtung <TOWARD_SIGN> einfädeln.", "5": "In Richtung <TOWARD_SIGN> nach <RELATIVE_DIRECTION> einfädeln."}, "relative_directions": ["Links", "<PERSON><PERSON><PERSON>"], "empty_street_name_labels": ["<PERSON><PERSON>we<PERSON>", "Radwe<PERSON>", "Mountainbike Strecke", "Fußgängerübergang", "the stairs", "the bridge", "the tunnel"], "example_phrases": {"0": ["Merge."], "1": ["<PERSON><PERSON> left."], "2": ["Merge onto I 76 West/Pennsylvania Turnpike."], "3": ["Merge right onto I 83 South."], "4": ["Merge toward Baltimore."], "5": ["Merge right toward Baltimore."]}}, "merge_verbal": {"phrases": {"0": "Einfädeln.", "1": "<RELATIVE_DIRECTION> einfädeln.", "2": "Auf <STREET_NAMES> einfädeln.", "3": "<RELATIVE_DIRECTION> auf <STREET_NAMES> einfädeln.", "4": "In Richtung <TOWARD_SIGN> einfädeln.", "5": "In Richtung <TOWARD_SIGN> nach <RELATIVE_DIRECTION> einfädeln."}, "relative_directions": ["Links", "<PERSON><PERSON><PERSON>"], "empty_street_name_labels": ["<PERSON><PERSON>we<PERSON>", "Radwe<PERSON>", "Mountainbike Strecke", "Fußgängerübergang", "the stairs", "the bridge", "the tunnel"], "example_phrases": {"0": ["Merge."], "1": ["<PERSON><PERSON> left."], "2": ["Merge onto I 76 West/Pennsylvania Turnpike."], "3": ["Merge right onto I 83 South."], "4": ["Merge toward Baltimore."], "5": ["Merge right toward Baltimore."]}}, "post_transition_transit_verbal": {"phrases": {"0": "<TRANSIT_STOP_COUNT> <TRANSIT_STOP_COUNT_LABEL> fahren."}, "transit_stop_count_labels": {"one": "<PERSON><PERSON><PERSON>", "other": "<PERSON><PERSON><PERSON><PERSON>"}, "example_phrases": {"0": ["Travel 1 stop.", "Travel 3 stops."]}}, "post_transition_verbal": {"phrases": {"0": "<LENGTH> weiter der Route folgen.", "1": "<LENGTH> weiter auf <STREET_NAMES>."}, "empty_street_name_labels": ["<PERSON><PERSON>we<PERSON>", "Radwe<PERSON>", "Mountainbike Strecke", "Fußgängerübergang", "the stairs", "the bridge", "the tunnel"], "metric_lengths": ["<KILOMETERS> Kilometer", "einen Kilometer", "<METERS> Meter", "weniger als 10 Meter"], "us_customary_lengths": ["<MILES> <PERSON><PERSON>", "eine <PERSON>", "eine halbe <PERSON>", "eine viertel Meile", "<FEET> <PERSON><PERSON>", "weniger als 10 Fuß"], "example_phrases": {"0": ["Continue for 300 feet.", "Continue for 9 miles."], "1": ["Continue on Pennsylvania 7 43 for 6.2 miles.", "Continue on Main Street, Vermont 30 for 1 tenth of a mile."]}}, "ramp": {"phrases": {"0": "Auf die Auffahrt leicht nach <RELATIVE_DIRECTION> abbiegen.", "1": "Auf die Auffahrt leicht nach <RELATIVE_DIRECTION> auf <BRANCH_SIGN> abbie<PERSON>.", "2": "Auf die Auffahrt leicht nach <RELATIVE_DIRECTION> abbie<PERSON> Richtung <TOWARD_SIGN>.", "3": "Auf die Auffahrt leicht nach <RELATIVE_DIRECTION> auf <BRANCH_SIGN> abb<PERSON><PERSON> Rich<PERSON>g <TOWARD_SIGN>.", "4": "Auf die Auffahrt <NAME_SIGN> leicht nach <RELATIVE_DIRECTION> abbiegen.", "5": "Auf die Auffahrt nach <RELATIVE_DIRECTION> abbiegen.", "6": "Auf die Auffahrt nach <RELATIVE_DIRECTION> auf <BRANCH_SIGN> abbie<PERSON>.", "7": "Auf die Auffahrt nach <RELATIVE_DIRECTION> a<PERSON><PERSON><PERSON> Richtung <TOWARD_SIGN>.", "8": "Auf die Auffahrt nach <RELATIVE_DIRECTION> auf <BRANCH_SIGN> abb<PERSON><PERSON> Rich<PERSON>g <TOWARD_SIGN>.", "9": "Auf die Auffahrt <NAME_SIGN> nach <RELATIVE_DIRECTION> abbiegen.", "10": "Auf die Auffahrt abbiegen.", "11": "Auf die Auffahrt <BRANCH_SIGN> abbiegen.", "12": "Auf die Auffahrt abbiegen Richtung <TOWARD_SIGN>.", "13": "Auf die Auffahrt <BRANCH_SIGN> a<PERSON><PERSON><PERSON> <TOWARD_SIGN>.", "14": "Auf die Auffahrt <NAME_SIGN> abbiegen."}, "relative_directions": ["links", "rechts"], "example_phrases": {"0": ["Take the ramp on the left.", "Take the ramp on the right."], "1": ["Take the I 95 ramp on the right."], "2": ["Take the ramp on the left toward JFK."], "3": ["Take the South Conduit Avenue ramp on the left toward JFK."], "4": ["Take the Gettysburg Pike ramp on the right."], "5": ["Turn left to take the ramp.", "Turn right to take the ramp."], "6": ["Turn left to take the PA 283 West ramp."], "7": ["Turn left to take the ramp toward Harrisburg/Harrisburg International Airport."], "8": ["Turn left to take the PA 283 West ramp toward Harrisburg/Harrisburg International Airport."], "9": ["Turn right to take the Gettysburg Pike ramp."], "10": ["Take the ramp."], "11": ["Take the I 95 ramp."], "12": ["Take the ramp toward JFK."], "13": ["Take the Soutch Conduit Avenue ramp toward JFK."], "14": ["Take the Gettysburg ramp."]}}, "ramp_straight": {"phrases": {"0": "Geradeaus auf die Auffahrt fahren.", "1": "Auf <BRANCH_SIGN> geradeaus auf die Auffahrt fahren.", "2": "Geradeaus auf die Auffahrt fahren Richtung <TOWARD_SIGN>.", "3": "Auf <BRANCH_SIGN> geradeaus auf die Auffahrt fahren Richtung <TOWARD_SIGN>.", "4": "Geradeaus auf die Auffahrt <NAME_SIGN> fahren."}, "example_phrases": {"0": ["Stay straight to take the ramp."], "1": ["Stay straight to take the US 322 East ramp."], "2": ["Stay straight to take the ramp toward Hershey."], "3": ["Stay straight to take the US 322 East/US 422 East/US 522 East/US 622 East ramp toward Hershey/Palmdale/Palmyra/Campbelltown."], "4": ["Stay straight to take the Gettysburg Pike ramp."]}}, "ramp_straight_verbal": {"phrases": {"0": "Geradeaus auf die Auffahrt fahren.", "1": "Auf <BRANCH_SIGN> geradeaus auf die Auffahrt fahren.", "2": "Geradeaus auf die Auffahrt fahren Richtung <TOWARD_SIGN>.", "3": "Auf <BRANCH_SIGN> geradeaus auf die Auffahrt fahren Richtung <TOWARD_SIGN>.", "4": "Geradeaus auf die Auffahrt <NAME_SIGN> fahren."}, "example_phrases": {"0": ["Stay straight to take the ramp."], "1": ["Stay straight to take the U.S. 3 22 East ramp."], "2": ["Stay straight to take the ramp toward Hershey."], "3": ["Stay straight to take the U.S. 3 22 East, U.S. 4 22 East ramp toward Hershey, Palmdale."], "4": ["Stay straight to take the Gettysburg Pike ramp."]}}, "ramp_verbal": {"phrases": {"0": "Auf die Auffahrt leicht nach <RELATIVE_DIRECTION> abbiegen.", "1": "Auf die Auffahrt leicht nach <RELATIVE_DIRECTION> auf <BRANCH_SIGN> abbie<PERSON>.", "2": "Auf die Auffahrt leicht nach <RELATIVE_DIRECTION> abbie<PERSON> Richtung <TOWARD_SIGN>.", "3": "Auf die Auffahrt leicht nach <RELATIVE_DIRECTION> auf <BRANCH_SIGN> abb<PERSON><PERSON> Rich<PERSON>g <TOWARD_SIGN>.", "4": "Auf die Auffahrt <NAME_SIGN> leicht nach <RELATIVE_DIRECTION> abbiegen.", "5": "Auf die Auffahrt nach <RELATIVE_DIRECTION> abbiegen.", "6": "Auf die Auffahrt nach <RELATIVE_DIRECTION> auf <BRANCH_SIGN> abbie<PERSON>.", "7": "Auf die Auffahrt nach <RELATIVE_DIRECTION> a<PERSON><PERSON><PERSON> Richtung <TOWARD_SIGN>.", "8": "Auf die Auffahrt nach <RELATIVE_DIRECTION> auf <BRANCH_SIGN> abb<PERSON><PERSON> Rich<PERSON>g <TOWARD_SIGN>.", "9": "Auf die Auffahrt <NAME_SIGN> nach <RELATIVE_DIRECTION> abbiegen.", "10": "Auf die Auffahrt abbiegen.", "11": "Auf die Auffahrt <BRANCH_SIGN> abbiegen.", "12": "Auf die Auffahrt abbiegen Richtung <TOWARD_SIGN>.", "13": "Auf die Auffahrt <BRANCH_SIGN> a<PERSON><PERSON><PERSON> <TOWARD_SIGN>.", "14": "Auf die Auffahrt <NAME_SIGN> abbiegen."}, "relative_directions": ["links", "rechts"], "example_phrases": {"0": ["Take the ramp on the left.", "Take the ramp on the right."], "1": ["Take the Interstate 95 ramp on the right."], "2": ["Take the ramp on the left toward JFK."], "3": ["Take the South Conduit Avenue ramp on the left toward JFK."], "4": ["Take the Gettysburg Pike ramp on the right."], "5": ["Turn left to take the ramp.", "Turn right to take the ramp."], "6": ["Turn left to take the Pennsylvania 2 83 West ramp."], "7": ["Turn left to take the ramp toward Harrisburg/Harrisburg International Airport."], "8": ["Turn left to take the Pennsylvania 2 83 West ramp toward Harrisburg, Harrisburg International Airport."], "9": ["Turn right to take the Gettysburg Pike ramp."], "10": ["Take the ramp."], "11": ["Take the Interstate 95 ramp."], "12": ["Take the ramp toward JFK."], "13": ["Take the South Conduit Avenue ramp toward JFK."], "14": ["Take the Gettysburg Pike ramp."]}}, "sharp": {"phrases": {"0": "Scharf nach <RELATIVE_DIRECTION> abbiegen.", "1": "Scharf nach <RELATIVE_DIRECTION> auf <STREET_NAMES> abbiegen.", "2": "Scharf nach <RELATIVE_DIRECTION> auf <BEGIN_STREET_NAMES> abbiegen. Dann weiter auf <STREET_NAMES>.", "3": "Scharf nach <RELATIVE_DIRECTION> abbiegen um auf <STREET_NAMES> zu bleiben.", "4": "An <JUNCTION_NAME> scharf nach <RELATIVE_DIRECTION> abbiegen.", "5": "Scharf nach <RELATIVE_DIRECTION> in Richtung <TOWARD_SIGN> abbiegen."}, "empty_street_name_labels": ["<PERSON><PERSON>we<PERSON>", "Radwe<PERSON>", "Mountainbike Strecke", "Fußgängerübergang", "the stairs", "the bridge", "the tunnel"], "relative_directions": ["links", "rechts"], "example_phrases": {"0": ["Make a sharp right."], "1": ["Make a sharp left onto Arlen Road."], "2": ["Make a sharp right onto Belair Road/US 1 Business. Continue on US 1 Business."], "3": ["Make a sharp left to stay on US 15 South."], "4": ["Make a sharp right at Mannenbashi East."], "5": ["Make a sharp left toward Baltimore."]}}, "sharp_verbal": {"phrases": {"0": "Scharf nach <RELATIVE_DIRECTION> abbiegen.", "1": "Scharf nach <RELATIVE_DIRECTION> auf <STREET_NAMES> abbiegen.", "2": "Scharf nach <RELATIVE_DIRECTION> auf <BEGIN_STREET_NAMES> abbiegen.", "3": "Scharf nach <RELATIVE_DIRECTION> abbiegen um auf <STREET_NAMES> zu bleiben.", "4": "An <JUNCTION_NAME> scharf nach <RELATIVE_DIRECTION> abbiegen.", "5": "Scharf nach <RELATIVE_DIRECTION> in Richtung <TOWARD_SIGN> abbiegen."}, "empty_street_name_labels": ["<PERSON><PERSON>we<PERSON>", "Radwe<PERSON>", "Mountainbike Strecke", "Fußgängerübergang", "the stairs", "the bridge", "the tunnel"], "relative_directions": ["links", "rechts"], "example_phrases": {"0": ["Make a sharp right."], "1": ["Make a sharp left onto Arlen Road."], "2": ["Make a sharp right onto Belair Road, U.S. 1 Business."], "3": ["Make a sharp left to stay on U.S. 15 South."], "4": ["Make a sharp right at Mannenbashi East."], "5": ["Make a sharp left toward Baltimore."]}}, "start": {"phrases": {"0": "Richtung <CARDINAL_DIRECTION>.", "1": "Auf <STREET_NAMES> Richtung <CARDINAL_DIRECTION>.", "2": "Auf <BEGIN_STREET_NAMES> Richtung <CARDINAL_DIRECTION>. Dann weiter auf <STREET_NAMES>.", "4": "Richtung <CARDINAL_DIRECTION> fahren.", "5": "Auf <STREET_NAMES> Richtung <CARDINAL_DIRECTION> fahren.", "6": "Auf <BEGIN_STREET_NAMES> Richtung <CARDINAL_DIRECTION> fahren. Dann weiter auf <STREET_NAMES>.", "8": "Richtung <CARDINAL_DIRECTION> laufen.", "9": "Auf <STREET_NAMES> Richtung <CARDINAL_DIRECTION> laufen.", "10": "Auf <BEGIN_STREET_NAMES> Richtung <CARDINAL_DIRECTION> laufen. Dann weiter auf <STREET_NAMES>.", "16": "Richtung <CARDINAL_DIRECTION> radeln.", "17": "Auf <STREET_NAMES> Richtung <CARDINAL_DIRECTION> radeln.", "18": "Auf <BEGIN_STREET_NAMES> Richtung <CARDINAL_DIRECTION> radeln. Dann weiter auf <STREET_NAMES>."}, "cardinal_directions": ["Norden", "Nordosten", "<PERSON><PERSON>", "Südosten", "S<PERSON>den", "Südwesten", "Westen", "Nordwesten"], "empty_street_name_labels": ["<PERSON><PERSON>we<PERSON>", "Radwe<PERSON>", "Mountainbike Strecke", "Fußgängerübergang", "the stairs", "the bridge", "the tunnel"], "example_phrases": {"0": ["<PERSON><PERSON><PERSON>.", "Richtung Norden."], "1": ["Auf Hauptstraße Richtung Südwesten.", "Head west on the walkway", "Head east on the cycleway", "Head north on the mountain bike trail"], "2": ["Auf A 1 Richtung Süden. Dann weiter auf B 13."], "4": ["Drive east.", "Drive north."], "5": ["Drive southwest on 5th Avenue."], "6": ["Drive south on North Prince Street/US 222/PA 272. Continue on US 222/PA 272."], "8": ["Walk east.", "Walk north."], "9": ["Walk southwest on 5th Avenue.", "Walk west on the walkway"], "10": ["Walk south on North Prince Street/US 222/PA 272. Continue on US 222/PA 272."], "16": ["Bike east.", "Bike north."], "17": ["Bike southwest on 5th Avenue.", "Bike east on the cycleway", "Bike north on the mountain bike trail"], "18": ["Bike south on North Prince Street/US 222/PA 272. Continue on US 222/PA 272."]}}, "start_verbal": {"phrases": {"0": "Richtung <CARDINAL_DIRECTION>.", "1": "<LENGTH> Richtung <CARDINAL_DIRECTION>.", "2": "Auf <STREET_NAMES> Richtung <CARDINAL_DIRECTION>.", "3": "<LENGTH> auf <STREET_NAMES> Richtung <CARDINAL_DIRECTION>.", "4": "Auf <BEGIN_STREET_NAMES> Richtung <CARDINAL_DIRECTION>.", "5": "Richtung <CARDINAL_DIRECTION> fahren.", "6": "<LENGTH> Richtung <CARDINAL_DIRECTION> fahren.", "7": "Auf <STREET_NAMES> Richtung <CARDINAL_DIRECTION> fahren.", "8": "<LENGTH> auf <STREET_NAMES> Richtung <CARDINAL_DIRECTION> fahren.", "9": "Auf <BEGIN_STREET_NAMES> Richtung <CARDINAL_DIRECTION> fahren.", "10": "Richtung <CARDINAL_DIRECTION> laufen.", "11": "<LENGTH> Richtung <CARDINAL_DIRECTION> laufen.", "12": "Auf <STREET_NAMES> Richtung <CARDINAL_DIRECTION> laufen.", "13": "<LENGTH> auf <STREET_NAMES> Richtung <CARDINAL_DIRECTION> laufen.", "14": "Auf <BEGIN_STREET_NAMES> Richtung <CARDINAL_DIRECTION> laufen.", "15": "Richtung <CARDINAL_DIRECTION> radeln.", "16": "<LENGTH> Richtung <CARDINAL_DIRECTION> radeln.", "17": "Auf <STREET_NAMES> Richtung <CARDINAL_DIRECTION> radeln.", "18": "<LENGTH> auf <STREET_NAMES> Richtung <CARDINAL_DIRECTION> radeln.", "19": "Auf <BEGIN_STREET_NAMES> Richtung <CARDINAL_DIRECTION> radeln."}, "cardinal_directions": ["Norden", "Nordosten", "<PERSON><PERSON>", "Südosten", "S<PERSON>den", "Südwesten", "Westen", "Nordwesten"], "empty_street_name_labels": ["<PERSON><PERSON>we<PERSON>", "Radwe<PERSON>", "Mountainbike Strecke", "Fußgängerübergang", "the stairs", "the bridge", "the tunnel"], "metric_lengths": ["<KILOMETERS> Kilometer", "einen Kilometer", "<METERS> Meter", "weniger als 10 Meter"], "us_customary_lengths": ["<MILES> <PERSON><PERSON>", "eine <PERSON>", "eine halbe <PERSON>", "eine viertel Meile", "<FEET> <PERSON><PERSON>", "weniger als 10 Fuß"], "example_phrases": {"0": ["Head east.", "Head north."], "1": ["Head east for a half mile.", "Head north for 1 kilometer."], "2": ["Head southwest on 5th Avenue."], "3": ["Head southwest on 5th Avenue for 1 tenth of a mile."], "4": ["Head south on North Prince Street, U.S. 2 22."], "5": ["Drive east.", "Drive north."], "6": ["Drive east for a half mile.", "Drive north for 1 kilometer."], "7": ["Drive southwest on 5th Avenue."], "8": ["Drive southwest on 5th Avenue for 1 tenth of a mile."], "9": ["Drive south on North Prince Street, U.S. 2 22."], "10": ["Walk east.", "Walk north."], "11": ["Walk east for a half mile.", "Walk north for 1 kilometer."], "12": ["Walk southwest on 5th Avenue."], "13": ["Walk southwest on 5th Avenue for 1 tenth of a mile."], "14": ["Walk south on North Prince Street, U.S. 2 22."], "15": ["Bike east.", "Bike north."], "16": ["Bike east for a half mile.", "Bike north for 1 kilometer."], "17": ["Bike southwest on 5th Avenue."], "18": ["Bike southwest on 5th Avenue for 1 tenth of a mile."], "19": ["Bike south on North Prince Street, U.S. 2 22."]}}, "transit": {"phrases": {"0": "Mit <TRANSIT_NAME> fahren. (<TRANSIT_STOP_COUNT> <TRANSIT_STOP_COUNT_LABEL>)", "1": "Mit <TRANSIT_NAME> Richtung <TRANSIT_HEADSIGN> fahren. (<TRANSIT_STOP_COUNT> <TRANSIT_STOP_COUNT_LABEL>)"}, "empty_transit_name_labels": ["Straßenbahn", "U-Bahn", "<PERSON>ug", "Bus", "<PERSON><PERSON><PERSON><PERSON>", "Kabelbahn", "Luftseilbahn", "Standseilbahn"], "transit_stop_count_labels": {"one": "<PERSON><PERSON><PERSON>", "other": "<PERSON><PERSON><PERSON><PERSON>"}, "example_phrases": {"0": ["Take the New Haven. (1 stop)", "Take the metro. (2 stops)", "Take the bus. (12 stops)"], "1": ["Take the F toward JAMAICA - 179 ST. (10 stops)", "Take the ferry toward Staten Island. (1 stop)"]}}, "transit_connection_destination": {"phrases": {"0": "Bahnhof verlassen.", "1": "<TRANSIT_STOP> verlassen.", "2": "<STATION_LABEL> <TRANSIT_STOP> verlassen."}, "station_label": "Bahnhof", "example_phrases": {"0": ["Exit the station."], "1": ["Exit the Embarcadero Station."], "2": ["Exit the 8 St - NYU Station."]}}, "transit_connection_destination_verbal": {"phrases": {"0": "Bahnhof verlassen.", "1": "<TRANSIT_STOP> verlassen.", "2": "<STATION_LABEL> <TRANSIT_STOP> verlassen."}, "station_label": "Bahnhof", "example_phrases": {"0": ["Exit the station."], "1": ["Exit the Embarcadero Station."], "2": ["Exit the 8 St - NYU Station."]}}, "transit_connection_start": {"phrases": {"0": "Bahnhof betreten.", "1": "<TRANSIT_STOP> betreten.", "2": "<STATION_LABEL> <TRANSIT_STOP> betreten."}, "station_label": "Bahnhof", "example_phrases": {"0": ["Enter the station."], "1": ["Enter the Embarcadero Station."], "2": ["Enter the 8 St - NYU Station."]}}, "transit_connection_start_verbal": {"phrases": {"0": "Bahnhof betreten.", "1": "<TRANSIT_STOP> betreten.", "2": "<STATION_LABEL> <TRANSIT_STOP> betreten."}, "station_label": "Bahnhof", "example_phrases": {"0": ["Enter the station."], "1": ["Enter the Embarcadero Station."], "2": ["Enter the 8 St - NYU Station."]}}, "transit_connection_transfer": {"phrases": {"0": "Am Bahnhof umsteigen.", "1": "Am <TRANSIT_STOP> umsteigen.", "2": "Am <STATION_LABEL> <TRANSIT_STOP> umsteigen."}, "station_label": "Bahnhof", "example_phrases": {"0": ["Transfer at the station."], "1": ["Transfer at the Embarcadero Station."], "2": ["Transfer at the 8 St - NYU Station."]}}, "transit_connection_transfer_verbal": {"phrases": {"0": "Am Bahnhof umsteigen.", "1": "Am <TRANSIT_STOP> umsteigen.", "2": "Am <STATION_LABEL> <TRANSIT_STOP> umsteigen."}, "station_label": "Bahnhof", "example_phrases": {"0": ["Transfer at the station."], "1": ["Transfer at the Embarcadero Station."], "2": ["Transfer at the 8 St - NYU Station."]}}, "transit_remain_on": {"phrases": {"0": "Mit <TRANSIT_NAME> weiter. (<TRANSIT_STOP_COUNT> <TRANSIT_STOP_COUNT_LABEL>)", "1": "Mit <TRANSIT_NAME> Richtung <TRANSIT_HEADSIGN> weiter. (<TRANSIT_STOP_COUNT> <TRANSIT_STOP_COUNT_LABEL>)"}, "empty_transit_name_labels": ["Straßenbahn", "U-Bahn", "<PERSON>ug", "Bus", "<PERSON><PERSON><PERSON><PERSON>", "Kabelbahn", "Luftseilbahn", "Standseilbahn"], "transit_stop_count_labels": {"one": "<PERSON><PERSON><PERSON>", "other": "<PERSON><PERSON><PERSON><PERSON>"}, "example_phrases": {"0": ["Remain on the New Haven. (1 stop)", "<PERSON><PERSON><PERSON> on the train. (3 stops)"], "1": ["Remain on the F toward JAMAICA - 179 ST. (10 stops)"]}}, "transit_remain_on_verbal": {"phrases": {"0": "Mit <TRANSIT_NAME> weiter fahren.", "1": "Mit <TRANSIT_NAME> Richtung <TRANSIT_HEADSIGN> weiter fahren."}, "empty_transit_name_labels": ["Straßenbahn", "U-Bahn", "<PERSON>ug", "Bus", "<PERSON><PERSON><PERSON><PERSON>", "Kabelbahn", "Luftseilbahn", "Standseilbahn"], "example_phrases": {"0": ["<PERSON><PERSON><PERSON> on the New Haven."], "1": ["Remain on the F toward JAMAICA - 179 ST."]}}, "transit_transfer": {"phrases": {"0": "Zu <TRANSIT_NAME> umsteigen. (<TRANSIT_STOP_COUNT> <TRANSIT_STOP_COUNT_LABEL>)", "1": "Zu <TRANSIT_NAME> Richtung <TRANSIT_HEADSIGN> umsteigen. (<TRANSIT_STOP_COUNT> <TRANSIT_STOP_COUNT_LABEL>)"}, "empty_transit_name_labels": ["Straßenbahn", "U-Bahn", "<PERSON>ug", "Bus", "<PERSON><PERSON><PERSON><PERSON>", "Kabelbahn", "Luftseilbahn", "Standseilbahn"], "transit_stop_count_labels": {"one": "<PERSON><PERSON><PERSON>", "other": "<PERSON><PERSON><PERSON><PERSON>"}, "example_phrases": {"0": ["Transfer to take the New Haven. (1 stop)", "Transfer to take the tram. (4 stops)"], "1": ["Transfer to take the F toward JAMAICA - 179 ST. (10 stops)"]}}, "transit_transfer_verbal": {"phrases": {"0": "Zu <TRANSIT_NAME> umsteigen.", "1": "Zu <TRANSIT_NAME> Richtung <TRANSIT_HEADSIGN> umsteigen."}, "empty_transit_name_labels": ["Straßenbahn", "U-Bahn", "<PERSON>ug", "Bus", "<PERSON><PERSON><PERSON><PERSON>", "Kabelbahn", "Luftseilbahn", "Standseilbahn"], "example_phrases": {"0": ["Transfer to take the New Haven."], "1": ["Transfer to take the F toward JAMAICA - 179 ST."]}}, "transit_verbal": {"phrases": {"0": "Mit <TRANSIT_NAME> fahren.", "1": "Mit <TRANSIT_NAME> Richtung <TRANSIT_HEADSIGN> fahren."}, "empty_transit_name_labels": ["Straßenbahn", "U-Bahn", "<PERSON>ug", "Bus", "<PERSON><PERSON><PERSON><PERSON>", "Kabelbahn", "Luftseilbahn", "Standseilbahn"], "example_phrases": {"0": ["Take the New Haven."], "1": ["Take the F toward JAMAICA - 179 ST."]}}, "turn": {"phrases": {"0": "<RELATIVE_DIRECTION> abbiegen.", "1": "<RELATIVE_DIRECTION> auf <STREET_NAMES> abbiegen.", "2": "<RELATIVE_DIRECTION> auf <BEGIN_STREET_NAMES> abbiegen. Dann weiter auf <STREET_NAMES>.", "3": "<RELATIVE_DIRECTION> abbiegen um auf <STREET_NAMES> zu bleiben.", "4": "An <JUNCTION_NAME> nach <RELATIVE_DIRECTION> abbiegen.", "5": "In Richtung <TOWARD_SIGN> nach <RELATIVE_DIRECTION> abbie<PERSON>."}, "empty_street_name_labels": ["<PERSON><PERSON>we<PERSON>", "Radwe<PERSON>", "Mountainbike Strecke", "Fußgängerübergang", "the stairs", "the bridge", "the tunnel"], "relative_directions": ["Links", "<PERSON><PERSON><PERSON>"], "example_phrases": {"0": ["Turn left."], "1": ["Turn right onto Flatbush Avenue."], "2": ["Turn left onto North Bond Street/US 1 Business/MD 924. Continue on MD 924."], "3": ["Turn right to stay on Sunstone Drive."], "4": ["Turn right at Mannenbashi East."], "5": ["Turn left toward Baltimore."]}}, "turn_verbal": {"phrases": {"0": "<RELATIVE_DIRECTION> abbiegen.", "1": "<RELATIVE_DIRECTION> auf <STREET_NAMES> abbiegen.", "2": "<RELATIVE_DIRECTION> auf <BEGIN_STREET_NAMES> abbiegen. Dann weiter auf <STREET_NAMES>.", "3": "<RELATIVE_DIRECTION> abbiegen um auf <STREET_NAMES> zu bleiben.", "4": "An <JUNCTION_NAME> nach <RELATIVE_DIRECTION> abbiegen.", "5": "In Richtung <TOWARD_SIGN> nach <RELATIVE_DIRECTION> abbie<PERSON>."}, "empty_street_name_labels": ["<PERSON><PERSON>we<PERSON>", "Radwe<PERSON>", "Mountainbike Strecke", "Fußgängerübergang", "the stairs", "the bridge", "the tunnel"], "relative_directions": ["Links", "<PERSON><PERSON><PERSON>"], "example_phrases": {"0": ["Turn left."], "1": ["Turn right onto Flatbush Avenue."], "2": ["Turn left onto North Bond Street, U.S. 1 Business."], "3": ["Turn right to stay on Sunstone Drive."], "4": ["Turn right at Mannenbashi East."], "5": ["Turn left toward Baltimore."]}}, "uturn": {"phrases": {"0": "Nach <RELATIVE_DIRECTION> eine Kehrtwende machen.", "1": "Nach <RELATIVE_DIRECTION> eine Kehrtwende auf <STREET_NAMES> machen.", "2": "Nach <RELATIVE_DIRECTION> eine <PERSON>twende machen um auf <STREET_NAMES> zu bleiben.", "3": "Nach <RELATIVE_DIRECTION> eine Kehrtwende bei <CROSS_STREET_NAMES> machen.", "4": "Nach <RELATIVE_DIRECTION> eine Kehrtwende bei <CROSS_STREET_NAMES> auf <STREET_NAMES> machen.", "5": "Nach <RELATIVE_DIRECTION> eine Kehrtwende bei <CROSS_STREET_NAMES> machen um auf <STREET_NAMES> zu bleiben.", "6": "An <JUNCTION_NAME> eine Kehrtwende nach <RELATIVE_DIRECTION> machen.", "7": "In Richtung <TOWARD_SIGN> eine Kehrtwende nach <RELATIVE_DIRECTION> machen."}, "empty_street_name_labels": ["<PERSON><PERSON>we<PERSON>", "Radwe<PERSON>", "Mountainbike Strecke", "Fußgängerübergang", "the stairs", "the bridge", "the tunnel"], "relative_directions": ["links", "rechts"], "example_phrases": {"0": ["Make a left U-turn."], "1": ["Make a right U-turn onto Bunker Hill Road."], "2": ["Make a left U-turn to stay on Bunker Hill Road."], "3": ["Make a left U-turn at Devonshire Road."], "4": ["Make a left U-turn at Devonshire Road onto Jonestown Road/US 22."], "5": ["Make a left U-turn at Devonshire Road to stay on Jonestown Road/US 22."], "6": ["Make a right U-turn at Mannenbashi East."], "7": ["Make a left U-turn toward Baltimore."]}}, "uturn_verbal": {"phrases": {"0": "Nach <RELATIVE_DIRECTION> eine Kehrtwende machen.", "1": "Nach <RELATIVE_DIRECTION> eine Kehrtwende auf <STREET_NAMES> machen.", "2": "Nach <RELATIVE_DIRECTION> eine <PERSON>twende machen um auf <STREET_NAMES> zu bleiben.", "3": "Nach <RELATIVE_DIRECTION> eine Kehrtwende bei <CROSS_STREET_NAMES> machen.", "4": "Nach <RELATIVE_DIRECTION> eine Kehrtwende bei <CROSS_STREET_NAMES> auf <STREET_NAMES> machen.", "5": "Nach <RELATIVE_DIRECTION> eine Kehrtwende bei <CROSS_STREET_NAMES> machen um auf <STREET_NAMES> zu bleiben.", "6": "An <JUNCTION_NAME> eine Kehrtwende nach <RELATIVE_DIRECTION> machen.", "7": "In Richtung <TOWARD_SIGN> eine Kehrtwende nach <RELATIVE_DIRECTION> machen."}, "empty_street_name_labels": ["<PERSON><PERSON>we<PERSON>", "Radwe<PERSON>", "Mountainbike Strecke", "Fußgängerübergang", "the stairs", "the bridge", "the tunnel"], "relative_directions": ["links", "rechts"], "example_phrases": {"0": ["Make a left U-turn."], "1": ["Make a right U-turn onto Bunker Hill Road."], "2": ["Make a left U-turn to stay on Bunker Hill Road."], "3": ["Make a left U-turn at Devonshire Road."], "4": ["Make a left U-turn at Devonshire Road onto Jonestown Road, U.S. 22."], "5": ["Make a left U-turn at Devonshire Road to stay on Jonestown Road, U.S. 22."], "6": ["Make a right U-turn at Mannenbashi East."], "7": ["Make a left U-turn toward Baltimore."]}}, "verbal_multi_cue": {"phrases": {"0": "<CURRENT_VERBAL_CUE> Dann <NEXT_VERBAL_CUE>", "1": "<CURRENT_VERBAL_CUE> Dan<PERSON>, in <LENGTH>, <NEXT_VERBAL_CUE>"}, "metric_lengths": ["<KILOMETERS> Kilometern", "einen Kilometer", "<METERS> Metern", "weniger als 10 Metern"], "us_customary_lengths": ["<MILES> <PERSON><PERSON>", "eine <PERSON>", "eine halbe <PERSON>", "eine viertel Meile", "<FEET> <PERSON><PERSON>", "weniger als 10 Fuß"], "example_phrases": {"0": ["Bear right onto East Fayette Street. Then Turn right onto North Gay Street."], "1": ["Bear right onto East Fayette Street. Then, in 500 feet, Turn right onto North Gay Street."]}}, "approach_verbal_alert": {"phrases": {"0": "In <LENGTH>, <CURRENT_VERBAL_CUE>"}, "metric_lengths": ["<KILOMETERS> Kilometern", "einen Kilometer", "<METERS> Metern", "weniger als 10 Metern"], "us_customary_lengths": ["<MILES> <PERSON><PERSON>", "eine <PERSON>", "eine halbe <PERSON>", "eine viertel Meile", "<FEET> <PERSON><PERSON>", "weniger als 10 Fuß"], "example_phrases": {"0": ["In a quarter mile, Turn right onto North Gay Street."]}}, "pass": {"phrases": {"0": "Pass <OBJECT_LABEL>.", "1": "Pass traffic signals on <OBJECT_LABEL>."}, "object_labels": ["the gate", "the bollards", "ways intersection"], "example_phrases": {"0": ["Pass the gate."]}}, "elevator": {"phrases": {"0": "<PERSON> Aufzug nehmen.", "1": "Aufzug nehmen nach <LEVEL>."}, "example_phrases": {"0": ["Take the elevator."], "1": ["Take the elevator to Level 1."]}}, "steps": {"phrases": {"0": "Die Treppen nehmen.", "1": "Die Treppen nehmen nach <LEVEL>."}, "example_phrases": {"0": ["Take the stairs."], "1": ["Take the stairs to Level 2."]}}, "escalator": {"phrases": {"0": "Die Rolltreppe nehmen.", "1": "Die Rolltreppe nehmen nach <LEVEL>."}, "example_phrases": {"0": ["Take the escalator."], "1": ["Take the escalator to Level 3."]}}, "enter_building": {"phrases": {"0": "In das Gebäude eintreten.", "1": "In das Gebäude eintreten und weitergehen auf <STREET_NAMES>."}, "empty_street_name_labels": ["<PERSON><PERSON>we<PERSON>", "Radwe<PERSON>", "Mountainbike Strecke", "Fußgängerübergang"], "example_phrases": {"0": ["Enter the building."], "1": ["Enter the building, and continue on the walkway."]}}, "exit_building": {"phrases": {"0": "Das Gebäude verlassen.", "1": "Das Gebäude verlassen und weitergehen auf <STREET_NAMES>."}, "empty_street_name_labels": ["<PERSON><PERSON>we<PERSON>", "Radwe<PERSON>", "Mountainbike Strecke", "Fußgängerübergang"], "example_phrases": {"0": ["Exit the building."], "1": ["Exit the building, and continue on the walkway."]}}}}
{"posix_locale": "tr_TR.UTF-8", "aliases": ["tr"], "instructions": {"arrive": {"phrases": {"0": "Varış: <TIME>.", "1": "Varış: <TIME> <TRANSIT_STOP> durağında."}, "example_phrases": {"0": ["Arrive: 8:02 AM."], "1": ["Arrive: 8:02 AM at 8 St - NYU."]}}, "arrive_verbal": {"phrases": {"0": "Saat <TIME>'da <PERSON>ı<PERSON>.", "1": "Saat <TIME>'da <TRANSIT_STOP> du<PERSON><PERSON><PERSON><PERSON> varı<PERSON>."}, "example_phrases": {"0": ["Arrive at 8:02 AM."], "1": ["Arrive at 8:02 AM at 8 St - NYU."]}}, "bear": {"phrases": {"0": "<RELATIVE_DIRECTION>.", "1": "<STREET_NAMES> caddesine doğru <RELATIVE_DIRECTION> y<PERSON><PERSON>in.", "2": "<BEGIN_STREET_NAMES> caddesine doğru <RELATIVE_DIRECTION> yönelin. <STREET_NAMES> caddesine devam edin.", "3": "<STREET_NAMES> caddesinde kalmak için <RELATIVE_DIRECTION> y<PERSON>nelin.", "4": "<JUNCTION_NAME> kavşağında <RELATIVE_DIRECTION> y<PERSON><PERSON><PERSON>.", "5": "<TOWARD_SIGN> i<PERSON><PERSON><PERSON> doğru <RELATIVE_DIRECTION> y<PERSON><PERSON><PERSON>."}, "empty_street_name_labels": ["yaya ge<PERSON>idi", "bisiklet yolu", "dağ bisikleti yolu", "the crosswalk", "the stairs", "the bridge", "the tunnel"], "relative_directions": ["sol", "sağ"], "example_phrases": {"0": ["Bear right."], "1": ["<PERSON> left onto Arlen Road."], "2": ["Bear right onto Belair Road/US 1 Business. Continue on US 1 Business."], "3": ["<PERSON> left to stay on US 15 South."], "4": ["Bear right at Mannenbashi East."], "5": ["Bear left toward Baltimore."]}}, "bear_verbal": {"phrases": {"0": "<RELATIVE_DIRECTION> y<PERSON><PERSON><PERSON>.", "1": "<STREET_NAMES> caddesine doğru <RELATIVE_DIRECTION> y<PERSON><PERSON>in.", "2": "<BEGIN_STREET_NAMES> caddesine doğ<PERSON> <RELATIVE_DIRECTION> y<PERSON><PERSON>in.", "3": "<STREET_NAMES> caddesinde kalmak için <RELATIVE_DIRECTION> y<PERSON>nelin.", "4": "<JUNCTION_NAME> kavşağında <RELATIVE_DIRECTION> y<PERSON><PERSON><PERSON>.", "5": "<TOWARD_SIGN> i<PERSON><PERSON><PERSON> doğru <RELATIVE_DIRECTION> y<PERSON><PERSON><PERSON>."}, "empty_street_name_labels": ["yaya ge<PERSON>idi", "bisiklet yolu", "dağ bisikleti yolu", "the crosswalk", "the stairs", "the bridge", "the tunnel"], "relative_directions": ["sol", "sağ"], "example_phrases": {"0": ["Bear right."], "1": ["<PERSON> left onto Arlen Road."], "2": ["Bear right onto Belair Road, U.S. 1 Business."], "3": ["<PERSON> left to stay on U.S. 15 South."], "4": ["Bear right at Mannenbashi East."], "5": ["Bear left toward Baltimore."]}}, "becomes": {"phrases": {"0": "<PREVIOUS_STREET_NAMES>, <STREET_NAMES> o<PERSON><PERSON>."}, "example_phrases": {"0": ["Vine Street becomes Middletown Road."]}}, "becomes_verbal": {"phrases": {"0": "<PREVIOUS_STREET_NAMES>, <STREET_NAMES> o<PERSON><PERSON>."}, "example_phrases": {"0": ["Vine Street becomes Middletown Road."]}}, "continue": {"phrases": {"0": "<PERSON>am edin.", "1": "<STREET_NAMES> caddesine devam edin.", "2": "<JUNCTION_NAME> kavşağından devam edin.", "3": "<TOWARD_SIGN> işaretine doğru devam edin."}, "empty_street_name_labels": ["yaya ge<PERSON>idi", "bisiklet yolu", "dağ bisikleti yolu", "the crosswalk", "the stairs", "the bridge", "the tunnel"], "example_phrases": {"0": ["Continue."], "1": ["Continue on 10th Avenue."], "2": ["Continue at Mannenbashi East."], "3": ["Continue toward Baltimore."]}}, "continue_verbal": {"phrases": {"0": "<PERSON>am edin.", "1": "<LENGTH> boyunca devam edin.", "2": "<STREET_NAMES> caddesine devam edin.", "3": "<STREET_NAMES> caddesinde <LENGTH> boyunca devam edin.", "4": "<JUNCTION_NAME> kavşağından devam edin.", "5": "<JUNCTION_NAME> kavşağından <LENGTH> boyunca devam edin.", "6": "<TOWARD_SIGN> işaretine doğru devam edin.", "7": "<TOWARD_SIGN> i<PERSON><PERSON><PERSON> for <LENGTH> boyunca devam edin."}, "empty_street_name_labels": ["yaya ge<PERSON>idi", "bisiklet yolu", "dağ bisikleti yolu", "the crosswalk", "the stairs", "the bridge", "the tunnel"], "metric_lengths": ["<KILOMETERS> kilometre", "1 kilometre", "<METERS> metre", "10 kilometreden az"], "us_customary_lengths": ["<MILES> mil", "1 mil", "yarım mil", "<PERSON><PERSON><PERSON> mil", "<FEET> fit", "10 fitten az"], "example_phrases": {"0": ["Continue."], "1": ["Continue for 300 feet."], "2": ["Continue on 10th Avenue."], "3": ["Continue on 10th Avenue for 3 miles."], "4": ["Continue at Mannenbashi East."], "5": ["Continue at Mannenbashi East for 2 miles."], "6": ["Continue toward Baltimore."], "7": ["Continue toward Baltimore for 5 miles."]}}, "continue_verbal_alert": {"phrases": {"0": "Decam edin.", "1": "<STREET_NAMES>caddesine devam edin.", "2": "<JUNCTION_NAME> kavşağına devam edin.", "3": "<TOWARD_SIGN> işaretine doğru devam edin."}, "empty_street_name_labels": ["yaya ge<PERSON>idi", "bisiklet yolu", "dağ bisikleti yolu", "the crosswalk", "the stairs", "the bridge", "the tunnel"], "example_phrases": {"0": ["Continue."], "1": ["Continue on 10th Avenue."], "2": ["Continue at Mannenbashi East."], "3": ["Continue toward Baltimore."]}}, "depart": {"phrases": {"0": "Kalkış: <TIME>.", "1": "Kalkış: <TRANSIT_STOP> durağından saat <TIME>'da kalk<PERSON><PERSON>."}, "example_phrases": {"0": ["Depart: 8:02 AM."], "1": ["Depart: 8:02 AM from 8 St - NYU."]}}, "depart_verbal": {"phrases": {"0": "Saat <TIME>'da ka<PERSON><PERSON>.", "1": "<TRANSIT_STOP> durağından saat <TIME>'da kalk<PERSON><PERSON>."}, "example_phrases": {"0": ["Depart at 8:02 AM."], "1": ["Depart at 8:02 AM from 8 St - NYU."]}}, "destination": {"phrases": {"0": "Hedefinize vardınız.", "1": "<DESTINATION> <PERSON><PERSON><PERSON><PERSON>na vardınız.", "2": "Hedefiniz <RELATIVE_DIRECTION>da.", "3": "<DESTINATION> durağı <RELATIVE_DIRECTION>da."}, "relative_directions": ["sol", "sağ"], "example_phrases": {"0": ["You have arrived at your destination."], "1": ["You have arrived at 3206 Powelton Avenue."], "2": ["Your destination is on the left.", "Your destination is on the right."], "3": ["Lancaster Brewing Company is on the left."]}}, "destination_verbal": {"phrases": {"0": "Hedefinize vardınız.", "1": "<DESTINATION> <PERSON><PERSON><PERSON><PERSON>na vardınız.", "2": "Hedefiniz <RELATIVE_DIRECTION>da.", "3": "<DESTINATION> durağı <RELATIVE_DIRECTION>da."}, "relative_directions": ["sol", "sağ"], "example_phrases": {"0": ["You have arrived at your destination."], "1": ["You have arrived at 32 o6 Powelton Avenue."], "2": ["Your destination is on the left.", "Your destination is on the right."], "3": ["Lancaster Brewing Company is on the left."]}}, "destination_verbal_alert": {"phrases": {"0": "Hedefinize varacaksınız.", "1": "<DESTINATION> dura<PERSON><PERSON>na varacaksınız.", "2": "Hedefiniz <RELATIVE_DIRECTION>da kalacak.", "3": "<DESTINATION> durağı <RELATIVE_DIRECTION>da olacak."}, "relative_directions": ["sol", "sağ"], "example_phrases": {"0": ["You will arrive at your destination."], "1": ["You will arrive at 32 o6 Powelton Avenue."], "2": ["Your destination will be on the left.", "Your destination will be on the right."], "3": ["Lancaster Brewing Company will be on the left."]}}, "enter_ferry": {"phrases": {"0": "<PERSON><PERSON><PERSON><PERSON> binin.", "1": "<STREET_NAMES> caddesine d<PERSON>.", "2": "<STREET_NAMES> caddesine dö<PERSON>n <FERRY_LABEL>.", "3": "<TOWARD_SIGN> i<PERSON><PERSON><PERSON> doğru feribota binin."}, "empty_street_name_labels": ["yaya ge<PERSON>idi", "bisiklet yolu", "dağ bisikleti yolu", "the crosswalk", "the stairs", "the bridge", "the tunnel"], "ferry_label": "<PERSON><PERSON><PERSON>", "example_phrases": {"0": ["Take the Ferry."], "1": ["Take the Millersburg Ferry."], "2": ["Take the Bridgeport - Port Jefferson Ferry."], "3": ["Take the ferry toward Cape May."]}}, "enter_ferry_verbal": {"phrases": {"0": "<PERSON><PERSON><PERSON><PERSON> binin.", "1": "<STREET_NAMES> caddesine d<PERSON>.", "2": "<STREET_NAMES> caddesine dö<PERSON>n <FERRY_LABEL>.", "3": "<TOWARD_SIGN> i<PERSON><PERSON><PERSON> doğru feribota binin."}, "empty_street_name_labels": ["yaya ge<PERSON>idi", "bisiklet yolu", "dağ bisikleti yolu", "the crosswalk", "the stairs", "the bridge", "the tunnel"], "ferry_label": "<PERSON><PERSON><PERSON>", "example_phrases": {"0": ["Take the Ferry."], "1": ["Take the Millersburg Ferry."], "2": ["Take the Bridgeport - Port Jefferson Ferry."], "3": ["Take the ferry toward Cape May."]}}, "enter_roundabout": {"phrases": {"0": "<PERSON><PERSON><PERSON> ka<PERSON> girin.", "1": "<PERSON><PERSON>ner kavşağa girin ve <ORDINAL_VALUE> çıkıştan çıkın.", "2": "<PERSON><PERSON><PERSON> kavşağa girin ve <ORDINAL_VALUE> çıkıştan <ROUNDABOUT_EXIT_STREET_NAMES> caddesine doğru çı<PERSON>ın.", "3": "<PERSON><PERSON><PERSON> kavşağa girin ve <ORDINAL_VALUE> çıkıştan <ROUNDABOUT_EXIT_BEGIN_STREET_NAMES> caddesine doğru çıkın. <ROUNDABOUT_EXIT_STREET_NAMES> caddesine doğru devam edin.", "4": "<PERSON><PERSON><PERSON> kavşağa girin ve <ORDINAL_VALUE> çıkıştan <TOWARD_SIGN> işaretine doğru çıkın.", "5": "<PERSON><PERSON><PERSON> kavş<PERSON>ğa girin ve <ROUNDABOUT_EXIT_STREET_NAMES> caddesine doğru ç<PERSON>ın.", "6": "<PERSON><PERSON><PERSON> kavş<PERSON>a girin ve <ROUNDABOUT_EXIT_BEGIN_STREET_NAMES> caddesine doğru çıkın.<ROUNDABOUT_EXIT_STREET_NAMES> caddesine devam edin.", "7": "<PERSON><PERSON><PERSON> kavş<PERSON>ğa girin ve <TOWARD_SIGN> işaretine doğru ç<PERSON>ı<PERSON>.", "8": "<STREET_NAMES> caddesine girin", "9": "<STREET_NAMES> caddesine girin ve <ORDINAL_VALUE> çıkıştan çıkın.", "10": "<STREET_NAMES> caddesine girin ve <ORDINAL_VALUE> çıkıştan <ROUNDABOUT_EXIT_STREET_NAMES> caddesine çıkın.", "11": "<STREET_NAMES> caddesine girin ve <ORDINAL_VALUE> çıkıştan <ROUNDABOUT_EXIT_BEGIN_STREET_NAMES> caddesine çıkın. <ROUNDABOUT_EXIT_STREET_NAMES> caddesine devam edin.", "12": "<STREET_NAMES> caddesine girin ve<ORDINAL_VALUE> çıkıştan <TOWARD_SIGN> işaretine doğru çıkın.", "13": "<STREET_NAMES> caddesine girin ve <ROUNDABOUT_EXIT_STREET_NAMES> caddesine doğru çıkın.", "14": "<STREET_NAMES> caddesine girin ve <ROUNDABOUT_EXIT_BEGIN_STREET_NAMES> caddesine doğru çıkın.<ROUNDABOUT_EXIT_STREET_NAMES> caddesine devam edin.", "15": "<STREET_NAMES> caddesine girin ve <TOWARD_SIGN> işaretine doğru ç<PERSON>ın."}, "ordinal_values": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Üçüncü", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Altıncı", "<PERSON><PERSON><PERSON>", "Se<PERSON>zinci", "Do<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "empty_street_name_labels": ["yaya ge<PERSON>idi", "bisiklet yolu", "dağ bisikleti yolu", "the crosswalk", "the stairs", "the bridge", "the tunnel"], "example_phrases": {"0": ["Enter the roundabout."], "1": ["Enter the roundabout and take the 1st exit.", "Enter the roundabout and take the 2nd exit.", "Enter the roundabout and take the 3rd exit.", "Enter the roundabout and take the 4th exit.", "Enter the roundabout and take the 5th exit.", "Enter the roundabout and take the 6th exit.", "Enter the roundabout and take the 7th exit.", "Enter the roundabout and take the 8th exit.", "Enter the roundabout and take the 9th exit.", "Enter the roundabout and take the 10th exit."], "2": ["Enter the roundabout and take the 3rd exit onto Main Street."], "3": ["Enter the roundabout and take the 3rd exit onto US 322/Main Street. Continue on US 322."], "4": ["Enter the roundabout and take the 3rd exit toward Baltimore."], "5": ["Enter the roundabout and take the exit onto Main Street."], "6": ["Enter the roundabout and take the exit onto US 322/Main Street. Continue on US 322."], "7": ["Enter the roundabout and take the exit toward Baltimore."], "8": ["Enter Dupont Circle."], "9": ["Enter Dupont Circle and take the 1st exit."], "10": ["Enter Dupont Circle and take the 3rd exit onto Main Street."], "11": ["Enter Dupont Circle and take the 3rd exit onto US 322/Main Street. Continue on US 322."], "12": ["Enter Dupont Circle and take the 3rd exit toward Baltimore."], "13": ["Enter Dupont Circle and take the exit onto Main Street."], "14": ["Enter Dupont Circle and take the exit onto US 322/Main Street. Continue on US 322."], "15": ["Enter Dupont Circle and take the exit toward Baltimore."]}}, "enter_roundabout_verbal": {"phrases": {"0": "<PERSON><PERSON><PERSON> ka<PERSON> girin.", "1": "<PERSON><PERSON>ner kavşağa girin ve <ORDINAL_VALUE> çıkıştan çıkın.", "2": "<PERSON><PERSON><PERSON> kavşağa girin ve <ORDINAL_VALUE> çıkıştan <ROUNDABOUT_EXIT_STREET_NAMES> caddesine doğru çı<PERSON>ın.", "3": "<PERSON><PERSON><PERSON> kavşağa girin ve <ORDINAL_VALUE> çıkıştan <ROUNDABOUT_EXIT_BEGIN_STREET_NAMES> caddesine doğru ç<PERSON>ın.", "4": "<PERSON><PERSON><PERSON> kavşağa girin ve <ORDINAL_VALUE> çıkıştan <TOWARD_SIGN> işaretine doğru çıkın.", "5": "<PERSON><PERSON><PERSON> kavşağa girin <ROUNDABOUT_EXIT_STREET_NAMES> caddesine doğru ç<PERSON>ın.", "6": "<PERSON><PERSON><PERSON> kavş<PERSON>ğa girin ve <ROUNDABOUT_EXIT_BEGIN_STREET_NAMES> caddesine doğ<PERSON>ın.", "7": "<PERSON><PERSON><PERSON> kavş<PERSON>ğa girin ve <TOWARD_SIGN> işaretine doğru ç<PERSON>ı<PERSON>.", "8": "<STREET_NAMES> caddesine girin", "9": "<STREET_NAMES> caddesine girin ve <ORDINAL_VALUE> çıkıştan çıkın.", "10": "<STREET_NAMES> caddesine girin ve<ORDINAL_VALUE> çıkıştan <ROUNDABOUT_EXIT_STREET_NAMES> caddesine doğru çıkın.", "11": "<STREET_NAMES> caddesine girin ve <ORDINAL_VALUE> çıkıştan <ROUNDABOUT_EXIT_BEGIN_STREET_NAMES> caddesine doğru çı<PERSON>ın.", "12": "<STREET_NAMES> caddesine girin ve <ORDINAL_VALUE> çıkıştan <TOWARD_SIGN> işaretine doğru çıkın.", "13": "<STREET_NAMES> caddesine girin ve <ROUNDABOUT_EXIT_STREET_NAMES> caddesine doğru çıkın.", "14": "<STREET_NAMES> caddesine girin ve <ROUNDABOUT_EXIT_BEGIN_STREET_NAMES>caddesine doğru çıkın.", "15": "<STREET_NAMES> caddesine girin ve <TOWARD_SIGN> işaretine doğru ç<PERSON>ın."}, "ordinal_values": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Üçüncü", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Altıncı", "<PERSON><PERSON><PERSON>", "Se<PERSON>zinci", "Do<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "empty_street_name_labels": ["yaya ge<PERSON>idi", "bisiklet yolu", "dağ bisikleti yolu", "the crosswalk", "the stairs", "the bridge", "the tunnel"], "example_phrases": {"0": ["Enter the roundabout."], "1": ["Enter the roundabout and take the 1st exit.", "Enter the roundabout and take the 2nd exit.", "Enter the roundabout and take the 3rd exit.", "Enter the roundabout and take the 4th exit.", "Enter the roundabout and take the 5th exit.", "Enter the roundabout and take the 6th exit.", "Enter the roundabout and take the 7th exit.", "Enter the roundabout and take the 8th exit.", "Enter the roundabout and take the 9th exit.", "Enter the roundabout and take the 10th exit."], "2": ["Enter the roundabout and take the 3rd exit onto Main Street."], "3": ["Enter the roundabout and take the 3rd exit onto U.S. 3 22/Main Street."], "4": ["Enter the roundabout and take the 3rd exit toward Baltimore."], "5": ["Enter the roundabout and take the exit onto Main Street."], "6": ["Enter the roundabout and take the exit onto U.S. 3 22/Main Street."], "7": ["Enter the roundabout and take the exit toward Baltimore."], "8": ["Enter Dupont Circle."], "9": ["Enter Dupont Circle and take the 1st exit."], "10": ["Enter Dupont Circle and take the 3rd exit onto Main Street."], "11": ["Enter Dupont Circle and take the 3rd exit onto U.S. 3 22/Main Street."], "12": ["Enter Dupont Circle and take the 3rd exit toward Baltimore."], "13": ["Enter Dupont Circle and take the exit onto Main Street."], "14": ["Enter Dupont Circle and take the exit onto U.S. 3 22/Main Street."], "15": ["Enter Dupont Circle and take the exit toward Baltimore."]}}, "exit": {"phrases": {"0": "<RELATIVE_DIRECTION>daki çıkıştan çıkın.", "1": "<RELATIVE_DIRECTION>daki <NUMBER_SIGN> çıkıştan çıkın.", "2": "<RELATIVE_DIRECTION>taki <BRANCH_SIGN> çıkıştan çıkın.", "3": "<RELATIVE_DIRECTION>daki <NUMBER_SIGN> çıkıştan <BRANCH_SIGN> işaretine doğru çıkın.", "4": "<RELATIVE_DIRECTION>daki <PERSON> <TOWARD_SIGN> işaretine doğru çıkın.", "5": "<RELATIVE_DIRECTION>daki <NUMBER_SIGN> çıkıştan <TOWARD_SIGN> işaretine doğru çıkın.", "6": "<RELATIVE_DIRECTION>daki <BRANCH_SIGN> çıkıştan <TOWARD_SIGN> işaretine doğru çıkın.", "7": "<RELATIVE_DIRECTION>daki <BRANCH_SIGN> üzerinde bulunan <NUMBER_SIGN> çıkışından <TOWARD_SIGN> işaretine doğru çıkın.", "8": "<RELATIVE_DIRECTION>daki <NAME_SIGN> çıkışından çıkın.", "10": "<RELATIVE_DIRECTION>daki <NAME_SIGN> çıkışından <BRANCH_SIGN> işaretine doğru çıkın.", "12": "<RELATIVE_DIRECTION>daki <NAME_SIGN> çıkışından <TOWARD_SIGN> işaretine doğru çıkın.", "14": "<RELATIVE_DIRECTION>daki <BRANCH_SIGN> üzerinde bulunan <NAME_SIGN> çıkışından <TOWARD_SIGN> işaretine doğru çıkın.", "15": "Çıkın", "16": "<NUMBER_SIGN> çıkıştan çıkın.", "17": "<BRANCH_SIGN> çıkışından çıkın.", "18": "<NUMBER_SIGN> çıkıştan <BRANCH_SIGN> işaretine doğru çıkın.", "19": "<TOWARD_SIGN> <PERSON><PERSON><PERSON>.", "20": "<TOWARD_SIGN> işaretine doğru <NUMBER_SIGN> çıkıştan çıkın.", "21": "<BRANCH_SIGN> çıkışından <TOWARD_SIGN> işaretine doğru çıkın.", "22": "<BRANCH_SIGN> üzerindeki <NUMBER_SIGN> çıkıştan <TOWARD_SIGN> işaretine doğru çıkın.", "23": "<NAME_SIGN> çıkışından çıkın.", "25": "<BRANCH_SIGN> üzerindeki <NAME_SIGN> çıkışından çıkın.", "27": "<TOWARD_SIGN> işaretine doğru <NAME_SIGN> çıkışından çıkın.", "29": "<BRANCH_SIGN> üzerindeki <NAME_SIGN> çıkıştan <TOWARD_SIGN> işaretine doğru çıkın."}, "relative_directions": ["sol", "sağ"], "example_phrases": {"0": ["Take the exit on the left.", "Take the exit on the right."], "1": ["Take exit 67 B-A on the right."], "2": ["Take the US 322 West exit on the right."], "3": ["Take exit 67 B-A on the right onto US 322 West."], "4": ["Take the exit on the right toward Lewistown."], "5": ["Take exit 67 B-A on the right toward Lewistown."], "6": ["Take the US 322 West exit on the right toward Lewistown."], "7": ["Take exit 67 B-A on the right onto US 322 West toward Lewistown/State College."], "8": ["Take the White Marsh Boulevard exit on the left."], "10": ["Take the White Marsh Boulevard exit on the left onto MD 43 East."], "12": ["Take the White Marsh Boulevard exit on the left toward White Marsh."], "14": ["Take the White Marsh Boulevard exit on the left onto MD 43 East toward White Marsh."], "15": ["Take the exit."], "16": ["Take exit 67 B-A."], "17": ["Take the US 322 West exit."], "18": ["Take exit 67 B-A onto US 322 West."], "19": ["Take the exit toward Lewistown."], "20": ["Take exit 67 B-A toward Lewistown."], "21": ["Take the US 322 West exit toward Lewistown."], "22": ["Take exit 67 B-A onto US 322 West toward Lewistown/State College."], "23": ["Take the White Marsh Boulevard exit."], "25": ["Take the White Marsh Boulevard exit onto MD 43 East."], "27": ["Take the White Marsh Boulevard exit toward White Marsh."], "29": ["Take the White Marsh Boulevard exit onto MD 43 East toward White Marsh."]}}, "exit_roundabout": {"phrases": {"0": "Döner kavşaktan çıkın", "1": "<STREET_NAMES> caddesine doğru döner kavşaktan çıkın.", "2": "<BEGIN_STREET_NAMES> caddesine doğru döner kavşaktan çıkın. <STREET_NAMES> caddesine devam edin.", "3": "<TOWARD_SIGN> işaretine doğru döner kavşaktan çıkın."}, "empty_street_name_labels": ["yaya ge<PERSON>idi", "bisiklet yolu", "dağ bisikleti yolu", "the crosswalk", "the stairs", "the bridge", "the tunnel"], "example_phrases": {"0": ["Exit the roundabout."], "1": ["Exit the roundabout onto Philadelphia Road/MD 7."], "2": ["Exit the roundabout onto Catoctin Mountain Highway/US 15. Continue on US 15."], "3": ["Exit the roundabout toward Baltimore."]}}, "exit_roundabout_verbal": {"phrases": {"0": "Döner kavşaktan çıkın.", "1": "<STREET_NAMES> caddesine doğru döner kavşaktan çıkın.", "2": "<BEGIN_STREET_NAMES> caddesine doğru döner kavşaktan çıkın.", "3": "<TOWARD_SIGN> işaretine doğru döner kavşaktan çıkın."}, "empty_street_name_labels": ["yaya ge<PERSON>idi", "bisiklet yolu", "dağ bisikleti yolu", "the crosswalk", "the stairs", "the bridge", "the tunnel"], "example_phrases": {"0": ["Exit the roundabout."], "1": ["Exit the roundabout onto Philadelphia Road, Maryland 7."], "2": ["Exit the roundabout onto Catoctin Mountain Highway, U.S. 15."], "3": ["Exit the roundabout toward Baltimore."]}}, "exit_verbal": {"phrases": {"0": "<RELATIVE_DIRECTION>daki çıkıştan çıkın.", "1": "Take exit on the <RELATIVE_DIRECTION>daki <NUMBER_SIGN> çıkıştan çıkın.", "2": "<RELATIVE_DIRECTION>daki <BRANCH_SIGN> çıkışından çıkın.", "3": "<RELATIVE_DIRECTION>daki <NUMBER_SIGN> çıkıştan <BRANCH_SIGN> doğ<PERSON>.", "4": "<RELATIVE_DIRECTION>daki <TOWARD_SIGN> işaretine doğru <PERSON>.", "5": "<RELATIVE_DIRECTION>daki <NUMBER_SIGN> çıkıştan <TOWARD_SIGN> işaretine doğru çıkın.", "6": "<RELATIVE_DIRECTION>daki <BRANCH_SIGN> çıkışından <TOWARD_SIGN> işaretine doğru çıkın.", "7": "<RELATIVE_DIRECTION>daki <BRANCH_SIGN> üzerinde bulunan <NUMBER_SIGN> çıkıştan <TOWARD_SIGN> işaretine doğru çı<PERSON>ın.", "8": "<RELATIVE_DIRECTION>daki <NAME_SIGN> çıkıştan çıkın.", "10": "<RELATIVE_DIRECTION>daki <BRANCH_SIGN> işaretine doğru <NAME_SIGN> çıkışından çıkın.", "12": "Take the <NAME_SIGN> exit on the <RELATIVE_DIRECTION> toward <TOWARD_SIGN>.", "14": "<RELATIVE_DIRECTION>daki <BRANCH_SIGN> üzerinde bulunan <NAME_SIGN> çıkışından <TOWARD_SIGN> işaretine doğru çıkın.", "15": "Çıkıştan çıkın", "16": "<NUMBER_SIGN> çıkıştan çıkın.", "17": "<BRANCH_SIGN> çıkışından çıkın.", "18": "<BRANCH_SIGN> üzerinde bulunan <NUMBER_SIGN> çıkışından çıkın.", "19": "<TOWARD_SIGN> işaretine giden çıkıştan çıkın.", "20": "<TOWARD_SIGN> işaretine giden <NUMBER_SIGN> çıkıştan çıkın.", "21": "<TOWARD_SIGN> i<PERSON><PERSON><PERSON> giden <BRANCH_SIGN> çıkışından çıkın.", "22": "<TOWARD_SIGN> i<PERSON><PERSON><PERSON> giden <BRANCH_SIGN> üzerinde bulunan <NUMBER_SIGN> çıkıştan çıkın.", "23": "<NAME_SIGN> çıkışından çıkın.", "25": "<BRANCH_SIGN> işaretine doğru <NAME_SIGN> çıkışından çıkın.", "27": "<TOWARD_SIGN> işaretine doğru <NAME_SIGN> çıkışından çıkın.", "29": "<BRANCH_SIGN> üzerinde bulunan <TOWARD_SIGN> işaretine doğru <NAME_SIGN> çıkışından çıkın."}, "relative_directions": ["sol", "sağ"], "example_phrases": {"0": ["Take the exit on the left.", "Take the exit on the right."], "1": ["Take exit 67 B-A on the right."], "2": ["Take the U.S. 3 22 West exit on the right."], "3": ["Take exit 67 B-A on the right onto U.S. 3 22 West."], "4": ["Take the exit on the right toward Lewistown."], "5": ["Take exit 67 B-A on the right toward Lewistown."], "6": ["Take the U.S. 3 22 West exit on the right toward Lewistown."], "7": ["Take exit 67 B-A on the right onto U.S. 3 22 West toward Lewistown, State College."], "8": ["Take the White Marsh Boulevard exit on the left."], "10": ["Take the White Marsh Boulevard exit on the left onto Maryland 43 East."], "12": ["Take the White Marsh Boulevard exit on the left toward White Marsh."], "14": ["Take the White Marsh Boulevard exit on the left onto Maryland 43 East toward White Marsh."], "15": ["Take the exit."], "16": ["Take exit 67 B-A."], "17": ["Take the US 322 West exit."], "18": ["Take exit 67 B-A onto US 322 West."], "19": ["Take the exit toward Lewistown."], "20": ["Take exit 67 B-A toward Lewistown."], "21": ["Take the US 322 West exit toward Lewistown."], "22": ["Take exit 67 B-A onto US 322 West toward Lewistown/State College."], "23": ["Take the White Marsh Boulevard exit."], "25": ["Take the White Marsh Boulevard exit onto MD 43 East."], "27": ["Take the White Marsh Boulevard exit toward White Marsh."], "29": ["Take the White Marsh Boulevard exit onto MD 43 East toward White Marsh."]}}, "exit_visual": {"phrases": {"0": "Çıkın <EXIT_NUMBERS>"}, "example_phrases": {"0": ["Exit 1A"]}}, "keep": {"phrases": {"0": "Çatalın <RELATIVE_DIRECTION> yönünde kalın.", "1": "<NUMBER_SIGN> çıkışından çıkmak için <RELATIVE_DIRECTION>da kalın.", "2": "<STREET_NAMES> caddesine girmek için <RELATIVE_DIRECTION>da kalın.", "3": "<STREET_NAMES> caddesine giden <NUMBER_SIGN> çıkışından çıkmak için <RELATIVE_DIRECTION>da kalın.", "4": "<TOWARD_SIGN> i<PERSON>aretine doğru <RELATIVE_DIRECTION>da kalın.", "5": "Keep <RELATIVE_DIRECTION> to take exit <NUMBER_SIGN> toward <TOWARD_SIGN>.", "6": "<STREET_NAMES> caddesine girmek için <TOWARD_SIGN> işaretine doğru <RELATIVE_DIRECTION>da kalın.", "7": "<STREET_NAMES> üzerinde bulunan <NUMBER_SIGN> çıkışından çıkmak için <TOWARD_SIGN> işaretine doğru <RELATIVE_DIRECTION>da kalın."}, "empty_street_name_labels": ["yaya ge<PERSON>idi", "bisiklet yolu", "dağ bisikleti yolu", "the crosswalk", "the stairs", "the bridge", "the tunnel"], "relative_directions": ["sol", "straight", "sağ"], "example_phrases": {"0": ["Keep left at the fork.", "Keep straight at the fork.", "Keep right at the fork."], "1": ["Keep right to take exit 62."], "2": ["Keep right to take I 895 South."], "3": ["Keep right to take exit 62 onto I 895 South."], "4": ["Keep right toward Annapolis."], "5": ["Keep right to take exit 62 toward Annapolis."], "6": ["Keep right to take I 895 South toward Annapolis."], "7": ["Keep right to take exit 62 onto I 895 South toward Annapolis."]}}, "keep_to_stay_on": {"phrases": {"0": "<STREET_NAMES> caddesinde kalmak için <RELATIVE_DIRECTION>da kalın.", "1": "<STREET_NAMES> caddesinde kalmak amacıyla <NUMBER_SIGN> çıkıştan çıkmak için <RELATIVE_DIRECTION>da kalın.", "2": "<STREET_NAMES> caddesinde kalmak için <TOWARD_SIGN> işaretine doğru <RELATIVE_DIRECTION>da kalın.", "3": "<STREET_NAMES> caddesinde kalmak amacıyla <NUMBER_SIGN> çıkıştan çıkmak için <TOWARD_SIGN> işaretine doğru <RELATIVE_DIRECTION>da kalın."}, "empty_street_name_labels": ["yaya ge<PERSON>idi", "bisiklet yolu", "dağ bisikleti yolu", "the crosswalk", "the stairs", "the bridge", "the tunnel"], "relative_directions": ["sol", "straight", "sağ"], "example_phrases": {"0": ["Keep left to stay on I 95 South.", "Keep straight to stay on I 95 South.", "Keep right to stay on I 95 South."], "1": ["Keep left to take exit 62 to stay on I 95 South."], "2": ["Keep left to stay on I 95 South toward Baltimore."], "3": ["Keep left to take exit 62 to stay on I 95 South toward Baltimore."]}}, "keep_to_stay_on_verbal": {"phrases": {"0": "<STREET_NAMES> caddesinde kalmak için <RELATIVE_DIRECTION>da kalın.", "1": "<STREET_NAMES> caddesinde kalmak amacıyla <NUMBER_SIGN> için <RELATIVE_DIRECTION>da kalın.", "2": "<STREET_NAMES> caddesinde kalmak için <TOWARD_SIGN> işaretine doğru <RELATIVE_DIRECTION>da kalın.", "3": "<STREET_NAMES> caddesinde kalmak amacıyla <NUMBER_SIGN> çıkışından çıkmak için <TOWARD_SIGN> işaretine doğru <RELATIVE_DIRECTION>da kalın."}, "empty_street_name_labels": ["yaya ge<PERSON>idi", "bisiklet yolu", "dağ bisikleti yolu", "the crosswalk", "the stairs", "the bridge", "the tunnel"], "relative_directions": ["sol", "straight", "sağ"], "example_phrases": {"0": ["Keep left to stay on Interstate 95 South.", "Keep straight to stay on Interstate 95 South.", "Keep right to stay on Interstate 95 South."], "1": ["Keep left to take exit 62 to stay on Interstate 95 South."], "2": ["Keep left to stay on I 95 South toward Baltimore."], "3": ["Keep left to take exit 62 to stay on Interstate 95 South toward Baltimore."]}}, "keep_verbal": {"phrases": {"0": "Çatalın <RELATIVE_DIRECTION>da kalın.", "1": "<NUMBER_SIGN> çıkıştan çıkmak için <RELATIVE_DIRECTION>da kalın.", "2": "<STREET_NAMES> caddesine girmek için <RELATIVE_DIRECTION>da kalın.", "3": "<STREET_NAMES> caddesine doğru <NUMBER_SIGN> çıkıştan çıkmak için <RELATIVE_DIRECTION>da kalın.", "4": "<TOWARD_SIGN> i<PERSON>aretine doğru <RELATIVE_DIRECTION>da kalın.", "5": "<TOWARD_SIGN> işaretine doğru <NUMBER_SIGN> çıkıştan çıkmak için <RELATIVE_DIRECTION>da kalın.", "6": "<STREET_NAMES> caddesine girmek için <TOWARD_SIGN> işaretine doğru <RELATIVE_DIRECTION>da kalın.", "7": "<STREET_NAMES> cadd<PERSON><PERSON> bulunan <NUMBER_SIGN> çıkışından çıkmak için <TOWARD_SIGN> işaretine doğru <RELATIVE_DIRECTION>da kalın."}, "empty_street_name_labels": ["yaya ge<PERSON>idi", "bisiklet yolu", "dağ bisikleti yolu", "the crosswalk", "the stairs", "the bridge", "the tunnel"], "relative_directions": ["sol", "straight", "sağ"], "example_phrases": {"0": ["Keep left at the fork.", "Keep straight at the fork.", "Keep right at the fork."], "1": ["Keep right to take exit 62."], "2": ["Keep right to take Interstate 8 95 South."], "3": ["Keep right to take exit 62 onto Interstate 8 95 South."], "4": ["Keep right toward Annapolis."], "5": ["Keep right to take exit 62 toward Annapolis."], "6": ["Keep right to take Interstate 8 95 South toward Annapolis."], "7": ["Keep right to take exit 62 onto Interstate 8 95 South toward Annapolis."]}}, "merge": {"phrases": {"0": "<PERSON><PERSON>.", "1": "<RELATIVE_DIRECTION>dan yola katılın.", "2": "<STREET_NAMES> caddesine doğru yola katılın.", "3": "<STREET_NAMES> caddesine doğru <RELATIVE_DIRECTION>dan yola katılın.", "4": "<TOWARD_SIGN> işaretine doğru yola katılın.", "5": "<RELATIVE_DIRECTION>dan yola kat<PERSON><PERSON>n toward <TOWARD_SIGN> işaretine doğru."}, "relative_directions": ["sol", "sağ"], "empty_street_name_labels": ["yaya ge<PERSON>idi", "bisiklet yolu", "dağ bisikleti yolu", "the crosswalk", "the stairs", "the bridge", "the tunnel"], "example_phrases": {"0": ["Merge."], "1": ["<PERSON><PERSON> left."], "2": ["Merge onto I 76 West/Pennsylvania Turnpike."], "3": ["Merge right onto I 83 South."], "4": ["Merge toward Baltimore."], "5": ["Merge right toward Baltimore."]}}, "merge_verbal": {"phrases": {"0": "<PERSON><PERSON>.", "1": "<RELATIVE_DIRECTION> <PERSON><PERSON><PERSON> yola katı<PERSON>ın.", "2": "<STREET_NAMES> caddesine katılın.", "3": "<RELATIVE_DIRECTION> doğru <STREET_NAMES>caddesine katılın.", "4": "<TOWARD_SIGN> işaretine doğru yola katılın.", "5": "<RELATIVE_DIRECTION> doğru <TOWARD_SIGN> i<PERSON><PERSON><PERSON> katı<PERSON>ın."}, "relative_directions": ["sol", "sağ"], "empty_street_name_labels": ["yaya ge<PERSON>idi", "bisiklet yolu", "dağ bisikleti yolu", "the crosswalk", "the stairs", "the bridge", "the tunnel"], "example_phrases": {"0": ["Merge."], "1": ["<PERSON><PERSON> left."], "2": ["Merge onto I 76 West/Pennsylvania Turnpike."], "3": ["Merge right onto I 83 South."], "4": ["Merge toward Baltimore."], "5": ["Merge right toward Baltimore."]}}, "post_transition_transit_verbal": {"phrases": {"0": "Seyahat edin <TRANSIT_STOP_COUNT> <TRANSIT_STOP_COUNT_LABEL>."}, "transit_stop_count_labels": {"one": "durak", "other": "<PERSON><PERSON><PERSON>"}, "example_phrases": {"0": ["Travel 1 stop.", "Travel 3 stops."]}}, "post_transition_verbal": {"phrases": {"0": "<LENGTH> boyunca devam edin.", "1": "<STREET_NAMES> caddesinde <LENGTH> boyunca devam edin."}, "empty_street_name_labels": ["yaya ge<PERSON>idi", "bisiklet yolu", "dağ bisikleti yolu", "the crosswalk", "the stairs", "the bridge", "the tunnel"], "metric_lengths": ["<KILOMETERS>kilometre", "1 kilometre", "<METERS> metre", "10 kilometreden az"], "us_customary_lengths": ["<MILES> mil", "1 mil", "yarım mil", "<PERSON><PERSON><PERSON> mil", "<FEET>fit", "10 fitten daha az"], "example_phrases": {"0": ["Continue for 300 feet.", "Continue for 9 miles."], "1": ["Continue on Pennsylvania 7 43 for 6.2 miles.", "Continue on Main Street, Vermont 30 for 1 tenth of a mile."]}}, "ramp": {"phrases": {"0": "<RELATIVE_DIRECTION>daki rampayı kull<PERSON>ın.", "1": "<RELATIVE_DIRECTION>daki <BRANCH_SIGN> rampasını kullanın.", "2": "<TOWARD_SIGN> işaretine doğru <RELATIVE_DIRECTION>daki rampayı kullanın.", "3": "<TOWARD_SIGN> işaretine doğru <RELATIVE_DIRECTION>daki <BRANCH_SIGN> rampasını kullanın.", "4": "<RELATIVE_DIRECTION>daki <NAME_SIGN> rampasını kullanın.", "5": "Rampayı kullanmak için <RELATIVE_DIRECTION>a dönün.", "6": "<BRANCH_SIGN> rampasını kullanmak için <RELATIVE_DIRECTION>a dönün.", "7": "<TOWARD_SIGN> işaretine doğru rampayı kullanmak için <RELATIVE_DIRECTION>a dönün.", "8": "<TOWARD_SIGN> işaretine doğru <BRANCH_SIGN> rampasını kullanmak için <RELATIVE_DIRECTION>a dönün.", "9": "<NAME_SIGN> rampasını kullanmak için <RELATIVE_DIRECTION>a dönün.", "10": "Rampayı kullanın.", "11": "<BRANCH_SIGN> rampasını kullanın.", "12": "<TOWARD_SIGN> işaretine doğru rampayı kullanın.", "13": "<TOWARD_SIGN> işaretine doğru <BRANCH_SIGN> rampasını kullanın.", "14": "<NAME_SIGN> rampasını kullanın."}, "relative_directions": ["sol", "sağ"], "example_phrases": {"0": ["Take the ramp on the left.", "Take the ramp on the right."], "1": ["Take the I 95 ramp on the right."], "2": ["Take the ramp on the left toward JFK."], "3": ["Take the South Conduit Avenue ramp on the left toward JFK."], "4": ["Take the Gettysburg Pike ramp on the right."], "5": ["Turn left to take the ramp.", "Turn right to take the ramp."], "6": ["Turn left to take the PA 283 West ramp."], "7": ["Turn left to take the ramp toward Harrisburg/Harrisburg International Airport."], "8": ["Turn left to take the PA 283 West ramp toward Harrisburg/Harrisburg International Airport."], "9": ["Turn right to take the Gettysburg Pike ramp."], "10": ["Take the ramp."], "11": ["Take the I 95 ramp."], "12": ["Take the ramp toward JFK."], "13": ["Take the Soutch Conduit Avenue ramp toward JFK."], "14": ["Take the Gettysburg ramp."]}}, "ramp_straight": {"phrases": {"0": "Rampayı kullanmak için düz devam edin.", "1": "<BRANCH_SIGN> rampasını kullanmak için düz devam edin.", "2": "<TOWARD_SIGN> işaretine doğru rampayı kullanmak için düz devam edin.", "3": "<TOWARD_SIGN> işaretine doğru <BRANCH_SIGN> rampasını kullanmak için düz devam edin.", "4": "<NAME_SIGN> rampasını kullanmak için düz devam edin."}, "example_phrases": {"0": ["Stay straight to take the ramp."], "1": ["Stay straight to take the US 322 East ramp."], "2": ["Stay straight to take the ramp toward Hershey."], "3": ["Stay straight to take the US 322 East/US 422 East/US 522 East/US 622 East ramp toward Hershey/Palmdale/Palmyra/Campbelltown."], "4": ["Stay straight to take the Gettysburg Pike ramp."]}}, "ramp_straight_verbal": {"phrases": {"0": "Rampayı kullanmak için düz devam edin.", "1": "<BRANCH_SIGN> rampasını kullanmak için düz devam edin.", "2": "<TOWARD_SIGN> işaretine doğru rampayı kullanmak için düz devam edin.", "3": "<TOWARD_SIGN> işaretine doğru <BRANCH_SIGN> rampasını kullanmak için düz devam edin.", "4": "<NAME_SIGN> rampasını kullanmak için düz devam edin."}, "example_phrases": {"0": ["Stay straight to take the ramp."], "1": ["Stay straight to take the U.S. 3 22 East ramp."], "2": ["Stay straight to take the ramp toward Hershey."], "3": ["Stay straight to take the U.S. 3 22 East, U.S. 4 22 East ramp toward Hershey, Palmdale."], "4": ["Stay straight to take the Gettysburg Pike ramp."]}}, "ramp_verbal": {"phrases": {"0": "<RELATIVE_DIRECTION>daki rampayı kull<PERSON>ın.", "1": "<RELATIVE_DIRECTION>daki <BRANCH_SIGN> rampasını kullanın.", "2": "<TOWARD_SIGN> işaretine doğru <RELATIVE_DIRECTION>daki rampayı kullanın.", "3": "<TOWARD_SIGN> işaretine doğru <RELATIVE_DIRECTION>daki <BRANCH_SIGN> rampasını kullanın.", "4": "<RELATIVE_DIRECTION>daki <NAME_SIGN> rampasını kullanın.", "5": "Rampayı kullanmak için <RELATIVE_DIRECTION>a dönün.", "6": "<BRANCH_SIGN> rampasını kullanmak için <RELATIVE_DIRECTION>a dönün.", "7": "<TOWARD_SIGN> işaretine doğru rampayı kullanmak için <RELATIVE_DIRECTION>a dönün.", "8": "<TOWARD_SIGN> işaretine doğru <BRANCH_SIGN> rampasını kullanmak için <RELATIVE_DIRECTION>a dönün.", "9": "<NAME_SIGN> rampasını kullanmak için <RELATIVE_DIRECTION>a dönün.", "10": "Rampayı kullanın.", "11": "<BRANCH_SIGN> rampasını kullanın.", "12": "<TOWARD_SIGN> işaretine doğru rampayı kullanın.", "13": "<TOWARD_SIGN> işaretine doğru <BRANCH_SIGN> rampasını kullanın.", "14": "<NAME_SIGN> rampasını kullanın."}, "relative_directions": ["sol", "sağ"], "example_phrases": {"0": ["Take the ramp on the left.", "Take the ramp on the right."], "1": ["Take the Interstate 95 ramp on the right."], "2": ["Take the ramp on the left toward JFK."], "3": ["Take the South Conduit Avenue ramp on the left toward JFK."], "4": ["Take the Gettysburg Pike ramp on the right."], "5": ["Turn left to take the ramp.", "Turn right to take the ramp."], "6": ["Turn left to take the Pennsylvania 2 83 West ramp."], "7": ["Turn left to take the ramp toward Harrisburg/Harrisburg International Airport."], "8": ["Turn left to take the Pennsylvania 2 83 West ramp toward Harrisburg, Harrisburg International Airport."], "9": ["Turn right to take the Gettysburg Pike ramp."], "10": ["Take the ramp."], "11": ["Take the Interstate 95 ramp."], "12": ["Take the ramp toward JFK."], "13": ["Take the South Conduit Avenue ramp toward JFK."], "14": ["Take the Gettysburg Pike ramp."]}}, "sharp": {"phrases": {"0": "<RELATIVE_DIRECTION>a doğru keskin bir dönüş yapın.", "1": "<STREET_NAMES> caddesine doğru <RELATIVE_DIRECTION>a keskin bir dönüş yapın.", "2": "<BEGIN_STREET_NAMES> caddesine doğru <RELATIVE_DIRECTION>a keskin bir dönüş yapın. <STREET_NAMES> caddesine devam edin.", "3": "<STREET_NAMES> caddesinde kalmak için <RELATIVE_DIRECTION>a doğru keskin bir dönüş yapın.", "4": "<JUNCTION_NAME> kavşağında <RELATIVE_DIRECTION>a doğru keskin bir dönüş yapın.", "5": "<TOWARD_SIGN> işaretine doğru <RELATIVE_DIRECTION>a keskin bir dönüş yapın."}, "empty_street_name_labels": ["yaya ge<PERSON>idi", "bisiklet yolu", "dağ bisikleti yolu", "the crosswalk", "the stairs", "the bridge", "the tunnel"], "relative_directions": ["sol", "sağ"], "example_phrases": {"0": ["Make a sharp left."], "1": ["Make a sharp right onto Flatbush Avenue."], "2": ["Make a sharp left onto North Bond Street/US 1 Business/MD 924. Continue on MD 924."], "3": ["Make a sharp right to stay on Sunstone Drive."], "4": ["Make a sharp right at Mannenbashi East."], "5": ["Make a sharp left toward Baltimore."]}}, "sharp_verbal": {"phrases": {"0": "<RELATIVE_DIRECTION>a doğru keskin bir dönüş yapın.", "1": "<STREET_NAMES> caddesine doğru <RELATIVE_DIRECTION>a keskin bir dönüş yapın.", "2": "<BEGIN_STREET_NAMES> caddesine doğru <RELATIVE_DIRECTION>a keskin bir dön<PERSON>ş yapın.", "3": "<STREET_NAMES> caddesinde kalmak için <RELATIVE_DIRECTION>a doğru keskin bir dönüş yapın.", "4": "<JUNCTION_NAME> kavşağında <RELATIVE_DIRECTION>a doğru keskin bir dönüş yapın.", "5": "<TOWARD_SIGN> işaretine doğru <RELATIVE_DIRECTION>a keskin bir dönüş yapın."}, "empty_street_name_labels": ["yaya ge<PERSON>idi", "bisiklet yolu", "dağ bisikleti yolu", "the crosswalk", "the stairs", "the bridge", "the tunnel"], "relative_directions": ["sol", "sağ"], "example_phrases": {"0": ["Make a sharp left."], "1": ["Make a sharp right onto Flatbush Avenue."], "2": ["Make a sharp left onto North Bond Street, U.S. 1 Business."], "3": ["Make a sharp right to stay on Sunstone Drive."], "4": ["Make a sharp right at Mannenbashi East."], "5": ["Make a sharp left toward Baltimore."]}}, "start": {"phrases": {"0": "<CARDINAL_DIRECTION> <PERSON><PERSON><PERSON><PERSON><PERSON>.", "1": "<STREET_NAMES> caddesinde <CARDINAL_DIRECTION> <PERSON><PERSON><PERSON><PERSON><PERSON>.", "2": "<BEGIN_STREET_NAMES> caddesinde <CARDINAL_DIRECTION> y<PERSON><PERSON><PERSON><PERSON> il<PERSON>leyin. <STREET_NAMES> caddesine devam edin.", "4": "<CARDINAL_DIRECTION> y<PERSON><PERSON><PERSON><PERSON> doğru <PERSON>.", "5": "<STREET_NAMES> caddesinde <CARDINAL_DIRECTION> yönüne doğru sü<PERSON>ün.", "6": "<BEGIN_STREET_NAMES> caddesinde <CARDINAL_DIRECTION> yö<PERSON><PERSON><PERSON> doğ<PERSON> sü<PERSON>ü<PERSON>. <STREET_NAMES> caddesine devam edin.", "8": "<CARDINAL_DIRECTION> <PERSON><PERSON><PERSON><PERSON><PERSON>.", "9": "<STREET_NAMES> caddesinde <CARDINAL_DIRECTION> yö<PERSON><PERSON><PERSON>.", "10": "<BEGIN_STREET_NAMES> caddesinde <CARDINAL_DIRECTION> yö<PERSON><PERSON><PERSON> y<PERSON>.<STREET_NAMES> caddesine devam edin.", "16": "<CARDINAL_DIRECTION> yönüne bisikletinizle ilerleyin.", "17": "<STREET_NAMES> caddesinde <CARDINAL_DIRECTION> yönüne bisikletinizle ilerleyin.", "18": "<BEGIN_STREET_NAMES> caddesinde <CARDINAL_DIRECTION> yönüne bisikletinizle ilerleyin. <STREET_NAMES> caddesine devam edin."}, "cardinal_directions": ["k<PERSON>y", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "güneybatı", "batı", "kuzeybatı"], "empty_street_name_labels": ["yaya ge<PERSON>idi", "bisiklet yolu", "dağ bisikleti yolu", "the crosswalk", "the stairs", "the bridge", "the tunnel"], "example_phrases": {"0": ["Head east.", "Head north."], "1": ["Head southwest on 5th Avenue.", "Head west on the walkway", "Head east on the cycleway", "Head north on the mountain bike trail"], "2": ["Head south on North Prince Street/US 222/PA 272. Continue on US 222/PA 272."], "4": ["Drive east.", "Drive north."], "5": ["Drive southwest on 5th Avenue."], "6": ["Drive south on North Prince Street/US 222/PA 272. Continue on US 222/PA 272."], "8": ["Walk east.", "Walk north."], "9": ["Walk southwest on 5th Avenue.", "Walk west on the walkway"], "10": ["Walk south on North Prince Street/US 222/PA 272. Continue on US 222/PA 272."], "16": ["Bike east.", "Bike north."], "17": ["Bike southwest on 5th Avenue.", "Bike east on the cycleway", "Bike north on the mountain bike trail"], "18": ["Bike south on North Prince Street/US 222/PA 272. Continue on US 222/PA 272."]}}, "start_verbal": {"phrases": {"0": "<CARDINAL_DIRECTION> <PERSON><PERSON><PERSON><PERSON><PERSON>.", "1": "<LENGTH> boy<PERSON><PERSON> <CARDINAL_DIRECTION> y<PERSON><PERSON><PERSON><PERSON>.", "2": "<STREET_NAMES> caddesinde <CARDINAL_DIRECTION> <PERSON><PERSON><PERSON><PERSON><PERSON>.", "3": "<STREET_NAMES> cadd<PERSON>nde <LENGTH> boyun<PERSON> <CARDINAL_DIRECTION> y<PERSON><PERSON><PERSON><PERSON>.", "4": "<BEGIN_STREET_NAMES> caddesinde <CARDINAL_DIRECTION> y<PERSON><PERSON><PERSON><PERSON>.", "5": "<CARDINAL_DIRECTION> <PERSON><PERSON><PERSON><PERSON><PERSON>.", "6": "<LENGTH> boy<PERSON><PERSON> <CARDINAL_DIRECTION> y<PERSON><PERSON><PERSON><PERSON> s<PERSON>.", "7": "<STREET_NAMES> caddesinde <CARDINAL_DIRECTION> <PERSON><PERSON><PERSON><PERSON><PERSON>.", "8": "<STREET_NAMES> cadd<PERSON>nde <LENGTH> boyun<PERSON> <CARDINAL_DIRECTION> y<PERSON><PERSON><PERSON><PERSON>.", "9": "<BEGIN_STREET_NAMES> caddesinde <CARDINAL_DIRECTION> y<PERSON><PERSON><PERSON><PERSON>.", "10": "<CARDINAL_DIRECTION> <PERSON><PERSON><PERSON><PERSON><PERSON>.", "11": "<CARDINAL_DIRECTION> yönünde <LENGTH> <PERSON><PERSON><PERSON> y<PERSON>.", "12": "<STREET_NAMES> caddesinde <CARDINAL_DIRECTION> yönüne doğru yü<PERSON>ün.", "13": "<STREET_NAMES> cadd<PERSON>nde <CARDINAL_DIRECTION> y<PERSON><PERSON><PERSON><PERSON> doğru <LENGTH> boy<PERSON><PERSON>.", "14": "<BEGIN_STREET_NAMES> caddesinde <CARDINAL_DIRECTION> yö<PERSON>üne doğru <PERSON>.", "15": "<CARDINAL_DIRECTION> yönüne bisikletinizle ilerleyin.", "16": "<CARDINAL_DIRECTION> yönüne <LENGTH> boyunca bisikletinizle ilerleyin.", "17": "<STREET_NAMES> caddesinde <CARDINAL_DIRECTION> yönüne bisikletinizle ilerleyin.", "18": "<STREET_NAMES> caddesinde <CARDINAL_DIRECTION> yönünde <LENGTH> boyunca bisikletinizle ilerleyin.", "19": "<BEGIN_STREET_NAMES> caddesinde <CARDINAL_DIRECTION> yönüne bisikletinizle ilerleyin."}, "cardinal_directions": ["k<PERSON>y", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "güneybatı", "batı", "kuzeybatı"], "empty_street_name_labels": ["yaya ge<PERSON>idi", "bisiklet yolu", "dağ bisikleti yolu", "the crosswalk", "the stairs", "the bridge", "the tunnel"], "metric_lengths": ["<KILOMETERS>kilometre", "1 kilometre", "<METERS> metre", "10 kilometreden az"], "us_customary_lengths": ["<MILES> mil", "1 mil", "yarım mil", "<PERSON><PERSON><PERSON> mil", "<FEET>fit", "10 fitten daha az"], "example_phrases": {"0": ["Head east.", "Head north."], "1": ["Head east for a half mile.", "Head north for 1 kilometer."], "2": ["Head southwest on 5th Avenue."], "3": ["Head southwest on 5th Avenue for 1 tenth of a mile."], "4": ["Head south on North Prince Street, U.S. 2 22."], "5": ["Drive east.", "Drive north."], "6": ["Drive east for a half mile.", "Drive north for 1 kilometer."], "7": ["Drive southwest on 5th Avenue."], "8": ["Drive southwest on 5th Avenue for 1 tenth of a mile."], "9": ["Drive south on North Prince Street, U.S. 2 22."], "10": ["Walk east.", "Walk north."], "11": ["Walk east for a half mile.", "Walk north for 1 kilometer."], "12": ["Walk southwest on 5th Avenue."], "13": ["Walk southwest on 5th Avenue for 1 tenth of a mile."], "14": ["Walk south on North Prince Street, U.S. 2 22."], "15": ["Bike east.", "Bike north."], "16": ["Bike east for a half mile.", "Bike north for 1 kilometer."], "17": ["Bike southwest on 5th Avenue."], "18": ["Bike southwest on 5th Avenue for 1 tenth of a mile."], "19": ["Bike south on North Prince Street, U.S. 2 22."]}}, "transit": {"phrases": {"0": "<TRANSIT_NAME> ge<PERSON><PERSON><PERSON><PERSON> kullanın (<TRANSIT_STOP_COUNT> <TRANSIT_STOP_COUNT_LABEL>)", "1": "<TRANSIT_HEADSIGN> işaretine doğru <TRANSIT_NAME> geç<PERSON>şini kullanın. (<TRANSIT_STOP_COUNT> <TRANSIT_STOP_COUNT_LABEL>)"}, "empty_transit_name_labels": ["tramvay", "metro", "tren", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "feribot", "teleferik", "gondol", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], "transit_stop_count_labels": {"one": "durak", "other": "<PERSON><PERSON><PERSON>"}, "example_phrases": {"0": ["Take the New Haven. (1 stop)", "Take the metro. (2 stops)", "Take the bus. (12 stops)"], "1": ["Take the F toward JAMAICA - 179 ST. (10 stops)", "Take the ferry toward Staten Island. (1 stop)"]}}, "transit_connection_destination": {"phrases": {"0": "İstasyondan çıkın.", "1": "<TRANSIT_STOP> istasyonundan çıkın.", "2": "<TRANSIT_STOP> <STATION_LABEL> istasyonundan çıkın."}, "station_label": "İstasyon", "example_phrases": {"0": ["Exit the station."], "1": ["Exit the Embarcadero Station."], "2": ["Exit the 8 St - NYU Station."]}}, "transit_connection_destination_verbal": {"phrases": {"0": "İstasyondan çıkın.", "1": "<TRANSIT_STOP> istasyonundan çıkın.", "2": "<TRANSIT_STOP> <STATION_LABEL> istasyonundan çıkın."}, "station_label": "İstasyon", "example_phrases": {"0": ["Exit the station."], "1": ["Exit the Embarcadero Station."], "2": ["Exit the 8 St - NYU Station."]}}, "transit_connection_start": {"phrases": {"0": "İstasyonuna girin.", "1": "<TRANSIT_STOP> du<PERSON><PERSON><PERSON><PERSON> girin.", "2": "<TRANSIT_STOP> <STATION_LABEL><PERSON><PERSON><PERSON><PERSON><PERSON> girin."}, "station_label": "İstasyon", "example_phrases": {"0": ["Enter the station."], "1": ["Enter the Embarcadero Station."], "2": ["Enter the 8 St - NYU Station."]}}, "transit_connection_start_verbal": {"phrases": {"0": "İstasyonuna girin.", "1": "<TRANSIT_STOP> du<PERSON><PERSON><PERSON><PERSON> girin.", "2": "<TRANSIT_STOP> <STATION_LABEL><PERSON><PERSON><PERSON><PERSON><PERSON> girin."}, "station_label": "İstasyon", "example_phrases": {"0": ["Enter the station."], "1": ["Enter the Embarcadero Station."], "2": ["Enter the 8 St - NYU Station."]}}, "transit_connection_transfer": {"phrases": {"0": "İstasyonunda aktarma yapın.", "1": "<TRANSIT_STOP> istasyonunda aktarma yapın.", "2": "<TRANSIT_STOP> <STATION_LABEL> istasyonunda aktarma yapın."}, "station_label": "İstasyon", "example_phrases": {"0": ["Transfer at the station."], "1": ["Transfer at the Embarcadero Station."], "2": ["Transfer at the 8 St - NYU Station."]}}, "transit_connection_transfer_verbal": {"phrases": {"0": "İstasyonunda aktarma yapın.", "1": "<TRANSIT_STOP> istasyonunda aktarma yapın.", "2": "<TRANSIT_STOP> <STATION_LABEL> istasyonunda aktarma yapın."}, "station_label": "İstasyon", "example_phrases": {"0": ["Transfer at the station."], "1": ["Transfer at the Embarcadero Station."], "2": ["Transfer at the 8 St - NYU Station."]}}, "transit_remain_on": {"phrases": {"0": "<TRANSIT_NAME> geç<PERSON>şinde kalın. (<TRANSIT_STOP_COUNT> <TRANSIT_STOP_COUNT_LABEL>)", "1": "<TRANSIT_HEADSIGN> işaretine doğru <TRANSIT_NAME> geçişinde kalın. (<TRANSIT_STOP_COUNT> <TRANSIT_STOP_COUNT_LABEL>)"}, "empty_transit_name_labels": ["tramvay", "metro", "tren", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "feribot", "teleferik", "gondol", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], "transit_stop_count_labels": {"one": "durak", "other": "<PERSON><PERSON><PERSON>"}, "example_phrases": {"0": ["Remain on the New Haven. (1 stop)", "<PERSON><PERSON><PERSON> on the train. (3 stops)"], "1": ["Remain on the F toward JAMAICA - 179 ST. (10 stops)"]}}, "transit_remain_on_verbal": {"phrases": {"0": "<TRANSIT_NAME> geçişinde kalın.", "1": "<TRANSIT_HEADSIGN> işaretine doğru <TRANSIT_NAME> geçişinde kalın."}, "empty_transit_name_labels": ["tramvay", "metro", "tren", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "feribot", "teleferik", "gondol", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], "example_phrases": {"0": ["<PERSON><PERSON><PERSON> on the New Haven."], "1": ["Remain on the F toward JAMAICA - 179 ST."]}}, "transit_transfer": {"phrases": {"0": "<TRANSIT_NAME> geçişini kullanmak için aktarma yapın. (<TRANSIT_STOP_COUNT> <TRANSIT_STOP_COUNT_LABEL>)", "1": "<TRANSIT_HEADSIGN> işaretine doğru <TRANSIT_NAME> geçişini kullanmak için aktarma yapın (<TRANSIT_STOP_COUNT> <TRANSIT_STOP_COUNT_LABEL>)"}, "empty_transit_name_labels": ["tramvay", "metro", "tren", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "feribot", "teleferik", "gondol", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], "transit_stop_count_labels": {"one": "durak", "other": "<PERSON><PERSON><PERSON>"}, "example_phrases": {"0": ["Transfer to take the New Haven. (1 stop)", "Transfer to take the tram. (4 stops)"], "1": ["Transfer to take the F toward JAMAICA - 179 ST. (10 stops)"]}}, "transit_transfer_verbal": {"phrases": {"0": "<TRANSIT_NAME> geçişini kullanmak için aktarma yapın.", "1": "<TRANSIT_HEADSIGN> işaretine doğru <TRANSIT_NAME> geçişini kullanmak için aktarma yapın."}, "empty_transit_name_labels": ["tramvay", "metro", "tren", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "feribot", "teleferik", "gondol", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], "example_phrases": {"0": ["Transfer to take the New Haven."], "1": ["Transfer to take the F toward JAMAICA - 179 ST."]}}, "transit_verbal": {"phrases": {"0": "<TRANSIT_NAME> geçişini kullanın.", "1": "<TRANSIT_HEADSIGN> işaretine doğru <TRANSIT_NAME> geçişini kullanın."}, "empty_transit_name_labels": ["tramvay", "metro", "tren", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "feribot", "teleferik", "gondol", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], "example_phrases": {"0": ["Take the New Haven."], "1": ["Take the F toward JAMAICA - 179 ST."]}}, "turn": {"phrases": {"0": "<RELATIVE_DIRECTION>a dönün.", "1": "<STREET_NAMES> caddesine doğru <RELATIVE_DIRECTION>a dönün.", "2": "<BEGIN_STREET_NAMES> caddesine doğru <RELATIVE_DIRECTION>a dönün.<STREET_NAMES> caddesine devam edin.", "3": "<STREET_NAMES> caddesinde kalmak için <RELATIVE_DIRECTION>a dönün.", "4": "<JUNCTION_NAME> kavşağında <RELATIVE_DIRECTION>a dönün.", "5": "<TOWARD_SIGN> i<PERSON><PERSON><PERSON> <RELATIVE_DIRECTION>a dönün."}, "empty_street_name_labels": ["yaya ge<PERSON>idi", "bisiklet yolu", "dağ bisikleti yolu", "the crosswalk", "the stairs", "the bridge", "the tunnel"], "relative_directions": ["sol", "sağ"], "example_phrases": {"0": ["Turn left."], "1": ["Turn right onto Flatbush Avenue."], "2": ["Turn left onto North Bond Street/US 1 Business/MD 924. Continue on MD 924."], "3": ["Turn right to stay on Sunstone Drive."], "4": ["Turn right at Mannenbashi East."], "5": ["Turn left toward Baltimore."]}}, "turn_verbal": {"phrases": {"0": "<RELATIVE_DIRECTION>a dönün.", "1": "<STREET_NAMES> caddesine doğru <RELATIVE_DIRECTION>a dönün.", "2": "<BEGIN_STREET_NAMES> caddesine do<PERSON> <RELATIVE_DIRECTION>a dönün.", "3": "<STREET_NAMES> caddesinde kalmak için <RELATIVE_DIRECTION>a dönün.", "4": "<JUNCTION_NAME> kavşağında <RELATIVE_DIRECTION>a dönün.", "5": "<TOWARD_SIGN> i<PERSON><PERSON><PERSON> <RELATIVE_DIRECTION>a dönün."}, "empty_street_name_labels": ["yaya ge<PERSON>idi", "bisiklet yolu", "dağ bisikleti yolu", "the crosswalk", "the stairs", "the bridge", "the tunnel"], "relative_directions": ["sol", "sağ"], "example_phrases": {"0": ["Turn left."], "1": ["Turn right onto Flatbush Avenue."], "2": ["Turn left onto North Bond Street, U.S. 1 Business."], "3": ["Turn right to stay on Sunstone Drive."], "4": ["Turn right at Mannenbashi East."], "5": ["Turn left toward Baltimore."]}}, "uturn": {"phrases": {"0": "<RELATIVE_DIRECTION>a doğru U dönüşü yapın.", "1": "<STREET_NAMES> cadd<PERSON>zer<PERSON>e <RELATIVE_DIRECTION>a doğru U dönüşü yapın.", "2": "<STREET_NAMES> caddesinde kalmak için <RELATIVE_DIRECTION>a doğru U dönüşü yapın.", "3": "<CROSS_STREET_NAMES> caddesinde <RELATIVE_DIRECTION>a doğru U dönüşü yapın.", "4": "<CROSS_STREET_NAMES> caddesinde <STREET_NAMES> caddesine doğru <RELATIVE_DIRECTION>a doğru U dönüşü yapın.", "5": "<CROSS_STREET_NAMES> caddesinde <STREET_NAMES> caddesinde kalmak için <RELATIVE_DIRECTION>a doğru U dönüşü yapın.", "6": "<JUNCTION_NAME> kavşağında <RELATIVE_DIRECTION>a doğru U dönüşü yapın.", "7": "<TOWARD_SIGN> işaretine doğru <RELATIVE_DIRECTION>a doğru U dönüşü yapın."}, "empty_street_name_labels": ["yaya ge<PERSON>idi", "bisiklet yolu", "dağ bisikleti yolu", "the crosswalk", "the stairs", "the bridge", "the tunnel"], "relative_directions": ["sol", "sağ"], "example_phrases": {"0": ["Make a left U-turn."], "1": ["Make a right U-turn onto Bunker Hill Road."], "2": ["Make a left U-turn to stay on Bunker Hill Road."], "3": ["Make a left U-turn at Devonshire Road."], "4": ["Make a left U-turn at Devonshire Road onto Jonestown Road/US 22."], "5": ["Make a left U-turn at Devonshire Road to stay on Jonestown Road/US 22."], "6": ["Make a right U-turn at Mannenbashi East."], "7": ["Make a left U-turn toward Baltimore."]}}, "uturn_verbal": {"phrases": {"0": "<RELATIVE_DIRECTION>a doğru U dönüşü yapın.", "1": "<STREET_NAMES> caddesine doğru <RELATIVE_DIRECTION>a doğru U dönüşü yapın.", "2": "<STREET_NAMES> caddesinde kalmak için <RELATIVE_DIRECTION>a doğru U dönüşü yapın.", "3": "<CROSS_STREET_NAMES> caddesinde <RELATIVE_DIRECTION>a doğru U dönüşü yapın.", "4": "<CROSS_STREET_NAMES> caddesinde <STREET_NAMES> caddesine doğru <RELATIVE_DIRECTION>a doğru U dönüşü yapın.", "5": "<CROSS_STREET_NAMES> caddesinde <STREET_NAMES> caddesinde kalmak için <RELATIVE_DIRECTION>a doğru U dönüşü yapın.", "6": "<JUNCTION_NAME> kavşağında <RELATIVE_DIRECTION>a doğru U dönüşü yapın.", "7": "<TOWARD_SIGN> işaretine doğru <RELATIVE_DIRECTION>a doğru U dönüşü yapın."}, "empty_street_name_labels": ["yaya ge<PERSON>idi", "bisiklet yolu", "dağ bisikleti yolu", "the crosswalk", "the stairs", "the bridge", "the tunnel"], "relative_directions": ["sol", "sağ"], "example_phrases": {"0": ["Make a left U-turn."], "1": ["Make a right U-turn onto Bunker Hill Road."], "2": ["Make a left U-turn to stay on Bunker Hill Road."], "3": ["Make a left U-turn at Devonshire Road."], "4": ["Make a left U-turn at Devonshire Road onto Jonestown Road, U.S. 22."], "5": ["Make a left U-turn at Devonshire Road to stay on Jonestown Road, U.S. 22."], "6": ["Make a right U-turn at Mannenbashi East."], "7": ["Make a left U-turn toward Baltimore."]}}, "verbal_multi_cue": {"phrases": {"0": "<CURRENT_VERBAL_CUE> Sonra <NEXT_VERBAL_CUE>", "1": "<CURRENT_VERBAL_CUE> <PERSON><PERSON>, <LENGTH> son<PERSON>, <NEXT_VERBAL_CUE>"}, "metric_lengths": ["<KILOMETERS>kilometre", "1 kilometre", "<METERS> metre", "10 kilometreden az"], "us_customary_lengths": ["<MILES> mil", "1 mil", "yarım mil", "<PERSON><PERSON><PERSON> mil", "<FEET>fit", "10 fitten daha az"], "example_phrases": {"0": ["Bear right onto East Fayette Street. Then Turn right onto North Gay Street."], "1": ["Bear right onto East Fayette Street. Then, in 500 feet, Turn right onto North Gay Street."]}}, "approach_verbal_alert": {"phrases": {"0": "<LENGTH> son<PERSON>, <CURRENT_VERBAL_CUE>"}, "metric_lengths": ["<KILOMETERS>kilometre", "1 kilometre", "<METERS> metre", "10 kilometreden az"], "us_customary_lengths": ["<MILES> mil", "1 mil", "yarım mil", "<PERSON><PERSON><PERSON> mil", "<FEET>fit", "10 fitten daha az"], "example_phrases": {"0": ["In a quarter mile, Turn right onto North Gay Street."]}}, "pass": {"phrases": {"0": "Pass <OBJECT_LABEL>.", "1": "Pass traffic signals on <OBJECT_LABEL>."}, "object_labels": ["the gate", "the bollards", "ways intersection"], "example_phrases": {"0": ["Pass the gate."]}}, "elevator": {"phrases": {"0": "Take the elevator.", "1": "Take the elevator to <LEVEL>."}, "example_phrases": {"0": ["Take the elevator."], "1": ["Take the elevator to Level 1."]}}, "steps": {"phrases": {"0": "Take the stairs.", "1": "Take the stairs to <LEVEL>."}, "example_phrases": {"0": ["Take the stairs."], "1": ["Take the stairs to Level 2."]}}, "escalator": {"phrases": {"0": "Take the escalator.", "1": "Take the escalator to <LEVEL>."}, "example_phrases": {"0": ["Take the escalator."], "1": ["Take the escalator to Level 3."]}}, "enter_building": {"phrases": {"0": "Enter the building.", "1": "Enter the building, and continue on <STREET_NAMES>."}, "empty_street_name_labels": ["yaya ge<PERSON>idi", "bisiklet yolu", "dağ bisikleti yolu", "the crosswalk"], "example_phrases": {"0": ["Enter the building."], "1": ["Enter the building, and continue on the walkway."]}}, "exit_building": {"phrases": {"0": "Exit the building.", "1": "Exit the building, and continue on <STREET_NAMES>."}, "empty_street_name_labels": ["yaya ge<PERSON>idi", "bisiklet yolu", "dağ bisikleti yolu", "the crosswalk"], "example_phrases": {"0": ["Exit the building."], "1": ["Exit the building, and continue on the walkway."]}}}}
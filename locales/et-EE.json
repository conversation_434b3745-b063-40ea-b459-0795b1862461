{"posix_locale": "et_EE.UTF-8", "aliases": ["et"], "instructions": {"arrive": {"phrases": {"0": "Saabumisaeg: <TIME>.", "1": "Sa<PERSON>ud <TIME> vahepeatusesse <TRANSIT_STOP>."}, "example_phrases": {"0": ["Arrive: 8:02 AM."], "1": ["Arrive: 8:02 AM at 8 St - NYU."]}}, "arrive_verbal": {"phrases": {"0": "Saabumisaeg <TIME>.", "1": "Sa<PERSON>ud <TIME> vahepeatusesse <TRANSIT_STOP>."}, "example_phrases": {"0": ["Arrive at 8:02 AM."], "1": ["Arrive at 8:02 AM at 8 St - NYU."]}}, "bear": {"phrases": {"0": "<PERSON><PERSON><PERSON> <RELATIVE_DIRECTION>.", "1": "<PERSON><PERSON><PERSON> <RELATIVE_DIRECTION> <STREET_NAMES> teele.", "2": "<PERSON><PERSON><PERSON> <RELATIVE_DIRECTION> <BEGIN_STREET_NAMES> teed mööda. Jätka piki <STREET_NAMES>.", "3": "<PERSON><PERSON><PERSON> <RELATIVE_DIRECTION> ja j<PERSON><PERSON> <STREET_NAMES> teele.", "4": "<JUNCTION_NAME> tee<PERSON><PERSON> võ<PERSON> <RELATIVE_DIRECTION>.", "5": "<PERSON><PERSON><PERSON> su<PERSON> <RELATIVE_DIRECTION> <TOWARD_SIGN> poole."}, "empty_street_name_labels": ["läbipää<PERSON>", "rattatee", "rada mägiratastele", "ülekäigurajal", "the stairs", "the bridge", "the tunnel"], "relative_directions": ["vasakul", "paremal"], "example_phrases": {"0": ["Bear right."], "1": ["<PERSON> left onto Arlen Road."], "2": ["Bear right onto Belair Road/US 1 Business. Continue on US 1 Business."], "3": ["<PERSON> left to stay on US 15 South."], "4": ["Bear right at Mannenbashi East."], "5": ["Bear left toward Baltimore."]}}, "bear_verbal": {"phrases": {"0": "<PERSON><PERSON><PERSON> <RELATIVE_DIRECTION>.", "1": "<PERSON><PERSON><PERSON> <RELATIVE_DIRECTION> <STREET_NAMES> teele.", "2": "<PERSON><PERSON><PERSON> <RELATIVE_DIRECTION> <BEGIN_STREET_NAMES> teele.", "3": "<PERSON><PERSON><PERSON> <RELATIVE_DIRECTION> ja j<PERSON><PERSON> <STREET_NAMES> teele.", "4": "<JUNCTION_NAME> tee<PERSON><PERSON> võ<PERSON> <RELATIVE_DIRECTION>.", "5": "<PERSON><PERSON><PERSON> su<PERSON> <RELATIVE_DIRECTION> <TOWARD_SIGN> poole."}, "empty_street_name_labels": ["läbipää<PERSON>", "rattatee", "rada mägiratastele", "ülekäigurajal", "the stairs", "the bridge", "the tunnel"], "relative_directions": ["vasakul", "paremal"], "example_phrases": {"0": ["Bear right."], "1": ["<PERSON> left onto Arlen Road."], "2": ["Bear right onto Belair Road, U.S. 1 Business."], "3": ["<PERSON> left to stay on U.S. 15 South."], "4": ["Bear right at Mannenbashi East."], "5": ["Bear left toward Baltimore."]}}, "becomes": {"phrases": {"0": "Senise <PREVIOUS_STREET_NAMES> nimi on nüüd <STREET_NAMES>."}, "example_phrases": {"0": ["Vine Street becomes Middletown Road."]}}, "becomes_verbal": {"phrases": {"0": "Senise <PREVIOUS_STREET_NAMES> nimi on nüüd <STREET_NAMES>."}, "example_phrases": {"0": ["Vine Street becomes Middletown Road."]}}, "continue": {"phrases": {"0": "Jätka.", "1": "Jätka mööda <STREET_NAMES> tänavat.", "2": "Jätka <JUNCTION_NAME> teeristis.", "3": "Jätka <TOWARD_SIGN> suunas."}, "empty_street_name_labels": ["läbipää<PERSON>", "rattatee", "rada mägiratastele", "ülekäigurajal", "the stairs", "the bridge", "the tunnel"], "example_phrases": {"0": ["Continue."], "1": ["Continue on 10th Avenue."], "2": ["Continue at Mannenbashi East."], "3": ["Continue toward Baltimore."]}}, "continue_verbal": {"phrases": {"0": "Jätka.", "1": "<PERSON><PERSON><PERSON> veel <LENGTH>.", "2": "Jätka mööda <STREET_NAMES> tänavat.", "3": "Jätka <STREET_NAMES> tänavale veel <LENGTH>.", "4": "Jätka <JUNCTION_NAME> teeristis.", "5": "Jätka <JUNCTION_NAME> teeristist veel <LENGTH>.", "6": "Jätka <TOWARD_SIGN> suunas.", "7": "Jätka <TOWARD_SIGN> su<PERSON>s veel <LENGTH>."}, "empty_street_name_labels": ["läbipää<PERSON>", "rattatee", "rada mägiratastele", "ülekäigurajal", "the stairs", "the bridge", "the tunnel"], "metric_lengths": ["<KILOMETERS> kilomeetrit", "1 kilomeeter", "<METERS> meetrit", "vähem kui 10 meetrit"], "us_customary_lengths": ["<MILES> mi<PERSON>", "1 miil", "pool miili", "veerand mi<PERSON>", "<FEET> jalga", "vähem kui 10 jalga"], "example_phrases": {"0": ["Continue."], "1": ["Continue for 300 feet."], "2": ["Continue on 10th Avenue."], "3": ["Continue on 10th Avenue for 3 miles."], "4": ["Continue at Mannenbashi East."], "5": ["Continue at Mannenbashi East for 2 miles."], "6": ["Continue toward Baltimore."], "7": ["Continue toward Baltimore for 5 miles."]}}, "continue_verbal_alert": {"phrases": {"0": "Jätka.", "1": "Jätka mööda <STREET_NAMES> tänavat.", "2": "Jätka <JUNCTION_NAME> teeristis.", "3": "Jätka <TOWARD_SIGN> suunas."}, "empty_street_name_labels": ["läbipää<PERSON>", "rattatee", "rada mägiratastele", "ülekäigurajal", "the stairs", "the bridge", "the tunnel"], "example_phrases": {"0": ["Continue."], "1": ["Continue on 10th Avenue."], "2": ["Continue at Mannenbashi East."], "3": ["Continue toward Baltimore."]}}, "depart": {"phrases": {"0": "Väljumisaeg: <TIME>.", "1": "Väljud <TIME> vahepeatusest <TRANSIT_STOP>."}, "example_phrases": {"0": ["Depart: 8:02 AM."], "1": ["Depart: 8:02 AM from 8 St - NYU."]}}, "depart_verbal": {"phrases": {"0": "Väljumisaeg <TIME>.", "1": "Väljud <TIME> vahepeatusest <TRANSIT_STOP>."}, "example_phrases": {"0": ["Depart at 8:02 AM."], "1": ["Depart at 8:02 AM from 8 St - NYU."]}}, "destination": {"phrases": {"0": "Sa oled saabunud oma si<PERSON>.", "1": "Sa oled saabunud si<PERSON> <DESTINATION>.", "2": "Sinu sihtkoht asub <RELATIVE_DIRECTION>.", "3": "<DESTINATION> asub <RELATIVE_DIRECTION>."}, "relative_directions": ["vasakul", "paremal"], "example_phrases": {"0": ["You have arrived at your destination."], "1": ["You have arrived at 3206 Powelton Avenue."], "2": ["Your destination is on the left.", "Your destination is on the right."], "3": ["Lancaster Brewing Company is on the left."]}}, "destination_verbal": {"phrases": {"0": "Sa oled saabunud oma si<PERSON>.", "1": "Sa oled saabunud si<PERSON> <DESTINATION>.", "2": "Sinu sihtkoht asub <RELATIVE_DIRECTION>.", "3": "<DESTINATION> asub <RELATIVE_DIRECTION>."}, "relative_directions": ["vasakul", "paremal"], "example_phrases": {"0": ["You have arrived at your destination."], "1": ["You have arrived at 32 o6 Powelton Avenue."], "2": ["Your destination is on the left.", "Your destination is on the right."], "3": ["Lancaster Brewing Company is on the left."]}}, "destination_verbal_alert": {"phrases": {"0": "Sa saabud oma si<PERSON>hta.", "1": "Sa saabud si<PERSON>hta <DESTINATION>.", "2": "Sinu sihtkoht asub <RELATIVE_DIRECTION>.", "3": "<DESTINATION> asub <RELATIVE_DIRECTION>."}, "relative_directions": ["vasakul", "paremal"], "example_phrases": {"0": ["You will arrive at your destination."], "1": ["You will arrive at 32 o6 Powelton Avenue."], "2": ["Your destination will be on the left.", "Your destination will be on the right."], "3": ["Lancaster Brewing Company will be on the left."]}}, "enter_ferry": {"phrases": {"0": "Mine praamile", "1": "Pööra <STREET_NAMES> tänavale.", "2": "<PERSON><PERSON> <FERRY_LABEL> p<PERSON><PERSON><PERSON> <STREET_NAMES> tänavale.", "3": "Mine praamile <TOWARD_SIGN> suunas."}, "empty_street_name_labels": ["läbipää<PERSON>", "rattatee", "rada mägiratastele", "ülekäigurajal", "the stairs", "the bridge", "the tunnel"], "ferry_label": "<PERSON><PERSON><PERSON>", "example_phrases": {"0": ["Take the Ferry."], "1": ["Take the Millersburg Ferry."], "2": ["Take the Bridgeport - Port Jefferson Ferry."], "3": ["Take the ferry toward Cape May."]}}, "enter_ferry_verbal": {"phrases": {"0": "Mine praamile", "1": "Pööra <STREET_NAMES> tänavale.", "2": "<PERSON><PERSON> <FERRY_LABEL> p<PERSON><PERSON><PERSON> <STREET_NAMES> tänavale.", "3": "Mine praamile <TOWARD_SIGN> suunas."}, "empty_street_name_labels": ["läbipää<PERSON>", "rattatee", "rada mägiratastele", "ülekäigurajal", "the stairs", "the bridge", "the tunnel"], "ferry_label": "<PERSON><PERSON><PERSON>", "example_phrases": {"0": ["Take the Ferry."], "1": ["Take the Millersburg Ferry."], "2": ["Take the Bridgeport - Port Jefferson Ferry."], "3": ["Take the ferry toward Cape May."]}}, "enter_roundabout": {"phrases": {"0": "<PERSON><PERSON><PERSON> ringteele.", "1": "Sõida ringteele ja v<PERSON><PERSON><PERSON> sealt <ORDINAL_VALUE> ärapöördest.", "2": "Sõida ringteele ja <ORDINAL_VALUE>ärapöördest liigu <ROUNDABOUT_EXIT_STREET_NAMES> teele.", "3": "<PERSON><PERSON><PERSON> ringteele ja <ORDINAL_VALUE> ärapöördest liigu <ROUNDABOUT_EXIT_BEGIN_STREET_NAMES> teele. <PERSON><PERSON><PERSON> möö<PERSON> <ROUNDABOUT_EXIT_STREET_NAMES> teed.", "4": "Sõida ringteele ja <ORDINAL_VALUE> ärapöördest liigu <TOWARD_SIGN> suunas.", "5": "<PERSON><PERSON><PERSON> ringteele ja pööra ära <ROUNDABOUT_EXIT_STREET_NAMES> teele.", "6": "<PERSON><PERSON><PERSON> ringteele ja pööra ära <ROUNDABOUT_EXIT_BEGIN_STREET_NAMES> teele. <PERSON><PERSON><PERSON> mööda <ROUNDABOUT_EXIT_STREET_NAMES> teed.", "7": "Sõida ringteele ja pö<PERSON>ra <PERSON>ra <TOWARD_SIGN> suunas.", "8": "Liigu <STREET_NAMES> tänavale", "9": "Liigu <STREET_NAMES> tänavale ja lahku sealt <ORDINAL_VALUE> ärapöördest. ", "10": "Sõida <STREET_NAMES> tänavale ja <ORDINAL_VALUE> ärapöördest suundu <ROUNDABOUT_EXIT_STREET_NAMES> tänavale.", "11": "Sõida <STREET_NAMES> tänavale ja <ORDINAL_VALUE> ärapöördest suundu <ROUNDABOUT_EXIT_BEGIN_STREET_NAMES> tänavale. Jät<PERSON> mööda <ROUNDABOUT_EXIT_STREET_NAMES> tänavat.", "12": "Liigu <STREET_NAMES> tänavale ja lahku sealt <ORDINAL_VALUE> ärapöördest <TOWARD_SIGN> suunas.", "13": "Sõida <STREET_NAMES> tänavale ja pööra siis <ROUNDABOUT_EXIT_STREET_NAMES> tänavale.", "14": "Sõida <STREET_NAMES> tänavale ja pööra siis <ROUNDABOUT_EXIT_BEGIN_STREET_NAMES> tänavale. <PERSON><PERSON><PERSON> mööda <ROUNDABOUT_EXIT_STREET_NAMES> tänavat.", "15": "Liigu <STREET_NAMES> tänavale ja lahku seakt <TOWARD_SIGN> suunas."}, "ordinal_values": ["1.", "2.", "3.", "4.", "5.", "6.", "7.", "8.", "9.", "10."], "empty_street_name_labels": ["läbipää<PERSON>", "rattatee", "rada mägiratastele", "ülekäigurajal", "the stairs", "the bridge", "the tunnel"], "example_phrases": {"0": ["Enter the roundabout."], "1": ["Enter the roundabout and take the 1st exit.", "Enter the roundabout and take the 2nd exit.", "Enter the roundabout and take the 3rd exit.", "Enter the roundabout and take the 4th exit.", "Enter the roundabout and take the 5th exit.", "Enter the roundabout and take the 6th exit.", "Enter the roundabout and take the 7th exit.", "Enter the roundabout and take the 8th exit.", "Enter the roundabout and take the 9th exit.", "Enter the roundabout and take the 10th exit."], "2": ["Enter the roundabout and take the 3rd exit onto Main Street."], "3": ["Enter the roundabout and take the 3rd exit onto US 322/Main Street. Continue on US 322."], "4": ["Enter the roundabout and take the 3rd exit toward Baltimore."], "5": ["Enter the roundabout and take the exit onto Main Street."], "6": ["Enter the roundabout and take the exit onto US 322/Main Street. Continue on US 322."], "7": ["Enter the roundabout and take the exit toward Baltimore."], "8": ["Enter Dupont Circle."], "9": ["Enter Dupont Circle and take the 1st exit."], "10": ["Enter Dupont Circle and take the 3rd exit onto Main Street."], "11": ["Enter Dupont Circle and take the 3rd exit onto US 322/Main Street. Continue on US 322."], "12": ["Enter Dupont Circle and take the 3rd exit toward Baltimore."], "13": ["Enter Dupont Circle and take the exit onto Main Street."], "14": ["Enter Dupont Circle and take the exit onto US 322/Main Street. Continue on US 322."], "15": ["Enter Dupont Circle and take the exit toward Baltimore."]}}, "enter_roundabout_verbal": {"phrases": {"0": "<PERSON><PERSON><PERSON> ringteele.", "1": "Sõida ringteele ja v<PERSON><PERSON><PERSON> sealt <ORDINAL_VALUE> ärapöördest.", "2": "Sõida ringteele ja <ORDINAL_VALUE>ärapöördest liigu <ROUNDABOUT_EXIT_STREET_NAMES> teele.", "3": "Sõida ringteele ja <ORDINAL_VALUE> ärapöördest liigu <ROUNDABOUT_EXIT_BEGIN_STREET_NAMES> teele.", "4": "Sõida ringteele ja <ORDINAL_VALUE> ärapöördest liigu <TOWARD_SIGN> suunas.", "5": "<PERSON><PERSON><PERSON> ringteele ja pööra ära <ROUNDABOUT_EXIT_STREET_NAMES> teele.", "6": "<PERSON><PERSON><PERSON> ringteele ja pööra ära <ROUNDABOUT_EXIT_BEGIN_STREET_NAMES> teele.", "7": "Sõida ringteele ja pö<PERSON>ra <PERSON>ra <TOWARD_SIGN> suunas.", "8": "Liigu <STREET_NAMES> tänavale", "9": "Liigu <STREET_NAMES> tänavale ja lahku sealt <ORDINAL_VALUE> ärapöördest. ", "10": "Sõida <STREET_NAMES> tänavale ja <ORDINAL_VALUE> ärapöördest suundu <ROUNDABOUT_EXIT_STREET_NAMES> tänavale.", "11": "Sõida <STREET_NAMES> tänavale ja <ORDINAL_VALUE> ärapöördest suundu <ROUNDABOUT_EXIT_BEGIN_STREET_NAMES> tänavale.", "12": "Liigu <STREET_NAMES> tänavale ja lahku sealt <ORDINAL_VALUE> ärapöördest <TOWARD_SIGN> suunas.", "13": "Sõida <STREET_NAMES> tänavale ja pööra siis <ROUNDABOUT_EXIT_STREET_NAMES> tänavale.", "14": "Sõida <STREET_NAMES> tänavale ja pööra siis <ROUNDABOUT_EXIT_BEGIN_STREET_NAMES> tänavale.", "15": "Liigu <STREET_NAMES> tänavale ja lahku seakt <TOWARD_SIGN> suunas."}, "ordinal_values": ["1.", "2.", "3.", "4.", "5.", "6.", "7.", "8.", "9.", "10."], "empty_street_name_labels": ["läbipää<PERSON>", "rattatee", "rada mägiratastele", "ülekäigurajal", "the stairs", "the bridge", "the tunnel"], "example_phrases": {"0": ["Enter the roundabout."], "1": ["Enter the roundabout and take the 1st exit.", "Enter the roundabout and take the 2nd exit.", "Enter the roundabout and take the 3rd exit.", "Enter the roundabout and take the 4th exit.", "Enter the roundabout and take the 5th exit.", "Enter the roundabout and take the 6th exit.", "Enter the roundabout and take the 7th exit.", "Enter the roundabout and take the 8th exit.", "Enter the roundabout and take the 9th exit.", "Enter the roundabout and take the 10th exit."], "2": ["Enter the roundabout and take the 3rd exit onto Main Street."], "3": ["Enter the roundabout and take the 3rd exit onto U.S. 3 22/Main Street."], "4": ["Enter the roundabout and take the 3rd exit toward Baltimore."], "5": ["Enter the roundabout and take the exit onto Main Street."], "6": ["Enter the roundabout and take the exit onto U.S. 3 22/Main Street."], "7": ["Enter the roundabout and take the exit toward Baltimore."], "8": ["Enter Dupont Circle."], "9": ["Enter Dupont Circle and take the 1st exit."], "10": ["Enter Dupont Circle and take the 3rd exit onto Main Street."], "11": ["Enter Dupont Circle and take the 3rd exit onto U.S. 3 22/Main Street."], "12": ["Enter Dupont Circle and take the 3rd exit toward Baltimore."], "13": ["Enter Dupont Circle and take the exit onto Main Street."], "14": ["Enter Dupont Circle and take the exit onto U.S. 3 22/Main Street."], "15": ["Enter Dupont Circle and take the exit toward Baltimore."]}}, "exit": {"phrases": {"0": "<PERSON><PERSON><PERSON> <RELATIVE_DIRECTION>.", "1": "<PERSON><PERSON><PERSON> <NUMBER_SIGN> ärapööret <RELATIVE_DIRECTION>.", "2": "<PERSON><PERSON><PERSON> <BRANCH_SIGN> ärapööret <RELATIVE_DIRECTION>.", "3": "<PERSON><PERSON><PERSON> <NUMBER_SIGN> ärapööret <RELATIVE_DIRECTION> <BRANCH_SIGN> su<PERSON><PERSON>.", "4": "<PERSON><PERSON><PERSON> <RELATIVE_DIRECTION> <TOWARD_SIGN> suunas.", "5": "<PERSON><PERSON><PERSON> <NUMBER_SIGN> ärapööret <RELATIVE_DIRECTION> <TOWARD_SIGN> su<PERSON><PERSON>.", "6": "<PERSON><PERSON><PERSON> <BRANCH_SIGN> ärapööret <RELATIVE_DIRECTION> <TOWARD_SIGN> su<PERSON><PERSON>.", "7": "<PERSON><PERSON><PERSON> <NUMBER_SIGN> ärapööret <RELATIVE_DIRECTION> <BRANCH_SIGN> ja <TOWARD_SIGN> su<PERSON><PERSON>.", "8": "<PERSON><PERSON><PERSON> <NAME_SIGN> ärapööret <RELATIVE_DIRECTION>.", "10": "<PERSON><PERSON><PERSON> <NAME_SIGN> ärapööret <RELATIVE_DIRECTION> <BRANCH_SIGN> su<PERSON><PERSON>.", "12": "<PERSON><PERSON><PERSON> <NAME_SIGN> ärapööret <RELATIVE_DIRECTION> <TOWARD_SIGN> su<PERSON><PERSON>.", "14": "<PERSON><PERSON><PERSON> <NAME_SIGN> ärapööret <RELATIVE_DIRECTION> <BRANCH_SIGN> teele <TOWARD_SIGN> su<PERSON><PERSON>.", "15": "Pööra <PERSON>.", "16": "<PERSON><PERSON><PERSON> <NUMBER_SIGN> ärapööret.", "17": "<PERSON><PERSON><PERSON> <BRANCH_SIGN> ärapööret.", "18": "<PERSON><PERSON><PERSON> <NUMBER_SIGN> ärapööret <BRANCH_SIGN> suunas.", "19": "Pööra ära <TOWARD_SIGN> suunas.", "20": "<PERSON><PERSON><PERSON> <NUMBER_SIGN> ärapööret <TOWARD_SIGN> suunas.", "21": "<PERSON><PERSON><PERSON> <BRANCH_SIGN> ärapööret <TOWARD_SIGN> suunas.", "22": "<PERSON><PERSON><PERSON> <NUMBER_SIGN> ärapööret <BRANCH_SIGN> ja <TOWARD_SIGN> su<PERSON><PERSON>.", "23": "<PERSON><PERSON><PERSON> <NAME_SIGN> ärapööret.", "25": "<PERSON><PERSON><PERSON> <NAME_SIGN> ärapööret <BRANCH_SIGN> suunas.", "27": "<PERSON><PERSON><PERSON> <NAME_SIGN> ärapööret <TOWARD_SIGN> suunas.", "29": "<PERSON><PERSON><PERSON> <NAME_SIGN> ärapööret <BRANCH_SIGN> ja <TOWARD_SIGN> su<PERSON><PERSON>."}, "relative_directions": ["vasakul", "paremal"], "example_phrases": {"0": ["Take the exit on the left.", "Take the exit on the right."], "1": ["Take exit 67 B-A on the right."], "2": ["Take the US 322 West exit on the right."], "3": ["Take exit 67 B-A on the right onto US 322 West."], "4": ["Take the exit on the right toward Lewistown."], "5": ["Take exit 67 B-A on the right toward Lewistown."], "6": ["Take the US 322 West exit on the right toward Lewistown."], "7": ["Take exit 67 B-A on the right onto US 322 West toward Lewistown/State College."], "8": ["Take the White Marsh Boulevard exit on the left."], "10": ["Take the White Marsh Boulevard exit on the left onto MD 43 East."], "12": ["Take the White Marsh Boulevard exit on the left toward White Marsh."], "14": ["Take the White Marsh Boulevard exit on the left onto MD 43 East toward White Marsh."], "15": ["Take the exit."], "16": ["Take exit 67 B-A."], "17": ["Take the US 322 West exit."], "18": ["Take exit 67 B-A onto US 322 West."], "19": ["Take the exit toward Lewistown."], "20": ["Take exit 67 B-A toward Lewistown."], "21": ["Take the US 322 West exit toward Lewistown."], "22": ["Take exit 67 B-A onto US 322 West toward Lewistown/State College."], "23": ["Take the White Marsh Boulevard exit."], "25": ["Take the White Marsh Boulevard exit onto MD 43 East."], "27": ["Take the White Marsh Boulevard exit toward White Marsh."], "29": ["Take the White Marsh Boulevard exit onto MD 43 East toward White Marsh."]}}, "exit_roundabout": {"phrases": {"0": "Sõida ringteelt ära.", "1": "S<PERSON>ida ringteelt ära <STREET_NAMES> teele.", "2": "Sõida ringteelt ära <BEGIN_STREET_NAMES> teele. Jätka mööda <STREET_NAMES> teed.", "3": "Sõida ringteelt ära <TOWARD_SIGN> suunas."}, "empty_street_name_labels": ["läbipää<PERSON>", "rattatee", "rada mägiratastele", "ülekäigurajal", "the stairs", "the bridge", "the tunnel"], "example_phrases": {"0": ["Exit the roundabout."], "1": ["Exit the roundabout onto Philadelphia Road/MD 7."], "2": ["Exit the roundabout onto Catoctin Mountain Highway/US 15. Continue on US 15."], "3": ["Exit the roundabout toward Baltimore."]}}, "exit_roundabout_verbal": {"phrases": {"0": "Sõida ringteelt ära.", "1": "S<PERSON>ida ringteelt ära <STREET_NAMES> teele.", "2": "S<PERSON>ida ringteelt ära <BEGIN_STREET_NAMES> teele.", "3": "Sõida ringteelt ära <TOWARD_SIGN> suunas."}, "empty_street_name_labels": ["läbipää<PERSON>", "rattatee", "rada mägiratastele", "ülekäigurajal", "the stairs", "the bridge", "the tunnel"], "example_phrases": {"0": ["Exit the roundabout."], "1": ["Exit the roundabout onto Philadelphia Road, Maryland 7."], "2": ["Exit the roundabout onto Catoctin Mountain Highway, U.S. 15."], "3": ["Exit the roundabout toward Baltimore."]}}, "exit_verbal": {"phrases": {"0": "<PERSON><PERSON><PERSON> <RELATIVE_DIRECTION>.", "1": "<PERSON><PERSON><PERSON> <NUMBER_SIGN> ärapööret <RELATIVE_DIRECTION>.", "2": "<PERSON><PERSON><PERSON> <BRANCH_SIGN> ärapööret <RELATIVE_DIRECTION>.", "3": "<PERSON><PERSON><PERSON> <NUMBER_SIGN> ärapööret <RELATIVE_DIRECTION> <BRANCH_SIGN> su<PERSON><PERSON>.", "4": "<PERSON><PERSON><PERSON> <RELATIVE_DIRECTION> <TOWARD_SIGN> suunas.", "5": "<PERSON><PERSON><PERSON> <NUMBER_SIGN> ärapööret <RELATIVE_DIRECTION> <TOWARD_SIGN> su<PERSON><PERSON>.", "6": "<PERSON><PERSON><PERSON> <BRANCH_SIGN> ärapööret <RELATIVE_DIRECTION> <TOWARD_SIGN> su<PERSON><PERSON>.", "7": "<PERSON><PERSON><PERSON> <NUMBER_SIGN> ärapööret <RELATIVE_DIRECTION> <BRANCH_SIGN> ja <TOWARD_SIGN> su<PERSON><PERSON>.", "8": "<PERSON><PERSON><PERSON> <NAME_SIGN> ärapööret <RELATIVE_DIRECTION>.", "10": "<PERSON><PERSON><PERSON> <NAME_SIGN> ärapööret <RELATIVE_DIRECTION> <BRANCH_SIGN> su<PERSON><PERSON>.", "12": "<PERSON><PERSON><PERSON> <NAME_SIGN> ärapööret <RELATIVE_DIRECTION> <TOWARD_SIGN> su<PERSON><PERSON>.", "14": "<PERSON><PERSON><PERSON> <NAME_SIGN> ärapööret <RELATIVE_DIRECTION> <BRANCH_SIGN> teele <TOWARD_SIGN> su<PERSON><PERSON>.", "15": "Pööra <PERSON>.", "16": "<PERSON><PERSON><PERSON> <NUMBER_SIGN> ärapööret.", "17": "<PERSON><PERSON><PERSON> <BRANCH_SIGN> ärapööret.", "18": "<PERSON><PERSON><PERSON> <NUMBER_SIGN> ärapööret <BRANCH_SIGN> suunas.", "19": "Pööra ära <TOWARD_SIGN> suunas.", "20": "<PERSON><PERSON><PERSON> <NUMBER_SIGN> ärapööret <TOWARD_SIGN> suunas.", "21": "<PERSON><PERSON><PERSON> <BRANCH_SIGN> ärapööret <TOWARD_SIGN> suunas.", "22": "<PERSON><PERSON><PERSON> <NUMBER_SIGN> ärapööret <BRANCH_SIGN> ja <TOWARD_SIGN> su<PERSON><PERSON>.", "23": "<PERSON><PERSON><PERSON> <NAME_SIGN> ärapööret.", "25": "<PERSON><PERSON><PERSON> <NAME_SIGN> ärapööret <BRANCH_SIGN> suunas.", "27": "<PERSON><PERSON><PERSON> <NAME_SIGN> ärapööret <TOWARD_SIGN> suunas.", "29": "<PERSON><PERSON><PERSON> <NAME_SIGN> ärapööret <BRANCH_SIGN> ja <TOWARD_SIGN> su<PERSON><PERSON>."}, "relative_directions": ["vasakul", "paremal"], "example_phrases": {"0": ["Take the exit on the left.", "Take the exit on the right."], "1": ["Take exit 67 B-A on the right."], "2": ["Take the U.S. 3 22 West exit on the right."], "3": ["Take exit 67 B-A on the right onto U.S. 3 22 West."], "4": ["Take the exit on the right toward Lewistown."], "5": ["Take exit 67 B-A on the right toward Lewistown."], "6": ["Take the U.S. 3 22 West exit on the right toward Lewistown."], "7": ["Take exit 67 B-A on the right onto U.S. 3 22 West toward Lewistown, State College."], "8": ["Take the White Marsh Boulevard exit on the left."], "10": ["Take the White Marsh Boulevard exit on the left onto Maryland 43 East."], "12": ["Take the White Marsh Boulevard exit on the left toward White Marsh."], "14": ["Take the White Marsh Boulevard exit on the left onto Maryland 43 East toward White Marsh."], "15": ["Take the exit."], "16": ["Take exit 67 B-A."], "17": ["Take the US 322 West exit."], "18": ["Take exit 67 B-A onto US 322 West."], "19": ["Take the exit toward Lewistown."], "20": ["Take exit 67 B-A toward Lewistown."], "21": ["Take the US 322 West exit toward Lewistown."], "22": ["Take exit 67 B-A onto US 322 West toward Lewistown/State College."], "23": ["Take the White Marsh Boulevard exit."], "25": ["Take the White Marsh Boulevard exit onto MD 43 East."], "27": ["Take the White Marsh Boulevard exit toward White Marsh."], "29": ["Take the White Marsh Boulevard exit onto MD 43 East toward White Marsh."]}}, "exit_visual": {"phrases": {"0": "Ärapööre <EXIT_NUMBERS>"}, "example_phrases": {"0": ["Exit 1A"]}}, "keep": {"phrases": {"0": "Tee hargnemisel hoia <RELATIVE_DIRECTION> suunas.", "1": "Hoia <RELATIVE_DIRECTION> ja kasuta <PERSON> <NUMBER_SIGN>.", "2": "Hoia <RELATIVE_DIRECTION> ja liigu <STREET_NAMES> tänavale.", "3": "Hoia <RELATIVE_DIRECTION> kasutades ärapööret <NUMBER_SIGN> <STREET_NAMES> tänavale.", "4": "Hoia <RELATIVE_DIRECTION> <TOWARD_SIGN> suunas.", "5": "Hoia <RELATIVE_DIRECTION> ja kasuta <NUMBER_SIGN> ärapööret <TOWARD_SIGN> suunas.", "6": "Hoia <RELATIVE_DIRECTION> ja liigu <STREET_NAMES> tänavale <TOWARD_SIGN> poole.", "7": "Hoia <RELATIVE_DIRECTION> ja kasuta <NUMBER_SIGN> ärapööret <STREET_NAMES> tänavale <TOWARD_SIGN> poole."}, "empty_street_name_labels": ["läbipää<PERSON>", "rattatee", "rada mägiratastele", "ülekäigurajal", "the stairs", "the bridge", "the tunnel"], "relative_directions": ["vasa<PERSON><PERSON>", "otse", "paremale"], "example_phrases": {"0": ["Keep left at the fork.", "Keep straight at the fork.", "Keep right at the fork."], "1": ["Keep right to take exit 62."], "2": ["Keep right to take I 895 South."], "3": ["Keep right to take exit 62 onto I 895 South."], "4": ["Keep right toward Annapolis."], "5": ["Keep right to take exit 62 toward Annapolis."], "6": ["Keep right to take I 895 South toward Annapolis."], "7": ["Keep right to take exit 62 onto I 895 South toward Annapolis."]}}, "keep_to_stay_on": {"phrases": {"0": "Hoia <RELATIVE_DIRECTION> ja j<PERSON><PERSON> m<PERSON> <STREET_NAMES> tänavat.", "1": "Hoia <RELATIVE_DIRECTION> ja kasuta <NUMBER_SIGN> ärapööret ning püsi <STREET_NAMES> tänaval.", "2": "Hoia <RELATIVE_DIRECTION> ja j<PERSON><PERSON> <STREET_NAMES> tänaval <TOWARD_SIGN> suunas.", "3": "Hoia <RELATIVE_DIRECTION> ja kasuta <NUMBER_SIGN> ärapööret et jätkata <STREET_NAMES> tänaval <TOWARD_SIGN> suunas."}, "empty_street_name_labels": ["läbipää<PERSON>", "rattatee", "rada mägiratastele", "ülekäigurajal", "the stairs", "the bridge", "the tunnel"], "relative_directions": ["vasa<PERSON><PERSON>", "otse", "paremale"], "example_phrases": {"0": ["Keep left to stay on I 95 South.", "Keep straight to stay on I 95 South.", "Keep right to stay on I 95 South."], "1": ["Keep left to take exit 62 to stay on I 95 South."], "2": ["Keep left to stay on I 95 South toward Baltimore."], "3": ["Keep left to take exit 62 to stay on I 95 South toward Baltimore."]}}, "keep_to_stay_on_verbal": {"phrases": {"0": "Hoia <RELATIVE_DIRECTION> ja j<PERSON><PERSON> m<PERSON> <STREET_NAMES> tänavat.", "1": "Hoia <RELATIVE_DIRECTION> ja kasuta <NUMBER_SIGN> ärapööret ning püsi <STREET_NAMES> tänaval.", "2": "Hoia <RELATIVE_DIRECTION> ja j<PERSON><PERSON> <STREET_NAMES> tänaval <TOWARD_SIGN> suunas.", "3": "Hoia <RELATIVE_DIRECTION> ja kasuta <NUMBER_SIGN> ärapööret et jätkata <STREET_NAMES> tänaval <TOWARD_SIGN> suunas."}, "empty_street_name_labels": ["läbipää<PERSON>", "rattatee", "rada mägiratastele", "ülekäigurajal", "the stairs", "the bridge", "the tunnel"], "relative_directions": ["vasa<PERSON><PERSON>", "otse", "paremale"], "example_phrases": {"0": ["Keep left to stay on Interstate 95 South.", "Keep straight to stay on Interstate 95 South.", "Keep right to stay on Interstate 95 South."], "1": ["Keep left to take exit 62 to stay on Interstate 95 South."], "2": ["Keep left to stay on I 95 South toward Baltimore."], "3": ["Keep left to take exit 62 to stay on Interstate 95 South toward Baltimore."]}}, "keep_verbal": {"phrases": {"0": "Tee hargnemisel hoia <RELATIVE_DIRECTION> suunas.", "1": "Hoia <RELATIVE_DIRECTION> ja kasuta <PERSON> <NUMBER_SIGN>.", "2": "Hoia <RELATIVE_DIRECTION> ja liigu <STREET_NAMES> tänavale.", "3": "Hoia <RELATIVE_DIRECTION> kasutades ärapööret <NUMBER_SIGN> <STREET_NAMES> tänavale.", "4": "Hoia <RELATIVE_DIRECTION> <TOWARD_SIGN> suunas.", "5": "Hoia <RELATIVE_DIRECTION> ja kasuta <NUMBER_SIGN> ärapööret <TOWARD_SIGN> suunas.", "6": "Hoia <RELATIVE_DIRECTION> ja liigu <STREET_NAMES> tänavale <TOWARD_SIGN> poole.", "7": "Hoia <RELATIVE_DIRECTION> ja kasuta <NUMBER_SIGN> ärapööret <STREET_NAMES> tänavale <TOWARD_SIGN> poole."}, "empty_street_name_labels": ["läbipää<PERSON>", "rattatee", "rada mägiratastele", "ülekäigurajal", "the stairs", "the bridge", "the tunnel"], "relative_directions": ["vasakul", "otse", "paremal"], "example_phrases": {"0": ["Keep left at the fork.", "Keep straight at the fork.", "Keep right at the fork."], "1": ["Keep right to take exit 62."], "2": ["Keep right to take Interstate 8 95 South."], "3": ["Keep right to take exit 62 onto Interstate 8 95 South."], "4": ["Keep right toward Annapolis."], "5": ["Keep right to take exit 62 toward Annapolis."], "6": ["Keep right to take Interstate 8 95 South toward Annapolis."], "7": ["Keep right to take exit 62 onto Interstate 8 95 South toward Annapolis."]}}, "merge": {"phrases": {"0": "<PERSON><PERSON><PERSON>.", "1": "<PERSON><PERSON><PERSON> <RELATIVE_DIRECTION>.", "2": "<PERSON><PERSON><PERSON> <STREET_NAMES> teel.", "3": "<PERSON><PERSON><PERSON> <RELATIVE_DIRECTION> <STREET_NAMES> teel.", "4": "<PERSON><PERSON><PERSON> <TOWARD_SIGN> suunas.", "5": "<PERSON><PERSON><PERSON> <RELATIVE_DIRECTION> <TOWARD_SIGN> suunas."}, "relative_directions": ["vasakul", "paremal"], "empty_street_name_labels": ["läbipää<PERSON>", "rattatee", "rada mägiratastele", "ülekäigurajal", "the stairs", "the bridge", "the tunnel"], "example_phrases": {"0": ["Merge."], "1": ["<PERSON><PERSON> left."], "2": ["Merge onto I 76 West/Pennsylvania Turnpike."], "3": ["Merge right onto I 83 South."], "4": ["Merge toward Baltimore."], "5": ["Merge right toward Baltimore."]}}, "merge_verbal": {"phrases": {"0": "<PERSON><PERSON><PERSON>.", "1": "<PERSON><PERSON><PERSON> <RELATIVE_DIRECTION>.", "2": "<PERSON><PERSON><PERSON> <STREET_NAMES> teel.", "3": "<PERSON><PERSON><PERSON> <RELATIVE_DIRECTION> <STREET_NAMES> teel.", "4": "<PERSON><PERSON><PERSON> <TOWARD_SIGN> suunas.", "5": "<PERSON><PERSON><PERSON> <RELATIVE_DIRECTION> <TOWARD_SIGN> suunas."}, "relative_directions": ["vasakul", "paremal"], "empty_street_name_labels": ["läbipää<PERSON>", "rattatee", "rada mägiratastele", "ülekäigurajal", "the stairs", "the bridge", "the tunnel"], "example_phrases": {"0": ["Merge."], "1": ["<PERSON><PERSON> left."], "2": ["Merge onto I 76 West/Pennsylvania Turnpike."], "3": ["Merge right onto I 83 South."], "4": ["Merge toward Baltimore."], "5": ["Merge right toward Baltimore."]}}, "post_transition_transit_verbal": {"phrases": {"0": "Sõida <TRANSIT_STOP_COUNT> <TRANSIT_STOP_COUNT_LABEL>."}, "transit_stop_count_labels": {"one": "peatus", "other": "peatused"}, "example_phrases": {"0": ["Travel 1 stop.", "Travel 3 stops."]}}, "post_transition_verbal": {"phrases": {"0": "<PERSON><PERSON><PERSON> veel <LENGTH>.", "1": "Jät<PERSON> <STREET_NAMES> tänaval veel <LENGTH>."}, "empty_street_name_labels": ["läbipää<PERSON>", "rattatee", "rada mägiratastele", "ülekäigurajal", "the stairs", "the bridge", "the tunnel"], "metric_lengths": ["<KILOMETERS> kilomeetrit", "1 kilomeeter", "<METERS> meetrit", "vähem kui 10 meetrit"], "us_customary_lengths": ["<MILES> mi<PERSON>", "1 miil", "pool miili", "veerand mi<PERSON>", "<FEET> jalga", "vähem kui 10 jalga"], "example_phrases": {"0": ["Continue for 300 feet.", "Continue for 9 miles."], "1": ["Continue on Pennsylvania 7 43 for 6.2 miles.", "Continue on Main Street, Vermont 30 for 1 tenth of a mile."]}}, "ramp": {"phrases": {"0": "Sõida <RELATIVE_DIRECTION> kald<PERSON>le.", "1": "Sõida <RELATIVE_DIRECTION> kald<PERSON>le <BRANCH_SIGN> suunas.", "2": "Sõida <RELATIVE_DIRECTION> as<PERSON><PERSON> kaldteele <TOWARD_SIGN> su<PERSON>s.", "3": "Sõida <RELATIVE_DIRECTION> asuvale <BRANCH_SIGN> kaldteele <TOWARD_SIGN> su<PERSON><PERSON>.", "4": "Sõida <RELATIVE_DIRECTION> asuvale <NAME_SIGN> kaldteele.", "5": "Pööra <RELATIVE_DIRECTION> ja s<PERSON><PERSON> kald<PERSON>le.", "6": "Pööra <RELATIVE_DIRECTION> ja s<PERSON><PERSON> <BRANCH_SIGN> kald<PERSON>le.", "7": "Pööra <RELATIVE_DIRECTION> ja s<PERSON><PERSON> kald<PERSON> <TOWARD_SIGN> su<PERSON><PERSON>.", "8": "Pööra <RELATIVE_DIRECTION> ja s<PERSON><PERSON> <BRANCH_SIGN> kaldteele <TOWARD_SIGN> su<PERSON><PERSON>.", "9": "Pööra <RELATIVE_DIRECTION> ja s<PERSON><PERSON> <NAME_SIGN> kaldteele.", "10": "<PERSON><PERSON><PERSON> ka<PERSON>.", "11": "Sõida <BRANCH_SIGN> kald<PERSON>le", "12": "<PERSON><PERSON><PERSON> kald<PERSON> <TOWARD_SIGN> suunas.", "13": "Sõida <BRANCH_SIGN> kald<PERSON><PERSON> <TOWARD_SIGN> su<PERSON><PERSON>.", "14": "Sõida <NAME_SIGN> kald<PERSON>le."}, "relative_directions": ["vasakul", "paremal"], "example_phrases": {"0": ["Take the ramp on the left.", "Take the ramp on the right."], "1": ["Take the I 95 ramp on the right."], "2": ["Take the ramp on the left toward JFK."], "3": ["Take the South Conduit Avenue ramp on the left toward JFK."], "4": ["Take the Gettysburg Pike ramp on the right."], "5": ["Turn left to take the ramp.", "Turn right to take the ramp."], "6": ["Turn left to take the PA 283 West ramp."], "7": ["Turn left to take the ramp toward Harrisburg/Harrisburg International Airport."], "8": ["Turn left to take the PA 283 West ramp toward Harrisburg/Harrisburg International Airport."], "9": ["Turn right to take the Gettysburg Pike ramp."], "10": ["Take the ramp."], "11": ["Take the I 95 ramp."], "12": ["Take the ramp toward JFK."], "13": ["Take the Soutch Conduit Avenue ramp toward JFK."], "14": ["Take the Gettysburg ramp."]}}, "ramp_straight": {"phrases": {"0": "Jätka otse ning sõida kald<PERSON>le.", "1": "Jätka otse ning sõida <BRANCH_SIGN> kald<PERSON>le.", "2": "Jätka otse ning s<PERSON>ida kaldteele <TOWARD_SIGN> suunas.", "3": "<PERSON>ät<PERSON> otse ning sõida <BRANCH_SIGN> kald<PERSON><PERSON> <TOWARD_SIGN> su<PERSON>s.", "4": "Jätka otse ning sõida <NAME_SIGN> kald<PERSON>le."}, "example_phrases": {"0": ["Stay straight to take the ramp."], "1": ["Stay straight to take the US 322 East ramp."], "2": ["Stay straight to take the ramp toward Hershey."], "3": ["Stay straight to take the US 322 East/US 422 East/US 522 East/US 622 East ramp toward Hershey/Palmdale/Palmyra/Campbelltown."], "4": ["Stay straight to take the Gettysburg Pike ramp."]}}, "ramp_straight_verbal": {"phrases": {"0": "Jätka otse ning sõida kald<PERSON>le.", "1": "Jätka otse ning sõida <BRANCH_SIGN> kald<PERSON>le.", "2": "Jätka otse ning s<PERSON>ida kaldteele <TOWARD_SIGN> suunas.", "3": "<PERSON>ät<PERSON> otse ning sõida <BRANCH_SIGN> kald<PERSON><PERSON> <TOWARD_SIGN> su<PERSON>s.", "4": "Jätka otse ning sõida <NAME_SIGN> kald<PERSON>le."}, "example_phrases": {"0": ["Stay straight to take the ramp."], "1": ["Stay straight to take the U.S. 3 22 East ramp."], "2": ["Stay straight to take the ramp toward Hershey."], "3": ["Stay straight to take the U.S. 3 22 East, U.S. 4 22 East ramp toward Hershey, Palmdale."], "4": ["Stay straight to take the Gettysburg Pike ramp."]}}, "ramp_verbal": {"phrases": {"0": "Sõida <RELATIVE_DIRECTION> kald<PERSON>le.", "1": "Sõida <RELATIVE_DIRECTION> kald<PERSON>le <BRANCH_SIGN> suunas.", "2": "Sõida <RELATIVE_DIRECTION> as<PERSON><PERSON> kaldteele <TOWARD_SIGN> su<PERSON>s.", "3": "Sõida <RELATIVE_DIRECTION> asuvale <BRANCH_SIGN> kaldteele <TOWARD_SIGN> su<PERSON><PERSON>.", "4": "Sõida <RELATIVE_DIRECTION> asuvale <NAME_SIGN> kaldteele.", "5": "Pööra <RELATIVE_DIRECTION> ja s<PERSON><PERSON> kald<PERSON>le.", "6": "Pööra <RELATIVE_DIRECTION> ja s<PERSON><PERSON> <BRANCH_SIGN> kald<PERSON>le.", "7": "Pööra <RELATIVE_DIRECTION> ja s<PERSON><PERSON> kald<PERSON> <TOWARD_SIGN> su<PERSON><PERSON>.", "8": "Pööra <RELATIVE_DIRECTION> ja s<PERSON><PERSON> <BRANCH_SIGN> kaldteele <TOWARD_SIGN> su<PERSON><PERSON>.", "9": "Pööra <RELATIVE_DIRECTION> ja s<PERSON><PERSON> <NAME_SIGN> kaldteele.", "10": "<PERSON><PERSON><PERSON> ka<PERSON>.", "11": "Sõida <BRANCH_SIGN> kald<PERSON>le", "12": "<PERSON><PERSON><PERSON> kald<PERSON> <TOWARD_SIGN> suunas.", "13": "Sõida <BRANCH_SIGN> kald<PERSON><PERSON> <TOWARD_SIGN> su<PERSON><PERSON>.", "14": "Sõida <NAME_SIGN> kald<PERSON>le."}, "relative_directions": ["vasakul", "paremal"], "example_phrases": {"0": ["Take the ramp on the left.", "Take the ramp on the right."], "1": ["Take the Interstate 95 ramp on the right."], "2": ["Take the ramp on the left toward JFK."], "3": ["Take the South Conduit Avenue ramp on the left toward JFK."], "4": ["Take the Gettysburg Pike ramp on the right."], "5": ["Turn left to take the ramp.", "Turn right to take the ramp."], "6": ["Turn left to take the Pennsylvania 2 83 West ramp."], "7": ["Turn left to take the ramp toward Harrisburg/Harrisburg International Airport."], "8": ["Turn left to take the Pennsylvania 2 83 West ramp toward Harrisburg, Harrisburg International Airport."], "9": ["Turn right to take the Gettysburg Pike ramp."], "10": ["Take the ramp."], "11": ["Take the Interstate 95 ramp."], "12": ["Take the ramp toward JFK."], "13": ["Take the South Conduit Avenue ramp toward JFK."], "14": ["Take the Gettysburg Pike ramp."]}}, "sharp": {"phrases": {"0": "Pöö<PERSON> j<PERSON>ult <RELATIVE_DIRECTION>.", "1": "<PERSON><PERSON><PERSON><PERSON> j<PERSON>ult <RELATIVE_DIRECTION> <STREET_NAMES> tänavale.", "2": "<PERSON><PERSON><PERSON><PERSON> j<PERSON> <RELATIVE_DIRECTION> <BEGIN_STREET_NAMES> tänavale. Jätka mööda <STREET_NAMES> teed.", "3": "<PERSON><PERSON><PERSON><PERSON> j<PERSON> <RELATIVE_DIRECTION> ja j<PERSON><PERSON> <STREET_NAMES> teel.", "4": "<JUNCTION_NAME> <PERSON><PERSON><PERSON> p<PERSON> j<PERSON> <RELATIVE_DIRECTION>.", "5": "<PERSON><PERSON><PERSON><PERSON> j<PERSON>ult <RELATIVE_DIRECTION> <TOWARD_SIGN> suunas."}, "empty_street_name_labels": ["läbipää<PERSON>", "rattatee", "rada mägiratastele", "ülekäigurajal", "the stairs", "the bridge", "the tunnel"], "relative_directions": ["vasakul", "paremal"], "example_phrases": {"0": ["Make a sharp left."], "1": ["Make a sharp right onto Flatbush Avenue."], "2": ["Make a sharp left onto North Bond Street/US 1 Business/MD 924. Continue on MD 924."], "3": ["Make a sharp right to stay on Sunstone Drive."], "4": ["Make a sharp right at Mannenbashi East."], "5": ["Make a sharp left toward Baltimore."]}}, "sharp_verbal": {"phrases": {"0": "Pöö<PERSON> j<PERSON>ult <RELATIVE_DIRECTION>.", "1": "<PERSON><PERSON><PERSON><PERSON> j<PERSON>ult <RELATIVE_DIRECTION> <STREET_NAMES> tänavale.", "2": "<PERSON><PERSON><PERSON><PERSON> j<PERSON>ult <RELATIVE_DIRECTION> <BEGIN_STREET_NAMES> teele.", "3": "<PERSON><PERSON><PERSON><PERSON> j<PERSON> <RELATIVE_DIRECTION> ja j<PERSON><PERSON> <STREET_NAMES> teel.", "4": "<JUNCTION_NAME> <PERSON><PERSON><PERSON> p<PERSON> j<PERSON> <RELATIVE_DIRECTION>.", "5": "<PERSON><PERSON><PERSON><PERSON> j<PERSON>ult <RELATIVE_DIRECTION> <TOWARD_SIGN> suunas."}, "empty_street_name_labels": ["läbipää<PERSON>", "rattatee", "rada mägiratastele", "ülekäigurajal", "the stairs", "the bridge", "the tunnel"], "relative_directions": ["vasakul", "paremal"], "example_phrases": {"0": ["Make a sharp left."], "1": ["Make a sharp right onto Flatbush Avenue."], "2": ["Make a sharp left onto North Bond Street, U.S. 1 Business."], "3": ["Make a sharp right to stay on Sunstone Drive."], "4": ["Make a sharp right at Mannenbashi East."], "5": ["Make a sharp left toward Baltimore."]}}, "start": {"phrases": {"0": "Suundu <CARDINAL_DIRECTION>.", "1": "Suundu piki <STREET_NAMES> <CARDINAL_DIRECTION>.", "2": "<PERSON>undu piki <BEGIN_STREET_NAMES> <CARDINAL_DIRECTION>. <PERSON><PERSON><PERSON> mööda <STREET_NAMES>.", "4": "Sõida <CARDINAL_DIRECTION>.", "5": "Sõida piki <STREET_NAMES> <CARDINAL_DIRECTION>.", "6": "Sõida piki <BEGIN_STREET_NAMES> <CARDINAL_DIRECTION>. <PERSON><PERSON><PERSON> mööda <STREET_NAMES>.", "8": "<PERSON><PERSON><PERSON><PERSON> <CARDINAL_DIRECTION>.", "9": "<PERSON><PERSON><PERSON><PERSON> piki <STREET_NAMES> <CARDINAL_DIRECTION>.", "10": "<PERSON><PERSON><PERSON><PERSON> piki <BEGIN_STREET_NAMES> <CARDINAL_DIRECTION>. <PERSON><PERSON><PERSON> mööda <STREET_NAMES>.", "16": "<PERSON><PERSON><PERSON> rattaga <CARDINAL_DIRECTION>.", "17": "<PERSON><PERSON><PERSON> rattaga m<PERSON>öda <STREET_NAMES> <CARDINAL_DIRECTION>.", "18": "<PERSON><PERSON><PERSON> rattaga piki <BEGIN_STREET_NAMES> <CARDINAL_DIRECTION>. <PERSON><PERSON><PERSON> m<PERSON>ö<PERSON> <STREET_NAMES>."}, "cardinal_directions": ["<PERSON><PERSON><PERSON><PERSON>", "kirde su<PERSON>s", "ida suunas", "kagu suunas", "lõuna suunas", "edela su<PERSON>s", "<PERSON><PERSON><PERSON><PERSON>", "loode suunas"], "empty_street_name_labels": ["läbipää<PERSON>", "rattatee", "rada mägiratastele", "ülekäigurajal", "the stairs", "the bridge", "the tunnel"], "example_phrases": {"0": ["Head east.", "Head north."], "1": ["Head southwest on 5th Avenue.", "Head west on the walkway", "Head east on the cycleway", "Head north on the mountain bike trail"], "2": ["Head south on North Prince Street/US 222/PA 272. Continue on US 222/PA 272."], "4": ["Drive east.", "Drive north."], "5": ["Drive southwest on 5th Avenue."], "6": ["Drive south on North Prince Street/US 222/PA 272. Continue on US 222/PA 272."], "8": ["Walk east.", "Walk north."], "9": ["Walk southwest on 5th Avenue.", "Walk west on the walkway"], "10": ["Walk south on North Prince Street/US 222/PA 272. Continue on US 222/PA 272."], "16": ["Bike east.", "Bike north."], "17": ["Bike southwest on 5th Avenue.", "Bike east on the cycleway", "Bike north on the mountain bike trail"], "18": ["Bike south on North Prince Street/US 222/PA 272. Continue on US 222/PA 272."]}}, "start_verbal": {"phrases": {"0": "Suundu <CARDINAL_DIRECTION>.", "1": "Suundu <LENGTH> <CARDINAL_DIRECTION>.", "2": "Suundu piki <STREET_NAMES> <CARDINAL_DIRECTION>.", "3": "Suundu <LENGTH> piki <STREET_NAMES> <CARDINAL_DIRECTION>.", "4": "Suundu piki <BEGIN_STREET_NAMES> <CARDINAL_DIRECTION>.", "5": "Sõida <CARDINAL_DIRECTION>.", "6": "Sõida <LENGTH> <CARDINAL_DIRECTION>.", "7": "Sõida piki <STREET_NAMES> <CARDINAL_DIRECTION>.", "8": "Sõida <LENGTH> piki <STREET_NAMES> <CARDINAL_DIRECTION>.", "9": "Sõida piki <BEGIN_STREET_NAMES> <CARDINAL_DIRECTION>.", "10": "<PERSON><PERSON><PERSON><PERSON> <CARDINAL_DIRECTION>.", "11": "<PERSON><PERSON><PERSON><PERSON> <LENGTH> <CARDINAL_DIRECTION>.", "12": "<PERSON><PERSON><PERSON><PERSON> piki <STREET_NAMES> <CARDINAL_DIRECTION>.", "13": "<PERSON><PERSON><PERSON><PERSON> <LENGTH> piki <STREET_NAMES> <CARDINAL_DIRECTION>.", "14": "<PERSON><PERSON><PERSON><PERSON> piki <BEGIN_STREET_NAMES> <CARDINAL_DIRECTION>.", "15": "<PERSON><PERSON><PERSON> rattaga <CARDINAL_DIRECTION>.", "16": "<PERSON><PERSON><PERSON> rattaga <LENGTH> <CARDINAL_DIRECTION>.", "17": "<PERSON><PERSON><PERSON> rattaga m<PERSON>öda <STREET_NAMES> <CARDINAL_DIRECTION>.", "18": "<PERSON><PERSON><PERSON> rattaga <LENGTH> m<PERSON><PERSON><PERSON> <STREET_NAMES> <CARDINAL_DIRECTION>.", "19": "<PERSON><PERSON><PERSON> rattaga mööda <BEGIN_STREET_NAMES> <CARDINAL_DIRECTION>."}, "cardinal_directions": ["<PERSON><PERSON><PERSON><PERSON>", "kirde su<PERSON>s", "ida suunas", "kagu suunas", "lõuna suunas", "edela su<PERSON>s", "<PERSON><PERSON><PERSON><PERSON>", "loode suunas"], "empty_street_name_labels": ["läbipää<PERSON>", "rattatee", "rada mägiratastele", "ülekäigurajal", "the stairs", "the bridge", "the tunnel"], "metric_lengths": ["<KILOMETERS> kilomeetrit", "1 kilomeeter", "<METERS> meetrit", "vähem kui 10 meetrit"], "us_customary_lengths": ["<MILES> mi<PERSON>", "1 miil", "pool miili", "veerand mi<PERSON>", "<FEET> jalga", "vähem kui 10 jalga"], "example_phrases": {"0": ["Head east.", "Head north."], "1": ["Head east for a half mile.", "Head north for 1 kilometer."], "2": ["Head southwest on 5th Avenue."], "3": ["Head southwest on 5th Avenue for 1 tenth of a mile."], "4": ["Head south on North Prince Street, U.S. 2 22."], "5": ["Drive east.", "Drive north."], "6": ["Drive east for a half mile.", "Drive north for 1 kilometer."], "7": ["Drive southwest on 5th Avenue."], "8": ["Drive southwest on 5th Avenue for 1 tenth of a mile."], "9": ["Drive south on North Prince Street, U.S. 2 22."], "10": ["Walk east.", "Walk north."], "11": ["Walk east for a half mile.", "Walk north for 1 kilometer."], "12": ["Walk southwest on 5th Avenue."], "13": ["Walk southwest on 5th Avenue for 1 tenth of a mile."], "14": ["Walk south on North Prince Street, U.S. 2 22."], "15": ["Bike east.", "Bike north."], "16": ["Bike east for a half mile.", "Bike north for 1 kilometer."], "17": ["Bike southwest on 5th Avenue."], "18": ["Bike southwest on 5th Avenue for 1 tenth of a mile."], "19": ["Bike south on North Prince Street, U.S. 2 22."]}}, "transit": {"phrases": {"0": "Sõida <TRANSIT_NAME> liiniga (<TRANSIT_STOP_COUNT> <TRANSIT_STOP_COUNT_LABEL>).", "1": "Sõida <TRANSIT_NAME> liiniga <TRANSIT_HEADSIGN> suunas (<TRANSIT_STOP_COUNT> <TRANSIT_STOP_COUNT_LABEL>)."}, "empty_transit_name_labels": ["tramm", "metroo", "rong", "buss", "praam", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gondel", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], "transit_stop_count_labels": {"one": "peatus", "other": "peatused"}, "example_phrases": {"0": ["Take the New Haven. (1 stop)", "Take the metro. (2 stops)", "Take the bus. (12 stops)"], "1": ["Take the F toward JAMAICA - 179 ST. (10 stops)", "Take the ferry toward Staten Island. (1 stop)"]}}, "transit_connection_destination": {"phrases": {"0": "<PERSON><PERSON><PERSON><PERSON> j<PERSON>.", "1": "Välju <TRANSIT_STOP> jaamast.", "2": "Välju <TRANSIT_STOP> <STATION_LABEL> jaamast."}, "station_label": "<PERSON><PERSON><PERSON>", "example_phrases": {"0": ["Exit the station."], "1": ["Exit the Embarcadero Station."], "2": ["Exit the 8 St - NYU Station."]}}, "transit_connection_destination_verbal": {"phrases": {"0": "<PERSON><PERSON><PERSON><PERSON> j<PERSON>.", "1": "Välju <TRANSIT_STOP> jaamast.", "2": "Välju <TRANSIT_STOP> <STATION_LABEL> jaamast."}, "station_label": "<PERSON><PERSON><PERSON>", "example_phrases": {"0": ["Exit the station."], "1": ["Exit the Embarcadero Station."], "2": ["Exit the 8 St - NYU Station."]}}, "transit_connection_start": {"phrases": {"0": "<PERSON><PERSON><PERSON> jaa<PERSON>.", "1": "Sisene <TRANSIT_STOP> jaama.", "2": "Sisene <TRANSIT_STOP> <STATION_LABEL> jaama."}, "station_label": "<PERSON><PERSON><PERSON>", "example_phrases": {"0": ["Enter the station."], "1": ["Enter the Embarcadero Station."], "2": ["Enter the 8 St - NYU Station."]}}, "transit_connection_start_verbal": {"phrases": {"0": "<PERSON><PERSON><PERSON> jaa<PERSON>.", "1": "Sisene <TRANSIT_STOP> jaama.", "2": "Sisene <TRANSIT_STOP> <STATION_LABEL> jaama."}, "station_label": "<PERSON><PERSON><PERSON>", "example_phrases": {"0": ["Enter the station."], "1": ["Enter the Embarcadero Station."], "2": ["Enter the 8 St - NYU Station."]}}, "transit_connection_transfer": {"phrases": {"0": "<PERSON><PERSON> jaa<PERSON>.", "1": "Istu <TRANSIT_STOP> jaamas ümber.", "2": "Istu <TRANSIT_STOP> <STATION_LABEL> jaamas ümber."}, "station_label": "<PERSON><PERSON><PERSON>", "example_phrases": {"0": ["Transfer at the station."], "1": ["Transfer at the Embarcadero Station."], "2": ["Transfer at the 8 St - NYU Station."]}}, "transit_connection_transfer_verbal": {"phrases": {"0": "<PERSON><PERSON> jaa<PERSON>.", "1": "Istu <TRANSIT_STOP> jaamas ümber.", "2": "Istu <TRANSIT_STOP> <STATION_LABEL> jaamas ümber."}, "station_label": "<PERSON><PERSON><PERSON>", "example_phrases": {"0": ["Transfer at the station."], "1": ["Transfer at the Embarcadero Station."], "2": ["Transfer at the 8 St - NYU Station."]}}, "transit_remain_on": {"phrases": {"0": "J<PERSON>ä <TRANSIT_NAME> liinile (<TRANSIT_STOP_COUNT> <TRANSIT_STOP_COUNT_LABEL>).", "1": "<PERSON><PERSON><PERSON> <TRANSIT_HEADSIGN> su<PERSON><PERSON> jä<PERSON> <TRANSIT_NAME> liinile (<TRANSIT_STOP_COUNT> <TRANSIT_STOP_COUNT_LABEL>)."}, "empty_transit_name_labels": ["tramm", "metroo", "rong", "buss", "praam", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gondel", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], "transit_stop_count_labels": {"one": "peatus", "other": "peatused"}, "example_phrases": {"0": ["Remain on the New Haven. (1 stop)", "<PERSON><PERSON><PERSON> on the train. (3 stops)"], "1": ["Remain on the F toward JAMAICA - 179 ST. (10 stops)"]}}, "transit_remain_on_verbal": {"phrases": {"0": "Jää <TRANSIT_NAME> liinile.", "1": "<TRANSIT_HEADSIGN> su<PERSON>s jä<PERSON> <TRANSIT_NAME> liinile."}, "empty_transit_name_labels": ["tramm", "metroo", "rong", "buss", "praam", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gondel", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], "example_phrases": {"0": ["<PERSON><PERSON><PERSON> on the New Haven."], "1": ["Remain on the F toward JAMAICA - 179 ST."]}}, "transit_transfer": {"phrases": {"0": "Istu ümber <TRANSIT_NAME> liinile (<TRANSIT_STOP_COUNT> <TRANSIT_STOP_COUNT_LABEL>). ", "1": "<TRANSIT_HEADSIGN> su<PERSON>s istu ümber <TRANSIT_NAME> liinile (<TRANSIT_STOP_COUNT> <TRANSIT_STOP_COUNT_LABEL>)."}, "empty_transit_name_labels": ["tramm", "metroo", "rong", "buss", "praam", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gondel", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], "transit_stop_count_labels": {"one": "peatus", "other": "peatused"}, "example_phrases": {"0": ["Transfer to take the New Haven. (1 stop)", "Transfer to take the tram. (4 stops)"], "1": ["Transfer to take the F toward JAMAICA - 179 ST. (10 stops)"]}}, "transit_transfer_verbal": {"phrases": {"0": "Istu ümber <TRANSIT_NAME> liinile.", "1": "<TRANSIT_HEADSIGN> su<PERSON>s istu ümber <TRANSIT_NAME> liinile."}, "empty_transit_name_labels": ["tramm", "metroo", "rong", "buss", "praam", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gondel", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], "example_phrases": {"0": ["Transfer to take the New Haven."], "1": ["Transfer to take the F toward JAMAICA - 179 ST."]}}, "transit_verbal": {"phrases": {"0": "<PERSON><PERSON><PERSON> <TRANSIT_NAME> liini.", "1": "<TRANSIT_HEADSIGN> su<PERSON><PERSON> ka<PERSON> <TRANSIT_NAME> liini."}, "empty_transit_name_labels": ["tramm", "metroo", "rong", "buss", "praam", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gondel", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], "example_phrases": {"0": ["Take the New Haven."], "1": ["Take the F toward JAMAICA - 179 ST."]}}, "turn": {"phrases": {"0": "Pööra <RELATIVE_DIRECTION>.", "1": "Pööra <RELATIVE_DIRECTION> <STREET_NAMES> teele.", "2": "Pööra <RELATIVE_DIRECTION> <BEGIN_STREET_NAMES> teele. Liigu edasi mööda <STREET_NAMES> teed.", "3": "Pööra <RELATIVE_DIRECTION> ja j<PERSON><PERSON> möö<PERSON> <STREET_NAMES> teed.", "4": "<JUNCTION_NAME> tee<PERSON><PERSON> <RELATIVE_DIRECTION>.", "5": "Pööra <RELATIVE_DIRECTION> <TOWARD_SIGN> suunas."}, "empty_street_name_labels": ["läbipää<PERSON>", "rattatee", "rada mägiratastele", "ülekäigurajal", "the stairs", "the bridge", "the tunnel"], "relative_directions": ["vasakul", "paremal"], "example_phrases": {"0": ["Turn left."], "1": ["Turn right onto Flatbush Avenue."], "2": ["Turn left onto North Bond Street/US 1 Business/MD 924. Continue on MD 924."], "3": ["Turn right to stay on Sunstone Drive."], "4": ["Turn right at Mannenbashi East."], "5": ["Turn left toward Baltimore."]}}, "turn_verbal": {"phrases": {"0": "Pööra <RELATIVE_DIRECTION>.", "1": "Pööra <RELATIVE_DIRECTION> <STREET_NAMES> teele.", "2": "Pööra <RELATIVE_DIRECTION> <BEGIN_STREET_NAMES> teele.", "3": "Pööra <RELATIVE_DIRECTION> ja j<PERSON><PERSON> möö<PERSON> <STREET_NAMES> teed.", "4": "<JUNCTION_NAME> tee<PERSON><PERSON> <RELATIVE_DIRECTION>.", "5": "Pööra <RELATIVE_DIRECTION> <TOWARD_SIGN> suunas."}, "empty_street_name_labels": ["läbipää<PERSON>", "rattatee", "rada mägiratastele", "ülekäigurajal", "the stairs", "the bridge", "the tunnel"], "relative_directions": ["vasakul", "paremal"], "example_phrases": {"0": ["Turn left."], "1": ["Turn right onto Flatbush Avenue."], "2": ["Turn left onto North Bond Street, U.S. 1 Business."], "3": ["Turn right to stay on Sunstone Drive."], "4": ["Turn right at Mannenbashi East."], "5": ["Turn left toward Baltimore."]}}, "uturn": {"phrases": {"0": "<RELATIVE_DIRECTION> p<PERSON><PERSON><PERSON> tagasi.", "1": "<RELATIVE_DIRECTION> p<PERSON><PERSON><PERSON> tagasi <STREET_NAMES> teele.", "2": "<RELATIVE_DIRECTION> pö<PERSON>ra tagasi ja jätka mööda <STREET_NAMES> teed.", "3": "<CROSS_STREET_NAMES> tee<PERSON><PERSON> <RELATIVE_DIRECTION> tagasi.", "4": "<CROSS_STREET_NAMES> teerist<PERSON> p<PERSON> <RELATIVE_DIRECTION> tagasi <STREET_NAMES> teele.", "5": "<CROSS_STREET_NAMES> teerist<PERSON> p<PERSON> <RELATIVE_DIRECTION> tagasi ja jää <STREET_NAMES> teele.", "6": "<JUNCTION_NAME> teeristist p<PERSON><PERSON>ra <RELATIVE_DIRECTION> tagasi.", "7": "<RELATIVE_DIRECTION> p<PERSON><PERSON><PERSON> tagasi <TOWARD_SIGN> suunas."}, "empty_street_name_labels": ["läbipää<PERSON>", "rattatee", "rada mägiratastele", "ülekäigurajal", "the stairs", "the bridge", "the tunnel"], "relative_directions": ["vasakul", "paremal"], "example_phrases": {"0": ["Make a left U-turn."], "1": ["Make a right U-turn onto Bunker Hill Road."], "2": ["Make a left U-turn to stay on Bunker Hill Road."], "3": ["Make a left U-turn at Devonshire Road."], "4": ["Make a left U-turn at Devonshire Road onto Jonestown Road/US 22."], "5": ["Make a left U-turn at Devonshire Road to stay on Jonestown Road/US 22."], "6": ["Make a right U-turn at Mannenbashi East."], "7": ["Make a left U-turn toward Baltimore."]}}, "uturn_verbal": {"phrases": {"0": "<RELATIVE_DIRECTION> tee tagasipööre.", "1": "<RELATIVE_DIRECTION> p<PERSON><PERSON><PERSON> tagasi <STREET_NAMES> teele.", "2": "<RELATIVE_DIRECTION> pö<PERSON>ra tagasi ja jätka mööda <STREET_NAMES> teed.", "3": "<CROSS_STREET_NAMES> tee<PERSON><PERSON> <RELATIVE_DIRECTION> tagasi.", "4": "<CROSS_STREET_NAMES> teerist<PERSON> p<PERSON> <RELATIVE_DIRECTION> tagasi <STREET_NAMES> teele.", "5": "<CROSS_STREET_NAMES> teerist<PERSON> p<PERSON> <RELATIVE_DIRECTION> tagasi ja jää <STREET_NAMES> teele.", "6": "<JUNCTION_NAME> tee<PERSON><PERSON> <RELATIVE_DIRECTION> tagasi.", "7": "<RELATIVE_DIRECTION> p<PERSON><PERSON><PERSON> tagasi <TOWARD_SIGN> suunas."}, "empty_street_name_labels": ["läbipää<PERSON>", "rattatee", "rada mägiratastele", "ülekäigurajal", "the stairs", "the bridge", "the tunnel"], "relative_directions": ["vasakul", "paremal"], "example_phrases": {"0": ["Make a left U-turn."], "1": ["Make a right U-turn onto Bunker Hill Road."], "2": ["Make a left U-turn to stay on Bunker Hill Road."], "3": ["Make a left U-turn at Devonshire Road."], "4": ["Make a left U-turn at Devonshire Road onto Jonestown Road, U.S. 22."], "5": ["Make a left U-turn at Devonshire Road to stay on Jonestown Road, U.S. 22."], "6": ["Make a right U-turn at Mannenbashi East."], "7": ["Make a left U-turn toward Baltimore."]}}, "verbal_multi_cue": {"phrases": {"0": "<CURRENT_VERBAL_CUE>, seej<PERSON><PERSON> <NEXT_VERBAL_CUE>", "1": "<CURRENT_VERBAL_CUE>, seej<PERSON><PERSON> <LENGTH> kaugusel <NEXT_VERBAL_CUE>"}, "metric_lengths": ["<KILOMETERS> kilomeetrit", "1 kilomeeter", "<METERS> meetrit", "vähem kui 10 meetrit"], "us_customary_lengths": ["<MILES> mi<PERSON>", "1 miil", "pool miili", "veerand mi<PERSON>", "<FEET> jalga", "vähem kui 10 jalga"], "example_phrases": {"0": ["Bear right onto East Fayette Street. Then Turn right onto North Gay Street."], "1": ["Bear right onto East Fayette Street. Then, in 500 feet, Turn right onto North Gay Street."]}}, "approach_verbal_alert": {"phrases": {"0": "<LENGTH> kaugusel <CURRENT_VERBAL_CUE>"}, "metric_lengths": ["<KILOMETERS> kilomeetrit", "1 kilomeeter", "<METERS> meetrit", "vähem kui 10 meetrit"], "us_customary_lengths": ["<MILES> mi<PERSON>", "1 miil", "pool miili", "veerand mi<PERSON>", "<FEET> jalga", "vähem kui 10 jalga"], "example_phrases": {"0": ["In a quarter mile, Turn right onto North Gay Street."]}}, "pass": {"phrases": {"0": "Pass <OBJECT_LABEL>.", "1": "Pass traffic signals on <OBJECT_LABEL>."}, "object_labels": ["the gate", "the bollards", "ways intersection"], "example_phrases": {"0": ["Pass the gate."]}}, "elevator": {"phrases": {"0": "<PERSON><PERSON> lifti.", "1": "Sõida liftiga kuni <LEVEL>."}, "example_phrases": {"0": ["Take the elevator."], "1": ["Take the elevator to Level 1."]}}, "steps": {"phrases": {"0": "Mine trepist üles.", "1": "<PERSON><PERSON><PERSON>i treppi mööda kuni <LEVEL>."}, "example_phrases": {"0": ["Take the stairs."], "1": ["Take the stairs to Level 2."]}}, "escalator": {"phrases": {"0": "Astu eskalaatorile.", "1": "Sõida eskalaatoriga kuni <LEVEL>."}, "example_phrases": {"0": ["Take the escalator."], "1": ["Take the escalator to Level 3."]}}, "enter_building": {"phrases": {"0": "<PERSON>sene hoones<PERSON>.", "1": "<PERSON><PERSON>e hoonesse ja jätka kulgemist möö<PERSON> <STREET_NAMES>."}, "empty_street_name_labels": ["läbipää<PERSON>", "rattatee", "rada mägiratastele", "ülekäigurajal"], "example_phrases": {"0": ["Enter the building."], "1": ["Enter the building, and continue on the walkway."]}}, "exit_building": {"phrases": {"0": "<PERSON><PERSON><PERSON><PERSON> hoonest.", "1": "<PERSON><PERSON><PERSON><PERSON> hoonest ja jätka kulgemist mööda <STREET_NAMES>."}, "empty_street_name_labels": ["läbipää<PERSON>", "rattatee", "rada mägiratastele", "ülekäigurajal"], "example_phrases": {"0": ["Exit the building."], "1": ["Exit the building, and continue on the walkway."]}}}}
{"posix_locale": "ja_JP.UTF-8", "aliases": ["ja"], "instructions": {"arrive": {"phrases": {"0": "到着: <TIME>", "1": "<TRANSIT_STOP>への到着: <TIME>"}, "example_phrases": {"0": ["Arrive: 8:02 AM."], "1": ["Arrive: 8:02 AM at 8 St - NYU."]}}, "arrive_verbal": {"phrases": {"0": "<TIME>に到着します。", "1": "<TIME>には<TRANSIT_STOP>に到着します。"}, "example_phrases": {"0": ["Arrive at 8:02 AM."], "1": ["Arrive at 8:02 AM at 8 St - NYU."]}}, "bear": {"phrases": {"0": "<RELATIVE_DIRECTION>方向です。", "1": "<RELATIVE_DIRECTION>方向です。その先<STREET_NAMES>です。", "2": "<RELATIVE_DIRECTION>方向、<BEGIN_STREET_NAMES>です。その先<STREET_NAMES>です。", "3": "<RELATIVE_DIRECTION>方向です。その先<STREET_NAMES>です。", "4": "<JUNCTION_NAME>を<RELATIVE_DIRECTION>方向です。", "5": "<RELATIVE_DIRECTION>方向、<TOWARD_SIGN>方面です。"}, "empty_street_name_labels": ["歩道", "自転車道", "自転車道", "the crosswalk", "the stairs", "the bridge", "the tunnel"], "relative_directions": ["左", "右"], "example_phrases": {"0": ["Bear right."], "1": ["<PERSON> left onto Arlen Road."], "2": ["Bear right onto Belair Road/US 1 Business. Continue on US 1 Business."], "3": ["<PERSON> left to stay on US 15 South."], "4": ["Bear right at Mannenbashi East."], "5": ["Bear left toward Baltimore."]}}, "bear_verbal": {"phrases": {"0": "<RELATIVE_DIRECTION>方向です。", "1": "<RELATIVE_DIRECTION>方向です。その先<STREET_NAMES>です。", "2": "<RELATIVE_DIRECTION>方向です。その先<BEGIN_STREET_NAMES>です。", "3": "<RELATIVE_DIRECTION>方向です。その先<STREET_NAMES>です。", "4": "<JUNCTION_NAME>を<RELATIVE_DIRECTION>方向です。", "5": "<RELATIVE_DIRECTION>方向、<TOWARD_SIGN>方面です。"}, "empty_street_name_labels": ["歩道", "自転車道", "自転車道", "the crosswalk", "the stairs", "the bridge", "the tunnel"], "relative_directions": ["左", "右"], "example_phrases": {"0": ["Bear right."], "1": ["<PERSON> left onto Arlen Road."], "2": ["Bear right onto Belair Road, U.S. 1 Business."], "3": ["<PERSON> left to stay on U.S. 15 South."], "4": ["Bear right at Mannenbashi East."], "5": ["Bear left toward Baltimore."]}}, "becomes": {"phrases": {"0": "<PREVIOUS_STREET_NAMES>から<STREET_NAMES>になります。"}, "example_phrases": {"0": ["Vine Street becomes Middletown Road."]}}, "becomes_verbal": {"phrases": {"0": "<PREVIOUS_STREET_NAMES>から<STREET_NAMES>になります。"}, "example_phrases": {"0": ["Vine Street becomes Middletown Road."]}}, "continue": {"phrases": {"0": "直進です。", "1": "<STREET_NAMES>を直進です。", "2": "<JUNCTION_NAME>を直進です。", "3": "<TOWARD_SIGN>方面、直進です。"}, "empty_street_name_labels": ["歩道", "自転車道", "自転車道", "the crosswalk", "the stairs", "the bridge", "the tunnel"], "example_phrases": {"0": ["Continue."], "1": ["Continue on 10th Avenue."], "2": ["Continue at Mannenbashi East."], "3": ["Continue toward Baltimore."]}}, "continue_verbal": {"phrases": {"0": "直進です。", "1": "<LENGTH>直進です。", "2": "<STREET_NAMES>を直進です。", "3": "<STREET_NAMES>を<LENGTH>直進です。", "4": "<JUNCTION_NAME>を直進です。", "5": "<JUNCTION_NAME>を<LENGTH>直進です。", "6": "<TOWARD_SIGN>方面、直進です。", "7": "<TOWARD_SIGN>方面、<LENGTH>直進です。"}, "empty_street_name_labels": ["歩道", "自転車道", "自転車道", "the crosswalk", "the stairs", "the bridge", "the tunnel"], "metric_lengths": ["<KILOMETERS>キロメートル", "1 キロメートル", "<METERS>メートル", "10メートル以内"], "us_customary_lengths": ["<MILES>マイル", "1 マイル", "0.5 マイル", "0.25 マイル", "<FEET>フィート", "10フィート以内"], "example_phrases": {"0": ["Continue."], "1": ["Continue for 300 feet."], "2": ["Continue on 10th Avenue."], "3": ["Continue on 10th Avenue for 3 miles."], "4": ["Continue at Mannenbashi East."], "5": ["Continue at Mannenbashi East for 2 miles."], "6": ["Continue toward Baltimore."], "7": ["Continue toward Baltimore for 5 miles."]}}, "continue_verbal_alert": {"phrases": {"0": "直進です。", "1": "<STREET_NAMES>を直進です。", "2": "<JUNCTION_NAME>を直進です。", "3": "<TOWARD_SIGN>方面、直進です。"}, "empty_street_name_labels": ["歩道", "自転車道", "自転車道", "the crosswalk", "the stairs", "the bridge", "the tunnel"], "example_phrases": {"0": ["Continue."], "1": ["Continue on 10th Avenue."], "2": ["Continue at Mannenbashi East."], "3": ["Continue toward Baltimore."]}}, "depart": {"phrases": {"0": "出発: <TIME>", "1": "<TRANSIT_STOP>の出発: <TIME>"}, "example_phrases": {"0": ["Depart: 8:02 AM."], "1": ["Depart: 8:02 AM from 8 St - NYU."]}}, "depart_verbal": {"phrases": {"0": "<TIME>に出発です。", "1": "<TIME>に<TRANSIT_STOP>を出発です。"}, "example_phrases": {"0": ["Depart at 8:02 AM."], "1": ["Depart at 8:02 AM from 8 St - NYU."]}}, "destination": {"phrases": {"0": "目的地に到着しました。", "1": "<DESTINATION>に到着しました。", "2": "目的地は<RELATIVE_DIRECTION>側です。", "3": "<DESTINATION>は<RELATIVE_DIRECTION>側です。"}, "relative_directions": ["左", "右"], "example_phrases": {"0": ["You have arrived at your destination."], "1": ["You have arrived at 3206 Powelton Avenue."], "2": ["Your destination is on the left.", "Your destination is on the right."], "3": ["Lancaster Brewing Company is on the left."]}}, "destination_verbal": {"phrases": {"0": "目的地に到着しました。", "1": "<DESTINATION>に到着しました。", "2": "目的地は<RELATIVE_DIRECTION>側です。", "3": "<DESTINATION>は<RELATIVE_DIRECTION>側です。"}, "relative_directions": ["左", "右"], "example_phrases": {"0": ["You have arrived at your destination."], "1": ["You have arrived at 32 o6 Powelton Avenue."], "2": ["Your destination is on the left.", "Your destination is on the right."], "3": ["Lancaster Brewing Company is on the left."]}}, "destination_verbal_alert": {"phrases": {"0": "まもなく目的地に到着します。", "1": "まもなく<DESTINATION>に到着します。", "2": "目的地は<RELATIVE_DIRECTION>側です。", "3": "<DESTINATION>は<RELATIVE_DIRECTION>側です。"}, "relative_directions": ["左", "右"], "example_phrases": {"0": ["You will arrive at your destination."], "1": ["You will arrive at 32 o6 Powelton Avenue."], "2": ["Your destination will be on the left.", "Your destination will be on the right."], "3": ["Lancaster Brewing Company will be on the left."]}}, "enter_ferry": {"phrases": {"0": "フェリーに乗ります。", "1": "<STREET_NAMES>です。", "2": "<STREET_NAMES>です。その先、<FERRY_LABEL>です。", "3": "<TOWARD_SIGN>方面のフェリーに乗ります。"}, "empty_street_name_labels": ["歩道", "自転車道", "自転車道", "the crosswalk", "the stairs", "the bridge", "the tunnel"], "ferry_label": "フェリー", "example_phrases": {"0": ["Take the Ferry."], "1": ["Take the Millersburg Ferry."], "2": ["Take the Bridgeport - Port Jefferson Ferry."], "3": ["Take the ferry toward Cape May."]}}, "enter_ferry_verbal": {"phrases": {"0": "フェリーに乗ります。", "1": "<STREET_NAMES>です。", "2": "<STREET_NAMES>です。その先、<FERRY_LABEL>です。", "3": "<TOWARD_SIGN>方面のフェリーに乗ります。"}, "empty_street_name_labels": ["歩道", "自転車道", "自転車道", "the crosswalk", "the stairs", "the bridge", "the tunnel"], "ferry_label": "フェリー", "example_phrases": {"0": ["Take the Ferry."], "1": ["Take the Millersburg Ferry."], "2": ["Take the Bridgeport - Port Jefferson Ferry."], "3": ["Take the ferry toward Cape May."]}}, "enter_roundabout": {"phrases": {"0": "環状交差点に入ります。", "1": "環状交差点に入り、<ORDINAL_VALUE>出口を出ます。", "2": "環状交差点に入り、<ORDINAL_VALUE>出口を出て<ROUNDABOUT_EXIT_STREET_NAMES>に入ります。", "3": "環状交差点に入り、<ORDINAL_VALUE>出口を出て<ROUNDABOUT_EXIT_BEGIN_STREET_NAMES>に入ります。その先、<ROUNDABOUT_EXIT_STREET_NAMES>を進みます。", "4": "環状交差点に入り、<TOWARD_SIGN>方面に<ORDINAL_VALUE>出口を出ます。", "5": "環状交差点に入り、<ROUNDABOUT_EXIT_STREET_NAMES>方面に出ます。", "6": "環状交差点に入り、<ROUNDABOUT_EXIT_BEGIN_STREET_NAMES>方面に出ます。その先、<ROUNDABOUT_EXIT_STREET_NAMES>を進みます。", "7": "環状交差点に入り、<TOWARD_SIGN>方面に出ます。", "8": "<STREET_NAMES>に入ります。", "9": "<STREET_NAMES>に入り、<ORDINAL_VALUE>出口を出ます。", "10": "<STREET_NAMES>に入り、<ROUNDABOUT_EXIT_STREET_NAMES>方面に<ORDINAL_VALUE>出口を出ます。", "11": "<STREET_NAMES>に入り、<ROUNDABOUT_EXIT_BEGIN_STREET_NAMES>方面に<ORDINAL_VALUE>出口を出ます。その先、<ROUNDABOUT_EXIT_STREET_NAMES>を進みます。", "12": "<STREET_NAMES>に入り、<TOWARD_SIGN>方面に<ORDINAL_VALUE>出口を出ます。", "13": "<STREET_NAMES>に入り、<ROUNDABOUT_EXIT_STREET_NAMES>方面に出ます。", "14": "<STREET_NAMES>に入り、<ROUNDABOUT_EXIT_BEGIN_STREET_NAMES>方面に出ます。その先<ROUNDABOUT_EXIT_STREET_NAMES>を進みます。", "15": "<STREET_NAMES>に入り、<TOWARD_SIGN>方面に出ます。"}, "ordinal_values": ["1つ目の", "2つ目の", "3つ目の", "4つ目の", "5つ目の", "6つ目の", "7つ目の", "8つ目の", "9つ目の", "10番目の"], "empty_street_name_labels": ["歩道", "自転車道", "自転車道", "the crosswalk", "the stairs", "the bridge", "the tunnel"], "example_phrases": {"0": ["Enter the roundabout."], "1": ["Enter the roundabout and take the 1st exit.", "Enter the roundabout and take the 2nd exit.", "Enter the roundabout and take the 3rd exit.", "Enter the roundabout and take the 4th exit.", "Enter the roundabout and take the 5th exit.", "Enter the roundabout and take the 6th exit.", "Enter the roundabout and take the 7th exit.", "Enter the roundabout and take the 8th exit.", "Enter the roundabout and take the 9th exit.", "Enter the roundabout and take the 10th exit."], "2": ["Enter the roundabout and take the 3rd exit onto Main Street."], "3": ["Enter the roundabout and take the 3rd exit onto US 322/Main Street. Continue on US 322."], "4": ["Enter the roundabout and take the 3rd exit toward Baltimore."], "5": ["Enter the roundabout and take the exit onto Main Street."], "6": ["Enter the roundabout and take the exit onto US 322/Main Street. Continue on US 322."], "7": ["Enter the roundabout and take the exit toward Baltimore."], "8": ["Enter Dupont Circle."], "9": ["Enter Dupont Circle and take the 1st exit."], "10": ["Enter Dupont Circle and take the 3rd exit onto Main Street."], "11": ["Enter Dupont Circle and take the 3rd exit onto US 322/Main Street. Continue on US 322."], "12": ["Enter Dupont Circle and take the 3rd exit toward Baltimore."], "13": ["Enter Dupont Circle and take the exit onto Main Street."], "14": ["Enter Dupont Circle and take the exit onto US 322/Main Street. Continue on US 322."], "15": ["Enter Dupont Circle and take the exit toward Baltimore."]}}, "enter_roundabout_verbal": {"phrases": {"0": "環状交差点に入ります。", "1": "環状交差点に入り、<ORDINAL_VALUE>出口を出ます。", "2": "環状交差点に入り、<ORDINAL_VALUE>出口を出て<ROUNDABOUT_EXIT_STREET_NAMES>に入ります。", "3": "環状交差点に入り、<ROUNDABOUT_EXIT_BEGIN_STREET_NAMES>方面に<ORDINAL_VALUE>出口を出ます。", "4": "環状交差点に入り、<TOWARD_SIGN>方面に<ORDINAL_VALUE>出口を出ます。", "5": "環状交差点に入り、<ROUNDABOUT_EXIT_STREET_NAMES>方面に出ます。", "6": "環状交差点に入り、<ROUNDABOUT_EXIT_BEGIN_STREET_NAMES>方面に出ます。", "7": "環状交差点に入り、<TOWARD_SIGN>方面に出ます。", "8": "<STREET_NAMES>に入ります。", "9": "<STREET_NAMES>に入り、<ORDINAL_VALUE>出口を出ます。", "10": "<STREET_NAMES>に入り、<ROUNDABOUT_EXIT_STREET_NAMES>方面に<ORDINAL_VALUE>出口を出ます。", "11": "<STREET_NAMES>に入り、<ROUNDABOUT_EXIT_BEGIN_STREET_NAMES>方面に<ORDINAL_VALUE>出口を出ます。", "12": "<STREET_NAMES>に入り、<TOWARD_SIGN>方面に<ORDINAL_VALUE>出口を出ます。", "13": "<STREET_NAMES>に入り、<ROUNDABOUT_EXIT_STREET_NAMES>方面に出ます。", "14": "<STREET_NAMES>に入り、<ROUNDABOUT_EXIT_BEGIN_STREET_NAMES>方面に出ます。", "15": "<STREET_NAMES>に入り、<TOWARD_SIGN>方面に出ます。"}, "ordinal_values": ["1 つ目の", "2 つ目の", "3 つ目の", "4 つ目の", "5 つ目の", "6 つ目の", "7 つ目の", "8 つ目の", "9 つ目の", "10 番目の"], "empty_street_name_labels": ["歩道", "自転車道", "自転車道", "the crosswalk", "the stairs", "the bridge", "the tunnel"], "example_phrases": {"0": ["Enter the roundabout."], "1": ["Enter the roundabout and take the 1st exit.", "Enter the roundabout and take the 2nd exit.", "Enter the roundabout and take the 3rd exit.", "Enter the roundabout and take the 4th exit.", "Enter the roundabout and take the 5th exit.", "Enter the roundabout and take the 6th exit.", "Enter the roundabout and take the 7th exit.", "Enter the roundabout and take the 8th exit.", "Enter the roundabout and take the 9th exit.", "Enter the roundabout and take the 10th exit."], "2": ["Enter the roundabout and take the 3rd exit onto Main Street."], "3": ["Enter the roundabout and take the 3rd exit onto U.S. 3 22/Main Street."], "4": ["Enter the roundabout and take the 3rd exit toward Baltimore."], "5": ["Enter the roundabout and take the exit onto Main Street."], "6": ["Enter the roundabout and take the exit onto U.S. 3 22/Main Street."], "7": ["Enter the roundabout and take the exit toward Baltimore."], "8": ["Enter Dupont Circle."], "9": ["Enter Dupont Circle and take the 1st exit."], "10": ["Enter Dupont Circle and take the 3rd exit onto Main Street."], "11": ["Enter Dupont Circle and take the 3rd exit onto U.S. 3 22/Main Street."], "12": ["Enter Dupont Circle and take the 3rd exit toward Baltimore."], "13": ["Enter Dupont Circle and take the exit onto Main Street."], "14": ["Enter Dupont Circle and take the exit onto U.S. 3 22/Main Street."], "15": ["Enter Dupont Circle and take the exit toward Baltimore."]}}, "exit": {"phrases": {"0": "<RELATIVE_DIRECTION>方向、出口です。", "1": "<RELATIVE_DIRECTION>方向、<NUMBER_SIGN>番出口です。", "2": "<RELATIVE_DIRECTION>方向、<BRANCH_SIGN>出口です。", "3": "<RELATIVE_DIRECTION>方向、<NUMBER_SIGN>番出口です。その先<BRANCH_SIGN>です。", "4": "<RELATIVE_DIRECTION>方向、<TOWARD_SIGN>方面出口です。", "5": "<RELATIVE_DIRECTION>方向、<TOWARD_SIGN>方面<NUMBER_SIGN>番出口です。", "6": "<RELATIVE_DIRECTION>方向、<TOWARD_SIGN>方面<BRANCH_SIGN>出口です。", "7": "<RELATIVE_DIRECTION>方向、<NUMBER_SIGN>番出口です。その先<BRANCH_SIGN>、<TOWARD_SIGN>方面です。", "8": "<RELATIVE_DIRECTION>方向、<NAME_SIGN>出口です。", "10": "<BRANCH_SIGN>を<RELATIVE_DIRECTION>方向、<NAME_SIGN>出口です。", "12": "<RELATIVE_DIRECTION>方向、<TOWARD_SIGN>方面<NAME_SIGN>出口です。", "14": "<RELATIVE_DIRECTION>方向、<NAME_SIGN>出口です。その先<BRANCH_SIGN>、<TOWARD_SIGN>方面です。", "15": "出口です。", "16": "<NUMBER_SIGN>番出口です。", "17": "<BRANCH_SIGN>出口です。", "18": "<NUMBER_SIGN>番出口です。その先<BRANCH_SIGN>です。", "19": "<TOWARD_SIGN>方面、出口です。", "20": "<TOWARD_SIGN>方面、<NUMBER_SIGN>番出口です。", "21": "<TOWARD_SIGN>方面、<BRANCH_SIGN>出口です。", "22": "<NUMBER_SIGN>番出口です。その先<BRANCH_SIGN>、<TOWARD_SIGN>方面です。", "23": "<NAME_SIGN>出口です。", "25": "<NAME_SIGN>出口です。その先<BRANCH_SIGN>です。", "27": "<NAME_SIGN>出口、<TOWARD_SIGN>方面です。", "29": "<NAME_SIGN>出口です。その先<BRANCH_SIGN>、<TOWARD_SIGN>方面です。"}, "relative_directions": ["左", "右"], "example_phrases": {"0": ["Take the exit on the left.", "Take the exit on the right."], "1": ["Take exit 67 B-A on the right."], "2": ["Take the US 322 West exit on the right."], "3": ["Take exit 67 B-A on the right onto US 322 West."], "4": ["Take the exit on the right toward Lewistown."], "5": ["Take exit 67 B-A on the right toward Lewistown."], "6": ["Take the US 322 West exit on the right toward Lewistown."], "7": ["Take exit 67 B-A on the right onto US 322 West toward Lewistown/State College."], "8": ["Take the White Marsh Boulevard exit on the left."], "10": ["Take the White Marsh Boulevard exit on the left onto MD 43 East."], "12": ["Take the White Marsh Boulevard exit on the left toward White Marsh."], "14": ["Take the White Marsh Boulevard exit on the left onto MD 43 East toward White Marsh."], "15": ["Take the exit."], "16": ["Take exit 67 B-A."], "17": ["Take the US 322 West exit."], "18": ["Take exit 67 B-A onto US 322 West."], "19": ["Take the exit toward Lewistown."], "20": ["Take exit 67 B-A toward Lewistown."], "21": ["Take the US 322 West exit toward Lewistown."], "22": ["Take exit 67 B-A onto US 322 West toward Lewistown/State College."], "23": ["Take the White Marsh Boulevard exit."], "25": ["Take the White Marsh Boulevard exit onto MD 43 East."], "27": ["Take the White Marsh Boulevard exit toward White Marsh."], "29": ["Take the White Marsh Boulevard exit onto MD 43 East toward White Marsh."]}}, "exit_roundabout": {"phrases": {"0": "環状交差点を出ます。", "1": "<STREET_NAMES>方面に環状交差点を出ます。", "2": "<BEGIN_STREET_NAMES>方面に環状交差点を出ます。その先、<STREET_NAMES>を進みます。", "3": "<TOWARD_SIGN>方面に環状交差点を出ます。"}, "empty_street_name_labels": ["歩道", "自転車道", "自転車道", "the crosswalk", "the stairs", "the bridge", "the tunnel"], "example_phrases": {"0": ["Exit the roundabout."], "1": ["Exit the roundabout onto Philadelphia Road/MD 7."], "2": ["Exit the roundabout onto Catoctin Mountain Highway/US 15. Continue on US 15."], "3": ["Exit the roundabout toward Baltimore."]}}, "exit_roundabout_verbal": {"phrases": {"0": "環状交差点を出ます。", "1": "<STREET_NAMES>方面に環状交差点を出ます。", "2": "<BEGIN_STREET_NAMES>方面に環状交差点を出ます。", "3": "<TOWARD_SIGN>方面に環状交差点を出ます。"}, "empty_street_name_labels": ["歩道", "自転車道", "自転車道", "the crosswalk", "the stairs", "the bridge", "the tunnel"], "example_phrases": {"0": ["Exit the roundabout."], "1": ["Exit the roundabout onto Philadelphia Road, Maryland 7."], "2": ["Exit the roundabout onto Catoctin Mountain Highway, U.S. 15."], "3": ["Exit the roundabout toward Baltimore."]}}, "exit_verbal": {"phrases": {"0": "<RELATIVE_DIRECTION>方向、出口です。", "1": "<RELATIVE_DIRECTION>方向、<NUMBER_SIGN>番出口です。", "2": "<RELATIVE_DIRECTION>方向、<BRANCH_SIGN>出口です。", "3": "<RELATIVE_DIRECTION>方向、<NUMBER_SIGN>番出口です。その先<BRANCH_SIGN>です。", "4": "<RELATIVE_DIRECTION>方向、<TOWARD_SIGN>方面出口です。", "5": "<RELATIVE_DIRECTION>方向<TOWARD_SIGN>方面、<NUMBER_SIGN>番出口です。", "6": "<RELATIVE_DIRECTION>方向、<TOWARD_SIGN>方面<BRANCH_SIGN>出口です。", "7": "<RELATIVE_DIRECTION>方向、<NUMBER_SIGN>番出口です。その先<BRANCH_SIGN>、<TOWARD_SIGN>方面です。", "8": "<RELATIVE_DIRECTION>方向、<NAME_SIGN>出口です。", "10": "<BRANCH_SIGN>を<RELATIVE_DIRECTION>方向、<NAME_SIGN>出口です。", "12": "<RELATIVE_DIRECTION>方向、<TOWARD_SIGN>方面<NAME_SIGN>出口です。", "14": "<RELATIVE_DIRECTION>方向、<NAME_SIGN>出口です。その先<BRANCH_SIGN>、<TOWARD_SIGN>方面です。", "15": "出口です。", "16": "<NUMBER_SIGN>番出口です。", "17": "<BRANCH_SIGN>出口です。", "18": "<NUMBER_SIGN>番出口です。その先<BRANCH_SIGN>です。", "19": "<TOWARD_SIGN>方面、出口です。", "20": "<TOWARD_SIGN>方面、<NUMBER_SIGN>番出口です。", "21": "<TOWARD_SIGN>方面、<BRANCH_SIGN>出口です。", "22": "<NUMBER_SIGN>番出口です。その先<BRANCH_SIGN>、<TOWARD_SIGN>方面です。", "23": "<NAME_SIGN>出口です。", "25": "<NAME_SIGN>出口です。その先<BRANCH_SIGN>です。", "27": "<NAME_SIGN>出口、<TOWARD_SIGN>方面です。", "29": "<NAME_SIGN>出口です。その先<BRANCH_SIGN>、<TOWARD_SIGN>方面です。"}, "relative_directions": ["左", "右"], "example_phrases": {"0": ["Take the exit on the left.", "Take the exit on the right."], "1": ["Take exit 67 B-A on the right."], "2": ["Take the U.S. 3 22 West exit on the right."], "3": ["Take exit 67 B-A on the right onto U.S. 3 22 West."], "4": ["Take the exit on the right toward Lewistown."], "5": ["Take exit 67 B-A on the right toward Lewistown."], "6": ["Take the U.S. 3 22 West exit on the right toward Lewistown."], "7": ["Take exit 67 B-A on the right onto U.S. 3 22 West toward Lewistown, State College."], "8": ["Take the White Marsh Boulevard exit on the left."], "10": ["Take the White Marsh Boulevard exit on the left onto Maryland 43 East."], "12": ["Take the White Marsh Boulevard exit on the left toward White Marsh."], "14": ["Take the White Marsh Boulevard exit on the left onto Maryland 43 East toward White Marsh."], "15": ["Take the exit."], "16": ["Take exit 67 B-A."], "17": ["Take the US 322 West exit."], "18": ["Take exit 67 B-A onto US 322 West."], "19": ["Take the exit toward Lewistown."], "20": ["Take exit 67 B-A toward Lewistown."], "21": ["Take the US 322 West exit toward Lewistown."], "22": ["Take exit 67 B-A onto US 322 West toward Lewistown/State College."], "23": ["Take the White Marsh Boulevard exit."], "25": ["Take the White Marsh Boulevard exit onto MD 43 East."], "27": ["Take the White Marsh Boulevard exit toward White Marsh."], "29": ["Take the White Marsh Boulevard exit onto MD 43 East toward White Marsh."]}}, "exit_visual": {"phrases": {"0": "<EXIT_NUMBERS>番出口"}, "example_phrases": {"0": ["Exit 1A"]}}, "keep": {"phrases": {"0": "分岐を<RELATIVE_DIRECTION>方向です。", "1": "<RELATIVE_DIRECTION>方向です。その先<NUMBER_SIGN>番出口です。", "2": "<RELATIVE_DIRECTION>方向です。その先<STREET_NAMES>です。", "3": "<RELATIVE_DIRECTION>方向、<NUMBER_SIGN>番出口です。その先<STREET_NAMES>です。", "4": "<RELATIVE_DIRECTION>方向、<TOWARD_SIGN>方面です。", "5": "<RELATIVE_DIRECTION>方向、<NUMBER_SIGN>番出口です。その先<TOWARD_SIGN>方面です。", "6": "<RELATIVE_DIRECTION>方向です。その先<STREET_NAMES>、<TOWARD_SIGN>方面です。", "7": "<RELATIVE_DIRECTION>方向、<NUMBER_SIGN>番出口です。その先<STREET_NAMES>、<TOWARD_SIGN>方面です。"}, "empty_street_name_labels": ["歩道", "自転車道", "自転車道", "the crosswalk", "the stairs", "the bridge", "the tunnel"], "relative_directions": ["左", "直進", "右"], "example_phrases": {"0": ["Keep left at the fork.", "Keep straight at the fork.", "Keep right at the fork."], "1": ["Keep right to take exit 62."], "2": ["Keep right to take I 895 South."], "3": ["Keep right to take exit 62 onto I 895 South."], "4": ["Keep right toward Annapolis."], "5": ["Keep right to take exit 62 toward Annapolis."], "6": ["Keep right to take I 895 South toward Annapolis."], "7": ["Keep right to take exit 62 onto I 895 South toward Annapolis."]}}, "keep_to_stay_on": {"phrases": {"0": "<RELATIVE_DIRECTION>方向です。その先<STREET_NAMES>です。", "1": "<RELATIVE_DIRECTION>方向、<NUMBER_SIGN>番出口です。その先<STREET_NAMES>です。", "2": "<RELATIVE_DIRECTION>方向です。その先<STREET_NAMES>、<TOWARD_SIGN>方面です。", "3": "<RELATIVE_DIRECTION>方向、<NUMBER_SIGN>番出口です。その先<STREET_NAMES>、<TOWARD_SIGN>方面です。"}, "empty_street_name_labels": ["歩道", "自転車道", "自転車道", "the crosswalk", "the stairs", "the bridge", "the tunnel"], "relative_directions": ["左", "直進", "右"], "example_phrases": {"0": ["Keep left to stay on I 95 South.", "Keep straight to stay on I 95 South.", "Keep right to stay on I 95 South."], "1": ["Keep left to take exit 62 to stay on I 95 South."], "2": ["Keep left to stay on I 95 South toward Baltimore."], "3": ["Keep left to take exit 62 to stay on I 95 South toward Baltimore."]}}, "keep_to_stay_on_verbal": {"phrases": {"0": "<RELATIVE_DIRECTION>方向です。その先<STREET_NAMES>です。", "1": "<RELATIVE_DIRECTION>方向、<NUMBER_SIGN>番出口です。その先<STREET_NAMES>です。", "2": "<RELATIVE_DIRECTION>方向です。その先<STREET_NAMES>、<TOWARD_SIGN>方面です。", "3": "<RELATIVE_DIRECTION>方向、<NUMBER_SIGN>番出口です。その先<STREET_NAMES>、<TOWARD_SIGN>方面です。"}, "empty_street_name_labels": ["歩道", "自転車道", "自転車道", "the crosswalk", "the stairs", "the bridge", "the tunnel"], "relative_directions": ["左", "直進", "右"], "example_phrases": {"0": ["Keep left to stay on Interstate 95 South.", "Keep straight to stay on Interstate 95 South.", "Keep right to stay on Interstate 95 South."], "1": ["Keep left to take exit 62 to stay on Interstate 95 South."], "2": ["Keep left to stay on I 95 South toward Baltimore."], "3": ["Keep left to take exit 62 to stay on Interstate 95 South toward Baltimore."]}}, "keep_verbal": {"phrases": {"0": "分岐を<RELATIVE_DIRECTION>方向です。", "1": "<RELATIVE_DIRECTION>方向、<NUMBER_SIGN>番出口です。", "2": "<RELATIVE_DIRECTION>方向です。その先<STREET_NAMES>です。", "3": "<RELATIVE_DIRECTION>方向、<NUMBER_SIGN>番出口です。その先<STREET_NAMES>です。", "4": "<RELATIVE_DIRECTION>方向です。その先、<TOWARD_SIGN>方面です。", "5": "<RELATIVE_DIRECTION>方向、<NUMBER_SIGN>番出口です。その先、<TOWARD_SIGN>方面です。", "6": "<RELATIVE_DIRECTION>方向です。その先<STREET_NAMES>、<TOWARD_SIGN>方面です。", "7": "<RELATIVE_DIRECTION>方向、<NUMBER_SIGN>番出口です。その先<STREET_NAMES>、<TOWARD_SIGN>方面です。"}, "empty_street_name_labels": ["歩道", "自転車道", "自転車道", "the crosswalk", "the stairs", "the bridge", "the tunnel"], "relative_directions": ["左", "直進", "右"], "example_phrases": {"0": ["Keep left at the fork.", "Keep straight at the fork.", "Keep right at the fork."], "1": ["Keep right to take exit 62."], "2": ["Keep right to take Interstate 8 95 South."], "3": ["Keep right to take exit 62 onto Interstate 8 95 South."], "4": ["Keep right toward Annapolis."], "5": ["Keep right to take exit 62 toward Annapolis."], "6": ["Keep right to take Interstate 8 95 South toward Annapolis."], "7": ["Keep right to take exit 62 onto Interstate 8 95 South toward Annapolis."]}}, "merge": {"phrases": {"0": "合流です。", "1": "<RELATIVE_DIRECTION>方向に合流です。", "2": "合流です。<STREET_NAMES>に入ります。", "3": "<RELATIVE_DIRECTION>方向に合流です。<STREET_NAMES>に入ります。", "4": "<TOWARD_SIGN>方面に合流です。", "5": "<RELATIVE_DIRECTION>方向に合流です。<TOWARD_SIGN>方面に進みます。"}, "relative_directions": ["左", "右"], "empty_street_name_labels": ["歩道", "自転車道", "自転車道", "the crosswalk", "the stairs", "the bridge", "the tunnel"], "example_phrases": {"0": ["Merge."], "1": ["<PERSON><PERSON> left."], "2": ["Merge onto I 76 West/Pennsylvania Turnpike."], "3": ["Merge right onto I 83 South."], "4": ["Merge toward Baltimore."], "5": ["Merge right toward Baltimore."]}}, "merge_verbal": {"phrases": {"0": "合流です。", "1": "<RELATIVE_DIRECTION>方向に合流です。", "2": "合流です。<STREET_NAMES>に入ります。", "3": "<RELATIVE_DIRECTION>に合流です。<STREET_NAMES>に入ります。", "4": "<TOWARD_SIGN>方面に合流です。", "5": "<RELATIVE_DIRECTION>方向に合流です。<TOWARD_SIGN>方面に進みます。"}, "relative_directions": ["左", "右"], "empty_street_name_labels": ["歩道", "自転車道", "自転車道", "the crosswalk", "the stairs", "the bridge", "the tunnel"], "example_phrases": {"0": ["Merge."], "1": ["<PERSON><PERSON> left."], "2": ["Merge onto I 76 West/Pennsylvania Turnpike."], "3": ["Merge right onto I 83 South."], "4": ["Merge toward Baltimore."], "5": ["Merge right toward Baltimore."]}}, "post_transition_transit_verbal": {"phrases": {"0": "<TRANSIT_STOP_COUNT><TRANSIT_STOP_COUNT_LABEL>乗車します。"}, "transit_stop_count_labels": {"one": "駅", "other": "駅"}, "example_phrases": {"0": ["Travel 1 stop.", "Travel 3 stops."]}}, "post_transition_verbal": {"phrases": {"0": "<LENGTH>直進です。", "1": "<STREET_NAMES>を<LENGTH>直進です。"}, "empty_street_name_labels": ["歩道", "自転車道", "自転車道", "the crosswalk", "the stairs", "the bridge", "the tunnel"], "metric_lengths": ["<KILOMETERS>キロメートル", "1キロメートル", "<METERS>メートル", "10メートル以内"], "us_customary_lengths": ["<MILES>マイル", "1マイル", "0.5マイル", "0.25マイル", "<FEET>フィート", "10フィート以内"], "example_phrases": {"0": ["Continue for 300 feet.", "Continue for 9 miles."], "1": ["Continue on Pennsylvania 7 43 for 6.2 miles.", "Continue on Main Street, Vermont 30 for 1 tenth of a mile."]}}, "ramp": {"phrases": {"0": "分岐を<RELATIVE_DIRECTION>方向です。", "1": "<BRANCH_SIGN>を<RELATIVE_DIRECTION>方向です。", "2": "分岐を<RELATIVE_DIRECTION>方向です。その先<TOWARD_SIGN>方面です。", "3": "<BRANCH_SIGN>を<RELATIVE_DIRECTION>方向です。その先<TOWARD_SIGN>方面です。", "4": "<NAME_SIGN>を<RELATIVE_DIRECTION>方向です。", "5": "分岐を<RELATIVE_DIRECTION>方向です。", "6": "<BRANCH_SIGN>を<RELATIVE_DIRECTION>方向です。", "7": "分岐を<RELATIVE_DIRECTION>方向です。その先<TOWARD_SIGN>方面です。", "8": "<BRANCH_SIGN>を<RELATIVE_DIRECTION>方向です。その先<TOWARD_SIGN>方面です。", "9": "<NAME_SIGN>を<RELATIVE_DIRECTION>方向です。", "10": "分岐です。", "11": "分岐を<BRANCH_SIGN>方面です。", "12": "分岐を<TOWARD_SIGN>方面です。", "13": "<BRANCH_SIGN>を<TOWARD_SIGN>方面です。", "14": "<NAME_SIGN>方面です。"}, "relative_directions": ["左", "右"], "example_phrases": {"0": ["Take the ramp on the left.", "Take the ramp on the right."], "1": ["Take the I 95 ramp on the right."], "2": ["Take the ramp on the left toward JFK."], "3": ["Take the South Conduit Avenue ramp on the left toward JFK."], "4": ["Take the Gettysburg Pike ramp on the right."], "5": ["Turn left to take the ramp.", "Turn right to take the ramp."], "6": ["Turn left to take the PA 283 West ramp."], "7": ["Turn left to take the ramp toward Harrisburg/Harrisburg International Airport."], "8": ["Turn left to take the PA 283 West ramp toward Harrisburg/Harrisburg International Airport."], "9": ["Turn right to take the Gettysburg Pike ramp."], "10": ["Take the ramp."], "11": ["Take the I 95 ramp."], "12": ["Take the ramp toward JFK."], "13": ["Take the Soutch Conduit Avenue ramp toward JFK."], "14": ["Take the Gettysburg ramp."]}}, "ramp_straight": {"phrases": {"0": "分岐を直進です。", "1": "<BRANCH_SIGN>を直進です。", "2": "分岐を直進です。その先<TOWARD_SIGN>方面です。", "3": "<BRANCH_SIGN>を直進です。その先<TOWARD_SIGN>方面です。", "4": "<NAME_SIGN>を直進です。"}, "example_phrases": {"0": ["Stay straight to take the ramp."], "1": ["Stay straight to take the US 322 East ramp."], "2": ["Stay straight to take the ramp toward Hershey."], "3": ["Stay straight to take the US 322 East/US 422 East/US 522 East/US 622 East ramp toward Hershey/Palmdale/Palmyra/Campbelltown."], "4": ["Stay straight to take the Gettysburg Pike ramp."]}}, "ramp_straight_verbal": {"phrases": {"0": "分岐を直進です。", "1": "<BRANCH_SIGN>を直進です。", "2": "分岐を直進です。その先<TOWARD_SIGN>方面です。", "3": "<BRANCH_SIGN>を直進です。その先<TOWARD_SIGN>方面です。", "4": "<NAME_SIGN>を直進です。"}, "example_phrases": {"0": ["Stay straight to take the ramp."], "1": ["Stay straight to take the U.S. 3 22 East ramp."], "2": ["Stay straight to take the ramp toward Hershey."], "3": ["Stay straight to take the U.S. 3 22 East, U.S. 4 22 East ramp toward Hershey, Palmdale."], "4": ["Stay straight to take the Gettysburg Pike ramp."]}}, "ramp_verbal": {"phrases": {"0": "分岐を<RELATIVE_DIRECTION>方向です。", "1": "<BRANCH_SIGN>を<RELATIVE_DIRECTION>方向です。", "2": "分岐を<RELATIVE_DIRECTION>方向です。その先<TOWARD_SIGN>方面です。", "3": "<BRANCH_SIGN>を<RELATIVE_DIRECTION>方向です。その先<TOWARD_SIGN>方面です。", "4": "<NAME_SIGN>を<RELATIVE_DIRECTION>方向です。", "5": "分岐を<RELATIVE_DIRECTION>方向です。", "6": "<BRANCH_SIGN>を<RELATIVE_DIRECTION>方向です。", "7": "分岐を<RELATIVE_DIRECTION>方向です。その先<TOWARD_SIGN>方面です。", "8": "<BRANCH_SIGN>を<RELATIVE_DIRECTION>方向です。その先<TOWARD_SIGN>方面です。", "9": "<NAME_SIGN>を<RELATIVE_DIRECTION>方向です。", "10": "分岐です。", "11": "分岐を<BRANCH_SIGN>方面です。", "12": "分岐を<TOWARD_SIGN>方面です。", "13": "<BRANCH_SIGN>を<TOWARD_SIGN>方面です。", "14": "分岐を<NAME_SIGN>方面です。"}, "relative_directions": ["左", "右"], "example_phrases": {"0": ["Take the ramp on the left.", "Take the ramp on the right."], "1": ["Take the Interstate 95 ramp on the right."], "2": ["Take the ramp on the left toward JFK."], "3": ["Take the South Conduit Avenue ramp on the left toward JFK."], "4": ["Take the Gettysburg Pike ramp on the right."], "5": ["Turn left to take the ramp.", "Turn right to take the ramp."], "6": ["Turn left to take the Pennsylvania 2 83 West ramp."], "7": ["Turn left to take the ramp toward Harrisburg/Harrisburg International Airport."], "8": ["Turn left to take the Pennsylvania 2 83 West ramp toward Harrisburg, Harrisburg International Airport."], "9": ["Turn right to take the Gettysburg Pike ramp."], "10": ["Take the ramp."], "11": ["Take the Interstate 95 ramp."], "12": ["Take the ramp toward JFK."], "13": ["Take the South Conduit Avenue ramp toward JFK."], "14": ["Take the Gettysburg Pike ramp."]}}, "sharp": {"phrases": {"0": "斜め<RELATIVE_DIRECTION>手前方向です。", "1": "斜め<RELATIVE_DIRECTION>手前方向です。その先<STREET_NAMES>です。", "2": "斜め<RELATIVE_DIRECTION>手前方向、<BEGIN_STREET_NAMES>です。その先、<STREET_NAMES>です。", "3": "斜め<RELATIVE_DIRECTION>手前方向です。その先<STREET_NAMES>です。", "4": "<JUNCTION_NAME>を斜め<RELATIVE_DIRECTION>手前方向です。", "5": "斜め<RELATIVE_DIRECTION>手前方向、<TOWARD_SIGN>方面です。"}, "empty_street_name_labels": ["歩道", "自転車道", "自転車道", "the crosswalk", "the stairs", "the bridge", "the tunnel"], "relative_directions": ["左", "右"], "example_phrases": {"0": ["Make a sharp left."], "1": ["Make a sharp right onto Flatbush Avenue."], "2": ["Make a sharp left onto North Bond Street/US 1 Business/MD 924. Continue on MD 924."], "3": ["Make a sharp right to stay on Sunstone Drive."], "4": ["Make a sharp right at Mannenbashi East."], "5": ["Make a sharp left toward Baltimore."]}}, "sharp_verbal": {"phrases": {"0": "斜め<RELATIVE_DIRECTION>手前方向です。", "1": "斜め<RELATIVE_DIRECTION>手前方向です。その先<STREET_NAMES>です。", "2": "斜め<RELATIVE_DIRECTION>手前方向です。その先<BEGIN_STREET_NAMES>です。", "3": "斜め<RELATIVE_DIRECTION>手前方向です。その先<STREET_NAMES>です。", "4": "<JUNCTION_NAME>を斜め<RELATIVE_DIRECTION>手前方向です。", "5": "斜め<RELATIVE_DIRECTION>手前方向、<TOWARD_SIGN>方面です。"}, "empty_street_name_labels": ["歩道", "自転車道", "自転車道", "the crosswalk", "the stairs", "the bridge", "the tunnel"], "relative_directions": ["左", "右"], "example_phrases": {"0": ["Make a sharp left."], "1": ["Make a sharp right onto Flatbush Avenue."], "2": ["Make a sharp left onto North Bond Street, U.S. 1 Business."], "3": ["Make a sharp right to stay on Sunstone Drive."], "4": ["Make a sharp right at Mannenbashi East."], "5": ["Make a sharp left toward Baltimore."]}}, "start": {"phrases": {"0": "<CARDINAL_DIRECTION>方向です。", "1": "<STREET_NAMES>を<CARDINAL_DIRECTION>方向です。", "2": "<BEGIN_STREET_NAMES>を<CARDINAL_DIRECTION>方向です。その先、<STREET_NAMES>です。", "4": "<CARDINAL_DIRECTION>方向です。", "5": "<STREET_NAMES>を<CARDINAL_DIRECTION>方向です。", "6": "<BEGIN_STREET_NAMES>を<CARDINAL_DIRECTION>方向です。その先、<STREET_NAMES>です。", "8": "<CARDINAL_DIRECTION>方向です。", "9": "<STREET_NAMES>を<CARDINAL_DIRECTION>方向です。", "10": "<BEGIN_STREET_NAMES>を<CARDINAL_DIRECTION>方向です。その先、<STREET_NAMES>です。", "16": "<CARDINAL_DIRECTION>方向です。", "17": "<STREET_NAMES>を<CARDINAL_DIRECTION>方向です。", "18": "<BEGIN_STREET_NAMES>を<CARDINAL_DIRECTION>方向です。その先、<STREET_NAMES>です。"}, "cardinal_directions": ["北", "北東", "東", "南<PERSON>", "南", "南<PERSON>", "西", "北西"], "empty_street_name_labels": ["歩道", "自転車道", "自転車道", "the crosswalk", "the stairs", "the bridge", "the tunnel"], "example_phrases": {"0": ["Head east.", "Head north."], "1": ["Head southwest on 5th Avenue.", "Head west on the walkway", "Head east on the cycleway", "Head north on the mountain bike trail"], "2": ["Head south on North Prince Street/US 222/PA 272. Continue on US 222/PA 272."], "4": ["Drive east.", "Drive north."], "5": ["Drive southwest on 5th Avenue."], "6": ["Drive south on North Prince Street/US 222/PA 272. Continue on US 222/PA 272."], "8": ["Walk east.", "Walk north."], "9": ["Walk southwest on 5th Avenue.", "Walk west on the walkway"], "10": ["Walk south on North Prince Street/US 222/PA 272. Continue on US 222/PA 272."], "16": ["Bike east.", "Bike north."], "17": ["Bike southwest on 5th Avenue.", "Bike east on the cycleway", "Bike north on the mountain bike trail"], "18": ["Bike south on North Prince Street/US 222/PA 272. Continue on US 222/PA 272."]}}, "start_verbal": {"phrases": {"0": "<CARDINAL_DIRECTION>方向です。", "1": "<CARDINAL_DIRECTION>方向に<LENGTH>です。", "2": "<STREET_NAMES>を<CARDINAL_DIRECTION>方向です。", "3": "<STREET_NAMES>を<CARDINAL_DIRECTION>方向に<LENGTH>です。", "4": "<BEGIN_STREET_NAMES>を<CARDINAL_DIRECTION>方向です。", "5": "<CARDINAL_DIRECTION>方向です。", "6": "<CARDINAL_DIRECTION>方向に<LENGTH>です。", "7": "<STREET_NAMES>を<CARDINAL_DIRECTION>方向です。", "8": "<STREET_NAMES>を<CARDINAL_DIRECTION>方向に<LENGTH>です。", "9": "<BEGIN_STREET_NAMES>を<CARDINAL_DIRECTION>方向です。", "10": "<CARDINAL_DIRECTION>方向です。", "11": "<CARDINAL_DIRECTION>方向に<LENGTH>です。", "12": "<STREET_NAMES>を<CARDINAL_DIRECTION>方向です。", "13": "<STREET_NAMES>を<CARDINAL_DIRECTION>方向に<LENGTH>です。", "14": "<BEGIN_STREET_NAMES>を<CARDINAL_DIRECTION>方向です。", "15": "<CARDINAL_DIRECTION>方向です。", "16": "<CARDINAL_DIRECTION>方向に<LENGTH>です。", "17": "<STREET_NAMES>を<CARDINAL_DIRECTION>方向です。", "18": "<STREET_NAMES>を<CARDINAL_DIRECTION>方向に<LENGTH>です。", "19": "<BEGIN_STREET_NAMES>を<CARDINAL_DIRECTION>方向です。"}, "cardinal_directions": ["北", "北東", "東", "南<PERSON>", "南", "南<PERSON>", "西", "北西"], "empty_street_name_labels": ["歩道", "自転車道", "自転車道", "the crosswalk", "the stairs", "the bridge", "the tunnel"], "metric_lengths": ["<KILOMETERS>キロメートル", "1キロメートル", "<METERS>メートル", "10メートル以内"], "us_customary_lengths": ["<MILES>マイル", "1マイル", "0.5マイル", "0.25マイル", "<FEET>フィート", "10フィート以内"], "example_phrases": {"0": ["Head east.", "Head north."], "1": ["Head east for a half mile.", "Head north for 1 kilometer."], "2": ["Head southwest on 5th Avenue."], "3": ["Head southwest on 5th Avenue for 1 tenth of a mile."], "4": ["Head south on North Prince Street, U.S. 2 22."], "5": ["Drive east.", "Drive north."], "6": ["Drive east for a half mile.", "Drive north for 1 kilometer."], "7": ["Drive southwest on 5th Avenue."], "8": ["Drive southwest on 5th Avenue for 1 tenth of a mile."], "9": ["Drive south on North Prince Street, U.S. 2 22."], "10": ["Walk east.", "Walk north."], "11": ["Walk east for a half mile.", "Walk north for 1 kilometer."], "12": ["Walk southwest on 5th Avenue."], "13": ["Walk southwest on 5th Avenue for 1 tenth of a mile."], "14": ["Walk south on North Prince Street, U.S. 2 22."], "15": ["Bike east.", "Bike north."], "16": ["Bike east for a half mile.", "Bike north for 1 kilometer."], "17": ["Bike southwest on 5th Avenue."], "18": ["Bike southwest on 5th Avenue for 1 tenth of a mile."], "19": ["Bike south on North Prince Street, U.S. 2 22."]}}, "transit": {"phrases": {"0": "<TRANSIT_NAME>に乗ります。(<TRANSIT_STOP_COUNT><TRANSIT_STOP_COUNT_LABEL>)", "1": "<TRANSIT_HEADSIGN>方面に<TRANSIT_NAME>に乗ります。(<TRANSIT_STOP_COUNT><TRANSIT_STOP_COUNT_LABEL>)"}, "empty_transit_name_labels": ["トラム", "地下鉄", "電車", "バス", "フェリー", "ケーブルカー", "ゴンドラ", "ケーブルカー"], "transit_stop_count_labels": {"one": "駅", "other": "駅"}, "example_phrases": {"0": ["Take the New Haven. (1 stop)", "Take the metro. (2 stops)", "Take the bus. (12 stops)"], "1": ["Take the F toward JAMAICA - 179 ST. (10 stops)", "Take the ferry toward Staten Island. (1 stop)"]}}, "transit_connection_destination": {"phrases": {"0": "駅を出ます。", "1": "<TRANSIT_STOP>を出ます。", "2": "<STATION_LABEL>の<TRANSIT_STOP>を出ます。"}, "station_label": "駅", "example_phrases": {"0": ["Exit the station."], "1": ["Exit the Embarcadero Station."], "2": ["Exit the 8 St - NYU Station."]}}, "transit_connection_destination_verbal": {"phrases": {"0": "駅を出ます。", "1": "<TRANSIT_STOP>を出ます。", "2": "<STATION_LABEL>の<TRANSIT_STOP>を出ます。"}, "station_label": "駅", "example_phrases": {"0": ["Exit the station."], "1": ["Exit the Embarcadero Station."], "2": ["Exit the 8 St - NYU Station."]}}, "transit_connection_start": {"phrases": {"0": "駅に入ります。", "1": "<TRANSIT_STOP>に入ります。", "2": "<STATION_LABEL>の<TRANSIT_STOP>に入ります。"}, "station_label": "駅", "example_phrases": {"0": ["Enter the station."], "1": ["Enter the Embarcadero Station."], "2": ["Enter the 8 St - NYU Station."]}}, "transit_connection_start_verbal": {"phrases": {"0": "駅に入ります。", "1": "<TRANSIT_STOP>に入ります。", "2": "<STATION_LABEL>の<TRANSIT_STOP>に入ります。"}, "station_label": "駅", "example_phrases": {"0": ["Enter the station."], "1": ["Enter the Embarcadero Station."], "2": ["Enter the 8 St - NYU Station."]}}, "transit_connection_transfer": {"phrases": {"0": "駅で乗り換えです。", "1": "<TRANSIT_STOP>で乗り換えです。", "2": "<STATION_LABEL>の<TRANSIT_STOP>で乗り換えです。"}, "station_label": "駅", "example_phrases": {"0": ["Transfer at the station."], "1": ["Transfer at the Embarcadero Station."], "2": ["Transfer at the 8 St - NYU Station."]}}, "transit_connection_transfer_verbal": {"phrases": {"0": "駅で乗り換えです。", "1": "<TRANSIT_STOP>で乗り換えです。", "2": "<STATION_LABEL>の<TRANSIT_STOP>で乗り換えです。"}, "station_label": "駅", "example_phrases": {"0": ["Transfer at the station."], "1": ["Transfer at the Embarcadero Station."], "2": ["Transfer at the 8 St - NYU Station."]}}, "transit_remain_on": {"phrases": {"0": "<TRANSIT_NAME>に乗ります。(<TRANSIT_STOP_COUNT><TRANSIT_STOP_COUNT_LABEL>)", "1": "<TRANSIT_HEADSIGN>方面に<TRANSIT_NAME>に乗ります。(<TRANSIT_STOP_COUNT><TRANSIT_STOP_COUNT_LABEL>)"}, "empty_transit_name_labels": ["トラム", "地下鉄", "電車", "バス", "フェリー", "ケーブルカー", "ゴンドラ", "ケーブルカー"], "transit_stop_count_labels": {"one": "駅", "other": "駅"}, "example_phrases": {"0": ["Remain on the New Haven. (1 stop)", "<PERSON><PERSON><PERSON> on the train. (3 stops)"], "1": ["Remain on the F toward JAMAICA - 179 ST. (10 stops)"]}}, "transit_remain_on_verbal": {"phrases": {"0": "<TRANSIT_NAME>に乗ります。", "1": "<TRANSIT_HEADSIGN>方面に<TRANSIT_NAME>に乗ります。"}, "empty_transit_name_labels": ["トラム", "地下鉄", "電車", "バス", "フェリー", "ケーブルカー", "ゴンドラ", "ケーブルカー"], "example_phrases": {"0": ["<PERSON><PERSON><PERSON> on the New Haven."], "1": ["Remain on the F toward JAMAICA - 179 ST."]}}, "transit_transfer": {"phrases": {"0": "<TRANSIT_NAME>に乗り換えです。(<TRANSIT_STOP_COUNT><TRANSIT_STOP_COUNT_LABEL>)", "1": "<TRANSIT_HEADSIGN>方面に<TRANSIT_NAME>に乗り換えです。(<TRANSIT_STOP_COUNT><TRANSIT_STOP_COUNT_LABEL>)"}, "empty_transit_name_labels": ["トラム", "地下鉄", "電車", "バス", "フェリー", "ケーブルカー", "ゴンドラ", "ケーブルカー"], "transit_stop_count_labels": {"one": "駅", "other": "駅"}, "example_phrases": {"0": ["Transfer to take the New Haven. (1 stop)", "Transfer to take the tram. (4 stops)"], "1": ["Transfer to take the F toward JAMAICA - 179 ST. (10 stops)"]}}, "transit_transfer_verbal": {"phrases": {"0": "<TRANSIT_NAME>に乗り換えです。", "1": "<TRANSIT_HEADSIGN>方面に<TRANSIT_NAME>に乗り換えです。"}, "empty_transit_name_labels": ["トラム", "地下鉄", "電車", "バス", "フェリー", "ケーブルカー", "ゴンドラ", "ケーブルカー"], "example_phrases": {"0": ["Transfer to take the New Haven."], "1": ["Transfer to take the F toward JAMAICA - 179 ST."]}}, "transit_verbal": {"phrases": {"0": "<TRANSIT_NAME>に乗ります。", "1": "<TRANSIT_HEADSIGN>方面に<TRANSIT_NAME>に乗ります。"}, "empty_transit_name_labels": ["トラム", "地下鉄", "電車", "バス", "フェリー", "ケーブルカー", "ゴンドラ", "ケーブルカー"], "example_phrases": {"0": ["Take the New Haven."], "1": ["Take the F toward JAMAICA - 179 ST."]}}, "turn": {"phrases": {"0": "<RELATIVE_DIRECTION>方向です。", "1": "<RELATIVE_DIRECTION>方向です。その先<STREET_NAMES>です。", "2": "<RELATIVE_DIRECTION>方向、<BEGIN_STREET_NAMES>です。その先、<STREET_NAMES>です。", "3": "<RELATIVE_DIRECTION>方向です。その先<STREET_NAMES>です。", "4": "<JUNCTION_NAME>を<RELATIVE_DIRECTION>方向です。", "5": "<RELATIVE_DIRECTION>方向、<TOWARD_SIGN>方面です。"}, "empty_street_name_labels": ["歩道", "自転車道", "自転車道", "the crosswalk", "the stairs", "the bridge", "the tunnel"], "relative_directions": ["左", "右"], "example_phrases": {"0": ["Turn left."], "1": ["Turn right onto Flatbush Avenue."], "2": ["Turn left onto North Bond Street/US 1 Business/MD 924. Continue on MD 924."], "3": ["Turn right to stay on Sunstone Drive."], "4": ["Turn right at Mannenbashi East."], "5": ["Turn left toward Baltimore."]}}, "turn_verbal": {"phrases": {"0": "<RELATIVE_DIRECTION>方向です。", "1": "<RELATIVE_DIRECTION>方向です。その先<STREET_NAMES>です。", "2": "<RELATIVE_DIRECTION>方向です。その先<BEGIN_STREET_NAMES>です。", "3": "<RELATIVE_DIRECTION>方向です。その先<STREET_NAMES>です。", "4": "<JUNCTION_NAME>を<RELATIVE_DIRECTION>方向です。", "5": "<RELATIVE_DIRECTION>方向、<TOWARD_SIGN>方面です。"}, "empty_street_name_labels": ["歩道", "自転車道", "自転車道", "the crosswalk", "the stairs", "the bridge", "the tunnel"], "relative_directions": ["左", "右"], "example_phrases": {"0": ["Turn left."], "1": ["Turn right onto Flatbush Avenue."], "2": ["Turn left onto North Bond Street, U.S. 1 Business."], "3": ["Turn right to stay on Sunstone Drive."], "4": ["Turn right at Mannenbashi East."], "5": ["Turn left toward Baltimore."]}}, "uturn": {"phrases": {"0": "<RELATIVE_DIRECTION>方向、Uターンです。", "1": "<RELATIVE_DIRECTION>方向、Uターンです。その先<STREET_NAMES>です。", "2": "<RELATIVE_DIRECTION>方向、Uターンです。その先<STREET_NAMES>です。", "3": "<CROSS_STREET_NAMES>を<RELATIVE_DIRECTION>方向、Uターンです。", "4": "<CROSS_STREET_NAMES>を<RELATIVE_DIRECTION>方向、Uターンです。その先<STREET_NAMES>です。", "5": "<CROSS_STREET_NAMES>を<RELATIVE_DIRECTION>方向、Uターンです。その先<STREET_NAMES>です。", "6": "<JUNCTION_NAME>を<RELATIVE_DIRECTION>方向、Uターンです。", "7": "<RELATIVE_DIRECTION>方向、<TOWARD_SIGN>方面にUターンです。"}, "empty_street_name_labels": ["歩道", "自転車道", "自転車道", "the crosswalk", "the stairs", "the bridge", "the tunnel"], "relative_directions": ["左", "右"], "example_phrases": {"0": ["Make a left U-turn."], "1": ["Make a right U-turn onto Bunker Hill Road."], "2": ["Make a left U-turn to stay on Bunker Hill Road."], "3": ["Make a left U-turn at Devonshire Road."], "4": ["Make a left U-turn at Devonshire Road onto Jonestown Road/US 22."], "5": ["Make a left U-turn at Devonshire Road to stay on Jonestown Road/US 22."], "6": ["Make a right U-turn at Mannenbashi East."], "7": ["Make a left U-turn toward Baltimore."]}}, "uturn_verbal": {"phrases": {"0": "<RELATIVE_DIRECTION>方向、Uターンです。", "1": "<RELATIVE_DIRECTION>方向、Uターンです。その先<STREET_NAMES>です。", "2": "<RELATIVE_DIRECTION>方向、Uターンです。その先<STREET_NAMES>です。", "3": "<CROSS_STREET_NAMES>を<RELATIVE_DIRECTION>方向、Uターンです。", "4": "<CROSS_STREET_NAMES>を<RELATIVE_DIRECTION>方向、Uターンです。その先<STREET_NAMES>です。", "5": "<CROSS_STREET_NAMES>を<RELATIVE_DIRECTION>方向、Uターンです。その先<STREET_NAMES>です。", "6": "<JUNCTION_NAME>を<RELATIVE_DIRECTION>方向、Uターンです。", "7": "<RELATIVE_DIRECTION>方向、<TOWARD_SIGN>方面にUターンです。"}, "empty_street_name_labels": ["歩道", "自転車道", "自転車道", "the crosswalk", "the stairs", "the bridge", "the tunnel"], "relative_directions": ["左", "右"], "example_phrases": {"0": ["Make a left U-turn."], "1": ["Make a right U-turn onto Bunker Hill Road."], "2": ["Make a left U-turn to stay on Bunker Hill Road."], "3": ["Make a left U-turn at Devonshire Road."], "4": ["Make a left U-turn at Devonshire Road onto Jonestown Road, U.S. 22."], "5": ["Make a left U-turn at Devonshire Road to stay on Jonestown Road, U.S. 22."], "6": ["Make a right U-turn at Mannenbashi East."], "7": ["Make a left U-turn toward Baltimore."]}}, "verbal_multi_cue": {"phrases": {"0": "<CURRENT_VERBAL_CUE>。その先<NEXT_VERBAL_CUE>", "1": "<CURRENT_VERBAL_CUE>。その先<LENGTH>で<NEXT_VERBAL_CUE>"}, "metric_lengths": ["<KILOMETERS>キロメートル", "1キロメートル", "<METERS>メートル", "10メートル以内"], "us_customary_lengths": ["<MILES>マイル", "1マイル", "0.5マイル", "0.25マイル", "<FEET>フィート", "10フィート以内"], "example_phrases": {"0": ["Bear right onto East Fayette Street. Then Turn right onto North Gay Street."], "1": ["Bear right onto East Fayette Street. Then, in 500 feet, Turn right onto North Gay Street."]}}, "approach_verbal_alert": {"phrases": {"0": "<LENGTH>先、<CURRENT_VERBAL_CUE>"}, "metric_lengths": ["<KILOMETERS>キロメートル", "1キロメートル", "<METERS>メートル", "10メートル以内"], "us_customary_lengths": ["<MILES>マイル", "1マイル", "0.5マイル", "0.25マイル", "<FEET>フィート", "10フィート以内"], "example_phrases": {"0": ["In a quarter mile, Turn right onto North Gay Street."]}}, "pass": {"phrases": {"0": "Pass <OBJECT_LABEL>.", "1": "Pass traffic signals on <OBJECT_LABEL>."}, "object_labels": ["the gate", "the bollards", "ways intersection"], "example_phrases": {"0": ["Pass the gate."]}}, "elevator": {"phrases": {"0": "Take the elevator.", "1": "Take the elevator to <LEVEL>."}, "example_phrases": {"0": ["Take the elevator."], "1": ["Take the elevator to Level 1."]}}, "steps": {"phrases": {"0": "Take the stairs.", "1": "Take the stairs to <LEVEL>."}, "example_phrases": {"0": ["Take the stairs."], "1": ["Take the stairs to Level 2."]}}, "escalator": {"phrases": {"0": "Take the escalator.", "1": "Take the escalator to <LEVEL>."}, "example_phrases": {"0": ["Take the escalator."], "1": ["Take the escalator to Level 3."]}}, "enter_building": {"phrases": {"0": "Enter the building.", "1": "Enter the building, and continue on <STREET_NAMES>."}, "empty_street_name_labels": ["歩道", "自転車道", "自転車道", "the crosswalk"], "example_phrases": {"0": ["Enter the building."], "1": ["Enter the building, and continue on the walkway."]}}, "exit_building": {"phrases": {"0": "Exit the building.", "1": "Exit the building, and continue on <STREET_NAMES>."}, "empty_street_name_labels": ["歩道", "自転車道", "自転車道", "the crosswalk"], "example_phrases": {"0": ["Exit the building."], "1": ["Exit the building, and continue on the walkway."]}}}}
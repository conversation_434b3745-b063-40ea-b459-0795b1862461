{"mjolnir": {"max_cache_size": 1000000000, "id_table_size": 1300000000, "use_lru_mem_cache": false, "lru_mem_cache_hard_control": false, "use_simple_mem_cache": false, "tile_dir": "test/data/utrecht_tiles", "tile_extract": "test/data/utrecht_tiles/tiles.tar", "traffic_extract": "/data/valhalla/traffic.tar", "admin": "/data/valhalla/admin.sqlite", "landmarks": "/data/valhalla/landmarks.sqlite", "timezone": "/data/valhalla/tz_world.sqlite", "transit_dir": "/data/valhalla/transit", "transit_feeds_dir": "/data/valhalla/transit_feeds", "hierarchy": true, "shortcuts": true, "include_driveways": true, "include_bicycle": true, "include_pedestrian": true, "include_driving": true, "import_bike_share_stations": false, "global_synchronized_cache": false, "max_concurrent_reader_users": 1, "reclassify_links": true, "data_processing": {"infer_internal_intersections": true, "infer_turn_channels": true, "apply_country_overrides": true, "use_admin_db": true, "use_direction_on_ways": false, "allow_alt_name": false, "use_urban_tag": false, "use_rest_area": false, "scan_tar": false}, "logging": {"type": "std_out", "color": true, "file_name": "path_to_some_file.log"}}, "additional_data": {"elevation": "/data/valhalla/elevation/"}, "loki": {"actions": ["locate", "route", "height", "sources_to_targets", "optimized_route", "isochrone", "trace_route", "trace_attributes", "transit_available", "expansion", "centroid", "status"], "use_connectivity": true, "service_defaults": {"radius": 0, "minimum_reachability": 50, "search_cutoff": 35000, "node_snap_tolerance": 5, "street_side_tolerance": 5, "street_side_max_distance": 1000, "heading_tolerance": 60}, "logging": {"type": "std_out", "color": true, "file_name": "path_to_some_file.log", "long_request": 100.0}, "service": {"proxy": "ipc:///tmp/loki"}}, "thor": {"logging": {"type": "std_out", "color": true, "file_name": "path_to_some_file.log", "long_request": 110.0}, "source_to_target_algorithm": "select_optimal", "service": {"proxy": "ipc:///tmp/thor"}, "max_reserved_labels_count_astar": 2000000, "max_reserved_labels_count_bidir_astar": 1000000, "max_reserved_labels_count_dijkstras": 4000000, "max_reserved_labels_count_bidir_dijkstras": 2000000, "extended_search": false, "costmatrix": {"check_reverse_connection": false, "allow_second_pass": false, "max_reserved_locations": 25, "hierarchy_limits": {"max_up_transitions": {"1": 400, "2": 100}}}, "bidirectional_astar": {"hierarchy_limits": {"max_up_transitions": {"1": 400, "2": 100}, "expand_within_distance": {"0": 100000000.0, "1": 20000, "2": 5000}}}, "unidirectional_astar": {"hierarchy_limits": {"max_up_transitions": {"1": 400, "2": 100}, "expand_within_distance": {"0": 100000000.0, "1": 100000, "2": 5000}}}}, "odin": {"logging": {"type": "std_out", "color": true, "file_name": "path_to_some_file.log"}, "service": {"proxy": "ipc:///tmp/odin"}, "markup_formatter": {"markup_enabled": false, "phoneme_format": "<TEXTUAL_STRING> (<span class=<QUOTES>phoneme<QUOTES>>/<VERBAL_STRING>/</span>)"}}, "meili": {"mode": "auto", "customizable": ["mode", "search_radius", "turn_penalty_factor", "gps_accuracy", "interpolation_distance", "sigma_z", "beta", "max_route_distance_factor", "max_route_time_factor"], "verbose": false, "default": {"sigma_z": 4.07, "gps_accuracy": 5.0, "beta": 3, "max_route_distance_factor": 5, "max_route_time_factor": 5, "max_search_radius": 100, "breakage_distance": 2000, "interpolation_distance": 10, "search_radius": 50, "geometry": false, "route": true, "turn_penalty_factor": 0}, "auto": {"turn_penalty_factor": 200, "search_radius": 50}, "pedestrian": {"turn_penalty_factor": 100, "search_radius": 50}, "bicycle": {"turn_penalty_factor": 140}, "multimodal": {"turn_penalty_factor": 70}, "logging": {"type": "std_out", "color": true, "file_name": "path_to_some_file.log"}, "service": {"proxy": "ipc:///tmp/meili"}, "grid": {"size": 500, "cache_size": 100240}}, "httpd": {"service": {"listen": "tcp://*:8002", "loopback": "ipc:///tmp/loopback", "interrupt": "ipc:///tmp/interrupt", "drain_seconds": 28, "shutdown_seconds": 1}}, "service_limits": {"auto": {"max_distance": 5000000.0, "max_locations": 20, "max_matrix_distance": 400000.0, "max_matrix_location_pairs": 2500}, "bus": {"max_distance": 5000000.0, "max_locations": 50, "max_matrix_distance": 400000.0, "max_matrix_location_pairs": 2500}, "taxi": {"max_distance": 5000000.0, "max_locations": 20, "max_matrix_distance": 400000.0, "max_matrix_location_pairs": 2500}, "pedestrian": {"max_distance": 250000.0, "max_locations": 50, "max_matrix_distance": 200000.0, "max_matrix_location_pairs": 2500, "min_transit_walking_distance": 1, "max_transit_walking_distance": 10000}, "motor_scooter": {"max_distance": 500000.0, "max_locations": 50, "max_matrix_distance": 200000.0, "max_matrix_location_pairs": 2500}, "motorcycle": {"max_distance": 500000.0, "max_locations": 50, "max_matrix_distance": 200000.0, "max_matrix_location_pairs": 2500}, "bicycle": {"max_distance": 500000.0, "max_locations": 50, "max_matrix_distance": 200000.0, "max_matrix_location_pairs": 2500}, "multimodal": {"max_distance": 500000.0, "max_locations": 50, "max_matrix_distance": 0.0, "max_matrix_location_pairs": 0}, "status": {"allow_verbose": false}, "transit": {"max_distance": 500000.0, "max_locations": 50, "max_matrix_distance": 200000.0, "max_matrix_location_pairs": 2500}, "truck": {"max_distance": 5000000.0, "max_locations": 20, "max_matrix_distance": 400000.0, "max_matrix_location_pairs": 2500}, "skadi": {"max_shape": 750000, "min_resample": 10.0}, "isochrone": {"max_contours": 4, "max_time_contour": 120, "max_distance": 25000.0, "max_locations": 1, "max_distance_contour": 200}, "trace": {"max_alternates": 3, "max_alternates_shape": 100, "max_distance": 200000.0, "max_gps_accuracy": 100.0, "max_search_radius": 100.0, "max_shape": 16000}, "bikeshare": {"max_distance": 500000.0, "max_locations": 50, "max_matrix_distance": 200000.0, "max_matrix_location_pairs": 2500}, "centroid": {"max_distance": 200000.0, "max_locations": 5}, "max_exclude_locations": 50, "max_reachability": 100, "max_radius": 200, "max_timedep_distance": 500000, "max_alternates": 2, "max_exclude_polygons_length": 10000, "max_distance_disable_hierarchy_culling": 0, "hierarchy_limits": {"allow_modification": false, "costmatrix": {"max_allowed_up_transitions": {"1": 400, "2": 100}}, "unidirectional_astar": {"max_allowed_up_transitions": {"1": 400, "2": 100}, "max_expand_within_distance": {"0": 100000000.0, "1": 100000, "2": 5000}}, "bidirectional_astar": {"max_allowed_up_transitions": {"1": 400, "2": 100}, "max_expand_within_distance": {"0": 100000000.0, "1": 20000, "2": 5000}}}, "allow_hard_exclusions": false}, "statsd": {"port": 8125, "prefix": "valhalla"}}
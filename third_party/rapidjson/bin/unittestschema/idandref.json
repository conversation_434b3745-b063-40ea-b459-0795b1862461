{"id": "http://example.com/root.json", "definitions": {"A": {"id": "#foo", "type": "integer"}, "B": {"id": "other.json", "definitions": {"X": {"id": "#bar", "type": "boolean"}, "Y": {"$ref": "#/definitions/X"}, "W": {"$ref": "#/definitions/Y"}, "Z": {"$ref": "#bar"}, "N": {"properties": {"NX": {"$ref": "#/definitions/X"}}}}}}, "properties": {"PA1": {"$ref": "http://example.com/root.json#/definitions/A"}, "PA2": {"$ref": "#/definitions/A"}, "PA3": {"$ref": "#foo"}, "PX1": {"$ref": "#/definitions/B/definitions/X"}, "PX2Y": {"$ref": "#/definitions/B/definitions/Y"}, "PX3Z": {"$ref": "#/definitions/B/definitions/Z"}, "PX4": {"$ref": "http://example.com/other.json#/definitions/X"}, "PX5": {"$ref": "other.json#/definitions/X"}, "PX6": {"$ref": "other.json#bar"}, "PX7W": {"$ref": "#/definitions/B/definitions/W"}, "PX8N": {"$ref": "#/definitions/B/definitions/N"}}}
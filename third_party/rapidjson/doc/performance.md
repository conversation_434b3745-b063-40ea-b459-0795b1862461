# Performance

There is a [native JSON benchmark collection] [1] which evaluates speed, memory usage and code size of various operations among 37 JSON libraries.

[1]: https://github.com/miloyip/nativejson-benchmark

The old performance article for RapidJSON 0.1 is provided [here](https://code.google.com/p/rapidjson/wiki/Performance).

Additionally, you may refer to the following third-party benchmarks.

## Third-party benchmarks

* [Basic benchmarks for miscellaneous C++ JSON parsers and generators](https://github.com/mloskot/json_benchmark) by <PERSON><PERSON><PERSON> (Jun 2013)
 * [casablanca](https://casablanca.codeplex.com/)
 * [json_spirit](https://github.com/cierelabs/json_spirit)
 * [jsoncpp](http://jsoncpp.sourceforge.net/)
 * [libjson](http://sourceforge.net/projects/libjson/)
 * [rapidjson](https://github.com/Tencent/rapidjson/)
 * [QJsonDocument](http://qt-project.org/doc/qt-5.0/qtcore/qjsondocument.html)
 
* [JSON Parser Benchmarking](http://chadaustin.me/2013/01/json-parser-benchmarking/) by <PERSON> (Jan 2013)
 * [sajson](https://github.com/chadaustin/sajson)
 * [rapidjson](https://github.com/Tencent/rapidjson/)
 * [vjson](https://code.google.com/p/vjson/)
 * [YAJL](http://lloyd.github.com/yajl/)
 * [Jansson](http://www.digip.org/jansson/)

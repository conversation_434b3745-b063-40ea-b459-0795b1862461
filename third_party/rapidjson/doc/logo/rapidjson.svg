<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<!-- Created with Inkscape (http://www.inkscape.org/) -->

<svg
   xmlns:dc="http://purl.org/dc/elements/1.1/"
   xmlns:cc="http://creativecommons.org/ns#"
   xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#"
   xmlns:svg="http://www.w3.org/2000/svg"
   xmlns="http://www.w3.org/2000/svg"
   xmlns:xlink="http://www.w3.org/1999/xlink"
   xmlns:sodipodi="http://sodipodi.sourceforge.net/DTD/sodipodi-0.dtd"
   xmlns:inkscape="http://www.inkscape.org/namespaces/inkscape"
   width="217.15039"
   height="60.831055"
   id="svg2"
   version="1.1"
   inkscape:version="0.48.4 r9939"
   sodipodi:docname="rapidjson.svg">
  <defs
     id="defs4">
    <linearGradient
       id="linearGradient3801">
      <stop
         style="stop-color:#000000;stop-opacity:1;"
         offset="0"
         id="stop3803" />
      <stop
         style="stop-color:#000000;stop-opacity:0;"
         offset="1"
         id="stop3805" />
    </linearGradient>
    <linearGradient
       inkscape:collect="always"
       xlink:href="#linearGradient3801"
       id="linearGradient3807"
       x1="81.25"
       y1="52.737183"
       x2="122.25"
       y2="52.737183"
       gradientUnits="userSpaceOnUse"
       gradientTransform="matrix(1.2378503,0,0,1.1662045,-226.99279,64.427324)" />
    <linearGradient
       inkscape:collect="always"
       xlink:href="#linearGradient3801"
       id="linearGradient3935"
       gradientUnits="userSpaceOnUse"
       gradientTransform="matrix(-1.4768835,0,0,2.2904698,246.48785,81.630301)"
       x1="81.25"
       y1="52.737183"
       x2="115.96579"
       y2="48.439766" />
    <linearGradient
       inkscape:collect="always"
       xlink:href="#linearGradient3801"
       id="linearGradient3947"
       gradientUnits="userSpaceOnUse"
       gradientTransform="matrix(1.2378503,0,0,1.1662045,-226.99279,-10.072676)"
       x1="81.25"
       y1="52.737183"
       x2="122.25"
       y2="52.737183" />
  </defs>
  <sodipodi:namedview
     id="base"
     pagecolor="#ffffff"
     bordercolor="#666666"
     borderopacity="1.0"
     inkscape:pageopacity="0.0"
     inkscape:pageshadow="2"
     inkscape:zoom="2"
     inkscape:cx="207.8959"
     inkscape:cy="-3.2283687"
     inkscape:document-units="px"
     inkscape:current-layer="layer1"
     showgrid="false"
     inkscape:window-width="1920"
     inkscape:window-height="1137"
     inkscape:window-x="-8"
     inkscape:window-y="-8"
     inkscape:window-maximized="1"
     fit-margin-top="10"
     fit-margin-left="10"
     fit-margin-right="10"
     fit-margin-bottom="10" />
  <metadata
     id="metadata7">
    <rdf:RDF>
      <cc:Work
         rdf:about="">
        <dc:format>image/svg+xml</dc:format>
        <dc:type
           rdf:resource="http://purl.org/dc/dcmitype/StillImage" />
        <dc:title></dc:title>
      </cc:Work>
    </rdf:RDF>
  </metadata>
  <g
     inkscape:label="Layer 1"
     inkscape:groupmode="layer"
     id="layer1"
     transform="translate(-39.132812,-38.772339)">
    <text
       sodipodi:linespacing="125%"
       id="text3939"
       y="79.862183"
       x="147.5"
       style="font-size:20px;font-style:normal;font-variant:normal;font-weight:normal;font-stretch:normal;text-align:center;line-height:125%;letter-spacing:0px;word-spacing:0px;writing-mode:lr-tb;text-anchor:middle;fill:#000000;fill-opacity:1;stroke:none;font-family:Microsoft JhengHei;-inkscape-font-specification:Microsoft JhengHei"
       xml:space="preserve"><tspan
         style="font-size:48px;font-style:normal;font-variant:normal;font-weight:normal;font-stretch:normal;font-family:Inconsolata;-inkscape-font-specification:Inconsolata"
         y="79.862183"
         x="147.5"
         id="tspan3941"
         sodipodi:role="line"><tspan
           id="tspan3943"
           style="font-size:42px;font-style:oblique;font-variant:normal;font-weight:normal;font-stretch:normal;text-align:center;line-height:125%;writing-mode:lr-tb;text-anchor:middle;font-family:Segoe UI;-inkscape-font-specification:Segoe UI Oblique">Rapid</tspan><tspan
           id="tspan3945"
           style="font-weight:bold;-inkscape-font-specification:Inconsolata Bold">JSON</tspan></tspan></text>
  </g>
</svg>

# 性能

有一个 [native JSON benchmark collection][1] 项目，能评估 37 个 JSON 库在不同操作下的速度、內存用量及代码大小。

[1]: https://github.com/miloyip/nativejson-benchmark

RapidJSON 0.1 版本的性能测试文章位于 [这里](https://code.google.com/p/rapidjson/wiki/Performance).

此外，你也可以参考以下这些第三方的评测。

## 第三方评测

* [Basic benchmarks for miscellaneous C++ JSON parsers and generators](https://github.com/mloskot/json_benchmark) by <PERSON><PERSON><PERSON> (Jun 2013)
 * [casablanca](https://casablanca.codeplex.com/)
 * [json_spirit](https://github.com/cierelabs/json_spirit)
 * [jsoncpp](http://jsoncpp.sourceforge.net/)
 * [libjson](http://sourceforge.net/projects/libjson/)
 * [rapidjson](https://github.com/Tencent/rapidjson/)
 * [QJsonDocument](http://qt-project.org/doc/qt-5.0/qtcore/qjsondocument.html)
 
* [JSON Parser Benchmarking](http://chadaustin.me/2013/01/json-parser-benchmarking/) by Chad Austin (Jan 2013)
 * [sajson](https://github.com/chadaustin/sajson)
 * [rapidjson](https://github.com/Tencent/rapidjson/)
 * [vjson](https://code.google.com/p/vjson/)
 * [YAJL](http://lloyd.github.com/yajl/)
 * [Jansson](http://www.digip.org/jansson/)

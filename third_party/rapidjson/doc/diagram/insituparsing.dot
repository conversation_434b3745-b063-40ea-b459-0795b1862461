digraph {
	compound=true
	fontname="Inconsolata, Consolas"
	fontsize=10
	margin="0,0"
	ranksep=0.2
	penwidth=0.5
	
	node [fontname="Inconsolata, Consolas", fontsize=10, penwidth=0.5]
	edge [fontname="Inconsolata, Consolas", fontsize=10, arrowhead=normal]

	{
		node [shape=record, fontsize="8", margin="0.04", height=0.2, color=gray]
		oldjson [label="\{|\"|m|s|g|\"|:|\"|H|e|l|l|o|\\|n|W|o|r|l|d|!|\"|,|\"|\\|u|0|0|7|3|t|a|r|s|\"|:|1|0|\}", xlabel="Before Parsing"]
		//newjson [label="\{|\"|<a>m|s|g|\\0|:|\"|<b>H|e|l|l|o|\\n|W|o|r|l|d|!|\\0|\"|,|\"|<c>s|t|a|r|s|\\0|t|a|r|s|:|1|0|\}", xlabel="After Parsing"]
		newjson [shape=plaintext, label=<
<table BORDER="0" CELLBORDER="1" CELLSPACING="0" CELLPADDING="2"><tr>
<td>{</td>
<td>"</td><td port="a">m</td><td>s</td><td>g</td><td bgcolor="yellow">\\0</td>
<td>:</td>
<td>"</td><td port="b">H</td><td>e</td><td>l</td><td>l</td><td>o</td><td bgcolor="yellow">\\n</td><td bgcolor="yellow">W</td><td bgcolor="yellow">o</td><td bgcolor="yellow">r</td><td bgcolor="yellow">l</td><td bgcolor="yellow">d</td><td bgcolor="yellow">!</td><td bgcolor="yellow">\\0</td><td>"</td>
<td>,</td>
<td>"</td><td port="c" bgcolor="yellow">s</td><td bgcolor="yellow">t</td><td bgcolor="yellow">a</td><td bgcolor="yellow">r</td><td bgcolor="yellow">s</td><td bgcolor="yellow">\\0</td><td>t</td><td>a</td><td>r</td><td>s</td>
<td>:</td>
<td>1</td><td>0</td>
<td>}</td>
</tr></table>
>, xlabel="After Parsing"]
	}

	subgraph cluster1 {
		margin="10,10"
		labeljust="left"
		label = "Document by In situ Parsing"
		style=filled
		fillcolor=gray95
		node [shape=Mrecord, style=filled, colorscheme=spectral7]
		
		root [label="{object|}", fillcolor=3]

		{			
			msg [label="{string|<a>}", fillcolor=5]
			helloworld [label="{string|<a>}", fillcolor=5]
			stars [label="{string|<a>}", fillcolor=5]
			ten [label="{number|10}", fillcolor=6]
		}
	}

	oldjson -> root [label=" ParseInsitu()" lhead="cluster1"]
	edge [arrowhead=vee]
	root -> { msg; stars }

	edge [arrowhead="none"]
	msg  -> helloworld
	stars -> ten

	{
		edge [arrowhead=vee, arrowtail=dot, arrowsize=0.5, dir=both, tailclip=false]
		msg:a:c -> newjson:a
		helloworld:a:c -> newjson:b
		stars:a:c -> newjson:c
	}

	//oldjson -> newjson [style=invis]
}
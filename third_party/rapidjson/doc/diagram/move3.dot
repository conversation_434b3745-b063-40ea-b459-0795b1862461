digraph {
	compound=true
	fontname="Inconsolata, Consolas"
	fontsize=10
	margin="0,0"
	ranksep=0.2
	penwidth=0.5
	forcelabels=true

	node [fontname="Inconsolata, Consolas", fontsize=10, penwidth=0.5]
	edge [fontname="Inconsolata, Consolas", fontsize=10, arrowhead=normal]

	subgraph cluster1 {
		margin="10,10"
		labeljust="left"
		label = "Before Moving"
		style=filled
		fillcolor=gray95

		node [shape=<PERSON><PERSON><PERSON>, style=filled, colorscheme=spectral7]

		c1 [label="{contacts:array|}", fillcolor=4]
		c11 [label="{|}"]
		c12 [label="{|}"]
		c13 [shape=none, label="...", style="solid"]
		o1 [label="{o:object|}", fillcolor=3]
		ghost [label="{o:object|}", style=invis]

		c1 -> o1 [style="dashed", constraint=false, label="AddMember"]

		edge [arrowhead=vee]
		c1 -> { c11; c12; c13 }
		o1 -> ghost [style=invis]
	}

	subgraph cluster2 {
		margin="10,10"
		labeljust="left"
		label = "After Moving"
		style=filled
		fillcolor=gray95

		node [shape=Mr<PERSON><PERSON>, style=filled, colorscheme=spectral7]

		c2 [label="{contacts:null|}", fillcolor=1]
		c3 [label="{array|}", fillcolor=4]
		c21 [label="{|}"]
		c22 [label="{|}"]
		c23 [shape="none", label="...", style="solid"]
		o2 [label="{o:object|}", fillcolor=3]
		cs [label="{string|\"contacts\"}", fillcolor=5]
		c2 -> o2 [style="dashed", constraint=false, label="AddMember", style=invis]

		edge [arrowhead=vee]
		c3 -> { c21; c22; c23 }
		o2 -> cs
		cs -> c3 [arrowhead=none]
	}
	ghost -> o2 [style=invis]
}

digraph {
	compound=true
	fontname="Inconsolata, Consolas"
	fontsize=10
	margin="0,0"
	ranksep=0.2
	nodesep=0.5
	penwidth=0.5
	colorscheme=spectral7
	
	node [shape=box, fontname="Inconsolata, Consolas", fontsize=10, penwidth=0.5, style=filled, fillcolor=white]
	edge [fontname="Inconsolata, Consolas", fontsize=10, penwidth=0.5]

	subgraph cluster1 {
		margin="10,10"
		labeljust="left"
		label = "SAX"
		style=filled
		fillcolor=6

		Reader -> Writer [style=invis]
	}

	subgraph cluster2 {
		margin="10,10"
		labeljust="left"
		label = "DOM"
		style=filled
		fillcolor=7

		Value
		Document
	}

	Handler [label="<<concept>>\nHandler"]

	{
		edge [arrowtail=onormal, dir=back]
		Value -> Document
		Handler -> Document
		Handler -> Writer
	}

	{
		edge [arrowhead=vee, style=dashed, constraint=false]
		Reader -> Handler [label="calls"]
		Value -> Handler [label="calls"]
		Document -> Reader [label="uses"]
	}
}
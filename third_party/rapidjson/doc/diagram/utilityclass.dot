digraph {
	rankdir=LR
	compound=true
	fontname="Inconsolata, Consolas"
	fontsize=10
	margin="0,0"
	ranksep=0.3
	nodesep=0.15
	penwidth=0.5
	colorscheme=spectral7
	
	node [shape=box, fontname="Inconsolata, Consolas", fontsize=10, penwidth=0.5, style=filled, fillcolor=white]
	edge [fontname="Inconsolata, Consolas", fontsize=10, penwidth=0.5]

	subgraph cluster0 {
		style=filled
		fillcolor=4

		Encoding [label="<<concept>>\nEncoding"]

		edge [arrowtail=onormal, dir=back]
		Encoding -> { UTF8; UTF16; UTF32; ASCII; AutoUTF }
		UTF16 -> { UTF16LE; UTF16BE }
		UTF32 -> { UTF32LE; UTF32BE }
	}

	subgraph cluster1 {
		style=filled
		fillcolor=5

		Stream [label="<<concept>>\nStream"]
		InputByteStream [label="<<concept>>\nInputByteStream"]
		OutputByteStream [label="<<concept>>\nOutputByteStream"]

		edge [arrowtail=onormal, dir=back]
		Stream -> { 
			StringStream; InsituStringStream; StringBuffer; 
			EncodedInputStream; EncodedOutputStream; 
			AutoUTFInputStream; AutoUTFOutputStream 
			InputByteStream; OutputByteStream
		}

		InputByteStream ->	{ MemoryStream; FlieReadStream }
		OutputByteStream -> { MemoryBuffer; FileWriteStream } 
	}

	subgraph cluster2 {
		style=filled
		fillcolor=3

		Allocator [label="<<concept>>\nAllocator"]

		edge [arrowtail=onormal, dir=back]
		Allocator -> { CrtAllocator; MemoryPoolAllocator }
	}

	{
		edge [arrowtail=odiamond, arrowhead=vee, dir=both]
		EncodedInputStream -> InputByteStream
		EncodedOutputStream -> OutputByteStream
		AutoUTFInputStream -> InputByteStream
		AutoUTFOutputStream -> OutputByteStream
		MemoryPoolAllocator -> Allocator [label="base", tailport=s]
	}

	{
		edge [arrowhead=vee, style=dashed]
		AutoUTFInputStream -> AutoUTF
		AutoUTFOutputStream -> AutoUTF
	}

	//UTF32LE -> Stream [style=invis]
}
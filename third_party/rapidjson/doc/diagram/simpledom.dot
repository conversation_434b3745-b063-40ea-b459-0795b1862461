digraph {
	compound=true
	fontname="Inconsolata, Consolas"
	fontsize=10
	margin="0,0"
	ranksep=0.2
	penwidth=0.5
	
	node [fontname="Inconsolata, Consolas", fontsize=10, penwidth=0.5]
	edge [fontname="Inconsolata, Consolas", fontsize=10, arrowhead=normal]

	{
		node [shape=record, fontsize="8", margin="0.04", height=0.2, color=gray]
		srcjson [label="\{|\"|p|r|o|j|e|c|t|\"|:|\"|r|a|p|i|d|j|s|o|n|\"|,|\"|s|t|a|r|s|\"|:|1|0|\}"]
		dstjson [label="\{|\"|p|r|o|j|e|c|t|\"|:|\"|r|a|p|i|d|j|s|o|n|\"|,|\"|s|t|a|r|s|\"|:|1|1|\}"]
	}

	{
		node [shape="box", style="filled", fillcolor="gray95"]
		Document2 [label="(Modified) Document"]
		Writer
	}

	subgraph cluster1 {
		margin="10,10"
		labeljust="left"
		label = "Document"
		style=filled
		fillcolor=gray95
		node [shape=Mrecord, style=filled, colorscheme=spectral7]
		
		root [label="{object|}", fillcolor=3]

		{			
			project [label="{string|\"project\"}", fillcolor=5]
			rapidjson [label="{string|\"rapidjson\"}", fillcolor=5]
			stars [label="{string|\"stars\"}", fillcolor=5]
			ten [label="{number|10}", fillcolor=6]
		}

		edge [arrowhead=vee]
		root -> { project; stars }

		edge [arrowhead="none"]
		project -> rapidjson
		stars -> ten
	}

	srcjson -> root [label=" Parse()", lhead="cluster1"]

	ten -> Document2 [label=" Increase \"stars\"", ltail="cluster1" ]
	Document2  -> Writer [label=" Traverse DOM by Accept()"]
	Writer -> dstjson [label=" Output to StringBuffer"]
}
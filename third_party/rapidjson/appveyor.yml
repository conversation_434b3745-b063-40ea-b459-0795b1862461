version: 1.1.0.{build}

configuration:
- Debug
- Release

environment:
  matrix:
  # - VS_VERSION: 9 2008
  #   VS_PLATFORM: win32
  # - VS_VERSION: 9 2008
  #   VS_PLATFORM: x64
  - APPVEYOR_BUILD_WORKER_IMAGE: Visual Studio 2013
    VS_VERSION: 10 2010
    VS_PLATFORM: win32
    CXX11: OFF
    CXX17: OFF
    MEMBERSMAP: OFF
  - APPVEYOR_BUILD_WORKER_IMAGE: Visual Studio 2013
    VS_VERSION: 10 2010
    VS_PLATFORM: x64
    CXX11: OFF
    CXX17: OFF
    MEMBERSMAP: ON
  - APPVEYOR_BUILD_WORKER_IMAGE: Visual Studio 2013
    VS_VERSION: 11 2012
    VS_PLATFORM: win32
    CXX11: OFF
    CXX17: OFF
    MEMBERSMAP: ON
  - APPVEYOR_BUILD_WORKER_IMAGE: Visual Studio 2013
    VS_VERSION: 11 2012
    VS_PLATFORM: x64
    CXX11: OFF
    CXX17: OFF
    MEMBERSMAP: OFF
  - APPVEYOR_BUILD_WORKER_IMAGE: Visual Studio 2013
    VS_VERSION: 12 2013
    VS_PLATFORM: win32
    CXX11: OFF
    CXX17: OFF
    MEMBERSMAP: OFF
  - APPVEYOR_BUILD_WORKER_IMAGE: Visual Studio 2013
    VS_VERSION: 12 2013
    VS_PLATFORM: x64
    CXX11: OFF
    CXX17: OFF
    MEMBERSMAP: ON
  - APPVEYOR_BUILD_WORKER_IMAGE: Visual Studio 2015
    VS_VERSION: 14 2015
    VS_PLATFORM: win32
    CXX11: OFF
    CXX17: OFF
    MEMBERSMAP: ON
  - APPVEYOR_BUILD_WORKER_IMAGE: Visual Studio 2015
    VS_VERSION: 14 2015
    VS_PLATFORM: x64
    CXX11: OFF
    CXX17: OFF
    MEMBERSMAP: OFF
  - APPVEYOR_BUILD_WORKER_IMAGE: Visual Studio 2017
    VS_VERSION: 15 2017
    VS_PLATFORM: win32
    CXX11: OFF
    CXX17: OFF
    MEMBERSMAP: OFF
  - APPVEYOR_BUILD_WORKER_IMAGE: Visual Studio 2017
    VS_VERSION: 15 2017
    VS_PLATFORM: x64
    CXX11: OFF
    CXX17: OFF
    MEMBERSMAP: ON
  - APPVEYOR_BUILD_WORKER_IMAGE: Visual Studio 2017
    VS_VERSION: 15 2017
    VS_PLATFORM: x64
    CXX11: ON
    CXX17: OFF
    MEMBERSMAP: OFF
  - APPVEYOR_BUILD_WORKER_IMAGE: Visual Studio 2017
    VS_VERSION: 15 2017
    VS_PLATFORM: x64
    CXX11: OFF
    CXX17: ON
    MEMBERSMAP: OFF
  - APPVEYOR_BUILD_WORKER_IMAGE: Visual Studio 2019
    VS_VERSION: 16 2019
    VS_PLATFORM: x64
    CXX11: OFF
    CXX17: ON
    MEMBERSMAP: ON

before_build:
- git submodule update --init --recursive
- cmake -H. -BBuild/VS -G "Visual Studio %VS_VERSION%" -DCMAKE_GENERATOR_PLATFORM=%VS_PLATFORM% -DCMAKE_VERBOSE_MAKEFILE=ON -DBUILD_SHARED_LIBS=true -DRAPIDJSON_BUILD_CXX11=%CXX11% -DRAPIDJSON_BUILD_CXX17=%CXX17% -DRAPIDJSON_USE_MEMBERSMAP=%MEMBERSMAP% -Wno-dev

build:
  project: Build\VS\RapidJSON.sln
  parallel: true
  verbosity: minimal

test_script:
- cd Build\VS && if %CONFIGURATION%==Debug (ctest --verbose -E perftest --build-config %CONFIGURATION%) else (ctest --verbose --build-config %CONFIGURATION%)

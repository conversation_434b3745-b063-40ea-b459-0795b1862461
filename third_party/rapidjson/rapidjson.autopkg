nuget {
	//Usage:  Write-NuGetPackage rapidjson.autopkg -defines:MYVERSION=1.1.0
	//Be sure you are running Powershell 3.0 and have the CoApp powershell extensions installed properly.
	nuspec {
		id = rapidjson;
		version : ${MYVERSION};
		title: "rapidjson";
		authors: {"https://github.com/Tencent/rapidjson/releases/tag/v1.1.0"};
		owners: {"@lsantos (github)"};
		licenseUrl: "https://github.com/Tencent/rapidjson/blob/master/license.txt";
		projectUrl: "https://github.com/Tencent/rapidjson/";
		iconUrl: "https://cdn1.iconfinder.com/data/icons/fatcow/32x32/json.png";
		requireLicenseAcceptance:false;
		summary: @"A fast JSON parser/generator for C++ with both SAX/DOM style API";
		
		// if you need to span several lines you can prefix a string with an @ symbol (exactly like c# does).
		description: @"Rapidjson is an attempt to create the fastest JSON parser and generator.

              - Small but complete. Supports both SAX and DOM style API. SAX parser only a few hundred lines of code.
              - Fast. In the order of magnitude of strlen(). Optionally supports SSE2/SSE4.2 for acceleration.
              - Self-contained. Minimal dependency on standard libraries. No BOOST, not even STL.
              - Compact. Each JSON value is 16 or 20 bytes for 32 or 64-bit machines respectively (excluding text string storage). With the custom memory allocator, parser allocates memory compactly during parsing.
              - Full  RFC4627 compliance. Supports UTF-8, UTF-16 and UTF-32.
              - Support both in-situ parsing (directly decode strings into the source JSON text) and non-destructive parsing (decode strings into new buffers).
              - Parse number to int/unsigned/int64_t/uint64_t/double depending on input
              - Support custom memory allocation. Also, the default memory pool allocator can also be supplied with a user buffer (such as a buffer allocated on user's heap or - programme stack) to minimize allocation.

              As the name implies, rapidjson is inspired by rapidxml.";
		
		releaseNotes: @"
Added
	Add Value::XXXMember(...) overloads for std::string (#335)

Fixed
	Include rapidjson.h for all internal/error headers.
	Parsing some numbers incorrectly in full-precision mode (kFullPrecisionParseFlag) (#342)
	Fix alignment of 64bit platforms (#328)
	Fix MemoryPoolAllocator::Clear() to clear user-buffer (0691502)

Changed
	CMakeLists for include as a thirdparty in projects (#334, #337)
	Change Document::ParseStream() to use stack allocator for Reader (ffbe386)";

		copyright: "Copyright 2015";
		tags: { native, coapp, JSON, nativepackage };
		language: en-US;
	};
	
	dependencies {
		packages : {
			//TODO:  Add dependencies here in [pkg.name]/[version] form per newline		
			//zlib/[1.2.8],			
		};
	}
	
	// the files that go into the content folders
	files {	
		#defines {
			SDK_ROOT 	 = .\;			
		}

		// grab all the files in the include folder
		// the folder that contains all the .h files will 
		// automatically get added to the Includes path.
		nestedinclude += {
			#destination = ${d_include}rapidjson;
			"${SDK_ROOT}include\rapidjson\**\*.h"
		};
	};
	
	targets {
		// We're trying to be standard about these sorts of thing. (Will help with config.h later :D)
		//Defines += HAS_EQCORE;
		// Fix creating the package with Raggles' fork of CoApp
		Includes += "$(MSBuildThisFileDirectory)../..${d_include}";
	};
}
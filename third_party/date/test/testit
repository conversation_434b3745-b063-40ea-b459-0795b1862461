#!/bin/sh
# The MIT License (MIT)
#
# Copyright (c) 2015, 2016 <PERSON>
#
# Permission is hereby granted, free of charge, to any person obtaining a copy
# of this software and associated documentation files (the "Software"), to deal
# in the Software without restriction, including without limitation the rights
# to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
# copies of the Software, and to permit persons to whom the Software is
# furnished to do so, subject to the following conditions:
#
# The above copyright notice and this permission notice shall be included in all
# copies or substantial portions of the Software.
#
# THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
# IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
# FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
# AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
# LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
# OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE
# SOFTWARE.

currentpath=`pwd`
origpath=$currentpath
currentdir=`basename $currentpath`
while [ $currentdir != "test" ]; do
	if [ $currentdir = "/" ]
	then
		echo "current directory must be in or under \"test\"."
		exit 1
	fi
	cd ..
	currentpath=`pwd`
	currentdir=`basename $currentpath`
done

cd ..
ROOT=`pwd`
cd $origpath

if [ -z "$CXX" ]
then
	CXX=clang++
fi

if [ -z "$CXX_LANG" ]
then
    CXX_LANG=c++14
fi
OPTIONS="-std=${CXX_LANG} $OPTIONS -I$ROOT -Wall $ROOT/src/tz.cpp -lcurl"

echo $ROOT
HEADER_INCLUDE="-I$ROOT/include -I$ROOT/include/date"

case $TRIPLE in
  *-*-mingw* | *-*-cygwin* | *-*-win*)
	TEST_EXE=test.exe
    ;;
  *)
    TEST_EXE=a.out
    ;;
esac

case $(uname -s) in
   NetBSD)
     THREAD_FLAGS=-lpthread
     ;;
esac

FAIL=0
PASS=0
UNIMPLEMENTED=0
IMPLEMENTED_FAIL=0
IMPLEMENTED_PASS=0

afunc() {
	fail=0
	pass=0
	if (ls ${TEST_PREFIX}*fail.cpp > /dev/null 2>&1)
	then
		for FILE in $(ls ${TEST_PREFIX}*fail.cpp); do
			if $CXX $OPTIONS $HEADER_INCLUDE $SOURCE_LIB $FILE $LIBS -o ./$TEST_EXE > /dev/null 2>&1
			then
				rm ./$TEST_EXE
				echo "$FILE should not compile"
				fail=$(($fail+1))
			else
				pass=$(($pass+1))
			fi
		done
	fi

	if (ls ${TEST_PREFIX}*pass.cpp > /dev/null 2>&1)
	then
		for FILE in $(ls ${TEST_PREFIX}*pass.cpp); do
            if [ "$VERBOSE" ]
            then
             	echo "Running test: " $FILE
            fi
			if $CXX $OPTIONS $HEADER_INCLUDE $SOURCE_LIB $FILE $LIBS $(test $1 = no || echo $THREAD_FLAGS) -o ./$TEST_EXE
			then
				if ./$TEST_EXE
				then
					rm ./$TEST_EXE
					pass=$(($pass+1))
				else
					echo "`pwd`/$FILE failed at run time"
					echo "Compile line was:" $CXX $OPTIONS $HEADER_INCLUDE $SOURCE_LIB $FILE $LIBS $(test $1 = no || echo $THREAD_FLAGS)
					fail=$(($fail+1))
					rm ./$TEST_EXE
				fi
			else
				echo "`pwd`/$FILE failed to compile"
				echo "Compile line was:" $CXX $OPTIONS $HEADER_INCLUDE $SOURCE_LIB $FILE $LIBS $(test $1 = no || echo $THREAD_FLAGS)
				fail=$(($fail+1))
			fi
		done
	fi

	if [ $fail -gt 0 ]
	then
		echo "failed $fail tests in `pwd`"
		IMPLEMENTED_FAIL=$(($IMPLEMENTED_FAIL+1))
	fi
	if [ $pass -gt 0 ]
	then
		echo "passed $pass tests in `pwd`"
		if [ $fail -eq 0 ]
		then
			IMPLEMENTED_PASS=$((IMPLEMENTED_PASS+1))
		fi
	fi
	if [ $fail -eq 0 -a $pass -eq 0 ]
	then
		echo "not implemented:  `pwd`"
		UNIMPLEMENTED=$(($UNIMPLEMENTED+1))
	fi

	FAIL=$(($FAIL+$fail))
	PASS=$(($PASS+$pass))

	for FILE in *
	do
		if [ -d "$FILE" ];
		then
			cd $FILE
			if [ $FILE = thread -o $1 = yes ]; then
				afunc yes
			else
				afunc no
			fi
			cd ..
		fi
	done
}

afunc no

echo "****************************************************"
echo "Results for `pwd`:"
echo "using `$CXX --version`"
echo "with $OPTIONS $HEADER_INCLUDE $SOURCE_LIB"
echo "----------------------------------------------------"
echo "sections without tests   : $UNIMPLEMENTED"
echo "sections with failures   : $IMPLEMENTED_FAIL"
echo "sections without failures: $IMPLEMENTED_PASS"
echo "                       +   ----"
echo "total number of sections : $(($UNIMPLEMENTED+$IMPLEMENTED_FAIL+$IMPLEMENTED_PASS))"
echo "----------------------------------------------------"
echo "number of tests failed   : $FAIL"
echo "number of tests passed   : $PASS"
echo "                       +   ----"
echo "total number of tests    : $(($FAIL+$PASS))"
echo "****************************************************"

exit $FAIL


# Osmium example programs

The programs in this directory are intended as examples for developers. They
contain extensive comments explaining what's going on. Note that the examples
only cover a small part of what Osmium can do, you should also read the manuals
and API documentation.

All programs can be run without arguments and they will tell you how to use
them.

## Very simple examples

* `osmium_read`
* `osmium_count`
* `osmium_debug`
* `osmium_tiles`

## Still reasonably simple examples

* `osmium_amenity_list`
* `osmium_read_with_progress`
* `osmium_filter_discussions`
* `osmium_convert`
* `osmium_pub_names`
* `osmium_road_length`

## More advanced examples

* `osmium_area_test`
* `osmium_create_pois`
* `osmium_tags_filter`

## Even more advanced examples

* `osmium_change_tags`
* `osmium_location_cache_create`
* `osmium_location_cache_use`
* `osmium_dump_internal`
* `osmium_index_lookup`

## License

The code in these example files is released into the Public Domain. Feel free
to copy the code and build on it.


name: clang-tidy

on: workflow_dispatch

jobs:
  clang-tidy:
    runs-on: ubuntu-latest
    strategy:
      fail-fast: false
      matrix:
        image: ["debian:bullseye", "debian:testing", "debian:experimental"]
        include:
          - image: "debian:bullseye"
            clang: 11
          - image: "debian:testing"
            clang: 15
          - image: "debian:experimental"
            clang: 15
    container:
      image: ${{ matrix.image }}
      env:
        BUILD_TYPE: Dev
        CC: clang-${{ matrix.clang }}
        CXX: clang++-${{ matrix.clang }}
        CPP_VERSION: c++11
        WITH_PROJ: ON
        APT_LISTCHANGES_FRONTEND: none
        DEBIAN_FRONTEND: noninteractive
    steps:
      - name: Prepare container (apt)
        run: |
          apt-get update -qq
          apt-get install -yq \
                  clang-${{ matrix.clang }} \
                  clang-tidy-${{ matrix.clang }} \
                  cmake \
                  git \
                  libboost-dev \
                  libbz2-dev \
                  libexpat1-dev \
                  libgdal-dev \
                  libgeos++-dev \
                  liblz4-dev \
                  libproj-dev \
                  libsparsehash-dev \
                  make \
                  zlib1g-dev
        shell: bash
      # Use v1 of checkout because v3 doesn't work with submodules
      - uses: actions/checkout@v1
        with:
          submodules: true
      - uses: ./.github/actions/install-protozero
      - uses: ./.github/actions/cmake
      - name: clang-tidy
        run: make clang-tidy | tee clang-tidy-${{ matrix.clang }}.log
        shell: bash
        working-directory: build
      - name: Upload Log
        uses: actions/upload-artifact@v3
        if: always()
        with:
          name: clang-tidy-${{ matrix.clang }}-log
          path: build/clang-tidy-${{ matrix.clang }}.log


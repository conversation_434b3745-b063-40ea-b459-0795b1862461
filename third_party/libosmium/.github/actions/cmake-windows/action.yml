name: Windows CMake

runs:
  using: composite
  steps:
    - name: Create build directory
      run: mkdir build
      shell: bash
    - name: Configure
      run: |
        cmake -LA .. \
              -DCMAKE_TOOLCHAIN_FILE=C:/vcpkg/scripts/buildsystems/vcpkg.cmake \
              -DBUILD_HEADERS=OFF \
              -DBUILD_BENCHMARKS=ON \
              -DOsmium_DEBUG=TRUE \
              -DPROTOZERO_INCLUDE_DIR=${GITHUB_WORKSPACE}/../protozero/include
      shell: bash
      working-directory: build


#-----------------------------------------------------------------------------
#
#  This file describes messages that <PERSON><PERSON><PERSON><PERSON> should suppress, because they
#  are about problems outside Libosmium that we can't fix anyway.
#
#  See http://valgrind.org/docs/manual/manual-core.html#manual-core.suppress
#
#-----------------------------------------------------------------------------

{
   dl_error1
   Memcheck:Cond
   fun:index
   fun:expand_dynamic_string_token
   fun:fillin_rpath
   fun:_dl_init_paths
   fun:dl_main
   fun:_dl_sysdep_start
   fun:_dl_start
}
{
   dl_error2
   Memcheck:Cond
   fun:index
   fun:expand_dynamic_string_token
   fun:_dl_map_object
   fun:map_doit
   fun:_dl_catch_error
   fun:do_preload
   fun:dl_main
   fun:_dl_sysdep_start
   fun:_dl_start
}
{
   libpoppler_leak
   Memcheck:Leak
   fun:malloc
   fun:gmalloc
   fun:copyString
}
{
   tmpfile
   Memcheck:Leak
   fun:malloc
   fun:fdopen@@GLIBC_*
   fun:tmpfile@@GLIBC_*
}

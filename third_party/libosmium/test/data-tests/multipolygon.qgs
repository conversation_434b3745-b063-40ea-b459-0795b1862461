<!DOCTYPE qgis PUBLIC 'http://mrcc.com/qgis.dtd' 'SYSTEM'>
<qgis projectname="" version="2.18.0">
  <title></title>
  <autotransaction active="0"/>
  <evaluateDefaultValues active="0"/>
  <layer-tree-group expanded="1" checked="Qt::Checked" name="">
    <customproperties/>
    <layer-tree-layer expanded="1" checked="Qt::Checked" id="perrors20140228163658956" name="Error Points">
      <customproperties/>
    </layer-tree-layer>
    <layer-tree-layer expanded="1" checked="Qt::Checked" id="lerrors20140228172357933" name="Error Lines">
      <customproperties/>
    </layer-tree-layer>
    <layer-tree-layer expanded="1" checked="Qt::Checked" id="multipolygons20140221151811742" name="multipolygons">
      <customproperties/>
    </layer-tree-layer>
    <layer-tree-group expanded="1" checked="Qt::Checked" name="Overview">
      <customproperties>
        <property key="embedded" value="1"/>
        <property key="embedded-invisible-layers"/>
        <property key="embedded_project" value="@OSM_TESTDATA@/grid/tests.qgs"/>
      </customproperties>
    </layer-tree-group>
    <layer-tree-group expanded="1" checked="Qt::Checked" name="Test Framework">
      <customproperties>
        <property key="embedded" value="1"/>
        <property key="embedded-invisible-layers"/>
        <property key="embedded_project" value="@OSM_TESTDATA@/grid/tests.qgs"/>
      </customproperties>
    </layer-tree-group>
  </layer-tree-group>
  <relations/>
  <mapcanvas>
    <units>degrees</units>
    <extent>
      <xmin>0.77500024999999972</xmin>
      <ymin>0.97250000000000003</ymin>
      <xmax>10.22498975000000065</xmax>
      <ymax>2.12750000000000039</ymax>
    </extent>
    <rotation>0</rotation>
    <projections>0</projections>
    <destinationsrs>
      <spatialrefsys>
        <proj4>+proj=longlat +datum=WGS84 +no_defs</proj4>
        <srsid>3452</srsid>
        <srid>4326</srid>
        <authid>EPSG:4326</authid>
        <description>WGS 84</description>
        <projectionacronym>longlat</projectionacronym>
        <ellipsoidacronym>WGS84</ellipsoidacronym>
        <geographicflag>true</geographicflag>
      </spatialrefsys>
    </destinationsrs>
    <rendermaptile>0</rendermaptile>
    <layer_coordinate_transform_info/>
  </mapcanvas>
  <layer-tree-canvas>
    <custom-order enabled="0">
      <item>perrors20140228163658956</item>
      <item>lerrors20140228172357933</item>
      <item>multipolygons20140221151811742</item>
      <item>labels20140221110837046</item>
      <item>nodes20140221105814325</item>
      <item>ways20140221105814338</item>
      <item>multipolygons20140224153504671</item>
      <item>titles20140220153751089</item>
      <item>grid20140213154358889</item>
    </custom-order>
  </layer-tree-canvas>
  <legend updateDrawingOrder="true">
    <legendlayer drawingOrder="-1" open="true" checked="Qt::Checked" name="Error Points" showFeatureCount="0">
      <filegroup open="true" hidden="false">
        <legendlayerfile isInOverview="0" layerid="perrors20140228163658956" visible="1"/>
      </filegroup>
    </legendlayer>
    <legendlayer drawingOrder="-1" open="true" checked="Qt::Checked" name="Error Lines" showFeatureCount="0">
      <filegroup open="true" hidden="false">
        <legendlayerfile isInOverview="0" layerid="lerrors20140228172357933" visible="1"/>
      </filegroup>
    </legendlayer>
    <legendlayer drawingOrder="-1" open="true" checked="Qt::Checked" name="multipolygons" showFeatureCount="0">
      <filegroup open="true" hidden="false">
        <legendlayerfile isInOverview="0" layerid="multipolygons20140221151811742" visible="1"/>
      </filegroup>
    </legendlayer>
    <legendgroup embedded="1" open="true" checked="Qt::Checked" name="Overview" project="@OSM_TESTDATA@/grid/tests.qgs"/>
    <legendgroup embedded="1" open="true" checked="Qt::Checked" name="Test Framework" project="@OSM_TESTDATA@/grid/tests.qgs"/>
  </legend>
  <projectlayers>
    <maplayer simplifyAlgorithm="0" minimumScale="-4.65661e-10" maximumScale="1e+08" simplifyDrawingHints="1" readOnly="0" minLabelScale="0" maxLabelScale="1e+08" simplifyDrawingTol="1" geometry="Line" simplifyMaxScale="1" type="vector" hasScaleBasedVisibilityFlag="0" simplifyLocal="1" scaleBasedLabelVisibilityFlag="0">
      <id>lerrors20140228172357933</id>
      <datasource>dbname='./multipolygon.db' table="lerrors" (GEOMETRY) sql=</datasource>
      <keywordList>
        <value></value>
      </keywordList>
      <layername>Error Lines</layername>
      <srs>
        <spatialrefsys>
          <proj4>+proj=longlat +datum=WGS84 +no_defs</proj4>
          <srsid>3452</srsid>
          <srid>4326</srid>
          <authid>EPSG:4326</authid>
          <description>WGS 84</description>
          <projectionacronym>longlat</projectionacronym>
          <ellipsoidacronym>WGS84</ellipsoidacronym>
          <geographicflag>true</geographicflag>
        </spatialrefsys>
      </srs>
      <provider encoding="System">spatialite</provider>
      <previewExpression>COALESCE( "OGC_FID", '&lt;NULL>' )</previewExpression>
      <vectorjoins/>
      <layerDependencies/>
      <expressionfields/>
      <defaults>
        <default field="ogc_fid" expression=""/>
        <default field="obj_type" expression=""/>
        <default field="obj_id" expression=""/>
        <default field="nodes" expression=""/>
        <default field="id1" expression=""/>
        <default field="id2" expression=""/>
        <default field="problem" expression=""/>
      </defaults>
      <map-layer-style-manager current="">
        <map-layer-style name=""/>
      </map-layer-style-manager>
      <edittypes>
        <edittype widgetv2type="TextEdit" name="ogc_fid">
          <widgetv2config IsMultiline="0" fieldEditable="1" constraint="" UseHtml="0" labelOnTop="0" constraintDescription="" notNull="0"/>
        </edittype>
        <edittype widgetv2type="TextEdit" name="obj_type">
          <widgetv2config IsMultiline="0" fieldEditable="1" constraint="" UseHtml="0" labelOnTop="0" constraintDescription="" notNull="0"/>
        </edittype>
        <edittype widgetv2type="TextEdit" name="obj_id">
          <widgetv2config IsMultiline="0" fieldEditable="1" constraint="" UseHtml="0" labelOnTop="0" constraintDescription="" notNull="0"/>
        </edittype>
        <edittype widgetv2type="TextEdit" name="nodes">
          <widgetv2config IsMultiline="0" fieldEditable="1" constraint="" UseHtml="0" labelOnTop="0" constraintDescription="" notNull="0"/>
        </edittype>
        <edittype widgetv2type="TextEdit" name="id1">
          <widgetv2config IsMultiline="0" fieldEditable="1" constraint="" UseHtml="0" labelOnTop="0" constraintDescription="" notNull="0"/>
        </edittype>
        <edittype widgetv2type="TextEdit" name="id2">
          <widgetv2config IsMultiline="0" fieldEditable="1" constraint="" UseHtml="0" labelOnTop="0" constraintDescription="" notNull="0"/>
        </edittype>
        <edittype widgetv2type="TextEdit" name="problem">
          <widgetv2config IsMultiline="0" fieldEditable="1" constraint="" UseHtml="0" labelOnTop="0" constraintDescription="" notNull="0"/>
        </edittype>
      </edittypes>
      <renderer-v2 attr="problem" forceraster="0" symbollevels="0" type="categorizedSymbol" enableorderby="0">
        <categories>
          <category render="true" symbol="0" value="intersection" label="intersection"/>
          <category render="true" symbol="1" value="role_should_be_outer" label="role_should_be_outer"/>
          <category render="true" symbol="2" value="role_should_be_inner" label="role_should_be_inner"/>
          <category render="true" symbol="3" value="duplicate_segment" label="duplicate_segment"/>
          <category render="true" symbol="4" value="duplicate_way" label="duplicate_way"/>
          <category render="true" symbol="5" value="inner_with_same_tags" label="inner_with_same_tags"/>
          <category render="true" symbol="6" value="way_in_multiple_rings" label="way_in_multiple_rings"/>
        </categories>
        <symbols>
          <symbol alpha="1" clip_to_extent="1" type="line" name="0">
            <layer pass="0" class="SimpleLine" locked="0">
              <prop k="capstyle" v="square"/>
              <prop k="customdash" v="5;2"/>
              <prop k="customdash_map_unit_scale" v="0,0,0,0,0,0"/>
              <prop k="customdash_unit" v="MM"/>
              <prop k="draw_inside_polygon" v="0"/>
              <prop k="joinstyle" v="bevel"/>
              <prop k="line_color" v="255,0,0,255"/>
              <prop k="line_style" v="solid"/>
              <prop k="line_width" v="0.5"/>
              <prop k="line_width_unit" v="MM"/>
              <prop k="offset" v="0"/>
              <prop k="offset_map_unit_scale" v="0,0,0,0,0,0"/>
              <prop k="offset_unit" v="MM"/>
              <prop k="use_custom_dash" v="0"/>
              <prop k="width_map_unit_scale" v="0,0,0,0,0,0"/>
            </layer>
          </symbol>
          <symbol alpha="1" clip_to_extent="1" type="line" name="1">
            <layer pass="0" class="SimpleLine" locked="0">
              <prop k="capstyle" v="square"/>
              <prop k="customdash" v="5;2"/>
              <prop k="customdash_map_unit_scale" v="0,0,0,0,0,0"/>
              <prop k="customdash_unit" v="MM"/>
              <prop k="draw_inside_polygon" v="0"/>
              <prop k="joinstyle" v="bevel"/>
              <prop k="line_color" v="255,122,33,255"/>
              <prop k="line_style" v="solid"/>
              <prop k="line_width" v="0.5"/>
              <prop k="line_width_unit" v="MM"/>
              <prop k="offset" v="0"/>
              <prop k="offset_map_unit_scale" v="0,0,0,0,0,0"/>
              <prop k="offset_unit" v="MM"/>
              <prop k="use_custom_dash" v="0"/>
              <prop k="width_map_unit_scale" v="0,0,0,0,0,0"/>
            </layer>
          </symbol>
          <symbol alpha="1" clip_to_extent="1" type="line" name="2">
            <layer pass="0" class="SimpleLine" locked="0">
              <prop k="capstyle" v="square"/>
              <prop k="customdash" v="5;2"/>
              <prop k="customdash_map_unit_scale" v="0,0,0,0,0,0"/>
              <prop k="customdash_unit" v="MM"/>
              <prop k="draw_inside_polygon" v="0"/>
              <prop k="joinstyle" v="bevel"/>
              <prop k="line_color" v="255,122,33,255"/>
              <prop k="line_style" v="dash"/>
              <prop k="line_width" v="0.5"/>
              <prop k="line_width_unit" v="MM"/>
              <prop k="offset" v="0"/>
              <prop k="offset_map_unit_scale" v="0,0,0,0,0,0"/>
              <prop k="offset_unit" v="MM"/>
              <prop k="use_custom_dash" v="0"/>
              <prop k="width_map_unit_scale" v="0,0,0,0,0,0"/>
            </layer>
          </symbol>
          <symbol alpha="1" clip_to_extent="1" type="line" name="3">
            <layer pass="0" class="SimpleLine" locked="0">
              <prop k="capstyle" v="square"/>
              <prop k="customdash" v="5;2"/>
              <prop k="customdash_map_unit_scale" v="0,0,0,0,0,0"/>
              <prop k="customdash_unit" v="MM"/>
              <prop k="draw_inside_polygon" v="0"/>
              <prop k="joinstyle" v="bevel"/>
              <prop k="line_color" v="43,219,0,255"/>
              <prop k="line_style" v="dot"/>
              <prop k="line_width" v="0.5"/>
              <prop k="line_width_unit" v="MM"/>
              <prop k="offset" v="0"/>
              <prop k="offset_map_unit_scale" v="0,0,0,0,0,0"/>
              <prop k="offset_unit" v="MM"/>
              <prop k="use_custom_dash" v="0"/>
              <prop k="width_map_unit_scale" v="0,0,0,0,0,0"/>
            </layer>
          </symbol>
          <symbol alpha="1" clip_to_extent="1" type="line" name="4">
            <layer pass="0" class="SimpleLine" locked="0">
              <prop k="capstyle" v="square"/>
              <prop k="customdash" v="5;2"/>
              <prop k="customdash_map_unit_scale" v="0,0,0,0,0,0"/>
              <prop k="customdash_unit" v="MM"/>
              <prop k="draw_inside_polygon" v="0"/>
              <prop k="joinstyle" v="bevel"/>
              <prop k="line_color" v="255,5,0,255"/>
              <prop k="line_style" v="dot"/>
              <prop k="line_width" v="0.5"/>
              <prop k="line_width_unit" v="MM"/>
              <prop k="offset" v="-0.6"/>
              <prop k="offset_map_unit_scale" v="0,0,0,0,0,0"/>
              <prop k="offset_unit" v="MM"/>
              <prop k="use_custom_dash" v="0"/>
              <prop k="width_map_unit_scale" v="0,0,0,0,0,0"/>
            </layer>
            <layer pass="0" class="SimpleLine" locked="0">
              <prop k="capstyle" v="square"/>
              <prop k="customdash" v="5;2"/>
              <prop k="customdash_map_unit_scale" v="0,0,0,0,0,0"/>
              <prop k="customdash_unit" v="MM"/>
              <prop k="draw_inside_polygon" v="0"/>
              <prop k="joinstyle" v="bevel"/>
              <prop k="line_color" v="255,0,0,255"/>
              <prop k="line_style" v="dot"/>
              <prop k="line_width" v="0.5"/>
              <prop k="line_width_unit" v="MM"/>
              <prop k="offset" v="0.6"/>
              <prop k="offset_map_unit_scale" v="0,0,0,0,0,0"/>
              <prop k="offset_unit" v="MM"/>
              <prop k="use_custom_dash" v="0"/>
              <prop k="width_map_unit_scale" v="0,0,0,0,0,0"/>
            </layer>
          </symbol>
          <symbol alpha="1" clip_to_extent="1" type="line" name="5">
            <layer pass="0" class="SimpleLine" locked="0">
              <prop k="capstyle" v="square"/>
              <prop k="customdash" v="5;2"/>
              <prop k="customdash_map_unit_scale" v="0,0,0,0,0,0"/>
              <prop k="customdash_unit" v="MM"/>
              <prop k="draw_inside_polygon" v="0"/>
              <prop k="joinstyle" v="bevel"/>
              <prop k="line_color" v="243,235,0,255"/>
              <prop k="line_style" v="solid"/>
              <prop k="line_width" v="0.6"/>
              <prop k="line_width_unit" v="MM"/>
              <prop k="offset" v="0"/>
              <prop k="offset_map_unit_scale" v="0,0,0,0,0,0"/>
              <prop k="offset_unit" v="MM"/>
              <prop k="use_custom_dash" v="0"/>
              <prop k="width_map_unit_scale" v="0,0,0,0,0,0"/>
            </layer>
            <layer pass="0" class="SimpleLine" locked="0">
              <prop k="capstyle" v="square"/>
              <prop k="customdash" v="5;2"/>
              <prop k="customdash_map_unit_scale" v="0,0,0,0,0,0"/>
              <prop k="customdash_unit" v="MM"/>
              <prop k="draw_inside_polygon" v="0"/>
              <prop k="joinstyle" v="bevel"/>
              <prop k="line_color" v="0,0,0,255"/>
              <prop k="line_style" v="dot"/>
              <prop k="line_width" v="0.5"/>
              <prop k="line_width_unit" v="MM"/>
              <prop k="offset" v="0"/>
              <prop k="offset_map_unit_scale" v="0,0,0,0,0,0"/>
              <prop k="offset_unit" v="MM"/>
              <prop k="use_custom_dash" v="0"/>
              <prop k="width_map_unit_scale" v="0,0,0,0,0,0"/>
            </layer>
          </symbol>
          <symbol alpha="1" clip_to_extent="1" type="line" name="6">
            <layer pass="0" class="SimpleLine" locked="0">
              <prop k="capstyle" v="square"/>
              <prop k="customdash" v="5;2"/>
              <prop k="customdash_map_unit_scale" v="0,0,0,0,0,0"/>
              <prop k="customdash_unit" v="MM"/>
              <prop k="draw_inside_polygon" v="0"/>
              <prop k="joinstyle" v="bevel"/>
              <prop k="line_color" v="60,208,0,255"/>
              <prop k="line_style" v="solid"/>
              <prop k="line_width" v="0.5"/>
              <prop k="line_width_unit" v="MM"/>
              <prop k="offset" v="0"/>
              <prop k="offset_map_unit_scale" v="0,0,0,0,0,0"/>
              <prop k="offset_unit" v="MM"/>
              <prop k="use_custom_dash" v="0"/>
              <prop k="width_map_unit_scale" v="0,0,0,0,0,0"/>
            </layer>
          </symbol>
        </symbols>
        <source-symbol>
          <symbol alpha="1" clip_to_extent="1" type="line" name="0">
            <layer pass="0" class="SimpleLine" locked="0">
              <prop k="capstyle" v="square"/>
              <prop k="customdash" v="5;2"/>
              <prop k="customdash_map_unit_scale" v="0,0,0,0,0,0"/>
              <prop k="customdash_unit" v="MM"/>
              <prop k="draw_inside_polygon" v="0"/>
              <prop k="joinstyle" v="bevel"/>
              <prop k="line_color" v="77,243,51,255"/>
              <prop k="line_style" v="solid"/>
              <prop k="line_width" v="0.26"/>
              <prop k="line_width_unit" v="MM"/>
              <prop k="offset" v="0"/>
              <prop k="offset_map_unit_scale" v="0,0,0,0,0,0"/>
              <prop k="offset_unit" v="MM"/>
              <prop k="use_custom_dash" v="0"/>
              <prop k="width_map_unit_scale" v="0,0,0,0,0,0"/>
            </layer>
          </symbol>
        </source-symbol>
        <rotation/>
        <sizescale scalemethod="diameter"/>
      </renderer-v2>
      <labeling type="simple"/>
      <customproperties>
        <property key="labeling" value="pal"/>
        <property key="labeling/addDirectionSymbol" value="false"/>
        <property key="labeling/angleOffset" value="0"/>
        <property key="labeling/blendMode" value="0"/>
        <property key="labeling/bufferBlendMode" value="0"/>
        <property key="labeling/bufferColorA" value="255"/>
        <property key="labeling/bufferColorB" value="255"/>
        <property key="labeling/bufferColorG" value="255"/>
        <property key="labeling/bufferColorR" value="255"/>
        <property key="labeling/bufferDraw" value="false"/>
        <property key="labeling/bufferJoinStyle" value="64"/>
        <property key="labeling/bufferNoFill" value="false"/>
        <property key="labeling/bufferSize" value="1"/>
        <property key="labeling/bufferSizeInMapUnits" value="false"/>
        <property key="labeling/bufferTransp" value="0"/>
        <property key="labeling/centroidWhole" value="false"/>
        <property key="labeling/decimals" value="3"/>
        <property key="labeling/displayAll" value="false"/>
        <property key="labeling/dist" value="0"/>
        <property key="labeling/distInMapUnits" value="false"/>
        <property key="labeling/enabled" value="false"/>
        <property key="labeling/fieldName" value=""/>
        <property key="labeling/fontBold" value="false"/>
        <property key="labeling/fontCapitals" value="0"/>
        <property key="labeling/fontFamily" value="Sans"/>
        <property key="labeling/fontItalic" value="false"/>
        <property key="labeling/fontLetterSpacing" value="0"/>
        <property key="labeling/fontLimitPixelSize" value="false"/>
        <property key="labeling/fontMaxPixelSize" value="10000"/>
        <property key="labeling/fontMinPixelSize" value="3"/>
        <property key="labeling/fontSize" value="10"/>
        <property key="labeling/fontSizeInMapUnits" value="false"/>
        <property key="labeling/fontStrikeout" value="false"/>
        <property key="labeling/fontUnderline" value="false"/>
        <property key="labeling/fontWeight" value="50"/>
        <property key="labeling/fontWordSpacing" value="0"/>
        <property key="labeling/formatNumbers" value="false"/>
        <property key="labeling/isExpression" value="false"/>
        <property key="labeling/labelOffsetInMapUnits" value="true"/>
        <property key="labeling/labelPerPart" value="false"/>
        <property key="labeling/leftDirectionSymbol" value="&lt;"/>
        <property key="labeling/limitNumLabels" value="false"/>
        <property key="labeling/maxCurvedCharAngleIn" value="20"/>
        <property key="labeling/maxCurvedCharAngleOut" value="-20"/>
        <property key="labeling/maxNumLabels" value="2000"/>
        <property key="labeling/mergeLines" value="false"/>
        <property key="labeling/minFeatureSize" value="0"/>
        <property key="labeling/multilineAlign" value="0"/>
        <property key="labeling/multilineHeight" value="1"/>
        <property key="labeling/namedStyle" value=""/>
        <property key="labeling/obstacle" value="true"/>
        <property key="labeling/placeDirectionSymbol" value="0"/>
        <property key="labeling/placement" value="2"/>
        <property key="labeling/placementFlags" value="10"/>
        <property key="labeling/plussign" value="false"/>
        <property key="labeling/preserveRotation" value="true"/>
        <property key="labeling/previewBkgrdColor" value="#ffffff"/>
        <property key="labeling/priority" value="5"/>
        <property key="labeling/quadOffset" value="4"/>
        <property key="labeling/reverseDirectionSymbol" value="false"/>
        <property key="labeling/rightDirectionSymbol" value=">"/>
        <property key="labeling/scaleMax" value="10000000"/>
        <property key="labeling/scaleMin" value="1"/>
        <property key="labeling/scaleVisibility" value="false"/>
        <property key="labeling/shadowBlendMode" value="6"/>
        <property key="labeling/shadowColorB" value="0"/>
        <property key="labeling/shadowColorG" value="0"/>
        <property key="labeling/shadowColorR" value="0"/>
        <property key="labeling/shadowDraw" value="false"/>
        <property key="labeling/shadowOffsetAngle" value="135"/>
        <property key="labeling/shadowOffsetDist" value="1"/>
        <property key="labeling/shadowOffsetGlobal" value="true"/>
        <property key="labeling/shadowOffsetUnits" value="1"/>
        <property key="labeling/shadowRadius" value="1.5"/>
        <property key="labeling/shadowRadiusAlphaOnly" value="false"/>
        <property key="labeling/shadowRadiusUnits" value="1"/>
        <property key="labeling/shadowScale" value="100"/>
        <property key="labeling/shadowTransparency" value="30"/>
        <property key="labeling/shadowUnder" value="0"/>
        <property key="labeling/shapeBlendMode" value="0"/>
        <property key="labeling/shapeBorderColorA" value="255"/>
        <property key="labeling/shapeBorderColorB" value="128"/>
        <property key="labeling/shapeBorderColorG" value="128"/>
        <property key="labeling/shapeBorderColorR" value="128"/>
        <property key="labeling/shapeBorderWidth" value="0"/>
        <property key="labeling/shapeBorderWidthUnits" value="1"/>
        <property key="labeling/shapeDraw" value="false"/>
        <property key="labeling/shapeFillColorA" value="255"/>
        <property key="labeling/shapeFillColorB" value="255"/>
        <property key="labeling/shapeFillColorG" value="255"/>
        <property key="labeling/shapeFillColorR" value="255"/>
        <property key="labeling/shapeJoinStyle" value="64"/>
        <property key="labeling/shapeOffsetUnits" value="1"/>
        <property key="labeling/shapeOffsetX" value="0"/>
        <property key="labeling/shapeOffsetY" value="0"/>
        <property key="labeling/shapeRadiiUnits" value="1"/>
        <property key="labeling/shapeRadiiX" value="0"/>
        <property key="labeling/shapeRadiiY" value="0"/>
        <property key="labeling/shapeRotation" value="0"/>
        <property key="labeling/shapeRotationType" value="0"/>
        <property key="labeling/shapeSVGFile" value=""/>
        <property key="labeling/shapeSizeType" value="0"/>
        <property key="labeling/shapeSizeUnits" value="1"/>
        <property key="labeling/shapeSizeX" value="0"/>
        <property key="labeling/shapeSizeY" value="0"/>
        <property key="labeling/shapeTransparency" value="0"/>
        <property key="labeling/shapeType" value="0"/>
        <property key="labeling/textColorA" value="255"/>
        <property key="labeling/textColorB" value="0"/>
        <property key="labeling/textColorG" value="0"/>
        <property key="labeling/textColorR" value="0"/>
        <property key="labeling/textTransp" value="0"/>
        <property key="labeling/upsidedownLabels" value="0"/>
        <property key="labeling/wrapChar" value=""/>
        <property key="labeling/xOffset" value="0"/>
        <property key="labeling/yOffset" value="0"/>
      </customproperties>
      <blendMode>0</blendMode>
      <featureBlendMode>0</featureBlendMode>
      <layerTransparency>0</layerTransparency>
      <displayfield>OGC_FID</displayfield>
      <label>0</label>
      <labelattributes>
        <label fieldname="" text="Label"/>
        <family fieldname="" name="Sans"/>
        <size fieldname="" units="pt" value="12"/>
        <bold fieldname="" on="0"/>
        <italic fieldname="" on="0"/>
        <underline fieldname="" on="0"/>
        <strikeout fieldname="" on="0"/>
        <color fieldname="" red="0" blue="0" green="0"/>
        <x fieldname=""/>
        <y fieldname=""/>
        <offset x="0" y="0" units="pt" yfieldname="" xfieldname=""/>
        <angle fieldname="" value="0" auto="0"/>
        <alignment fieldname="" value="center"/>
        <buffercolor fieldname="" red="255" blue="255" green="255"/>
        <buffersize fieldname="" units="pt" value="1"/>
        <bufferenabled fieldname="" on=""/>
        <multilineenabled fieldname="" on=""/>
        <selectedonly on=""/>
      </labelattributes>
      <annotationform>.</annotationform>
      <aliases>
        <alias field="ogc_fid" index="0" name=""/>
        <alias field="obj_type" index="1" name=""/>
        <alias field="obj_id" index="2" name=""/>
        <alias field="nodes" index="3" name=""/>
        <alias field="id1" index="4" name=""/>
        <alias field="id2" index="5" name=""/>
        <alias field="problem" index="6" name=""/>
      </aliases>
      <excludeAttributesWMS/>
      <excludeAttributesWFS/>
      <attributeactions default="-1"/>
      <attributetableconfig actionWidgetStyle="dropDown" sortExpression="" sortOrder="0">
        <columns/>
      </attributetableconfig>
      <editform>.</editform>
      <editforminit/>
      <editforminitcodesource>0</editforminitcodesource>
      <editforminitfilepath></editforminitfilepath>
      <editforminitcode><![CDATA[]]></editforminitcode>
      <featformsuppress>0</featformsuppress>
      <editorlayout>generatedlayout</editorlayout>
      <widgets/>
      <conditionalstyles>
        <rowstyles/>
        <fieldstyles/>
      </conditionalstyles>
    </maplayer>
    <maplayer simplifyAlgorithm="0" minimumScale="-4.65661e-10" maximumScale="1e+08" simplifyDrawingHints="1" readOnly="0" minLabelScale="0" maxLabelScale="1e+08" simplifyDrawingTol="1" geometry="Polygon" simplifyMaxScale="1" type="vector" hasScaleBasedVisibilityFlag="0" simplifyLocal="1" scaleBasedLabelVisibilityFlag="0">
      <id>multipolygons20140221151811742</id>
      <datasource>dbname='./multipolygon.db' table="multipolygons" (GEOMETRY) sql=</datasource>
      <keywordList>
        <value></value>
      </keywordList>
      <layername>multipolygons</layername>
      <srs>
        <spatialrefsys>
          <proj4>+proj=longlat +datum=WGS84 +no_defs</proj4>
          <srsid>3452</srsid>
          <srid>4326</srid>
          <authid>EPSG:4326</authid>
          <description>WGS 84</description>
          <projectionacronym>longlat</projectionacronym>
          <ellipsoidacronym>WGS84</ellipsoidacronym>
          <geographicflag>true</geographicflag>
        </spatialrefsys>
      </srs>
      <provider encoding="System">spatialite</provider>
      <previewExpression></previewExpression>
      <vectorjoins/>
      <layerDependencies/>
      <expressionfields/>
      <defaults>
        <default field="ogc_fid" expression=""/>
        <default field="id" expression=""/>
        <default field="from_type" expression=""/>
      </defaults>
      <map-layer-style-manager current="">
        <map-layer-style name=""/>
      </map-layer-style-manager>
      <edittypes>
        <edittype widgetv2type="TextEdit" name="ogc_fid">
          <widgetv2config IsMultiline="0" fieldEditable="1" constraint="" UseHtml="0" labelOnTop="0" constraintDescription="" notNull="0"/>
        </edittype>
        <edittype widgetv2type="TextEdit" name="id">
          <widgetv2config IsMultiline="0" fieldEditable="1" constraint="" UseHtml="0" labelOnTop="0" constraintDescription="" notNull="0"/>
        </edittype>
        <edittype widgetv2type="TextEdit" name="from_type">
          <widgetv2config IsMultiline="0" fieldEditable="1" constraint="" UseHtml="0" labelOnTop="0" constraintDescription="" notNull="0"/>
        </edittype>
      </edittypes>
      <renderer-v2 forceraster="0" symbollevels="0" type="singleSymbol" enableorderby="0">
        <symbols>
          <symbol alpha="0.494118" clip_to_extent="1" type="fill" name="0">
            <layer pass="0" class="SimpleFill" locked="0">
              <prop k="border_width_map_unit_scale" v="0,0,0,0,0,0"/>
              <prop k="color" v="0,170,255,255"/>
              <prop k="joinstyle" v="bevel"/>
              <prop k="offset" v="0,0"/>
              <prop k="offset_map_unit_scale" v="0,0,0,0,0,0"/>
              <prop k="offset_unit" v="MM"/>
              <prop k="outline_color" v="0,0,0,255"/>
              <prop k="outline_style" v="solid"/>
              <prop k="outline_width" v="0.26"/>
              <prop k="outline_width_unit" v="MM"/>
              <prop k="style" v="solid"/>
            </layer>
          </symbol>
        </symbols>
        <rotation/>
        <sizescale scalemethod="diameter"/>
      </renderer-v2>
      <labeling type="simple"/>
      <customproperties>
        <property key="labeling" value="pal"/>
        <property key="labeling/addDirectionSymbol" value="false"/>
        <property key="labeling/angleOffset" value="0"/>
        <property key="labeling/blendMode" value="0"/>
        <property key="labeling/bufferBlendMode" value="0"/>
        <property key="labeling/bufferColorA" value="255"/>
        <property key="labeling/bufferColorB" value="255"/>
        <property key="labeling/bufferColorG" value="255"/>
        <property key="labeling/bufferColorR" value="255"/>
        <property key="labeling/bufferDraw" value="false"/>
        <property key="labeling/bufferJoinStyle" value="64"/>
        <property key="labeling/bufferNoFill" value="false"/>
        <property key="labeling/bufferSize" value="1"/>
        <property key="labeling/bufferSizeInMapUnits" value="false"/>
        <property key="labeling/bufferTransp" value="0"/>
        <property key="labeling/centroidWhole" value="false"/>
        <property key="labeling/decimals" value="3"/>
        <property key="labeling/displayAll" value="false"/>
        <property key="labeling/dist" value="0"/>
        <property key="labeling/distInMapUnits" value="false"/>
        <property key="labeling/enabled" value="false"/>
        <property key="labeling/fieldName" value=""/>
        <property key="labeling/fontBold" value="false"/>
        <property key="labeling/fontCapitals" value="0"/>
        <property key="labeling/fontFamily" value="Sans"/>
        <property key="labeling/fontItalic" value="false"/>
        <property key="labeling/fontLetterSpacing" value="0"/>
        <property key="labeling/fontLimitPixelSize" value="false"/>
        <property key="labeling/fontMaxPixelSize" value="10000"/>
        <property key="labeling/fontMinPixelSize" value="3"/>
        <property key="labeling/fontSize" value="10"/>
        <property key="labeling/fontSizeInMapUnits" value="false"/>
        <property key="labeling/fontStrikeout" value="false"/>
        <property key="labeling/fontUnderline" value="false"/>
        <property key="labeling/fontWeight" value="50"/>
        <property key="labeling/fontWordSpacing" value="0"/>
        <property key="labeling/formatNumbers" value="false"/>
        <property key="labeling/isExpression" value="false"/>
        <property key="labeling/labelOffsetInMapUnits" value="true"/>
        <property key="labeling/labelPerPart" value="false"/>
        <property key="labeling/leftDirectionSymbol" value="&lt;"/>
        <property key="labeling/limitNumLabels" value="false"/>
        <property key="labeling/maxCurvedCharAngleIn" value="20"/>
        <property key="labeling/maxCurvedCharAngleOut" value="-20"/>
        <property key="labeling/maxNumLabels" value="2000"/>
        <property key="labeling/mergeLines" value="false"/>
        <property key="labeling/minFeatureSize" value="0"/>
        <property key="labeling/multilineAlign" value="0"/>
        <property key="labeling/multilineHeight" value="1"/>
        <property key="labeling/namedStyle" value=""/>
        <property key="labeling/obstacle" value="true"/>
        <property key="labeling/placeDirectionSymbol" value="0"/>
        <property key="labeling/placement" value="0"/>
        <property key="labeling/placementFlags" value="0"/>
        <property key="labeling/plussign" value="false"/>
        <property key="labeling/preserveRotation" value="true"/>
        <property key="labeling/previewBkgrdColor" value="#ffffff"/>
        <property key="labeling/priority" value="5"/>
        <property key="labeling/quadOffset" value="4"/>
        <property key="labeling/reverseDirectionSymbol" value="false"/>
        <property key="labeling/rightDirectionSymbol" value=">"/>
        <property key="labeling/scaleMax" value="10000000"/>
        <property key="labeling/scaleMin" value="1"/>
        <property key="labeling/scaleVisibility" value="false"/>
        <property key="labeling/shadowBlendMode" value="6"/>
        <property key="labeling/shadowColorB" value="0"/>
        <property key="labeling/shadowColorG" value="0"/>
        <property key="labeling/shadowColorR" value="0"/>
        <property key="labeling/shadowDraw" value="false"/>
        <property key="labeling/shadowOffsetAngle" value="135"/>
        <property key="labeling/shadowOffsetDist" value="1"/>
        <property key="labeling/shadowOffsetGlobal" value="true"/>
        <property key="labeling/shadowOffsetUnits" value="1"/>
        <property key="labeling/shadowRadius" value="1.5"/>
        <property key="labeling/shadowRadiusAlphaOnly" value="false"/>
        <property key="labeling/shadowRadiusUnits" value="1"/>
        <property key="labeling/shadowScale" value="100"/>
        <property key="labeling/shadowTransparency" value="30"/>
        <property key="labeling/shadowUnder" value="0"/>
        <property key="labeling/shapeBlendMode" value="0"/>
        <property key="labeling/shapeBorderColorA" value="255"/>
        <property key="labeling/shapeBorderColorB" value="128"/>
        <property key="labeling/shapeBorderColorG" value="128"/>
        <property key="labeling/shapeBorderColorR" value="128"/>
        <property key="labeling/shapeBorderWidth" value="0"/>
        <property key="labeling/shapeBorderWidthUnits" value="1"/>
        <property key="labeling/shapeDraw" value="false"/>
        <property key="labeling/shapeFillColorA" value="255"/>
        <property key="labeling/shapeFillColorB" value="255"/>
        <property key="labeling/shapeFillColorG" value="255"/>
        <property key="labeling/shapeFillColorR" value="255"/>
        <property key="labeling/shapeJoinStyle" value="64"/>
        <property key="labeling/shapeOffsetUnits" value="1"/>
        <property key="labeling/shapeOffsetX" value="0"/>
        <property key="labeling/shapeOffsetY" value="0"/>
        <property key="labeling/shapeRadiiUnits" value="1"/>
        <property key="labeling/shapeRadiiX" value="0"/>
        <property key="labeling/shapeRadiiY" value="0"/>
        <property key="labeling/shapeRotation" value="0"/>
        <property key="labeling/shapeRotationType" value="0"/>
        <property key="labeling/shapeSVGFile" value=""/>
        <property key="labeling/shapeSizeType" value="0"/>
        <property key="labeling/shapeSizeUnits" value="1"/>
        <property key="labeling/shapeSizeX" value="0"/>
        <property key="labeling/shapeSizeY" value="0"/>
        <property key="labeling/shapeTransparency" value="0"/>
        <property key="labeling/shapeType" value="0"/>
        <property key="labeling/textColorA" value="255"/>
        <property key="labeling/textColorB" value="0"/>
        <property key="labeling/textColorG" value="0"/>
        <property key="labeling/textColorR" value="0"/>
        <property key="labeling/textTransp" value="0"/>
        <property key="labeling/upsidedownLabels" value="0"/>
        <property key="labeling/wrapChar" value=""/>
        <property key="labeling/xOffset" value="0"/>
        <property key="labeling/yOffset" value="0"/>
      </customproperties>
      <blendMode>0</blendMode>
      <featureBlendMode>0</featureBlendMode>
      <layerTransparency>0</layerTransparency>
      <displayfield>OGC_FID</displayfield>
      <label>0</label>
      <labelattributes>
        <label fieldname="" text="Label"/>
        <family fieldname="" name="Sans"/>
        <size fieldname="" units="pt" value="12"/>
        <bold fieldname="" on="0"/>
        <italic fieldname="" on="0"/>
        <underline fieldname="" on="0"/>
        <strikeout fieldname="" on="0"/>
        <color fieldname="" red="0" blue="0" green="0"/>
        <x fieldname=""/>
        <y fieldname=""/>
        <offset x="0" y="0" units="pt" yfieldname="" xfieldname=""/>
        <angle fieldname="" value="0" auto="0"/>
        <alignment fieldname="" value="center"/>
        <buffercolor fieldname="" red="255" blue="255" green="255"/>
        <buffersize fieldname="" units="pt" value="1"/>
        <bufferenabled fieldname="" on=""/>
        <multilineenabled fieldname="" on=""/>
        <selectedonly on=""/>
      </labelattributes>
      <annotationform>.</annotationform>
      <aliases>
        <alias field="ogc_fid" index="0" name=""/>
        <alias field="id" index="1" name=""/>
        <alias field="from_type" index="2" name=""/>
      </aliases>
      <excludeAttributesWMS/>
      <excludeAttributesWFS/>
      <attributeactions default="0"/>
      <attributetableconfig actionWidgetStyle="dropDown" sortExpression="" sortOrder="0">
        <columns/>
      </attributetableconfig>
      <editform>.</editform>
      <editforminit/>
      <editforminitcodesource>0</editforminitcodesource>
      <editforminitfilepath></editforminitfilepath>
      <editforminitcode><![CDATA[]]></editforminitcode>
      <featformsuppress>0</featformsuppress>
      <editorlayout>generatedlayout</editorlayout>
      <widgets/>
      <conditionalstyles>
        <rowstyles/>
        <fieldstyles/>
      </conditionalstyles>
    </maplayer>
    <maplayer simplifyAlgorithm="0" minimumScale="0" maximumScale="1e+08" simplifyDrawingHints="0" readOnly="0" minLabelScale="0" maxLabelScale="1e+08" simplifyDrawingTol="1" geometry="Point" simplifyMaxScale="1" type="vector" hasScaleBasedVisibilityFlag="0" simplifyLocal="1" scaleBasedLabelVisibilityFlag="0">
      <id>perrors20140228163658956</id>
      <datasource>dbname='./multipolygon.db' table="perrors" (GEOMETRY) sql=</datasource>
      <keywordList>
        <value></value>
      </keywordList>
      <layername>Error Points</layername>
      <srs>
        <spatialrefsys>
          <proj4>+proj=longlat +datum=WGS84 +no_defs</proj4>
          <srsid>3452</srsid>
          <srid>4326</srid>
          <authid>EPSG:4326</authid>
          <description>WGS 84</description>
          <projectionacronym>longlat</projectionacronym>
          <ellipsoidacronym>WGS84</ellipsoidacronym>
          <geographicflag>true</geographicflag>
        </spatialrefsys>
      </srs>
      <provider encoding="System">spatialite</provider>
      <previewExpression>COALESCE( "OGC_FID", '&lt;NULL>' )</previewExpression>
      <vectorjoins/>
      <layerDependencies/>
      <expressionfields/>
      <defaults>
        <default field="ogc_fid" expression=""/>
        <default field="obj_type" expression=""/>
        <default field="obj_id" expression=""/>
        <default field="nodes" expression=""/>
        <default field="id1" expression=""/>
        <default field="id2" expression=""/>
        <default field="problem" expression=""/>
      </defaults>
      <map-layer-style-manager current="">
        <map-layer-style name=""/>
      </map-layer-style-manager>
      <edittypes>
        <edittype widgetv2type="TextEdit" name="ogc_fid">
          <widgetv2config IsMultiline="0" fieldEditable="1" constraint="" UseHtml="0" labelOnTop="0" constraintDescription="" notNull="0"/>
        </edittype>
        <edittype widgetv2type="TextEdit" name="obj_type">
          <widgetv2config IsMultiline="0" fieldEditable="1" constraint="" UseHtml="0" labelOnTop="0" constraintDescription="" notNull="0"/>
        </edittype>
        <edittype widgetv2type="TextEdit" name="obj_id">
          <widgetv2config IsMultiline="0" fieldEditable="1" constraint="" UseHtml="0" labelOnTop="0" constraintDescription="" notNull="0"/>
        </edittype>
        <edittype widgetv2type="TextEdit" name="nodes">
          <widgetv2config IsMultiline="0" fieldEditable="1" constraint="" UseHtml="0" labelOnTop="0" constraintDescription="" notNull="0"/>
        </edittype>
        <edittype widgetv2type="TextEdit" name="id1">
          <widgetv2config IsMultiline="0" fieldEditable="1" constraint="" UseHtml="0" labelOnTop="0" constraintDescription="" notNull="0"/>
        </edittype>
        <edittype widgetv2type="TextEdit" name="id2">
          <widgetv2config IsMultiline="0" fieldEditable="1" constraint="" UseHtml="0" labelOnTop="0" constraintDescription="" notNull="0"/>
        </edittype>
        <edittype widgetv2type="TextEdit" name="problem">
          <widgetv2config IsMultiline="0" fieldEditable="1" constraint="" UseHtml="0" labelOnTop="0" constraintDescription="" notNull="0"/>
        </edittype>
      </edittypes>
      <renderer-v2 attr="problem" forceraster="0" symbollevels="0" type="categorizedSymbol" enableorderby="0">
        <categories>
          <category render="true" symbol="0" value="touching_ring" label="touching_ring"/>
          <category render="true" symbol="1" value="ring_not_closed" label="ring_not_closed"/>
          <category render="true" symbol="2" value="duplicate_node" label="duplicate_node"/>
          <category render="true" symbol="3" value="intersection" label="intersection"/>
        </categories>
        <symbols>
          <symbol alpha="1" clip_to_extent="1" type="marker" name="0">
            <layer pass="0" class="SimpleMarker" locked="0">
              <prop k="angle" v="0"/>
              <prop k="color" v="255,0,0,255"/>
              <prop k="horizontal_anchor_point" v="1"/>
              <prop k="joinstyle" v="bevel"/>
              <prop k="name" v="diamond"/>
              <prop k="offset" v="0,0"/>
              <prop k="offset_map_unit_scale" v="0,0,0,0,0,0"/>
              <prop k="offset_unit" v="MM"/>
              <prop k="outline_color" v="255,255,255,255"/>
              <prop k="outline_style" v="solid"/>
              <prop k="outline_width" v="0.4"/>
              <prop k="outline_width_map_unit_scale" v="0,0,0,0,0,0"/>
              <prop k="outline_width_unit" v="MM"/>
              <prop k="scale_method" v="diameter"/>
              <prop k="size" v="2.8"/>
              <prop k="size_map_unit_scale" v="0,0,0,0,0,0"/>
              <prop k="size_unit" v="MM"/>
              <prop k="vertical_anchor_point" v="1"/>
            </layer>
          </symbol>
          <symbol alpha="1" clip_to_extent="1" type="marker" name="1">
            <layer pass="0" class="SimpleMarker" locked="0">
              <prop k="angle" v="0"/>
              <prop k="color" v="255,0,0,255"/>
              <prop k="horizontal_anchor_point" v="1"/>
              <prop k="joinstyle" v="bevel"/>
              <prop k="name" v="triangle"/>
              <prop k="offset" v="0,0"/>
              <prop k="offset_map_unit_scale" v="0,0,0,0,0,0"/>
              <prop k="offset_unit" v="MM"/>
              <prop k="outline_color" v="255,255,255,255"/>
              <prop k="outline_style" v="solid"/>
              <prop k="outline_width" v="0.4"/>
              <prop k="outline_width_map_unit_scale" v="0,0,0,0,0,0"/>
              <prop k="outline_width_unit" v="MM"/>
              <prop k="scale_method" v="diameter"/>
              <prop k="size" v="2.8"/>
              <prop k="size_map_unit_scale" v="0,0,0,0,0,0"/>
              <prop k="size_unit" v="MM"/>
              <prop k="vertical_anchor_point" v="1"/>
            </layer>
          </symbol>
          <symbol alpha="1" clip_to_extent="1" type="marker" name="2">
            <layer pass="0" class="SimpleMarker" locked="0">
              <prop k="angle" v="0"/>
              <prop k="color" v="255,255,255,255"/>
              <prop k="horizontal_anchor_point" v="1"/>
              <prop k="joinstyle" v="bevel"/>
              <prop k="name" v="circle"/>
              <prop k="offset" v="0,0"/>
              <prop k="offset_map_unit_scale" v="0,0,0,0,0,0"/>
              <prop k="offset_unit" v="MM"/>
              <prop k="outline_color" v="255,0,0,255"/>
              <prop k="outline_style" v="solid"/>
              <prop k="outline_width" v="0.4"/>
              <prop k="outline_width_map_unit_scale" v="0,0,0,0,0,0"/>
              <prop k="outline_width_unit" v="MM"/>
              <prop k="scale_method" v="diameter"/>
              <prop k="size" v="2.4"/>
              <prop k="size_map_unit_scale" v="0,0,0,0,0,0"/>
              <prop k="size_unit" v="MM"/>
              <prop k="vertical_anchor_point" v="1"/>
            </layer>
            <layer pass="0" class="SimpleMarker" locked="0">
              <prop k="angle" v="0"/>
              <prop k="color" v="255,0,0,255"/>
              <prop k="horizontal_anchor_point" v="1"/>
              <prop k="joinstyle" v="bevel"/>
              <prop k="name" v="circle"/>
              <prop k="offset" v="0,0"/>
              <prop k="offset_map_unit_scale" v="0,0,0,0,0,0"/>
              <prop k="offset_unit" v="MM"/>
              <prop k="outline_color" v="255,0,0,255"/>
              <prop k="outline_style" v="solid"/>
              <prop k="outline_width" v="0.8"/>
              <prop k="outline_width_map_unit_scale" v="0,0,0,0,0,0"/>
              <prop k="outline_width_unit" v="MM"/>
              <prop k="scale_method" v="diameter"/>
              <prop k="size" v="0.5"/>
              <prop k="size_map_unit_scale" v="0,0,0,0,0,0"/>
              <prop k="size_unit" v="MM"/>
              <prop k="vertical_anchor_point" v="1"/>
            </layer>
          </symbol>
          <symbol alpha="1" clip_to_extent="1" type="marker" name="3">
            <layer pass="0" class="SimpleMarker" locked="0">
              <prop k="angle" v="0"/>
              <prop k="color" v="139,168,110,255"/>
              <prop k="horizontal_anchor_point" v="1"/>
              <prop k="joinstyle" v="bevel"/>
              <prop k="name" v="cross2"/>
              <prop k="offset" v="0,0"/>
              <prop k="offset_map_unit_scale" v="0,0,0,0,0,0"/>
              <prop k="offset_unit" v="MM"/>
              <prop k="outline_color" v="255,0,0,255"/>
              <prop k="outline_style" v="solid"/>
              <prop k="outline_width" v="0.6"/>
              <prop k="outline_width_map_unit_scale" v="0,0,0,0,0,0"/>
              <prop k="outline_width_unit" v="MM"/>
              <prop k="scale_method" v="diameter"/>
              <prop k="size" v="2"/>
              <prop k="size_map_unit_scale" v="0,0,0,0,0,0"/>
              <prop k="size_unit" v="MM"/>
              <prop k="vertical_anchor_point" v="1"/>
            </layer>
          </symbol>
        </symbols>
        <source-symbol>
          <symbol alpha="1" clip_to_extent="1" type="marker" name="0">
            <layer pass="0" class="SimpleMarker" locked="0">
              <prop k="angle" v="0"/>
              <prop k="color" v="139,168,110,255"/>
              <prop k="horizontal_anchor_point" v="1"/>
              <prop k="joinstyle" v="bevel"/>
              <prop k="name" v="circle"/>
              <prop k="offset" v="0,0"/>
              <prop k="offset_map_unit_scale" v="0,0,0,0,0,0"/>
              <prop k="offset_unit" v="MM"/>
              <prop k="outline_color" v="0,0,0,255"/>
              <prop k="outline_style" v="solid"/>
              <prop k="outline_width" v="0"/>
              <prop k="outline_width_map_unit_scale" v="0,0,0,0,0,0"/>
              <prop k="outline_width_unit" v="MM"/>
              <prop k="scale_method" v="area"/>
              <prop k="size" v="2"/>
              <prop k="size_map_unit_scale" v="0,0,0,0,0,0"/>
              <prop k="size_unit" v="MM"/>
              <prop k="vertical_anchor_point" v="1"/>
            </layer>
          </symbol>
        </source-symbol>
        <rotation/>
        <sizescale scalemethod="diameter"/>
      </renderer-v2>
      <labeling type="simple"/>
      <customproperties>
        <property key="labeling" value="pal"/>
        <property key="labeling/addDirectionSymbol" value="false"/>
        <property key="labeling/angleOffset" value="0"/>
        <property key="labeling/blendMode" value="0"/>
        <property key="labeling/bufferBlendMode" value="0"/>
        <property key="labeling/bufferColorA" value="255"/>
        <property key="labeling/bufferColorB" value="255"/>
        <property key="labeling/bufferColorG" value="255"/>
        <property key="labeling/bufferColorR" value="255"/>
        <property key="labeling/bufferDraw" value="false"/>
        <property key="labeling/bufferJoinStyle" value="64"/>
        <property key="labeling/bufferNoFill" value="false"/>
        <property key="labeling/bufferSize" value="1"/>
        <property key="labeling/bufferSizeInMapUnits" value="false"/>
        <property key="labeling/bufferTransp" value="0"/>
        <property key="labeling/centroidWhole" value="false"/>
        <property key="labeling/decimals" value="3"/>
        <property key="labeling/displayAll" value="false"/>
        <property key="labeling/dist" value="0"/>
        <property key="labeling/distInMapUnits" value="false"/>
        <property key="labeling/enabled" value="false"/>
        <property key="labeling/fieldName" value=""/>
        <property key="labeling/fontBold" value="false"/>
        <property key="labeling/fontCapitals" value="0"/>
        <property key="labeling/fontFamily" value="Sans"/>
        <property key="labeling/fontItalic" value="false"/>
        <property key="labeling/fontLetterSpacing" value="0"/>
        <property key="labeling/fontLimitPixelSize" value="false"/>
        <property key="labeling/fontMaxPixelSize" value="10000"/>
        <property key="labeling/fontMinPixelSize" value="3"/>
        <property key="labeling/fontSize" value="10"/>
        <property key="labeling/fontSizeInMapUnits" value="false"/>
        <property key="labeling/fontStrikeout" value="false"/>
        <property key="labeling/fontUnderline" value="false"/>
        <property key="labeling/fontWeight" value="50"/>
        <property key="labeling/fontWordSpacing" value="0"/>
        <property key="labeling/formatNumbers" value="false"/>
        <property key="labeling/isExpression" value="false"/>
        <property key="labeling/labelOffsetInMapUnits" value="true"/>
        <property key="labeling/labelPerPart" value="false"/>
        <property key="labeling/leftDirectionSymbol" value="&lt;"/>
        <property key="labeling/limitNumLabels" value="false"/>
        <property key="labeling/maxCurvedCharAngleIn" value="20"/>
        <property key="labeling/maxCurvedCharAngleOut" value="-20"/>
        <property key="labeling/maxNumLabels" value="2000"/>
        <property key="labeling/mergeLines" value="false"/>
        <property key="labeling/minFeatureSize" value="0"/>
        <property key="labeling/multilineAlign" value="0"/>
        <property key="labeling/multilineHeight" value="1"/>
        <property key="labeling/namedStyle" value=""/>
        <property key="labeling/obstacle" value="true"/>
        <property key="labeling/placeDirectionSymbol" value="0"/>
        <property key="labeling/placement" value="0"/>
        <property key="labeling/placementFlags" value="0"/>
        <property key="labeling/plussign" value="false"/>
        <property key="labeling/preserveRotation" value="true"/>
        <property key="labeling/previewBkgrdColor" value="#ffffff"/>
        <property key="labeling/priority" value="5"/>
        <property key="labeling/quadOffset" value="4"/>
        <property key="labeling/reverseDirectionSymbol" value="false"/>
        <property key="labeling/rightDirectionSymbol" value=">"/>
        <property key="labeling/scaleMax" value="10000000"/>
        <property key="labeling/scaleMin" value="1"/>
        <property key="labeling/scaleVisibility" value="false"/>
        <property key="labeling/shadowBlendMode" value="6"/>
        <property key="labeling/shadowColorB" value="0"/>
        <property key="labeling/shadowColorG" value="0"/>
        <property key="labeling/shadowColorR" value="0"/>
        <property key="labeling/shadowDraw" value="false"/>
        <property key="labeling/shadowOffsetAngle" value="135"/>
        <property key="labeling/shadowOffsetDist" value="1"/>
        <property key="labeling/shadowOffsetGlobal" value="true"/>
        <property key="labeling/shadowOffsetUnits" value="1"/>
        <property key="labeling/shadowRadius" value="1.5"/>
        <property key="labeling/shadowRadiusAlphaOnly" value="false"/>
        <property key="labeling/shadowRadiusUnits" value="1"/>
        <property key="labeling/shadowScale" value="100"/>
        <property key="labeling/shadowTransparency" value="30"/>
        <property key="labeling/shadowUnder" value="0"/>
        <property key="labeling/shapeBlendMode" value="0"/>
        <property key="labeling/shapeBorderColorA" value="255"/>
        <property key="labeling/shapeBorderColorB" value="128"/>
        <property key="labeling/shapeBorderColorG" value="128"/>
        <property key="labeling/shapeBorderColorR" value="128"/>
        <property key="labeling/shapeBorderWidth" value="0"/>
        <property key="labeling/shapeBorderWidthUnits" value="1"/>
        <property key="labeling/shapeDraw" value="false"/>
        <property key="labeling/shapeFillColorA" value="255"/>
        <property key="labeling/shapeFillColorB" value="255"/>
        <property key="labeling/shapeFillColorG" value="255"/>
        <property key="labeling/shapeFillColorR" value="255"/>
        <property key="labeling/shapeJoinStyle" value="64"/>
        <property key="labeling/shapeOffsetUnits" value="1"/>
        <property key="labeling/shapeOffsetX" value="0"/>
        <property key="labeling/shapeOffsetY" value="0"/>
        <property key="labeling/shapeRadiiUnits" value="1"/>
        <property key="labeling/shapeRadiiX" value="0"/>
        <property key="labeling/shapeRadiiY" value="0"/>
        <property key="labeling/shapeRotation" value="0"/>
        <property key="labeling/shapeRotationType" value="0"/>
        <property key="labeling/shapeSVGFile" value=""/>
        <property key="labeling/shapeSizeType" value="0"/>
        <property key="labeling/shapeSizeUnits" value="1"/>
        <property key="labeling/shapeSizeX" value="0"/>
        <property key="labeling/shapeSizeY" value="0"/>
        <property key="labeling/shapeTransparency" value="0"/>
        <property key="labeling/shapeType" value="0"/>
        <property key="labeling/textColorA" value="255"/>
        <property key="labeling/textColorB" value="0"/>
        <property key="labeling/textColorG" value="0"/>
        <property key="labeling/textColorR" value="0"/>
        <property key="labeling/textTransp" value="0"/>
        <property key="labeling/upsidedownLabels" value="0"/>
        <property key="labeling/wrapChar" value=""/>
        <property key="labeling/xOffset" value="0"/>
        <property key="labeling/yOffset" value="0"/>
      </customproperties>
      <blendMode>0</blendMode>
      <featureBlendMode>0</featureBlendMode>
      <layerTransparency>0</layerTransparency>
      <displayfield>OGC_FID</displayfield>
      <label>0</label>
      <labelattributes>
        <label fieldname="" text="Label"/>
        <family fieldname="" name="Sans"/>
        <size fieldname="" units="pt" value="12"/>
        <bold fieldname="" on="0"/>
        <italic fieldname="" on="0"/>
        <underline fieldname="" on="0"/>
        <strikeout fieldname="" on="0"/>
        <color fieldname="" red="0" blue="0" green="0"/>
        <x fieldname=""/>
        <y fieldname=""/>
        <offset x="0" y="0" units="pt" yfieldname="" xfieldname=""/>
        <angle fieldname="" value="0" auto="0"/>
        <alignment fieldname="" value="center"/>
        <buffercolor fieldname="" red="255" blue="255" green="255"/>
        <buffersize fieldname="" units="pt" value="1"/>
        <bufferenabled fieldname="" on=""/>
        <multilineenabled fieldname="" on=""/>
        <selectedonly on=""/>
      </labelattributes>
      <annotationform>.</annotationform>
      <aliases>
        <alias field="ogc_fid" index="0" name=""/>
        <alias field="obj_type" index="1" name=""/>
        <alias field="obj_id" index="2" name=""/>
        <alias field="nodes" index="3" name=""/>
        <alias field="id1" index="4" name=""/>
        <alias field="id2" index="5" name=""/>
        <alias field="problem" index="6" name=""/>
      </aliases>
      <excludeAttributesWMS/>
      <excludeAttributesWFS/>
      <attributeactions default="-1"/>
      <attributetableconfig actionWidgetStyle="dropDown" sortExpression="" sortOrder="0">
        <columns/>
      </attributetableconfig>
      <editform>.</editform>
      <editforminit/>
      <editforminitcodesource>0</editforminitcodesource>
      <editforminitfilepath></editforminitfilepath>
      <editforminitcode><![CDATA[]]></editforminitcode>
      <featformsuppress>0</featformsuppress>
      <editorlayout>generatedlayout</editorlayout>
      <widgets/>
      <conditionalstyles>
        <rowstyles/>
        <fieldstyles/>
      </conditionalstyles>
    </maplayer>
  </projectlayers>
  <properties>
    <WMSContactPerson type="QString"></WMSContactPerson>
    <WMSOnlineResource type="QString"></WMSOnlineResource>
    <WMSContactOrganization type="QString"></WMSContactOrganization>
    <WMSExtent type="QStringList">
      <value>0.82500024999999999</value>
      <value>-0.35415386986094277</value>
      <value>8.17498974999999994</value>
      <value>3.45415386986094308</value>
    </WMSExtent>
    <WMSKeywordList type="QStringList">
      <value></value>
    </WMSKeywordList>
    <WFSUrl type="QString"></WFSUrl>
    <Paths>
      <Absolute type="bool">false</Absolute>
    </Paths>
    <WMSServiceTitle type="QString">mp test</WMSServiceTitle>
    <WFSLayers type="QStringList"/>
    <WMSContactMail type="QString"></WMSContactMail>
    <PositionPrecision>
      <DecimalPlaces type="int">2</DecimalPlaces>
      <Automatic type="bool">true</Automatic>
      <DegreeFormat type="QString">D</DegreeFormat>
    </PositionPrecision>
    <WCSUrl type="QString"></WCSUrl>
    <WMSContactPhone type="QString"></WMSContactPhone>
    <WMSServiceCapabilities type="bool">true</WMSServiceCapabilities>
    <WMSServiceAbstract type="QString"></WMSServiceAbstract>
    <WMSAddWktGeometry type="bool">false</WMSAddWktGeometry>
    <Measure>
      <Ellipsoid type="QString">NONE</Ellipsoid>
    </Measure>
    <WFSTLayers>
      <Insert type="QStringList"/>
      <Update type="QStringList"/>
      <Delete type="QStringList"/>
    </WFSTLayers>
    <Gui>
      <SelectionColorBluePart type="int">0</SelectionColorBluePart>
      <CanvasColorGreenPart type="int">255</CanvasColorGreenPart>
      <CanvasColorRedPart type="int">255</CanvasColorRedPart>
      <SelectionColorRedPart type="int">255</SelectionColorRedPart>
      <SelectionColorAlphaPart type="int">255</SelectionColorAlphaPart>
      <SelectionColorGreenPart type="int">255</SelectionColorGreenPart>
      <CanvasColorBluePart type="int">255</CanvasColorBluePart>
    </Gui>
    <Identify>
      <disabledLayers type="QStringList"/>
    </Identify>
    <Macros>
      <pythonCode type="QString"></pythonCode>
    </Macros>
    <WMSAccessConstraints type="QString"></WMSAccessConstraints>
    <WCSLayers type="QStringList"/>
    <Legend>
      <filterByMap type="bool">false</filterByMap>
    </Legend>
    <SpatialRefSys>
      <ProjectCrs type="QString">EPSG:4326</ProjectCrs>
    </SpatialRefSys>
    <DefaultStyles>
      <Fill type="QString"></Fill>
      <Line type="QString"></Line>
      <Marker type="QString"></Marker>
      <RandomColors type="bool">true</RandomColors>
      <AlphaInt type="int">255</AlphaInt>
      <ColorRamp type="QString"></ColorRamp>
    </DefaultStyles>
    <WMSFees type="QString"></WMSFees>
    <Measurement>
      <DistanceUnits type="QString">meters</DistanceUnits>
      <AreaUnits type="QString">m2</AreaUnits>
    </Measurement>
    <WMSUrl type="QString"></WMSUrl>
  </properties>
  <visibility-presets/>
</qgis>

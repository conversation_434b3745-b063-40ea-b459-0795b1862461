<?xml version='1.0' encoding='UTF-8'?>
<osm version="0.6" generator="testdata" upload="false">
    <node id="701000" version="1" timestamp="2014-01-01T00:00:00Z" uid="1" user="test" changeset="1" lon="7.11" lat="1.01"/>
    <node id="701001" version="1" timestamp="2014-01-01T00:00:00Z" uid="1" user="test" changeset="1" lon="7.11" lat="1.04"/>
    <node id="701002" version="1" timestamp="2014-01-01T00:00:00Z" uid="1" user="test" changeset="1" lon="7.14" lat="1.04"/>
    <node id="701003" version="1" timestamp="2014-01-01T00:00:00Z" uid="1" user="test" changeset="1" lon="7.14" lat="1.01"/>
    <way id="701800" version="1" timestamp="2014-01-01T00:00:00Z" uid="1" user="test" changeset="1">
        <nd ref="701000"/>
        <nd ref="701001"/>
        <nd ref="701002"/>
        <tag k="test:section" v="mp-geom"/>
        <tag k="test:id" v="701"/>
    </way>
    <way id="701801" version="1" timestamp="2014-01-01T00:00:00Z" uid="1" user="test" changeset="1">
        <nd ref="701002"/>
        <nd ref="701003"/>
        <nd ref="701000"/>
        <tag k="test:section" v="mp-geom"/>
        <tag k="test:id" v="701"/>
    </way>
    <relation id="701900" version="1" timestamp="2014-01-01T00:00:00Z" uid="1" user="test" changeset="1">
        <member type="way" ref="701800" role="outer"/>
        <member type="way" ref="701801" role="outer"/>
        <tag k="type" v="multipolygon"/>
        <tag k="test:section" v="mp-geom"/>
        <tag k="test:id" v="701"/>
        <tag k="landuse" v="forest"/>
    </relation>
    <relation id="701901" version="1" timestamp="2014-01-01T00:00:00Z" uid="1" user="test" changeset="1">
        <member type="way" ref="701802" role="outer"/> <!-- missing member -->
        <tag k="type" v="multipolygon"/>
        <tag k="landuse" v="forest"/>
    </relation>
</osm>

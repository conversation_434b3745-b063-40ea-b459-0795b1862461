---
Checks: '*,-abseil-string-find-str-contains,-altera-*,-android-cloexec-*,-bugprone-branch-clone,-bugprone-easily-swappable-parameters,-bugprone-macro-parentheses,-cert-dcl21-cpp,-cert-err58-cpp,-clang-analyzer-optin.cplusplus.VirtualCall,-cppcoreguidelines-avoid-c-arrays,-cppcoreguidelines-avoid-magic-numbers,-cppcoreguidelines-macro-usage,-cppcoreguidelines-non-private-member-variables-in-classes,-cppcoreguidelines-owning-memory,-cppcoreguidelines-pro-bounds-array-to-pointer-decay,-cppcoreguidelines-pro-bounds-constant-array-index,-cppcoreguidelines-pro-bounds-pointer-arithmetic,-cppcoreguidelines-pro-type-const-cast,-cppcoreguidelines-pro-type-reinterpret-cast,-cppcoreguidelines-pro-type-static-cast-downcast,-cppcoreguidelines-pro-type-vararg,-fuchsia-*,-google-runtime-references,-hicpp-avoid-c-arrays,-hicpp-invalid-access-moved,-hicpp-no-array-decay,-hicpp-no-assembler,-hicpp-vararg,-llvmlibc-*,-llvm-qualified-auto,-misc-macro-parentheses,-misc-non-private-member-variables-in-classes,-misc-no-recursion,-misc-unused-parameters,-modernize-avoid-c-arrays,-modernize-make-unique,-modernize-raw-string-literal,-modernize-use-trailing-return-type,-readability-avoid-const-params-in-decls,-readability-function-cognitive-complexity,-readability-identifier-length,-readability-implicit-bool-cast,-readability-implicit-bool-conversion,-readability-magic-numbers,-readability-qualified-auto'
#
#  For a list of check options, see:
#  https://clang.llvm.org/extra/clang-tidy/checks/list.html
#
#  Disabled checks:
#
#  abseil-string-find-str-contains
#    We don't want the dependency.
#
#  altera-*
#    Doesn't apply.
#
#  android-cloexec-*
#    O_CLOEXEC isn't available on Windows making this non-portable.
#
#  bugprone-branch-clone
#    Nice idea but collides but with switch statements we'll need to use
#    fall-throughs to fix this, which is also bad.
#
#  bugprone-easily-swappable-parameters
#    Interesting test, but not something we can do much about in many places.
#
#  bugprone-macro-parentheses
#    False positive in the only place where it reports something and
#    disabling locally doesn't work.
#
#  cert-dcl21-cpp
#    It is unclear whether this is still a good recommendation in modern C++.
#
#  cert-err58-cpp
#    Used in several singelton factory functions. I don't know of a better
#    way to do this and it is rather unlikely that this will be a problem.
#
#  clang-analyzer-optin.cplusplus.VirtualCall
#    Disable a warning we get from the Catch test framework.
#
#  cppcoreguidelines-avoid-c-arrays
#    Alias for modernize-avoid-c-arrays.
#
#  cppcoreguidelines-avoid-magic-numbers
#    Generally good advice, but there are too many places where this is
#    useful, for instance in tests.
#
#  cppcoreguidelines-macro-usage
#    There are cases where we actually need macros.
#
#  cppcoreguidelines-non-private-member-variables-in-classes
#    Alias for misc-non-private-member-variables-in-classes
#
#  cppcoreguidelines-owning-memory
#    Don't want to add dependency on gsl library.
#
#  cppcoreguidelines-pro-bounds-array-to-pointer-decay
#    Limited use and many false positives including for all asserts.
#
#  cppcoreguidelines-pro-bounds-constant-array-index
#    Is needed for low-level code.
#
#  cppcoreguidelines-pro-bounds-pointer-arithmetic
#    This is a low-level library, it needs to do pointer arithmetic.
#
#  cppcoreguidelines-pro-type-const-cast
#    When you need it, you need it.
#
#  cppcoreguidelines-pro-type-reinterpret-cast
#    This is a low-level library, it needs to do reinterpret-casts.
#
#  cppcoreguidelines-pro-type-static-cast-downcast
#    This is needed and totally okay if we are sure about the types.
#
#  cppcoreguidelines-pro-type-vararg
#    We need some of these functions at least and for some functions it isn't
#    even clear that those are vararg functions.
#
#  fuchsia-*
#    Much too strict.
#
#  google-runtime-references
#    This is just a matter of preference, and we can't change the interfaces
#    now anyways.
#
#  hicpp-avoid-c-arrays
#    Alias for modernize-avoid-c-arrays.
#
#  hicpp-invalid-access-moved
#    Creates false positives.
#
#  hicpp-no-array-decay
#    Alias for cppcoreguidelines-pro-bounds-array-to-pointer-decay.
#
#  hicpp-no-assembler
#    Reports are from macros we don't have any control over.
#
#  hicpp-vararg
#    Too strict, sometimes calling vararg functions is necessary.
#
#  llvm-qualified-auto
#  readability-qualified-auto
#    This reports too many cases. Typical case is an iterator that might be
#    a pointer on one system but some special type on another.
#
#  llvmlibc-*
#    Doesn't apply to us.
#
#  misc-macro-parentheses
#    Old name for bugprone-macro-parentheses.
#
#  misc-non-private-member-variables-in-classes
#    Reports this also for structs, which doesn't make any sense. There is
#    an option "IgnoreClassesWithAllMemberVariablesBeingPublic" which should
#    disable this, but it didn't work for me.
#
#  misc-no-recursion
#    There is nothing wrong with recursion.
#
#  misc-unused-parameters
#    Can't be fixed, because then Doxygen will complain. (In file
#    include/osmium/area/problem_reporter.hpp).
#
#  modernize-avoid-c-arrays
#    Makes sense for some array, but especially for char arrays using
#    std::array isn't a good solution.
#
#  modernize-make-unique
#    This is a C++11 library and C++ doesn't have std::make_unique.
#
#  modernize-raw-string-literal
#    Readability isn't that much better, arguably worse because of the new
#    syntax.
#
#  modernize-use-trailing-return-type
#    I am not quite that modern.
#
#  readability-avoid-const-params-in-decls
#    This is header only library, so the declaration and implementation are
#    often the same and we want to have the const in implementations.
#
#  readability-function-cognitive-complexity
#    Sometimes the large functions are needed.
#
#  readability-identifier-length
#    Too strict.
#
#  readability-implicit-bool-cast
#    Old name for readability-implicit-bool-conversion.
#
#  readability-implicit-bool-conversion
#    I don't think this makes the code more readable.
#
#  readability-magic-numbers
#    Alias for cppcoreguidelines-avoid-magic-numbers.
#
#WarningsAsErrors: '*'
HeaderFilterRegex: '\/include\/osmium\/.*'
...

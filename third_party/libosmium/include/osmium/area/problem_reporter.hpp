#ifndef OSMIUM_AREA_PROBLEM_REPORTER_HPP
#define OSMIUM_AREA_PROBLEM_REPORTER_HPP

/*

This file is part of Osmium (https://osmcode.org/libosmium).

Copyright 2013-2023 <PERSON><PERSON> <<EMAIL>> and others (see README).

Boost Software License - Version 1.0 - August 17th, 2003

Permission is hereby granted, free of charge, to any person or organization
obtaining a copy of the software and accompanying documentation covered by
this license (the "Software") to use, reproduce, display, distribute,
execute, and transmit the Software, and to prepare derivative works of the
Software, and to permit third-parties to whom the Software is furnished to
do so, all subject to the following:

The copyright notices in the Software and this entire statement, including
the above license grant, this restriction and the following disclaimer,
must be included in all copies of the Software, in whole or in part, and
all derivative works of the Software, unless such copies or derivative
works are solely in the form of machine-executable object code generated by
a source language processor.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
FITNESS FOR A PARTICULAR PURPOSE, TITLE AND NON-INFRINGEMENT. IN NO EVENT
SHALL THE COPYRIGHT HOLDERS OR ANYONE DISTRIBUTING THE SOFTWARE BE LIABLE
FOR ANY DAMAGES OR OTHER LIABILITY, WHETHER IN CONTRACT, TORT OR OTHERWISE,
ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER
DEALINGS IN THE SOFTWARE.

*/

#include <osmium/osm/item_type.hpp>
#include <osmium/osm/location.hpp>
#include <osmium/osm/types.hpp>

#include <cstddef>

namespace osmium {

    class NodeRef;
    class Way;

    namespace area {

        /**
         * When assembling a multipolygon/area from a multipolygon relation
         * or a closed way several problems can be detected. This includes
         * intersections between lines, wrong role attributes on relation
         * members etc. These problems are reported by the area::Assembler
         * class to the ProblemReporter class or one of its child classes.
         *
         * This is the parent class which does nothing with the reports.
         * Child classes are expected to implement different ways of
         * reporting the problems.
         */
        class ProblemReporter {

        protected:

            // Type of object we are currently working on
            osmium::item_type m_object_type = osmium::item_type::undefined;

            // ID of the relation/way we are currently working on
            osmium::object_id_type m_object_id = 0;

            // Number of nodes in the area
            size_t m_nodes = 0;

        public:

            ProblemReporter() = default;

            ProblemReporter(const ProblemReporter&) = default;
            ProblemReporter& operator=(const ProblemReporter&) = default;

            ProblemReporter(ProblemReporter&&) noexcept = default;
            ProblemReporter& operator=(ProblemReporter&&) noexcept = default;

            virtual ~ProblemReporter() noexcept = default;

            /**
             * Set the object the next problem reports will be on.
             *
             * @param object_type The type of the object.
             * @param object_id The ID of the object.
             */
            void set_object(osmium::item_type object_type, osmium::object_id_type object_id) noexcept {
                m_object_type = object_type;
                m_object_id = object_id;
            }

            osmium::object_id_type object_id() const noexcept {
                return m_object_id;
            }

            void set_nodes(size_t nodes) noexcept {
                m_nodes = nodes;
            }

// Disable "unused-parameter" warning, so that the compiler will not complain.
// We can't remove the parameter names, because then doxygen will complain.
#pragma GCC diagnostic push
#pragma GCC diagnostic ignored "-Wunused-parameter"

            /**
             * Report a duplicate node, ie. two nodes with the same location.
             *
             * @param node_id1  ID of the first node.
             * @param node_id2  ID of the second node.
             * @param location  Location of both nodes.
             */
            virtual void report_duplicate_node(osmium::object_id_type node_id1, osmium::object_id_type node_id2, osmium::Location location) {
            }

            /**
             * Report a node/location where rings touch. This is often wrong,
             * but not necessarily so.
             *
             * @param node_id   ID of the node.
             * @param location  Location of the node.
             */
            virtual void report_touching_ring(osmium::object_id_type node_id, osmium::Location location) {
            }

            /**
             * Report an intersection between two segments.
             *
             * @param way1_id        ID of the first involved way.
             * @param way1_seg_start Location where the segment of the first way with the intersection starts
             * @param way1_seg_end   Location where the segment of the first way with the intersection ends
             * @param way2_id        ID of the second involved way.
             * @param way2_seg_start Location where the segment of the second way with the intersection starts
             * @param way2_seg_end   Location where the segment of the second way with the intersection ends
             * @param intersection   Location of the intersection. This might be slightly off the correct location due to rounding.
             */
            virtual void report_intersection(osmium::object_id_type way1_id, osmium::Location way1_seg_start, osmium::Location way1_seg_end,
                                             osmium::object_id_type way2_id, osmium::Location way2_seg_start, osmium::Location way2_seg_end, osmium::Location intersection) {
            }

            /**
             * Report a duplicate segments. Two or more segments are directly
             * on top of each other. This can be a problem, if there is a
             * spike for instance, or it could be okay, if there are touching
             * inner rings.
             *
             * @param nr1  NodeRef of one end of the segment.
             * @param nr2  NodeRef of the other end of the segment.
             */
            virtual void report_duplicate_segment(const osmium::NodeRef& nr1, const osmium::NodeRef& nr2) {
            }

            /**
             * Report a duplicate segments. Two or more segments are directly
             * on top of each other. This can be a problem, if there is a
             * spike for instance, or it could be okay, if there are touching
             * inner rings.
             *
             * @param nr1  NodeRef of one end of the segment.
             * @param nr2  NodeRef of the other end of the segment.
             */
            virtual void report_overlapping_segment(const osmium::NodeRef& nr1, const osmium::NodeRef& nr2) {
            }

            /**
             * Report an open ring.
             *
             * @param nr   NodeRef of one end of the ring.
             * @param way  Optional pointer to way the end node is in.
             */
            virtual void report_ring_not_closed(const osmium::NodeRef& nr, const osmium::Way* way) {
            }

            /**
             * Report a segment that should have role "outer", but has a different role.
             *
             * @param way_id     ID of the way this segment is in.
             * @param seg_start  Start of the segment with the wrong role.
             * @param seg_end    End of the segment with the wrong role.
             */
            virtual void report_role_should_be_outer(osmium::object_id_type way_id, osmium::Location seg_start, osmium::Location seg_end) {
            }

            /**
             * Report a segment that should have role "inner", but has a different role.
             *
             * @param way_id         ID of the way this segment is in.
             * @param seg_start      Start of the segment with the wrong role.
             * @param seg_end        End of the segment with the wrong role.
             */
            virtual void report_role_should_be_inner(osmium::object_id_type way_id, osmium::Location seg_start, osmium::Location seg_end) {
            }

            /**
             * Report a way that is in multiple rings.
             *
             * @param way The way.
             */
            virtual void report_way_in_multiple_rings(const osmium::Way& way) {
            }

            /**
             * Report a way with role inner that has the same tags as the
             * relation or outer ways.
             *
             * @param way The way.
             */
            virtual void report_inner_with_same_tags(const osmium::Way& way) {
            }

            /**
             * Report an invalid location in a way.
             *
             * @param way_id  ID of the way the node is in.
             * @param node_id ID of the node with the invalid location.
             */
            virtual void report_invalid_location(osmium::object_id_type way_id, osmium::object_id_type node_id) {
            }

            /**
             * Report a way that is more than once in a relation.
             *
             * @param way The way
             */
            virtual void report_duplicate_way(const osmium::Way& way) {
            }

            /**
             * In addition to reporting specific problems, this is used to
             * report all ways belonging to areas having problems.
             *
             * @param way The way
             */
            virtual void report_way(const osmium::Way& way) {
            }

#pragma GCC diagnostic pop

        }; // class ProblemReporter

    } // namespace area

} // namespace osmium

#endif // OSMIUM_AREA_PROBLEM_REPORTER_HPP

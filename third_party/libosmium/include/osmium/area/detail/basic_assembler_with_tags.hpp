#ifndef OSMIUM_AREA_DETAIL_BASIC_ASSEMBLER_WITH_TAGS_HPP
#define OSMIUM_AREA_DETAIL_BASIC_ASSEMBLER_WITH_TAGS_HPP

/*

This file is part of Osmium (https://osmcode.org/libosmium).

Copyright 2013-2023 <PERSON><PERSON> <<EMAIL>> and others (see README).

Boost Software License - Version 1.0 - August 17th, 2003

Permission is hereby granted, free of charge, to any person or organization
obtaining a copy of the software and accompanying documentation covered by
this license (the "Software") to use, reproduce, display, distribute,
execute, and transmit the Software, and to prepare derivative works of the
Software, and to permit third-parties to whom the Software is furnished to
do so, all subject to the following:

The copyright notices in the Software and this entire statement, including
the above license grant, this restriction and the following disclaimer,
must be included in all copies of the Software, in whole or in part, and
all derivative works of the Software, unless such copies or derivative
works are solely in the form of machine-executable object code generated by
a source language processor.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
FITNESS FOR A PARTICULAR PURPOSE, TITLE AND NON-INFRINGEMENT. IN NO EVENT
SHALL THE COPYRIGHT HOLDERS OR ANYONE DISTRIBUTING THE SOFTWARE BE LIABLE
FOR ANY DAMAGES OR OTHER LIABILITY, WHETHER IN CONTRACT, TORT OR OTHERWISE,
ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER
DEALINGS IN THE SOFTWARE.

*/

#include <osmium/area/assembler_config.hpp>
#include <osmium/area/detail/basic_assembler.hpp>
#include <osmium/area/stats.hpp>
#include <osmium/builder/osm_object_builder.hpp>
#include <osmium/osm/tag.hpp>

#include <cstring>

namespace osmium {

    namespace area {

        namespace detail {

            class BasicAssemblerWithTags : public detail::BasicAssembler {

            protected:

                bool report_ways() const noexcept {
                    if (!config().problem_reporter) {
                        return false;
                    }
                    return stats().duplicate_nodes ||
                           stats().duplicate_segments ||
                           stats().intersections ||
                           stats().open_rings ||
                           stats().short_ways ||
                           stats().touching_rings ||
                           stats().ways_in_multiple_rings ||
                           stats().wrong_role;
                }

                static void copy_tags_without_type(osmium::builder::AreaBuilder& builder, const osmium::TagList& tags) {
                    osmium::builder::TagListBuilder tl_builder{builder};
                    for (const osmium::Tag& tag : tags) {
                        if (std::strcmp(tag.key(), "type") != 0) {
                            tl_builder.add_tag(tag.key(), tag.value());
                        }
                    }
                }

            public:

                using config_type = osmium::area::AssemblerConfig;

                explicit BasicAssemblerWithTags(const config_type& config) :
                    BasicAssembler(config) {
                }

            }; // class BasicAssemblerWithTags

        } // namespace detail

    } // namespace area

} // namespace osmium

#endif // OSMIUM_AREA_DETAIL_BASIC_ASSEMBLER_WITH_TAGS_HPP

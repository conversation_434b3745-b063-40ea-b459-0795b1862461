#ifndef OSMIUM_AREA_GEOM_ASSEMBLER_HPP
#define OSMIUM_AREA_GEOM_ASSEMBLER_HPP

/*

This file is part of Osmium (https://osmcode.org/libosmium).

Copyright 2013-2023 <PERSON><PERSON>f <<EMAIL>> and others (see README).

Boost Software License - Version 1.0 - August 17th, 2003

Permission is hereby granted, free of charge, to any person or organization
obtaining a copy of the software and accompanying documentation covered by
this license (the "Software") to use, reproduce, display, distribute,
execute, and transmit the Software, and to prepare derivative works of the
Software, and to permit third-parties to whom the Software is furnished to
do so, all subject to the following:

The copyright notices in the Software and this entire statement, including
the above license grant, this restriction and the following disclaimer,
must be included in all copies of the Software, in whole or in part, and
all derivative works of the Software, unless such copies or derivative
works are solely in the form of machine-executable object code generated by
a source language processor.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
FITNESS FOR A PARTICULAR PURPOSE, TITLE AND NON-INFRINGEMENT. IN NO EVENT
SHALL THE COPYRIGHT HOLDERS OR ANYONE DISTRIBUTING THE SOFTWARE BE LIABLE
FOR ANY DAMAGES OR OTHER LIABILITY, WHETHER IN CONTRACT, TORT OR OTHERWISE,
ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER
DEALINGS IN THE SOFTWARE.

*/

#include <osmium/area/assembler_config.hpp>
#include <osmium/area/detail/basic_assembler.hpp>
#include <osmium/area/detail/segment_list.hpp>
#include <osmium/area/stats.hpp>
#include <osmium/builder/osm_object_builder.hpp>
#include <osmium/memory/buffer.hpp>
#include <osmium/osm/relation.hpp>
#include <osmium/osm/way.hpp>

namespace osmium {

    namespace area {

        /**
         * Assembles area objects from closed ways or multipolygon relations
         * and their members. Unlike the Assembler, this one doesn't take
         * tags into account at all. And it doesn't interpret all the config
         * settings and doesn't do all the checks and error reporting the
         * Assembler does.
         *
         * This class was developed specifically for the need of osm2pgsql.
         * Unless you know what you are doing, use the Assembler class instead
         * of this class. Contact the Libosmium developers if you want to use
         * this class.
         */
        class GeomAssembler : public detail::BasicAssembler {

        public:

            using config_type = osmium::area::AssemblerConfig;

            explicit GeomAssembler(const config_type& config) :
                detail::BasicAssembler(config) {
            }

            /**
             * Assemble an area from the given way.
             *
             * The resulting area is put into the out_buffer.
             *
             * @returns false if there was some kind of error building the
             *          area, true otherwise.
             */
            bool operator()(const osmium::Way& way, osmium::memory::Buffer& out_buffer) {
                segment_list().extract_segments_from_way(config().problem_reporter, stats().duplicate_nodes, way);

                if (!create_rings()) {
                    return false;
                }

                {
                    osmium::builder::AreaBuilder builder{out_buffer};
                    builder.initialize_from_object(way);
                    add_rings_to_area(builder);
                }
                out_buffer.commit();

                return true;
            }

            /**
             * Assemble an area from the given relation and its member ways
             * which are in the ways_buffer.
             *
             * The resulting area is put into the out_buffer.
             *
             * @returns false if there was some kind of error building the
             *          area, true otherwise.
             */
            bool operator()(const osmium::Relation& relation, const osmium::memory::Buffer& ways_buffer, osmium::memory::Buffer& out_buffer) {
                for (const auto& way : ways_buffer.select<osmium::Way>()) {
                    segment_list().extract_segments_from_way(config().problem_reporter, stats().duplicate_nodes, way);
                }

                if (!create_rings()) {
                    return false;
                }

                {
                    osmium::builder::AreaBuilder builder{out_buffer};
                    builder.initialize_from_object(relation);
                    add_rings_to_area(builder);
                }
                out_buffer.commit();

                return true;
            }

        }; // class GeomAssembler

    } // namespace area

} // namespace osmium

#endif // OSMIUM_AREA_GEOM_ASSEMBLER_HPP

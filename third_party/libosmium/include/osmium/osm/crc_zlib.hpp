#ifndef OSMIUM_OSM_CRC_ZLIB_HPP
#define OSMIUM_OSM_CRC_ZLIB_HPP

/*

This file is part of Osmium (https://osmcode.org/libosmium).

Copyright 2013-2023 <PERSON><PERSON> <<EMAIL>> and others (see README).

Boost Software License - Version 1.0 - August 17th, 2003

Permission is hereby granted, free of charge, to any person or organization
obtaining a copy of the software and accompanying documentation covered by
this license (the "Software") to use, reproduce, display, distribute,
execute, and transmit the Software, and to prepare derivative works of the
Software, and to permit third-parties to whom the Software is furnished to
do so, all subject to the following:

The copyright notices in the Software and this entire statement, including
the above license grant, this restriction and the following disclaimer,
must be included in all copies of the Software, in whole or in part, and
all derivative works of the Software, unless such copies or derivative
works are solely in the form of machine-executable object code generated by
a source language processor.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
FITNESS FOR A PARTICULAR PURPOSE, TITLE AND NON-INFRINGEMENT. IN NO EVENT
SHALL THE COPYRIGHT HOLDERS OR ANYONE DISTRIBUTING THE SOFTWARE BE LIABLE
FOR ANY DAMAGES OR OTHER LIABILITY, WHETHER IN CONTRACT, TORT OR OTHERWISE,
ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER
DEALINGS IN THE SOFTWARE.

*/

#include <zlib.h>

#include <cstddef>

namespace osmium {

    /**
     * This class is used together with the CRC class to implement a CRC32
     * checksum based on the implementation from zlib.
     *
     * Usage:
     *
     * @code
     * osmium::CRC<osmium::CRC_zlib> crc32;
     * const osmium::Node& node = ...;
     * crc32.update(node);
     * std::cout << crc32.checksum() << '\n';
     * @endcode
     */
    class CRC_zlib {

        unsigned long m_crc32 = ::crc32(0, nullptr, 0); // NOLINT(google-runtime-int)

    public:

        void process_byte(const unsigned char byte) noexcept {
            m_crc32 = ::crc32(m_crc32, &byte, 1U);
        }

        void process_bytes(const void* buffer, std::size_t byte_count) noexcept {
            m_crc32 = ::crc32(m_crc32, reinterpret_cast<const unsigned char*>(buffer), static_cast<unsigned int>(byte_count));
        }

        unsigned long checksum() const noexcept { // NOLINT(google-runtime-int)
            return m_crc32;
        }

    }; // class CRC_zlib

} // namespace osmium

#endif // OSMIUM_OSM_CRC_ZLIB_HPP

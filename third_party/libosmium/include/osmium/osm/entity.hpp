#ifndef OSMIUM_OSM_ENTITY_HPP
#define OSMIUM_OSM_ENTITY_HPP

/*

This file is part of Osmium (https://osmcode.org/libosmium).

Copyright 2013-2023 <PERSON><PERSON> <<EMAIL>> and others (see README).

Boost Software License - Version 1.0 - August 17th, 2003

Permission is hereby granted, free of charge, to any person or organization
obtaining a copy of the software and accompanying documentation covered by
this license (the "Software") to use, reproduce, display, distribute,
execute, and transmit the Software, and to prepare derivative works of the
Software, and to permit third-parties to whom the Software is furnished to
do so, all subject to the following:

The copyright notices in the Software and this entire statement, including
the above license grant, this restriction and the following disclaimer,
must be included in all copies of the Software, in whole or in part, and
all derivative works of the Software, unless such copies or derivative
works are solely in the form of machine-executable object code generated by
a source language processor.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
FITNESS FOR A PARTICULAR PURPOSE, TITLE AND NON-INFRINGEMENT. IN NO EVENT
SHALL THE COPYRIGHT HOLDERS OR ANYONE DISTRIBUTING THE SOFTWARE BE LIABLE
FOR ANY DAMAGES OR OTHER LIABILITY, WHETHER IN CONTRACT, TORT OR OTHERWISE,
ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER
DEALINGS IN THE SOFTWARE.

*/

#include <osmium/memory/item.hpp>
#include <osmium/osm/entity_bits.hpp>
#include <osmium/osm/item_type.hpp>

namespace osmium {

    namespace detail {

        template <typename TSubitem, typename TIter>
        inline TSubitem& subitem_of_type(TIter it, const TIter& end) {
            for (; it != end; ++it) {
                if (TSubitem::is_compatible_to(it->type()) && !it->removed()) {
                    return reinterpret_cast<TSubitem&>(*it);
                }
            }

            // If no subitem of the TSubitem type was found,
            // return a default constructed one.
            static TSubitem subitem;
            return subitem;
        }

    } // namespace detail

    /**
     * \brief OSMEntity is the abstract base class for the OSMObject and
     *        Changeset classes.
     */
    class OSMEntity : public osmium::memory::Item {

    public:

        constexpr static bool is_compatible_to(osmium::item_type t) noexcept {
            return t == osmium::item_type::node ||
                   t == osmium::item_type::way ||
                   t == osmium::item_type::relation ||
                   t == osmium::item_type::area ||
                   t == osmium::item_type::changeset;
        }

        explicit OSMEntity(osmium::memory::item_size_type size, osmium::item_type type) :
            Item(size, type) {
        }

        bool type_is_in(osmium::osm_entity_bits::type entity_bits) const {
            return (osm_entity_bits::from_item_type(type()) & entity_bits) != 0;
        }

    }; // class OSMEntity

} // namespace osmium

#endif // OSMIUM_OSM_ENTITY_HPP

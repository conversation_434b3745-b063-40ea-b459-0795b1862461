#ifndef OSMIUM_UTIL_MEMORY_HPP
#define OSMIUM_UTIL_MEMORY_HPP

/*

This file is part of Osmium (https://osmcode.org/libosmium).

Copyright 2013-2023 <PERSON><PERSON> <<EMAIL>> and others (see README).

Boost Software License - Version 1.0 - August 17th, 2003

Permission is hereby granted, free of charge, to any person or organization
obtaining a copy of the software and accompanying documentation covered by
this license (the "Software") to use, reproduce, display, distribute,
execute, and transmit the Software, and to prepare derivative works of the
Software, and to permit third-parties to whom the Software is furnished to
do so, all subject to the following:

The copyright notices in the Software and this entire statement, including
the above license grant, this restriction and the following disclaimer,
must be included in all copies of the Software, in whole or in part, and
all derivative works of the Software, unless such copies or derivative
works are solely in the form of machine-executable object code generated by
a source language processor.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
FITNESS FOR A PARTICULAR PURPOSE, TITLE AND NON-INFRINGEMENT. IN NO EVENT
SHALL THE COPYRIGHT HOLDERS OR ANYONE DISTRIBUTING THE SOFTWARE BE LIABLE
FOR ANY DAMAGES OR OTHER LIABILITY, WHETHER IN CONTRACT, TORT OR OTHERWISE,
ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER
DEALINGS IN THE SOFTWARE.

*/

#include <cstdlib>
#include <fstream>
#include <string>

#include <osmium/util/misc.hpp>

namespace osmium {

    class MemoryUsage {

        int64_t m_current = 0;
        int64_t m_peak    = 0;

#ifdef __linux__
        static int64_t parse_number(const std::string& line) {
            const auto f = line.find_first_of("0123456789");
            const auto l = line.find_last_of("0123456789");
            return osmium::detail::str_to_int<int64_t>(line.substr(f, l - f + 1).c_str());
        }
#endif

    public:
        /**
         * Get the memory usage for the current process. The constructor will
         * get the memory usage. Use the current() and peak() calls to access
         * the result.
         *
         * This will only work on Linux, on other architectures this will
         * always return 0.
         */
        MemoryUsage() {
#ifdef __linux__
            static const char* filename = "/proc/self/status";
            std::ifstream status_file(filename);

            if (status_file.is_open()) {
                std::string line;
                while (!status_file.eof()) {
                    std::getline(status_file, line);
                    if (line.substr(0, 6) == "VmPeak") {
                        m_peak = parse_number(line);
                    }
                    if (line.substr(0, 6) == "VmSize") {
                        m_current = parse_number(line);
                    }
                }
            }
#endif
        }

        /// Return current memory usage in MBytes
        int current() const {
            return static_cast<int>(m_current / 1024);
        }

        /// Return peak memory usage in MBytes
        int peak() const {
            return static_cast<int>(m_peak / 1024);
        }

    }; // class MemoryUsage

} // namespace osmium

#endif // OSMIUM_UTIL_MEMORY_HPP

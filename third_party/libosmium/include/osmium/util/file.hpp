#ifndef OSMIUM_UTIL_FILE_HPP
#define OSMIUM_UTIL_FILE_HPP

/*

This file is part of Osmium (https://osmcode.org/libosmium).

Copyright 2013-2023 <PERSON><PERSON> <<EMAIL>> and others (see README).

Boost Software License - Version 1.0 - August 17th, 2003

Permission is hereby granted, free of charge, to any person or organization
obtaining a copy of the software and accompanying documentation covered by
this license (the "Software") to use, reproduce, display, distribute,
execute, and transmit the Software, and to prepare derivative works of the
Software, and to permit third-parties to whom the Software is furnished to
do so, all subject to the following:

The copyright notices in the Software and this entire statement, including
the above license grant, this restriction and the following disclaimer,
must be included in all copies of the Software, in whole or in part, and
all derivative works of the Software, unless such copies or derivative
works are solely in the form of machine-executable object code generated by
a source language processor.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
FITNESS FOR A PARTICULAR PURPOSE, TITLE AND NON-INFRINGEMENT. IN NO EVENT
SHALL THE COPYRIGHT HOLDERS OR ANYONE DISTRIBUTING THE SOFTWARE BE LIABLE
FOR ANY DAMAGES OR OTHER LIABILITY, WHETHER IN CONTRACT, TORT OR OTHERWISE,
ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER
DEALINGS IN THE SOFTWARE.

*/

#include <cassert>
#include <cerrno>
#include <cstddef>
#include <cstdio>
#include <limits>
#include <string>
#include <sys/stat.h>
#include <sys/types.h>
#include <system_error>

#ifdef _WIN32
#  ifndef WIN32_LEAN_AND_MEAN
#   define WIN32_LEAN_AND_MEAN // Prevent winsock.h inclusion; avoid winsock2.h conflict
#  endif
# include <crtdbg.h>
# include <io.h>
# include <windows.h>
#endif

#ifndef _MSC_VER
# include <unistd.h>
#endif

namespace osmium {

#ifdef _MSC_VER
    namespace detail {

        // Disable parameter validation on Windows and reenable it
        // automatically when scope closes.
        // https://docs.microsoft.com/en-us/cpp/c-runtime-library/parameter-validation
        class disable_invalid_parameter_handler {

            static void invalid_parameter_handler(
                    const wchar_t* expression,
                    const wchar_t* function,
                    const wchar_t* file,
                    unsigned int line,
                    uintptr_t pReserved
                    ) {
                // do nothing
            }

            _invalid_parameter_handler old_handler;
            int old_report_mode;

        public:

            disable_invalid_parameter_handler() :
                old_handler(_set_thread_local_invalid_parameter_handler(invalid_parameter_handler)),
                old_report_mode(_CrtSetReportMode(_CRT_ASSERT, 0)) {
            }

            ~disable_invalid_parameter_handler() {
                _CrtSetReportMode(_CRT_ASSERT, old_report_mode);
                _set_thread_local_invalid_parameter_handler(old_handler);
            }

        }; // class disable_invalid_parameter_handler

    } // namespace detail
#endif

    inline namespace util {

        /**
         * Get file size.
         * This is a small wrapper around a system call.
         *
         * @param fd File descriptor
         * @returns file size
         * @throws std::system_error If system call failed
         */
        inline std::size_t file_size(int fd) {
#ifdef _MSC_VER
            // Windows implementation
            osmium::detail::disable_invalid_parameter_handler diph;
            // https://msdn.microsoft.com/en-us/library/dfbc2kec.aspx
            const auto size = ::_filelengthi64(fd);
            if (size < 0) {
                throw std::system_error{errno, std::system_category(), "Could not get file size"};
            }
            return static_cast<std::size_t>(size);
#else
            // Unix implementation
            struct stat s; // NOLINT clang-tidy
            if (::fstat(fd, &s) != 0) {
                throw std::system_error{errno, std::system_category(), "Could not get file size"};
            }
            return static_cast<std::size_t>(s.st_size);
#endif
        }

        /**
         * Get file size.
         * This is a small wrapper around a system call.
         *
         * @param name File name
         * @returns file size
         * @throws std::system_error If system call failed
         * @pre name must not be nullptr
         */
        inline std::size_t file_size(const char* name) {
#ifdef _MSC_VER
            // Windows implementation
            osmium::detail::disable_invalid_parameter_handler diph;
            // https://msdn.microsoft.com/en-us/library/14h5k7ff.aspx
            struct _stat64 s{};
            if (::_stati64(name, &s) != 0) {
                throw std::system_error{errno, std::system_category(), std::string{"Could not get file size of file '"} + name + "'"};
            }
#else
            // Unix implementation
            struct stat s; // NOLINT clang-tidy
            if (::stat(name, &s) != 0) {
                throw std::system_error{errno, std::system_category(), std::string{"Could not get file size of file '"} + name + "'"};
            }
#endif
            return static_cast<std::size_t>(s.st_size);
        }

        /**
         * Get file size.
         * This is a small wrapper around a system call.
         *
         * @param name File name
         * @returns file size
         * @throws std::system_error If system call failed
         */
        inline std::size_t file_size(const std::string& name) {
            return file_size(name.c_str());
        }

        /**
         * Resize file.
         * Small wrapper around ftruncate(2) system call.
         *
         * @param fd File descriptor
         * @param new_size New size
         * @throws std::system_error If ftruncate(2) call failed
         */
        inline void resize_file(int fd, std::size_t new_size) {
#ifdef _MSC_VER
            osmium::detail::disable_invalid_parameter_handler diph;
            assert(new_size <= static_cast<std::size_t>(std::numeric_limits<__int64>::max()));
            // https://msdn.microsoft.com/en-us/library/whx354w1.aspx
            if (::_chsize_s(fd, static_cast<__int64>(new_size)) != 0) {
#else
            if (::ftruncate(fd, static_cast<off_t>(new_size)) != 0) {
#endif
                throw std::system_error{errno, std::system_category(), "Could not resize file"};
            }
        }

        /**
         * Get the page size for this system.
         */
        inline std::size_t get_pagesize() noexcept {
#ifdef _WIN32
            // Windows implementation
            SYSTEM_INFO si;
            GetSystemInfo(&si);
            return si.dwPageSize;
#else
            // Unix implementation
            return static_cast<std::size_t>(::sysconf(_SC_PAGESIZE));
#endif
        }

        /**
         * Get current offset into file.
         *
         * @param fd Open file descriptor.
         * @returns File offset or 0 if it is not available.
         */
        inline std::size_t file_offset(int fd) noexcept {
#ifdef _MSC_VER
            osmium::detail::disable_invalid_parameter_handler diph;
            // https://msdn.microsoft.com/en-us/library/1yee101t.aspx
            const auto offset = _lseeki64(fd, 0, SEEK_CUR);
#else
            const auto offset = ::lseek(fd, 0, SEEK_CUR);
#endif
            if (offset == -1) {
                return 0;
            }
            return static_cast<std::size_t>(offset);
        }

        /**
         * Check whether the file descriptor refers to a TTY.
         *
         * @param fd Open file descriptor.
         */
        inline bool isatty(int fd) noexcept {
#ifdef _MSC_VER
            osmium::detail::disable_invalid_parameter_handler diph;
            // https://msdn.microsoft.com/en-us/library/f4s0ddew.aspx
            return _isatty(fd) != 0;
#else
            return ::isatty(fd) != 0;
#endif
        }

    } // namespace util

} // namespace osmium

#endif // OSMIUM_UTIL_FILE_HPP

#ifndef OSMIUM_INDEX_DETAIL_VECTOR_MAP_HPP
#define OSMIUM_INDEX_DETAIL_VECTOR_MAP_HPP

/*

This file is part of Osmium (https://osmcode.org/libosmium).

Copyright 2013-2023 <PERSON><PERSON> <<EMAIL>> and others (see README).

Boost Software License - Version 1.0 - August 17th, 2003

Permission is hereby granted, free of charge, to any person or organization
obtaining a copy of the software and accompanying documentation covered by
this license (the "Software") to use, reproduce, display, distribute,
execute, and transmit the Software, and to prepare derivative works of the
Software, and to permit third-parties to whom the Software is furnished to
do so, all subject to the following:

The copyright notices in the Software and this entire statement, including
the above license grant, this restriction and the following disclaimer,
must be included in all copies of the Software, in whole or in part, and
all derivative works of the Software, unless such copies or derivative
works are solely in the form of machine-executable object code generated by
a source language processor.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
FITNESS FOR A PARTICULAR PURPOSE, TITLE AND NON-INFRINGEMENT. IN NO EVENT
SHALL THE COPYRIGHT HOLDERS OR ANYONE DISTRIBUTING THE SOFTWARE BE LIABLE
FOR ANY DAMAGES OR OTHER LIABILITY, WHETHER IN CONTRACT, TORT OR OTHERWISE,
ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER
DEALINGS IN THE SOFTWARE.

*/

#include <osmium/index/index.hpp>
#include <osmium/index/map.hpp>
#include <osmium/io/detail/read_write.hpp>

#include <algorithm>
#include <cstddef>
#include <memory>
#include <utility>


namespace osmium {

    namespace index {

        namespace map {

            template <typename TVector, typename TId, typename TValue>
            class VectorBasedDenseMap : public Map<TId, TValue> {

                TVector m_vector;

            public:

                using element_type   = TValue;
                using vector_type    = TVector;
                using iterator       = typename vector_type::iterator;
                using const_iterator = typename vector_type::const_iterator;

                VectorBasedDenseMap() :
                    m_vector() {
                }

                VectorBasedDenseMap(const VectorBasedDenseMap&) = default;
                VectorBasedDenseMap& operator=(const VectorBasedDenseMap&) = default;

                VectorBasedDenseMap(VectorBasedDenseMap&&) noexcept = default;
                VectorBasedDenseMap& operator=(VectorBasedDenseMap&&) noexcept = default;

                ~VectorBasedDenseMap() noexcept override = default;

                explicit VectorBasedDenseMap(int fd) :
                    m_vector(fd) {
                }

                void reserve(const std::size_t size) final {
                    m_vector.reserve(size);
                }

                void set(const TId id, const TValue value) final {
                    if (size() <= id) {
                        m_vector.resize(id+1);
                    }
                    m_vector[id] = value;
                }

                TValue get(const TId id) const final {
                    if (id >= m_vector.size()) {
                        throw osmium::not_found{id};
                    }
                    const TValue value = m_vector[id];
                    if (value == osmium::index::empty_value<TValue>()) {
                        throw osmium::not_found{id};
                    }
                    return value;
                }

                TValue get_noexcept(const TId id) const noexcept final {
                    if (id >= m_vector.size()) {
                        return osmium::index::empty_value<TValue>();
                    }
                    return m_vector[id];
                }

                std::size_t size() const final {
                    return m_vector.size();
                }

                std::size_t byte_size() const {
                    return m_vector.size() * sizeof(element_type);
                }

                std::size_t used_memory() const final {
                    return sizeof(TValue) * size();
                }

                void clear() final {
                    m_vector.clear();
                    m_vector.shrink_to_fit();
                }

                void dump_as_array(const int fd) final {
                    osmium::io::detail::reliable_write(fd, reinterpret_cast<const char*>(m_vector.data()), byte_size());
                }

                iterator begin() {
                    return m_vector.begin();
                }

                iterator end() {
                    return m_vector.end();
                }

                const_iterator cbegin() const {
                    return m_vector.cbegin();
                }

                const_iterator cend() const {
                    return m_vector.cend();
                }

                const_iterator begin() const {
                    return m_vector.cbegin();
                }

                const_iterator end() const {
                    return m_vector.cend();
                }

            }; // class VectorBasedDenseMap


            template <typename TId, typename TValue, template <typename...> class TVector>
            class VectorBasedSparseMap : public Map<TId, TValue> {

            public:

                using element_type   = typename std::pair<TId, TValue>;
                using vector_type    = TVector<element_type>;
                using iterator       = typename vector_type::iterator;
                using const_iterator = typename vector_type::const_iterator;

            private:

                vector_type m_vector;

                typename vector_type::const_iterator find_id(const TId id) const noexcept {
                    const element_type element{
                        id,
                        osmium::index::empty_value<TValue>()};
                    return std::lower_bound(m_vector.begin(), m_vector.end(), element, [](const element_type& a, const element_type& b) {
                        return a.first < b.first;
                    });
                }

            public:

                VectorBasedSparseMap() :
                    m_vector() {
                }

                explicit VectorBasedSparseMap(int fd) :
                    m_vector(fd) {
                }

                VectorBasedSparseMap(const VectorBasedSparseMap&) = default;
                VectorBasedSparseMap& operator=(const VectorBasedSparseMap&) = default;

                VectorBasedSparseMap(VectorBasedSparseMap&&) noexcept = default;
                VectorBasedSparseMap& operator=(VectorBasedSparseMap&&) noexcept = default;

                ~VectorBasedSparseMap() noexcept override = default;

                void set(const TId id, const TValue value) final {
                    m_vector.push_back(element_type(id, value));
                }

                TValue get(const TId id) const final {
                    const auto result = find_id(id);
                    if (result == m_vector.end() || result->first != id) {
                        throw osmium::not_found{id};
                    }

                    return result->second;
                }

                TValue get_noexcept(const TId id) const noexcept final {
                    const auto result = find_id(id);
                    if (result == m_vector.end() || result->first != id) {
                        return osmium::index::empty_value<TValue>();
                    }

                    return result->second;
                }

                std::size_t size() const final {
                    return m_vector.size();
                }

                std::size_t byte_size() const {
                    return m_vector.size() * sizeof(element_type);
                }

                std::size_t used_memory() const final {
                    return sizeof(element_type) * size();
                }

                void clear() final {
                    m_vector.clear();
                    m_vector.shrink_to_fit();
                }

                void sort() final {
                    std::sort(m_vector.begin(), m_vector.end());
                }

                void dump_as_array(const int fd) final {
                    constexpr const size_t value_size = sizeof(TValue);
                    constexpr const size_t buffer_size = (10L * 1024L * 1024L) / value_size;
                    const std::unique_ptr<TValue[]> output_buffer{new TValue[buffer_size]};

                    size_t buffer_start_id = 0;
                    for (auto it = cbegin(); it != cend();) {
                        std::fill_n(output_buffer.get(), buffer_size, osmium::index::empty_value<TValue>());
                        size_t offset = 0;
                        for (; offset < buffer_size && it != end(); ++offset) {
                            if (buffer_start_id + offset == it->first) {
                                output_buffer[offset] = it->second;
                                ++it;
                            }
                        }
                        osmium::io::detail::reliable_write(fd, reinterpret_cast<const unsigned char*>(output_buffer.get()), offset * value_size);
                        buffer_start_id += buffer_size;
                    }
                }

                void dump_as_list(const int fd) final {
                    osmium::io::detail::reliable_write(fd, reinterpret_cast<const char*>(m_vector.data()), byte_size());
                }

                iterator begin() {
                    return m_vector.begin();
                }

                iterator end() {
                    return m_vector.end();
                }

                const_iterator cbegin() const {
                    return m_vector.cbegin();
                }

                const_iterator cend() const {
                    return m_vector.cend();
                }

                const_iterator begin() const {
                    return m_vector.cbegin();
                }

                const_iterator end() const {
                    return m_vector.cend();
                }

            }; // class VectorBasedSparseMap

        } // namespace map

    } // namespace index

} // namespace osmium

#endif // OSMIUM_INDEX_DETAIL_VECTOR_MAP_HPP

#ifndef OSMIUM_OSM_HPP
#define OSMIUM_OSM_HPP

/*

This file is part of Osmium (https://osmcode.org/libosmium).

Copyright 2013-2023 <PERSON><PERSON> <<EMAIL>> and others (see README).

Boost Software License - Version 1.0 - August 17th, 2003

Permission is hereby granted, free of charge, to any person or organization
obtaining a copy of the software and accompanying documentation covered by
this license (the "Software") to use, reproduce, display, distribute,
execute, and transmit the Software, and to prepare derivative works of the
Software, and to permit third-parties to whom the Software is furnished to
do so, all subject to the following:

The copyright notices in the Software and this entire statement, including
the above license grant, this restriction and the following disclaimer,
must be included in all copies of the Software, in whole or in part, and
all derivative works of the Software, unless such copies or derivative
works are solely in the form of machine-executable object code generated by
a source language processor.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
FITNESS FOR A PARTICULAR PURPOSE, TITLE AND NON-INFRINGEMENT. IN NO EVENT
SHALL THE COPYRIGHT HOLDERS OR ANYONE DISTRIBUTING THE SOFTWARE BE LIABLE
FOR ANY DAMAGES OR OTHER LIABILITY, WHETHER IN CONTRACT, TORT OR OTHERWISE,
ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER
DEALINGS IN THE SOFTWARE.

*/

#include <osmium/osm/area.hpp> // IWYU pragma: export
#include <osmium/osm/changeset.hpp> // IWYU pragma: export
#include <osmium/osm/entity.hpp> // IWYU pragma: export
#include <osmium/osm/entity_bits.hpp> // IWYU pragma: export
#include <osmium/osm/item_type.hpp> // IWYU pragma: export
#include <osmium/osm/location.hpp> // IWYU pragma: export
#include <osmium/osm/node.hpp> // IWYU pragma: export
#include <osmium/osm/node_ref.hpp> // IWYU pragma: export
#include <osmium/osm/node_ref_list.hpp> // IWYU pragma: export
#include <osmium/osm/object.hpp> // IWYU pragma: export
#include <osmium/osm/relation.hpp> // IWYU pragma: export
#include <osmium/osm/timestamp.hpp> // IWYU pragma: export
#include <osmium/osm/types.hpp> // IWYU pragma: export
#include <osmium/osm/way.hpp> // IWYU pragma: export

/**
 * @brief Namespace for everything in the Osmium library.
 */
namespace osmium {
} // namespace osmium

#endif // OSMIUM_OSM_HPP

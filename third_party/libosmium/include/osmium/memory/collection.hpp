#ifndef OSMIUM_MEMORY_COLLECTION_HPP
#define OSMIUM_MEMORY_COLLECTION_HPP

/*

This file is part of Osmium (https://osmcode.org/libosmium).

Copyright 2013-2023 <PERSON><PERSON>f <<EMAIL>> and others (see README).

Boost Software License - Version 1.0 - August 17th, 2003

Permission is hereby granted, free of charge, to any person or organization
obtaining a copy of the software and accompanying documentation covered by
this license (the "Software") to use, reproduce, display, distribute,
execute, and transmit the Software, and to prepare derivative works of the
Software, and to permit third-parties to whom the Software is furnished to
do so, all subject to the following:

The copyright notices in the Software and this entire statement, including
the above license grant, this restriction and the following disclaimer,
must be included in all copies of the Software, in whole or in part, and
all derivative works of the Software, unless such copies or derivative
works are solely in the form of machine-executable object code generated by
a source language processor.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
FITNESS FOR A PARTICULAR PURPOSE, TITLE AND NON-INFRINGEMENT. IN NO EVENT
SHALL THE COPYRIGHT HOLDERS OR ANYONE DISTRIBUTING THE SOFTWARE BE LIABLE
FOR ANY DAMAGES OR OTHER LIABILITY, WHETHER IN CONTRACT, TORT OR OTHERWISE,
ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER
DEALINGS IN THE SOFTWARE.

*/

#include <osmium/memory/item.hpp>

#include <iosfwd>
#include <iterator>
#include <type_traits>

namespace osmium {

    namespace memory {

        template <typename TMember>
        class CollectionIterator {

            // This data_type is either 'unsigned char*' or 'const unsigned
            // char*' depending on whether TMember is const. This allows this
            // class to be used as an iterator and as a const_iterator.
            using data_type = typename std::conditional<std::is_const<TMember>::value, const unsigned char*, unsigned char*>::type;

            data_type m_data;

        public:

            using iterator_category = std::forward_iterator_tag;
            using value_type        = TMember;
            using difference_type   = std::ptrdiff_t;
            using pointer           = value_type*;
            using reference         = value_type&;

            CollectionIterator() noexcept :
                m_data(nullptr) {
            }

            explicit CollectionIterator(data_type data) noexcept :
                m_data(data) {
            }

            CollectionIterator<TMember>& operator++() {
                m_data = reinterpret_cast<TMember*>(m_data)->next();
                return *static_cast<CollectionIterator<TMember>*>(this);
            }

            CollectionIterator<TMember> operator++(int) {
                CollectionIterator<TMember> tmp{*this};
                operator++();
                return tmp;
            }

            bool operator==(const CollectionIterator<TMember>& rhs) const noexcept {
                return m_data == rhs.m_data;
            }

            bool operator!=(const CollectionIterator<TMember>& rhs) const noexcept {
                return !(*this == rhs);
            }

            unsigned char* data() const noexcept {
                return m_data;
            }

            TMember& operator*() const noexcept {
                return *reinterpret_cast<TMember*>(m_data);
            }

            TMember* operator->() const noexcept {
                return reinterpret_cast<TMember*>(m_data);
            }

            template <typename TChar, typename TTraits>
            void print(std::basic_ostream<TChar, TTraits>& out) const {
                out << static_cast<const void*>(m_data);
            }

        }; // class CollectionIterator

        template <typename TChar, typename TTraits, typename TMember>
        inline std::basic_ostream<TChar, TTraits>& operator<<(std::basic_ostream<TChar, TTraits>& out, const CollectionIterator<TMember>& iter) {
            iter.print(out);
            return out;
        }

        template <typename TFilter, typename TMember>
        class CollectionFilterIterator {

            TFilter m_filter;
            CollectionIterator<TMember> m_it;
            CollectionIterator<TMember> m_end;

            void advance() {
                while (m_it != m_end) {
                    if (m_filter(*m_it)) {
                        break;
                    }
                    ++m_it;
                }
            }

        public:

            using iterator_category = std::forward_iterator_tag;
            using value_type        = const TMember;
            using difference_type   = std::ptrdiff_t;
            using pointer           = value_type*;
            using reference         = value_type&;

            CollectionFilterIterator(TFilter filter, CollectionIterator<TMember> begin, CollectionIterator<TMember> end) :
                m_filter(std::move(filter)),
                m_it(begin),
                m_end(end) {
                advance();
            }

            CollectionFilterIterator& operator++() {
                assert(m_it != m_end);
                ++m_it;
                advance();
                return *this;
            }

            CollectionFilterIterator operator++(int) const {
                CollectionFilterIterator tmp{*this};
                operator++();
                return tmp;
            }

            bool operator==(const CollectionFilterIterator& rhs) const noexcept {
                return m_it == rhs.m_it && m_end == rhs.m_end;
            }

            bool operator!=(const CollectionFilterIterator& rhs) const noexcept {
                return !(*this == rhs);
            }

            reference operator*() const noexcept {
                assert(m_it != m_end);
                return *m_it;
            }

            pointer operator->() const noexcept {
                assert(m_it != m_end);
                return &*m_it;
            }

        }; // class CollectionFilterIterator

        template <typename TMember, osmium::item_type TCollectionItemType>
        class Collection : public Item {

        public:

            using value_type      = TMember;
            using reference       = TMember&;
            using const_reference = const TMember&;
            using iterator        = CollectionIterator<TMember>;
            using const_iterator  = CollectionIterator<const TMember>;
            using size_type       = std::size_t;

            static constexpr osmium::item_type itemtype = TCollectionItemType;

            constexpr static bool is_compatible_to(const osmium::item_type t) noexcept {
                return t == itemtype;
            }

            Collection() noexcept :
                Item(sizeof(Collection<TMember, TCollectionItemType>), TCollectionItemType) {
            }

            /**
             * Does this collection contain any items?
             *
             * Complexity: Constant.
             */
            bool empty() const noexcept {
                return sizeof(Collection<TMember, TCollectionItemType>) == byte_size();
            }

            /**
             * Returns the number of items in this collection.
             *
             * Complexity: Linear in the number of items.
             */
            size_type size() const noexcept {
                return static_cast<size_type>(std::distance(begin(), end()));
            }

            iterator begin() noexcept {
                return iterator{data() + sizeof(Collection<TMember, TCollectionItemType>)};
            }

            iterator end() noexcept {
                return iterator{data() + byte_size()};
            }

            const_iterator cbegin() const noexcept {
                return const_iterator{data() + sizeof(Collection<TMember, TCollectionItemType>)};
            }

            const_iterator cend() const noexcept {
                return const_iterator{data() + byte_size()};
            }

            const_iterator begin() const noexcept {
                return cbegin();
            }

            const_iterator end() const noexcept {
                return cend();
            }

        }; // class Collection

    } // namespace memory

} // namespace osmium

#endif // OSMIUM_MEMORY_COLLECTION_HPP

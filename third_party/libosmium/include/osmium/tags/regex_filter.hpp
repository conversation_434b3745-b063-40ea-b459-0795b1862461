#ifndef OSMIUM_TAGS_REGEX_FILTER_HPP
#define OSMIUM_TAGS_REGEX_FILTER_HPP

/*

This file is part of Osmium (https://osmcode.org/libosmium).

Copyright 2013-2023 <PERSON><PERSON>f <<EMAIL>> and others (see README).

Boost Software License - Version 1.0 - August 17th, 2003

Permission is hereby granted, free of charge, to any person or organization
obtaining a copy of the software and accompanying documentation covered by
this license (the "Software") to use, reproduce, display, distribute,
execute, and transmit the Software, and to prepare derivative works of the
Software, and to permit third-parties to whom the Software is furnished to
do so, all subject to the following:

The copyright notices in the Software and this entire statement, including
the above license grant, this restriction and the following disclaimer,
must be included in all copies of the Software, in whole or in part, and
all derivative works of the Software, unless such copies or derivative
works are solely in the form of machine-executable object code generated by
a source language processor.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
FITNESS FOR A PARTICULAR PURPOSE, TITLE AND NON-INFRINGEMENT. IN NO EVENT
SHALL THE COPYRIGHT HOLDERS OR ANYONE DISTRIBUTING THE SOFTWARE BE LIABLE
FOR ANY DAMAGES OR OTHER LIABILITY, WHETHER IN CONTRACT, TORT OR OTHERWISE,
ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER
DEALINGS IN THE SOFTWARE.

*/

#include <osmium/tags/filter.hpp>

#include <regex>
#include <string>

namespace osmium {

    namespace tags {

        template <>
        struct match_key<std::regex> {
            bool operator()(const std::regex& rule_key, const char* tag_key) const {
                return std::regex_match(tag_key, rule_key);
            }
        }; // struct match_key<std::regex>

        template <>
        struct match_value<std::regex> {
            bool operator()(const std::regex& rule_value, const char* tag_value) const {
                return std::regex_match(tag_value, rule_value);
            }
        }; // struct match_value<std::regex>

        /// @deprecated Use osmium::TagsFilter instead.
        using RegexFilter = Filter<std::string, std::regex>;

    } // namespace tags

} // namespace osmium

#endif // OSMIUM_TAGS_REGEX_FILTER_HPP

#-----------------------------------------------------------------------------
#
#  Configuration for continuous integration service at appveyor.com
#
#-----------------------------------------------------------------------------

image: Visual Studio 2017

platform: x64

clone_depth: 1

#-----------------------------------------------------------------------------

environment:
  matrix:
  - config: Debug
    autocrlf: true
  - config: Release
    autocrlf: true
  - config: Debug
    autocrlf: false
  - config: Release
    autocrlf: false
  - config: MSYS2
    autocrlf: true
    APPVEYOR_BUILD_WORKER_IMAGE: Visual Studio 2019

#-----------------------------------------------------------------------------

# scripts that are called at very beginning, before repo cloning
init:
  - git config --global core.autocrlf %autocrlf%
  - git config --get core.autocrlf

clone_folder: c:\projects\libosmium

# The option --ask=20 is a workaround for a problem with the MSYS2 update
# process. Without it the following error is printed and the appveyor script
# halts: "msys2-runtime and catgets are in conflict. Remove catgets?"
# See also: https://github.com/Alexpux/MSYS2-packages/issues/1141
install:
  - git submodule update --init
  - cd c:\projects
  - git clone --depth 1 https://github.com/mapbox/protozero
  - if [%config%]==[MSYS2] (
          C:\msys64\usr\bin\pacman --noconfirm --sync --refresh --refresh --sysupgrade --sysupgrade --ask=20
       && C:\msys64\usr\bin\pacman -Rc --noconfirm mingw-w64-x86_64-gcc-libs
    )

build_script:
  - cd c:\projects\libosmium
  - if [%config%]==[MSYS2] (
        build-msys2.bat
    ) else (
        build-appveyor.bat
    )

# remove garbage VS messages
# https://help.appveyor.com/discussions/problems/4569-the-target-_convertpdbfiles-listed-in-a-beforetargets-attribute-at-c-does-not-exist-in-the-project-and-will-be-ignored
#before_build:
#  - del "C:\Program Files (x86)\MSBuild\14.0\Microsoft.Common.targets\ImportAfter\Xamarin.Common.targets"


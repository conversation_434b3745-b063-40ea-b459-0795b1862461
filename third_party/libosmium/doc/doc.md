
Osmium is a fast and flexible C++ library for working with OpenStreetMap
data.

This is the API documentation that was automatically created from the
source code. For more information about the Osmium Library see
https://osmcode.org/libosmium .

Osmium is free software and available under the Boost Software License.
The source code is available at https://github.com/osmcode/libosmium .

Osmium is a header-only library. You do not need to compile and link it,
just include the headers you need.

Everything in namespaces called "detail" is for internal Osmium use only,
do not depend on it in your code. Do not include any include files in
directories named "detail" directly. Include files in directories called
"experimental" and everything in namespaces called "experimental" is
unsupported and may change at any time regardless of the status of the rest
of the library.


name: Windows

on: [push, pull_request]
jobs:
  windows:
    runs-on: windows-latest
    steps:
      - uses: actions/checkout@v2
      - name: dependencies
        run: |
          choco install cmake
      - name: build
        run: |
          cmake -S . -B build -G "Visual Studio 17 2022" -A x64
          cmake --build build --target ALL_BUILD --config Release
      - name: test
        run: |
          cmake --build build --target RUN_TESTS --config Release

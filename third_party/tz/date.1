.\" This file is in the public domain, so clarified as of
.\" 2009-05-17 by <PERSON>.
.TH date 1 "" "Time Zone Database"
.SH NAME
date \- show and set date and time
.SH SYNOPSIS
.if n .nh
.if n .na
.B date
[
.B \-u
] [
.B \-c
] [
.B \-r
.I seconds
] [
.BI + format
] [
\fR[\fIyyyy\fR]\fImmddhhmm\fR[\fIyy\fR][\fB.\fIss\fR]
]
.SH DESCRIPTION
.ie '\(lq'' .ds lq \&"\"
.el .ds lq \(lq\"
.ie '\(rq'' .ds rq \&"\"
.el .ds rq \(rq\"
.de q
\\$3\*(lq\\$1\*(rq\\$2
..
The
.B date
command
without arguments writes the date and time to the standard output in
the form
.ce 1
Sat Mar  8 14:54:40 EST 2025
.br
with
.B EST
replaced by the local time zone's abbreviation
(or by the abbreviation for the time zone specified in the
.B TZ
environment variable if set).
The exact output format depends on the locale.
.PP
If a command-line argument starts with a plus sign (\c
.q "\fB+\fP" ),
the rest of the argument is used as a
.I format
that is processed by
.BR strftime (3)
to determine what to output;
a newline character is appended.
For example, the shell command:
.ce 1
date +"%Y\-%m\-%d %H:%M:%S %z"
.br
outputs a line like
.q "2025\-03\-08 14:54:40 \-0500"
instead.
.PP
These options are available:
.TP
.BR \-u " or " \-c
Use Universal Time when setting and showing the date and time.
.TP
.BI "\-r " seconds
Output the date that corresponds to
.I seconds
past the epoch of 1970-01-01 00:00:00 UTC, where
.I seconds
should be an integer, either decimal, octal (leading 0), or
hexadecimal (leading 0x), preceded by an optional sign.
.SH FILES
.ta \w'/usr/share/zoneinfo/Etc/UTC\0\0'u
/etc/localtime	local timezone file
.br
/usr/lib/locale/\f2L\fP/LC_TIME	description of time locale \f2L\fP
.br
/usr/share/zoneinfo	timezone directory
.br
/usr/share/zoneinfo/Etc/UTC	for UTC leap seconds
.SH SEE ALSO
.BR strftime (3).

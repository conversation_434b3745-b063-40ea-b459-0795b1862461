<!DOCTYPE html>
<html lang="en">
<head>
<title>How to Read the tz Database</title>
<meta charset="UTF-8">
<style>
pre {margin-left: 2em; white-space: pre-wrap;}
pre.td {margin-left: 0;}
td {text-align: center;}
table {border: 1px outset;}
th, td {border: 1px inset;}
table.rule {border: none; margin: auto;}
td.footnote {text-align: left;}
</style>
</head>
<body>
<h2>How to Read the <a href="https://en.wikipedia.org/wiki/Tz_database">tz
Database</a> Source Files</h2>
<h3>by <PERSON></h3>
<p>This guide uses the <code>America/Chicago</code> and
<code>Pacific/Honolulu</code> zones as examples of how to infer
times of day from the <a href="tz-link.html">tz database</a>
source files. It might be helpful, but not absolutely necessary,
for the reader to have already downloaded the
latest release of the database and become familiar with the basic layout
of the data files. The format is explained in the &ldquo;man
page&rdquo; for the zic compiler, <code>zic.8.txt</code>, in
the <code>code</code> subdirectory.
Although this guide covers many of the common cases, it is not a
complete summary of what zic accepts; the man page is the
authoritative reference.</p>

<p>We&rsquo;ll begin by talking about the rules for changing between standard
and daylight saving time since we&rsquo;ll need that information when we talk
about the zones.</p>

<p>First, let&rsquo;s consider the special daylight saving time rules
for Chicago (from the <code>northamerica</code> file in
the <code>data</code> subdirectory):</p>

<table>
<tr>
  <th colspan="6">From the Source File</th>
</tr>
<tr>
  <td colspan="6">
    <table class="rule">
      <tr><td style="border:none;text-align:left">
<pre class="td">
#Rule NAME    FROM TO    -   IN  ON      AT   SAVE LETTER
Rule  Chicago 1920 only  -   Jun 13      2:00 1:00 D
Rule  Chicago 1920 1921  -   Oct lastSun 2:00 0    S
Rule  Chicago 1921 only  -   Mar lastSun 2:00 1:00 D
Rule  Chicago 1922 1966  -   Apr lastSun 2:00 1:00 D
Rule  Chicago 1922 1954  -   Sep lastSun 2:00 0    S
Rule  Chicago 1955 1966  -   Oct lastSun 2:00 0    S
</pre>
  </td></tr></table></td>
</tr>
<tr>
  <th colspan="6">Reformatted a Bit</th>
</tr>
<tr>
  <th>From</th>
  <th>To</th>
  <th colspan="2">On</th>
  <th>At</th>
  <th>Action</th>
</tr>
<tr>
  <td colspan="2">1920 only</td>
  <td colspan="2">June 13<small><sup>th</sup></small></td>
  <td rowspan="6">02:00 local</td>
  <td>go to daylight saving time</td>
</tr>
<tr>
  <td>1920</td>
  <td>1921</td>
  <td rowspan="5">last Sunday</td>
  <td>in October</td>
  <td>return to standard time</td>
</tr>
<tr>
  <td colspan="2">1921 only</td>
  <td>in March</td>
  <td rowspan="2">go to daylight saving time</td>
</tr>
<tr>
  <td rowspan="2">1922</td>
  <td>1966</td>
  <td>in April</td>
</tr>
<tr>
  <td>1954</td>
  <td>in September</td>
  <td rowspan="2">return to standard time</td>
</tr>
<tr>
  <td>1955</td>
  <td>1966</td>
  <td>in October</td>
</tr>
</table>

<p>The <code>FROM</code> and <code>TO</code> columns, respectively, specify the
first and last calendar years defining a contiguous range over which a specific
Rule line is to apply.  The keyword <code>only</code> can be used in the
<code>TO</code> field to repeat the value of the <code>FROM</code> field in the
event that a rule should only apply to a single year.  Often, the keyword
<code>max</code> is used to extend a rule&rsquo;s application into the
indefinite future; it is a platform-agnostic stand-in for the largest
representable year.

<p>The next column, <code>-</code>, is reserved; for compatibility with earlier
releases, it always contains a hyphen, which acts as a kind of null value.
Prior to the 2020b release, it was called the <code>TYPE</code> field, though
it had not been used in the main data since the 2000e release.
An obsolescent supplementary file used the
field as a proof-of-concept to allow <code>zic</code> to apply a given Rule
line only to certain &ldquo;types&rdquo; of years within the specified range as
dictated by the output of a separate script, such as: only years which would
have a US presidential election, or only years which wouldn&rsquo;t.

<p>The <code>SAVE</code> column contains the local (wall clock) offset from
local standard time.
This is usually either zero for standard time or one hour for daylight
saving time; but there&rsquo;s no reason, in principle, why it can&rsquo;t
take on other values.

<p>The <code>LETTER</code> (sometimes called <code>LETTER/S</code>)
column can contain a variable
part of the usual abbreviation of the time zone&rsquo;s name, or it can just
be a hyphen if there&rsquo;s no variable part. For example, the abbreviation
used in the central time zone will be either &ldquo;CST&rdquo; or
&ldquo;CDT&rdquo;. The variable part is &lsquo;S&rsquo; or &lsquo;D&rsquo;;
and, sure enough, that&rsquo;s just what we find in
the <code>LETTER</code> column
in the <code>Chicago</code> rules. More about this when we talk about
&ldquo;Zone&rdquo; lines.

<p>One important thing to notice is that &ldquo;Rule&rdquo; lines
want at once to be both <i>transitions</i> and <i>steady states</i>:
<ul>
<li>On the one hand, they represent transitions between standard and
daylight saving time; and any number of Rule lines can be in effect
during a given period (which will always be a non-empty set of
contiguous calendar years).</li>
<li>On the other hand, the <code>SAVE</code> and <code>LETTER</code>
columns contain state that exists between transitions. More about this
when we talk about the US rules.</li>
</ul>

<p>In the example above, the transition to daylight saving time
happened on the 13<small><sup>th</sup></small> of June in 1920, and on
the last Sunday in March in 1921; but the return to standard time
happened on the last Sunday in October in both of those
years. Similarly, the rule for changing to daylight saving time was
the same from 1922 to 1966; but the rule for returning to standard
time changed in 1955. Got it?</p>

<p>OK, now for the somewhat more interesting &ldquo;US&rdquo; rules:</p>

<table>
<tr>
  <th colspan="6">From the Source File</th>
</tr>
<tr>
  <td colspan="6">
    <table class="rule">
      <tr><td style="border:none;text-align:left">
<pre class="td">
#Rule NAME FROM TO    -   IN  ON        AT   SAVE LETTER/S
Rule  US   1918 1919  -   Mar lastSun  2:00  1:00 D
Rule  US   1918 1919  -   Oct lastSun  2:00  0    S
Rule  US   1942 only  -   Feb 9        2:00  1:00 W # War
Rule  US   1945 only  -   Aug 14      23:00u 1:00 P # Peace
Rule  US   1945 only  -   Sep 30       2:00  0    S
Rule  US   1967 2006  -   Oct lastSun  2:00  0    S
Rule  US   1967 1973  -   Apr lastSun  2:00  1:00 D
Rule  US   1974 only  -   Jan 6        2:00  1:00 D
Rule  US   1975 only  -   Feb 23       2:00  1:00 D
Rule  US   1976 1986  -   Apr lastSun  2:00  1:00 D
Rule  US   1987 2006  -   Apr Sun&gt;=1   2:00  1:00 D
Rule  US   2007 max   -   Mar Sun&gt;=8   2:00  1:00 D
Rule  US   2007 max   -   Nov Sun&gt;=1   2:00  0    S
</pre>
  </td></tr></table></td>
</tr>
<tr>
  <th colspan="6">Reformatted a Bit</th>
</tr>
<tr>
  <th>From</th>
  <th>To</th>
  <th colspan="2">On</th>
  <th>At</th>
  <th>Action</th>
</tr>
<tr>
  <td rowspan="2">1918</td>
  <td rowspan="2">1919</td>
  <td rowspan="2">last Sunday</td>
  <td>in March</td>
  <td rowspan="3">02:00 local</td>
  <td>go to daylight saving time</td>
</tr>
<tr>
  <td>in October</td>
  <td>return to standard time</td>
</tr>
<tr>
  <td colspan="2">1942 only</td>
  <td colspan="2">February 9<small><sup>th</sup></small></td>
  <td>go to &ldquo;war time&rdquo;</td>
</tr>
<tr>
  <td colspan="2" rowspan="2">1945 only</td>
  <td colspan="2">August 14<small><sup>th</sup></small></td>
  <td>23:00 <a href="https://en.wikipedia.org/wiki/Universal_Time">UT</a></td>
  <td>
    rename &ldquo;war time&rdquo; to &ldquo;peace<br>time;&rdquo;
    clocks don&rsquo;t change
  </td>
</tr>
<tr>
  <td colspan="2">September 30<small><sup>th</sup></small></td>
  <td rowspan="9">02:00 local</td>
  <td rowspan="2">return to standard time</td>
</tr>
<tr>
  <td rowspan="2">1967</td>
  <td>2006</td>
  <td rowspan="2">last Sunday</td>
  <td>in October</td>
</tr>
<tr>
  <td>1973</td>
  <td>in April</td>
  <td rowspan="6">go to daylight saving time</td>
</tr>
<tr>
  <td colspan="2">1974 only</td>
  <td colspan="2">January 6<small><sup>th</sup></small></td>
</tr>
<tr>
  <td colspan="2">1975 only</td>
  <td colspan="2">February 23<small><sup>rd</sup></small></td>
</tr>
<tr>
  <td>1976</td>
  <td>1986</td>
  <td>last Sunday</td>
  <td rowspan="2">in April</td>
</tr>
<tr>
  <td>1987</td>
  <td>2006</td>
  <td>first Sunday</td>
</tr>
<tr>
  <td rowspan="2">2007</td>
  <td rowspan="2">present</td>
  <td colspan="2">second Sunday in March</td>
</tr>
<tr>
  <td colspan="2">first Sunday in November</td>
  <td>return to standard time</td>
</tr>
</table>

<p>There are two interesting things to note here.</p>

<p>First, the time that something happens (in the <code>AT</code>
column) is not necessarily the local (wall clock) time. The time can be
suffixed with &lsquo;s&rsquo; (for &ldquo;standard&rdquo;) to mean
local standard time, different from local (wall clock) time when observing
daylight saving time; or it can be suffixed with &lsquo;g&rsquo;,
&lsquo;u&rsquo;, or &lsquo;z&rsquo;, all three of which mean the
standard time at the
<a href="https://en.wikipedia.org/wiki/Prime_Meridian">prime meridian</a>.
&lsquo;g&rsquo; stands for &ldquo;<a
href="https://en.wikipedia.org/wiki/Greenwich_Mean_Time">GMT</a>&rdquo;;
&lsquo;u&rsquo; stands for &ldquo;<a
href="https://en.wikipedia.org/wiki/Universal_Time">UT</a>&rdquo; or &ldquo;<a
href="https://en.wikipedia.org/wiki/Coordinated_Universal_Time">UTC</a>&rdquo;
(whichever was official at the time); &lsquo;z&rsquo; stands for the
<a href="https://en.wikipedia.org/wiki/Nautical_time">nautical time zone</a>
Z (a.k.a. &ldquo;Zulu&rdquo; which, in turn, stands for &lsquo;Z&rsquo;).
The time can also be suffixed with &lsquo;w&rsquo; meaning local (wall
clock) time; but it usually isn&rsquo;t because that&rsquo;s the
default.</p>

<p>Second, the day in the <code>ON</code> column, in addition to
&ldquo;<code>lastSun</code>&rdquo; or a particular day of the month,
can have the form, &ldquo;<code>Sun&gt;=</code><i>x</i>&rdquo; or
&ldquo;<code>Sun&lt;=</code><i>x</i>,&rdquo; where <i>x</i> is a day
of the month. For example, &ldquo;<code>Sun&gt;=8</code>&rdquo; means
&ldquo;the first Sunday on or after the eighth of the month,&rdquo; in
other words, the second Sunday of the month. Furthermore, although
there are no examples above, the weekday needn&rsquo;t be
&ldquo;<code>Sun</code>&rdquo; in either form, but can be the usual
three-character English abbreviation for any day of the week.</p>

<p>And the US rules give us more examples of a couple of things
already mentioned:</p>

<ul>
<li>The rules for changing to and from daylight saving time are
actually <i>different sets</i> of rules; and the two sets can change
independently. Consider, for example, that the rule for the return to
standard time stayed the same from 1967 to 2006; but the rule for the
transition to daylight saving time changed several times in the same
period.  There can also be periods, 1946 to 1966 for example, when no
rule from this group is in effect, and so either no transition
happened in those years, or some other rule is in effect (perhaps a
state or other more local rule).</li>

<li>The <code>SAVE</code> and <code>LETTER</code> columns
contain <i>steady state</i>, not transitions. Consider, for example,
the transition from &ldquo;war time&rdquo; to &ldquo;peace time&rdquo;
that happened on August 14, 1945. The &ldquo;1:00&rdquo; in
the <code>SAVE</code> column is <i>not</i> an instruction to advance
the clock an hour. It means that clocks should <i>be</i> one hour
ahead of standard time, which they already are because of the previous
rule, so there should be no change.</li>

</ul>

<p>OK, now let&rsquo;s look at a Zone record:</p>

<table>
<tr>
  <th colspan="5">From the Source File</th>
</tr>
<tr>
  <td colspan="5">
    <table class="rule">
      <tr><td style="border:none;text-align:left">
<pre class="td">
#Zone       NAME      STDOFF   RULES FORMAT [UNTIL]
Zone  America/Chicago -5:50:36 -       LMT  1883 Nov 18 12:09:24
                      -6:00    US      C%sT 1920
                      -6:00    Chicago C%sT 1936 Mar  1  2:00
                      -5:00    -       EST  1936 Nov 15  2:00
                      -6:00    Chicago C%sT 1942
                      -6:00    US      C%sT 1946
                      -6:00    Chicago C%sT 1967
                      -6:00    US      C%sT
</pre>
  </td></tr></table></td>
</tr>
<tr>
  <th colspan="5">Columns Renamed</th>
</tr>
<tr>
  <th rowspan="2">Standard Offset<br>
    from <a href="https://en.wikipedia.org/wiki/Prime_Meridian">Prime
    Meridian</a></th>
  <th rowspan="2">Daylight<br>Saving Time</th>
  <th rowspan="2">Abbreviation(s)</th>
  <th colspan="2">Ending at Local Time</th>
</tr>
<tr>
  <th>Date</th>
  <th>Time</th>
</tr>
<tr>
  <td>&minus;5:50:36</td>
  <td>not observed</td>
  <td>LMT</td>
  <td>1883-11-18</td>
  <td>12:09:24</td>
</tr>
<tr>
  <td rowspan="2">&minus;6:00:00</td>
  <td>US rules</td>
  <td rowspan="2">CST or CDT</td>
  <td>1920-01-01</td>
  <td>00:00:00</td>
</tr>
<tr>
  <td>Chicago rules</td>
  <td>1936-03-01</td>
  <td rowspan="2">02:00:00</td>
</tr>
<tr>
  <td>&minus;5:00:00</td>
  <td>not observed</td>
  <td>EST</td>
  <td>1936-11-15</td>
</tr>
<tr>
  <td rowspan="4">&minus;6:00:00</td>
  <td>Chicago rules</td>
  <td>CST or CDT</td>
  <td>1942-01-01</td>
  <td rowspan="3">00:00:00</td>
</tr>
<tr>
  <td>US rules</td>
  <td>CST, CWT or CPT</td>
  <td>1946-01-01</td>
</tr>
<tr>
  <td>Chicago rules</td>
  <td rowspan="2">CST or CDT</td>
  <td>1967-01-01</td>
</tr>
<tr>
  <td>US rules</td>
  <td colspan="2">&mdash;</td>
</tr>
</table>

<p>There are a couple of interesting differences between Zones and Rules.</p>

<p>First, and somewhat trivially, whereas Rules are considered to
contain one or more records, a Zone is considered to be a single
record with zero or more <i>continuation lines</i>. Thus, the keyword,
&ldquo;<code>Zone</code>,&rdquo; and the zone name are not
repeated. The last line is the one without anything in
the <code>[UNTIL]</code> column.</p>

<p>Second, and more fundamentally, each line of a Zone represents a
steady state, not a transition between states. The state exists from
the date and time in the previous line&rsquo;s <code>[UNTIL]</code>
column up to the date and time in the current
line&rsquo;s <code>[UNTIL]</code> column. In other words, the date and
time in the <code>[UNTIL]</code> column is the instant that separates
this state from the next. Where that would be ambiguous because
we&rsquo;re setting our clocks back, the <code>[UNTIL]</code> column
specifies the first occurrence of the instant. The state specified by
the last line, the one without anything in the <code>[UNTIL]</code>
column, continues to the present.</p>

<p>The first line typically specifies the mean solar time observed
before the introduction of standard time. Since there&rsquo;s no line before
that, it has no beginning. <code>8-) </code> For some places near the <a
href="https://en.wikipedia.org/wiki/International_Date_Line">International
Date Line</a>, the first <i>two</i> lines will show solar times
differing by 24 hours; this corresponds to a movement of the Date
Line.  For example:</p>

<pre>
#Zone NAME          STDOFF   RULES FORMAT [UNTIL]
Zone America/Juneau 15:02:19 -     LMT    1867 Oct 18
                    -8:57:41 -     LMT    ...
</pre>

<p>When Alaska was purchased from Russia in 1867, the Date Line moved
from the Alaska/Canada border to the Bering Strait; and the time in
Alaska was then 24 hours earlier than it had
been. <code>&lt;aside&gt;</code>(6 October in the Julian calendar,
which Russia was still using then for religious reasons, was followed
by <i>a second instance of the same day with a different name</i>, 18
October in the Gregorian calendar. Isn&rsquo;t civil time
wonderful? <code>8-)</code>)<code>&lt;/aside&gt;</code></p>

<p>The abbreviation, &ldquo;LMT&rdquo; stands for &ldquo;local mean
time&rdquo;, which is an invention of
the <a href="https://en.wikipedia.org/wiki/Tz_database">tz
database</a> and was probably never actually used during the
period. Furthermore, the value is almost certainly wrong except in the
archetypal place after which the zone is named. (The tz database
usually doesn&rsquo;t provide a separate Zone record for places where
nothing significant happened after 1970.)</p>

<p>The <code>RULES</code> column tells us whether daylight saving time is being observed:
<ul>
<li>A hyphen, a kind of null value, means that we have not set our
clocks ahead of standard time.</li>

<li>An amount of time (usually but not necessarily &ldquo;1:00&rdquo;
meaning one hour) means that we have set our clocks ahead by that
amount.</li>

<li>Some alphabetic string means that we <i>might have</i> set our
clocks ahead; and we need to check the rule the name of which is the
given alphabetic string.</li>
</ul>

<p>An example of a specific amount of time is:</p>
<pre>
#Zone NAME            STDOFF RULES FORMAT [UNTIL]
Zone Pacific/Honolulu ...                 1933 Apr 30  2:00
                      -10:30 1:00  HDT    1933 May 21 12:00
                      ...
</pre>

<p>Hawaii tried daylight saving time for three weeks in 1933 and
decided they didn&rsquo;t like it. <code>8-) </code>Note that
the <code>STDOFF</code> column always contains the standard time
offset, so the local (wall clock) time during this period was GMT &minus;
10:30 + 1:00 = GMT &minus; 9:30.</p>

<p>The <code>FORMAT</code> column specifies the usual abbreviation of
the time zone name. It should have one of four forms:</p>
<ul>

<li>a time zone abbreviation that is a string of three or more
characters that are either ASCII alphanumerics,
&ldquo;<code>+</code>&rdquo;, or &ldquo;<code>-</code>&rdquo;</li>

<li>the string &ldquo;%z&rdquo;, in which case the
&ldquo;<code>%z</code>&rdquo; will be replaced by a numeric time zone
abbreviation</li>

<li>a pair of time zone abbreviations separated by a slash
(&lsquo;<code>/</code>&rsquo;), in which case the first string is the
abbreviation for the standard time name and the second string is the
abbreviation for the daylight saving time name</li>

<li>a string containing &ldquo;<code>%s</code>&rdquo;, in which case
the &ldquo;<code>%s</code>&rdquo; will be replaced by the text in the
appropriate Rule&rsquo;s <code>LETTER</code> column, and the resulting
string should be a time zone abbreviation</li>
</ul>

<p>The last two make sense only if there&rsquo;s a named rule in effect.</p>

<p>An example of a slash is:</p>
<pre>
#Zone NAME          STDOFF RULES FORMAT  [UNTIL]
Zone  Europe/London ...                  1996
                    0:00   EU    GMT/BST
</pre>

<p>The current time in the UK is called either Greenwich mean time or
British summer time.</p>

<p>One wrinkle, not fully explained in <code>zic.8.txt</code>, is what
happens when switching to a named rule. To what values should
the <code>SAVE</code> and <code>LETTER</code> data be initialized?</p>

<ul>
<li>If at least one transition has happened, use
the <code>SAVE</code> and <code>LETTER</code> data from the most
recent.</li>

<li>If switching to a named rule before any transition has happened,
assume standard time (<code>SAVE</code> zero), and use
the <code>LETTER</code> data from the earliest transition with
a <code>SAVE</code> of zero.

</ul>

<p>And three last things about the <code>FORMAT</code> column:</p>
<ul>

<li>The <a href="https://en.wikipedia.org/wiki/Tz_database">tz
database</a> gives abbreviations for time zones
in popular English-language usage. For
example, the last line in
<code>Zone</code> <code>Pacific/Honolulu</code> (shown below) gives
&ldquo;HST&rdquo; for &ldquo;Hawaii standard time&rdquo; even though the
<a href="https://www.law.cornell.edu/uscode/text/15/263">legal</a>
name for that time zone is &ldquo;Hawaii-Aleutian standard time.&rdquo;
This author has read that there are also some places in Australia where
popular time zone names differ from the legal ones.

<li>No attempt is made to <a
href="https://en.wikipedia.org/wiki/Internationalization_and_localization">localize</a>
the abbreviations. They are intended to be the values returned through the
<code>"%Z"</code> format specifier to
<a href="https://en.wikipedia.org/wiki/C_(programming_language)">C</a>&rsquo;s
<a href="https://pubs.opengroup.org/onlinepubs/9699919799/functions/strftime.html"><code>strftime</code></a>
function in the
<a href="https://kirste.userpage.fu-berlin.de/chemnet/use/info/libc/libc_19.html#SEC324">&ldquo;C&rdquo; locale</a>.

<li>If there is no generally accepted abbreviation for a time zone,
a numeric offset is used instead, e.g., <code>+07</code> for 7 hours
ahead of Greenwich. By convention, <code>-00</code> is used in a
zone while uninhabited, where the offset is zero but in some sense
the true offset is undefined.
</ul>

<p>As a final example, here&rsquo;s the complete history for Hawaii:</p>

<table>
<tr>
  <th colspan="6">Relevant Excerpts from the US Rules</th>
</tr>
<tr>
  <td colspan="6">
    <table class="rule">
      <tr><td style="border:none;text-align:left">
<pre class="td">
#Rule NAME FROM TO   -    IN  ON      AT     SAVE LETTER/S
Rule  US   1918 1919 -    Oct lastSun  2:00  0    S
Rule  US   1942 only -    Feb  9       2:00  1:00 W # War
Rule  US   1945 only -    Aug 14      23:00u 1:00 P # Peace
Rule  US   1945 only -    Sep lastSun  2:00  0    S
</pre>
  </td></tr></table></td>
</tr>
<tr>
  <th colspan="6">The Zone Record</th>
</tr>
<tr>
  <td colspan="6">
    <table class="rule">
      <tr><td style="border:none;text-align:left">
<pre class="td">
#Zone NAME            STDOFF    RULES FORMAT [UNTIL]
Zone Pacific/Honolulu -10:31:26 -     LMT    1896 Jan 13 12:00
                      -10:30    -     HST    1933 Apr 30  2:00
                      -10:30    1:00  HDT    1933 May 21  2:00
                      -10:30    US    H%sT   1947 Jun  8  2:00
                      -10:00    -     HST
</pre>
  </td></tr></table></td>
</tr>
<tr>
  <th colspan="6">What We Infer</th>
</tr>
<tr>
  <th rowspan="2">Wall-Clock<br>Offset from<br>Prime Meridian</th>
  <th rowspan="2">Adjust<br>Clocks</th>
  <th colspan="2">Time Zone</th>
  <th colspan="2">Ending at Local Time</th>
</tr>
<tr>
  <th>Abbrv.</th>
  <th>Name</th>
  <th>Date</th>
  <th>Time</th>
</tr>
<tr>
  <td>&minus;10:31:26</td>
  <td>&mdash;</td>
  <td>LMT</td>
  <td>local mean time</td>
  <td>1896-01-13</td>
  <td>12:00</td>
</tr>
<tr>
  <td>&minus;10:30</td>
  <td>+0:01:26</td>
  <td>HST</td>
  <td>Hawaii standard time</td>
  <td>1933-04-30</td>
  <td>02:00</td>
</tr>
<tr>
  <td>&minus;9:30</td>
  <td>+1:00</td>
  <td>HDT</td>
  <td>Hawaii daylight time</td>
  <td>1933-05-21</td>
  <td>12:00</td>
</tr>
<tr>
  <td>&minus;10:30&sup1;</td>
  <td>&minus;1:00&sup1;</td>
  <td>HST&sup1;</td>
  <td>Hawaii standard time</td>
  <td>1942-02-09</td>
  <td>02:00</td>
</tr>
<tr>
  <td rowspan="2">&minus;9:30</td>
  <td>+1:00</td>
  <td>HWT</td>
  <td>Hawaii war time</td>
  <td>1945-08-14</td>
  <td>13:30&sup2;</td>
</tr>
<tr>
  <td>0</td>
  <td>HPT</td>
  <td>Hawaii peace time</td>
  <td>1945-09-30</td>
  <td rowspan="2">02:00</td>
</tr>
<tr>
  <td>&minus;10:30</td>
  <td>&minus;1:00</td>
  <td rowspan="2">HST</td>
  <td rowspan="2">Hawaii standard time</td>
  <td>1947-06-08</td>
</tr>
<tr>
  <td>&minus;10:00&sup3;</td>
  <td>+0:30&sup3;</td>
  <td colspan="2">&mdash;</td>
</tr>
<tr>
  <td colspan="6" class="footnote">
    &sup1;Switching to US rules&hellip;most recent transition (in 1919) was to standard time
  </td>
</tr>
<tr>
  <td colspan="6" class="footnote">
    &sup2;23:00 <a href="https://en.wikipedia.org/wiki/Universal_Time">UT</a>
    + (&minus;9:30) = 13:30 local
  </td>
</tr>
<tr>
  <td colspan="6" class="footnote">
    &sup3;Since <a href="https://en.wikipedia.org/wiki/ISO_8601">1947&ndash;06&ndash;08T12:30Z</a>,
    the civil time in Hawaii has been
    <a href="https://en.wikipedia.org/wiki/Universal_Time">UT</a>/<a href="https://en.wikipedia.org/wiki/Coordinated_Universal_Time">UTC</a>
    &minus; 10:00 year-round.
  </td>
</tr>
</table>

<p>There will be a short quiz later. <code>8-)</code></p>

<hr>
<address>
This web page is in the public domain, so clarified as of
2015-10-20 by Bill Seymour.
<br>
All suggestions and corrections will be welcome; all flames will be amusing.
Mail to was at pobox dot com.
</address>
</body>
</html>

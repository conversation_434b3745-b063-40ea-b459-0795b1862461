.\" This file is in the public domain, so clarified as of
.\" 2009-05-17 by <PERSON>.
.TH newctime 3 "" "Time Zone Database"
.SH NAME
asctime, ctime, difftime, gmtime, localtime, mktime \- convert date and time
.SH SYNOPSIS
.nf
.B #include <time.h>
.PP
.B struct tm *localtime(time_t const *clock);
.B "struct tm *localtime_r(time_t const *restrict clock,"
.B "    struct tm *restrict result);"
.B "struct tm *localtime_rz(timezone_t restrict zone,"
.B "    time_t const *restrict clock,"
.B "    struct tm *restrict result);"
.PP
.B struct tm *gmtime(time_t const *clock);
.B "struct tm *gmtime_r(time_t const *restrict clock,"
.B "    struct tm *restrict result);"
.PP
.B time_t mktime(struct tm *tm);
.B "time_t mktime_z(timezone_t restrict zone,"
.B "    struct tm *restrict tm);"
.PP
.B double difftime(time_t time1, time_t time0);
.PP
.B [[deprecated]] char *asctime(struct tm const *tm);
.B [[deprecated]] char *ctime(time_t const *clock);
.PP
/* Only in POSIX.1-2017 and earlier.  */
.B char *ctime_r(time_t const *clock, char *buf);
.B "char *asctime_r(struct tm const *restrict tm,"
.B "    char *restrict result);"
.PP
.B cc ... \-ltz
.fi
.SH DESCRIPTION
.ie '\(en'' .ds en \-
.el .ds en \(en
.ie '\(lq'' .ds lq \&"\"
.el .ds lq \(lq\"
.ie '\(rq'' .ds rq \&"\"
.el .ds rq \(rq\"
.de q
\\$3\*(lq\\$1\*(rq\\$2
..
The
.B localtime
and
.B gmtime
functions
convert an integer, pointed to by
.IR clock ,
and
return pointers to
.q "tm"
structures, described below.
If the integer is out of range for conversion,
these functions return a null pointer.
The
.B localtime
function
corrects for the time zone and any time zone adjustments
(such as Daylight Saving Time in the United States).
The
.B gmtime
function converts to Coordinated Universal Time.
.PP
The
.BI * clock
timestamp represents the time in seconds since 1970-01-01 00:00:00
Coordinated Universal Time (UTC).
The POSIX standard says that timestamps must be nonnegative
and must ignore leap seconds.
Many implementations extend POSIX by allowing negative timestamps,
and can therefore represent timestamps that predate the
introduction of UTC and are some other flavor of Universal Time (UT).
Some implementations support leap seconds, in contradiction to POSIX.
.PP
The
.B mktime
function
converts the broken-down time,
expressed as local time,
in the structure pointed to by
.I tm
into a calendar time value with the same encoding as that of the values
returned by the
.B time
function.
The original values of the
.B tm_wday
and
.B tm_yday
components of the structure are ignored,
and the original values of the other components are not restricted
to their normal ranges.
(A positive or zero value for
.B tm_isdst
causes
.B mktime
to presume initially that daylight saving time
respectively,
is or is not in effect for the specified time.
A negative value for
.B tm_isdst
causes the
.B mktime
function to attempt to divine whether daylight saving time is in effect
for the specified time; in this case it does not use a consistent
rule and may give a different answer when later
presented with the same argument.)
On successful completion, the values of the
.B tm_wday
and
.B tm_yday
components of the structure are set appropriately,
and the other components are set to represent the specified calendar time,
but with their values forced to their normal ranges; the final value of
.B tm_mday
is not set until
.B tm_mon
and
.B tm_year
are determined.
The
.B mktime
function
returns the specified calendar time.
If the calendar time cannot be represented,
it returns \-1 without updating the structure.
To distinguish failure from a valid \-1 return,
you can set
.B tm_wday
or
.B tm_yday
to a negative value before calling
.BR mktime ;
if that value is still negative when
.B mktime
returns, the calendar time could not be represented.
.PP
The
.B difftime
function
returns the difference between two calendar times,
.RI ( time1
\-
.IR time0 ),
expressed in seconds.
.PP
The
.B asctime
function
converts a time value contained in a
.q "tm"
structure to a pointer to a
string of the form
.br
.ce
.eo
Thu Nov 24 18:22:48 1986\n\0
.br
.ec
Years requiring fewer than four characters are padded with leading zeroes.
For years longer than four characters, the string is of the form
.br
.ce
.eo
Thu Nov 24 18:22:48     81986\n\0
.ec
.br
with five spaces before the year.
This unusual format is designed to make it less likely that older
software that expects exactly 26 bytes of output will mistakenly output
misleading values for out-of-range years.
This function is deprecated starting in C23.
Callers can use
.B strftime
instead.
.PP
The
.B ctime
function is equivalent to calliing
.B localtime
and then calling
.B asctime
on the result.
Like
.BR asctime ,
this function is deprecated starting in C23.
Callers can use
.B localtime
and
.B strftime
instead.
.PP
The
.BR ctime_r ,
.BR localtime_r ,
.BR gmtime_r ,
and
.B asctime_r
functions
are like their unsuffixed counterparts, except that they accept an
additional argument specifying where to store the result if successful.
The
.B ctime_r
and
.B asctime_r
functions are present only on systems supporting POSIX.1-2017 and earlier,
as they are removed in POSIX.1-2024 and user code can define these
functions with other meanings.
.PP
The
.B localtime_rz
and
.B mktime_z
functions
are like their unsuffixed counterparts, except that they accept an
extra initial
.B zone
argument specifying the timezone to be used for conversion.
If
.B zone
is null, UT is used; otherwise,
.B zone
should be have been allocated by
.B tzalloc
and should not be freed until after all uses (e.g., by calls to
.BR strftime )
of the filled-in
.B tm_zone
fields.
.PP
Declarations of all the functions and externals, and the
.q "tm"
structure,
are in the
.B <time.h>
header file.
The structure (of type)
.B struct tm
includes the following fields:
.RS
.PP
.nf
.ta 2n +\w'long tm_gmtoff;nn'u
	int tm_sec;	/\(** seconds (0\*(en60) \(**/
	int tm_min;	/\(** minutes (0\*(en59) \(**/
	int tm_hour;	/\(** hours (0\*(en23) \(**/
	int tm_mday;	/\(** day of month (1\*(en31) \(**/
	int tm_mon;	/\(** month of year (0\*(en11) \(**/
	int tm_year;	/\(** year \- 1900 \(**/
	int tm_wday;	/\(** day of week (Sunday = 0) \(**/
	int tm_yday;	/\(** day of year (0\*(en365) \(**/
	int tm_isdst;	/\(** is daylight saving time in effect? \(**/
	char \(**tm_zone;	/\(** time zone abbreviation (optional) \(**/
	long tm_gmtoff;	/\(** offset from UT in seconds (optional) \(**/
.fi
.RE
.PP
The
.B tm_isdst
field
is non-zero if daylight saving time is in effect.
.PP
The
.B tm_gmtoff
field
is the offset (in seconds) of the time represented
from UT, with positive values indicating east
of the Prime Meridian.
The field's name is derived from Greenwich Mean Time, a precursor of UT.
.PP
In platforms conforming to POSIX.1-2024 the
.B "struct tm"
the
.B tm_zone
and
.B tm_gmtoff
fields exist, and are filled in.
For
.B localtime_rz
and
.B mktime_rz
the storage lifetime of the strings addressed by
.B tm_zone
extends until the corresponding
.B timezone_t
object is freed via
.BR tzfree .
For the other functions the lifetime extends until the
.I TZ
environment variable changes state and
.B tzset
is then called.
.PP
As a side effect, the
.BR ctime ,
.B localtime
and
.B mktime
functions also behave as if
.B tzset
were called.
The
.B ctime_r
and
.B localtime_r
functions might (or might not) also behave this way.
This is for compatibility with older platforms, as required by POSIX.
.SH FILES
.ta \w'/usr/share/zoneinfo/posixrules\0\0'u
/etc/localtime	local timezone file
.br
/usr/share/zoneinfo	timezone directory
.br
/usr/share/zoneinfo/posixrules	default DST rules (obsolete)
.br
/usr/share/zoneinfo/GMT	for UTC leap seconds
.PP
If /usr/share/zoneinfo/GMT is absent,
UTC leap seconds are loaded from /usr/share/zoneinfo/GMT0 if present.
.SH SEE ALSO
.BR getenv (3),
.BR newstrftime (3),
.BR newtzset (3),
.BR time (2),
.BR tzfile (5).
.SH NOTES
The return values of
.BR asctime ,
.BR ctime ,
.BR gmtime ,
and
.B localtime
point to static data
overwritten by each call.
The remaining functions and data are thread-safe.
.PP
The
.BR asctime ,
.BR asctime_r ,
.BR ctime ,
and
.B ctime_r
functions
behave strangely for years before 1000 or after 9999.
The 1989 and 1999 editions of the C Standard say
that years from \-99 through 999 are converted without
extra spaces, but this conflicts with longstanding
tradition and with this implementation.
The 2011 edition says that the behavior
is undefined if the year is before 1000 or after 9999.
Traditional implementations of these two functions are
restricted to years in the range 1900 through 2099.
To avoid this portability mess, new programs should use
.B strftime
instead.

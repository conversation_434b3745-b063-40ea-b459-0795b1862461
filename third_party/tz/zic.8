.\" This file is in the public domain, so clarified as of
.\" 2009-05-17 by <PERSON>.
.TH zic 8 "" "Time Zone Database"
.SH NAME
zic \- timezone compiler
.SH SYNOPSIS
.B zic
[
.I option
\&... ] [
.I filename
\&... ]
.SH DESCRIPTION
.ie '\(lq'' .ds lq \&"\"
.el .ds lq \(lq\"
.ie '\(rq'' .ds rq \&"\"
.el .ds rq \(rq\"
.de q
\\$3\*(lq\\$1\*(rq\\$2
..
.ie '\(la'' .ds < <
.el .ds < \(la
.ie '\(ra'' .ds > >
.el .ds > \(ra
.ie \n(.g .ds : \:
.el .ds : .
.ds d " degrees
.ds m " minutes
.ds s " seconds
.ds _ " \&
.if t \{\
. if \n(.g .if c \(de .if c \(fm .if c \(sd \{\
.  ds d \(de
.  ds m \(fm
.  ds s \(sd
.  ds _ \|
. \}
.\}
The
.B zic
program reads text from the file(s) named on the command line
and creates the timezone information format (TZif) files
specified in this input.
If a
.I filename
is
.q "\-" ,
standard input is read.
.SH OPTIONS
.TP
.B "\-\-version"
Output version information and exit.
.TP
.B \-\-help
Output short usage message and exit.
.TP
.BI "\-b " bloat
Output backward-compatibility data as specified by
.IR bloat .
If
.I bloat
is
.BR fat ,
generate additional data entries that work around potential bugs or
incompatibilities in older software, such as software that mishandles
the 64-bit generated data.
If
.I bloat
is
.BR slim ,
keep the output files small; this can help check for the bugs
and incompatibilities.
The default is
.BR slim ,
as software that mishandles 64-bit data typically
mishandles timestamps after the year 2038 anyway.
Also see the
.B \-r
option for another way to alter output size.
.TP
.BI "\-d " directory
Create time conversion information files in the named directory rather than
in the standard directory named below.
.TP
.BI "\-l " timezone
Use
.I timezone
as local time.
.B zic
will act as if the input contained a link line of the form
.sp
.ti +2
.ta \w'Link\0\0'u  +\w'\fItimezone\fP\0\0'u
Link	\fItimezone\fP		localtime
.sp
If
.I timezone
is
.BR \- ,
any already-existing link is removed.
.TP
.BI "\-L " leapsecondfilename
Read leap second information from the file with the given name.
If this option is not used,
no leap second information appears in output files.
.TP
.BI "\-p " timezone
Use
.IR timezone 's
rules when handling nonstandard
TZ strings like "EET\-2EEST" that lack transition rules.
.B zic
will act as if the input contained a link line of the form
.sp
.ti +2
Link	\fItimezone\fP		posixrules
.sp
If
.I timezone
is
.q "\-"
(the default), any already-existing link is removed.
.sp
Unless
.I timezone is
.q "\-" ,
this option is obsolete and poorly supported.
Among other things it should not be used for timestamps after the year 2037,
and it should not be combined with
.B "\-b slim"
if
.IR timezone 's
transitions are at standard time or Universal Time (UT) instead of local time.
.TP
.BR "\-r " "[\fB@\fP\fIlo\fP][\fB/@\fP\fIhi\fP]"
Limit the applicability of output files
to timestamps in the range from
.I lo
(inclusive) to
.I hi
(exclusive), where
.I lo
and
.I hi
are possibly signed decimal counts of seconds since the Epoch
(1970-01-01 00:00:00 UTC).
Omitted counts default to extreme values.
The output files use UT offset 0 and abbreviation
.q "\-00"
in place of the omitted timestamp data.
For example,
.q "zic \-r @0"
omits data intended for negative timestamps (i.e., before the Epoch), and
.q "zic \-r @0/@2147483648"
outputs data intended only for nonnegative timestamps that fit into
31-bit signed integers.
On platforms with GNU
.BR date ,
.q "zic \-r @$(date +%s)"
omits data intended for past timestamps.
Although this option typically reduces the output file's size,
the size can increase due to the need to represent the timestamp range
boundaries, particularly if
.I hi
causes a TZif file to contain explicit entries for
.RI pre- hi
transitions rather than concisely representing them
with a proleptic TZ string.
Also see the
.B "\-b slim"
option for another way to shrink output size.
.TP
.BI "\-R @" hi
Generate redundant trailing explicit transitions for timestamps
that occur less than
.I hi
seconds since the Epoch, even though the transitions could be
more concisely represented via the proleptic TZ string.
This option does not affect the represented timestamps.
Although it accommodates nonstandard TZif readers
that ignore the proleptic TZ string,
it increases the size of the altered output files.
.TP
.BI "\-t " file
When creating local time information, put the configuration link in
the named file rather than in the standard location.
.TP
.B \-v
Be more verbose, and complain about the following situations:
.RS
.PP
The input specifies a link to a link,
something not supported by some older parsers, including
.B zic
itself through release 2022e.
.PP
A year that appears in a data file is outside the range
of representable years.
.PP
A time of 24:00 or more appears in the input.
Pre-1998 versions of
.B zic
prohibit 24:00, and pre-2007 versions prohibit times greater than 24:00.
.PP
A rule goes past the start or end of the month.
Pre-2004 versions of
.B zic
prohibit this.
.PP
A time zone abbreviation uses a
.B %z
format.
Pre-2015 versions of
.B zic
do not support this.
.PP
A timestamp contains fractional seconds.
Pre-2018 versions of
.B zic
do not support this.
.PP
The input contains abbreviations that are mishandled by pre-2018 versions of
.B zic
due to a longstanding coding bug.
These abbreviations include
.q L
for
.q Link ,
.q mi
for
.q min ,
.q Sa
for
.q Sat ,
and
.q Su
for
.q Sun .
.PP
The output file does not contain all the information about the
long-term future of a timezone, because the future cannot be summarized as
a proleptic TZ string.  For example, as of 2023 this problem
occurs for Morocco's daylight-saving rules, as these rules are based
on predictions for when Ramadan will be observed, something that
a proleptic TZ string cannot represent.
.PP
The output contains data that may not be handled properly by client
code designed for older
.B zic
output formats.  These compatibility issues affect only timestamps
before 1970 or after the start of 2038.
.PP
The output contains a truncated leap second table,
which can cause some older TZif readers to misbehave.
This can occur if the
.B "\-L"
option is used, and either an Expires line is present or
the
.B "\-r"
option is also used.
.PP
The output file contains more than 1200 transitions,
which may be mishandled by some clients.
The current reference client supports at most 2000 transitions;
pre-2014 versions of the reference client support at most 1200
transitions.
.PP
A time zone abbreviation has fewer than 3 or more than 6 characters.
POSIX requires at least 3, and requires implementations to support
at least 6.
.PP
An output file name contains a byte that is not an ASCII letter,
.q "\-" ,
.q "/" ,
or
.q "_" ;
or it contains a file name component that contains more than 14 bytes
or that starts with
.q "\-" .
.RE
.SH FILES
Input files use the format described in this section; output files use
.BR tzfile (5)
format.
.PP
Input files should be text files, that is, they should be a series of
zero or more lines, each ending in a newline byte and containing at
most 2048 bytes counting the newline, and without any NUL bytes.
The input text's encoding
is typically UTF-8 or ASCII; it should have a unibyte representation
for the POSIX Portable Character Set (PPCS)
\*<https://pubs\*:.opengroup\*:.org/\*:onlinepubs/\*:9699919799/\*:basedefs/\*:V1_chap06\*:.html\*>
and the encoding's non-unibyte characters should consist entirely of
non-PPCS bytes.  Non-PPCS characters typically occur only in comments:
although output file names and time zone abbreviations can contain
nearly any character, other software will work better if these are
limited to the restricted syntax described under the
.B \-v
option.
.PP
Input lines are made up of fields.
Fields are separated from one another by one or more white space characters.
The white space characters are space, form feed, carriage return, newline,
tab, and vertical tab.
Leading and trailing white space on input lines is ignored.
An unquoted sharp character (#) in the input introduces a comment which extends
to the end of the line the sharp character appears on.
White space characters and sharp characters may be enclosed in double quotes
(") if they're to be used as part of a field.
Any line that is blank (after comment stripping) is ignored.
Nonblank lines are expected to be of one of three types:
rule lines, zone lines, and link lines.
.PP
Names must be in English and are case insensitive.
They appear in several contexts, and include month and weekday names
and keywords such as
.BR "maximum" ,
.BR "only" ,
.BR "Rolling" ,
and
.BR "Zone" .
A name can be abbreviated by omitting all but an initial prefix; any
abbreviation must be unambiguous in context.
.PP
A rule line has the form
.nf
.ti +2
.ta \w'Rule\0\0'u +\w'NAME\0\0'u +\w'FROM\0\0'u +\w'1973\0\0'u +\w'\-\0\0'u +\w'Apr\0\0'u +\w'lastSun\0\0'u +\w'2:00w\0\0'u +\w'1:00d\0\0'u
.sp
Rule	NAME	FROM	TO	\-	IN	ON	AT	SAVE	LETTER/S
.sp
For example:
.ti +2
.sp
Rule	US	1967	1973	\-	Apr	lastSun	2:00w	1:00d	D
.sp
.fi
The fields that make up a rule line are:
.TP
.B NAME
Gives the name of the rule set that contains this line.
The name must start with a character that is neither
an ASCII digit nor
.q \-
nor
.q + .
To allow for future extensions,
an unquoted name should not contain characters from the set
.ie \n(.g .q \f(CR!$%&\(aq()*,/:;<=>?@[\e]\(ha\(ga{|}\(ti\fP .
.el .ie t .q \f(CW!$%&'()*,/:;<=>?@[\e]^\(ga{|}~\fP .
.el .q !$%&'()*,/:;<=>?@[\e]^`{|}~ .
.TP
.B FROM
Gives the first year in which the rule applies.
Any signed integer year can be supplied; the proleptic Gregorian calendar
is assumed, with year 0 preceding year 1.
Rules can describe times that are not representable as time values,
with the unrepresentable times ignored; this allows rules to be portable
among hosts with differing time value types.
.TP
.B TO
Gives the final year in which the rule applies.
The word
.B maximum
(or an abbreviation) means the indefinite future, and the word
.B only
(or an abbreviation)
may be used to repeat the value of the
.B FROM
field.
.TP
.B \-
Is a reserved field and should always contain
.q \-
for compatibility with older versions of
.BR zic .
It was previously known as the
.B TYPE
field, which could contain values to allow a
separate script to further restrict in which
.q types
of years the rule would apply.
.TP
.B IN
Names the month in which the rule takes effect.
Month names may be abbreviated as mentioned previously;
for example, January can appear as
.q January ,
.q JANU
or
.q Ja ,
but not as
.q j
which would be ambiguous with both June and July.
.TP
.B ON
Gives the day on which the rule takes effect.
Recognized forms include:
.nf
.in +2
.sp
.ta \w'Sun<=25\0\0'u
5	the fifth of the month
lastSun	the last Sunday in the month
lastMon	the last Monday in the month
Sun>=8	first Sunday on or after the eighth
Sun<=25	last Sunday on or before the 25th
.fi
.in
.sp
A weekday name (e.g.,
.BR "Sunday" )
or a weekday name preceded by
.q "last"
(e.g.,
.BR "lastSunday" )
may be abbreviated as mentioned previously,
e.g.,
.q Su
for Sunday and
.q lastsa
for the last Saturday.
There must be no white space characters within the
.B ON
field.
The
.q <=
and
.q >=
constructs can result in a day in the neighboring month;
for example, the IN-ON combination
.q "Oct Sun>=31"
stands for the first Sunday on or after October 31,
even if that Sunday occurs in November.
.TP
.B AT
Gives the time of day at which the rule takes effect,
relative to 00:00, the start of a calendar day.
Recognized forms include:
.nf
.in +2
.sp
.ta \w'00:19:32.13\0\0'u
2	time in hours
2:00	time in hours and minutes
01:28:14	time in hours, minutes, and seconds
00:19:32.13	time with fractional seconds
12:00	midday, 12 hours after 00:00
15:00	3 PM, 15 hours after 00:00
24:00	end of day, 24 hours after 00:00
260:00	260 hours after 00:00
\-2:30	2.5 hours before 00:00
\-	equivalent to 0
.fi
.in
.sp
Although
.B zic
rounds times to the nearest integer second
(breaking ties to the even integer), the fractions may be useful
to other applications requiring greater precision.
The source format does not specify any maximum precision.
Any of these forms may be followed by the letter
.B w
if the given time is local or
.q "wall clock"
time,
.B s
if the given time is standard time without any adjustment for daylight saving,
or
.B u
(or
.B g
or
.BR z )
if the given time is universal time;
in the absence of an indicator,
local (wall clock) time is assumed.
These forms ignore leap seconds; for example,
if a leap second occurs at 00:59:60 local time,
.q "1:00"
stands for 3601 seconds after local midnight instead of the usual 3600 seconds.
The intent is that a rule line describes the instants when a
clock/calendar set to the type of time specified in the
.B AT
field would show the specified date and time of day.
.TP
.B SAVE
Gives the amount of time to be added to local standard time when the rule is in
effect, and whether the resulting time is standard or daylight saving.
This field has the same format as the
.B AT
field
except with a different set of suffix letters:
.B s
for standard time and
.B d
for daylight saving time.
The suffix letter is typically omitted, and defaults to
.B s
if the offset is zero and to
.B d
otherwise.
Negative offsets are allowed; in Ireland, for example, daylight saving
time is observed in winter and has a negative offset relative to
Irish Standard Time.
The offset is merely added to standard time; for example,
.B zic
does not distinguish a 10:30 standard time plus an 0:30
.B SAVE
from a 10:00 standard time plus a 1:00
.BR SAVE .
.TP
.B LETTER/S
Gives the
.q "variable part"
(for example, the
.q "S"
or
.q "D"
in
.q "EST"
or
.q "EDT" )
of time zone abbreviations to be used when this rule is in effect.
If this field is
.q \- ,
the variable part is null.
.PP
A zone line has the form
.sp
.nf
.ti +2
.ta \w'Zone\0\0'u +\w'Asia/Amman\0\0'u +\w'STDOFF\0\0'u +\w'Jordan\0\0'u +\w'FORMAT\0\0'u
Zone	NAME	STDOFF	RULES	FORMAT	[UNTIL]
.sp
For example:
.sp
.ti +2
Zone	Asia/Amman	2:00	Jordan	EE%sT	2017 Oct 27 01:00
.sp
.fi
The fields that make up a zone line are:
.TP
.B NAME
The name of the timezone.
This is the name used in creating the time conversion information file for the
timezone.
It should not contain a file name component
.q ".\&"
or
.q ".." ;
a file name component is a maximal substring that does not contain
.q "/" .
.TP
.B STDOFF
The amount of time to add to UT to get standard time,
without any adjustment for daylight saving.
This field has the same format as the
.B AT
and
.B SAVE
fields of rule lines, except without suffix letters;
begin the field with a minus sign if time must be subtracted from UT.
.TP
.B RULES
The name of the rules that apply in the timezone or,
alternatively, a field in the same format as a rule-line
.B SAVE
field,
giving the amount of time to be added to local standard time
and whether the resulting time is standard or daylight saving.
Standard time applies if this field is
.B \-
or for timestamps occurring before any rule takes effect.
When an amount of time is given, only the sum of standard time and
this amount matters.
.TP
.B FORMAT
The format for time zone abbreviations.
The pair of characters
.B %s
shows where to put the time zone abbreviation's variable part,
which is taken from the
.B LETTER/S
field of the corresponding rule;
any timestamps that precede the earliest rule use the
.B LETTER/S
of the earliest standard-time rule (which in this case must exist).
Alternatively, a format can use the pair of characters
.B %z
to stand for the UT offset in the form
.RI \(+- hh ,
.RI \(+- hhmm ,
or
.RI \(+- hhmmss ,
using the shortest form that does not lose information, where
.IR hh ,
.IR mm ,
and
.I ss
are the hours, minutes, and seconds east (+) or west (\-) of UT.
Alternatively,
a slash (/)
separates standard and daylight abbreviations.
To conform to POSIX, a time zone abbreviation should contain only
alphanumeric ASCII characters,
.q "+"
and
.q "\-".
By convention, the time zone abbreviation
.q "\-00"
is a placeholder that means local time is unspecified.
.TP
.B UNTIL
The time at which the UT offset or the rule(s) change for a location.
It takes the form of one to four fields YEAR [MONTH [DAY [TIME]]].
If this is specified,
the time zone information is generated from the given UT offset
and rule change until the time specified, which is interpreted using
the rules in effect just before the transition.
The month, day, and time of day have the same format as the IN, ON, and AT
fields of a rule; trailing fields can be omitted, and default to the
earliest possible value for the missing fields.
.IP
The next line must be a
.q "continuation"
line; this has the same form as a zone line except that the
string
.q "Zone"
and the name are omitted, as the continuation line will
place information starting at the time specified as the
.q "until"
information in the previous line in the file used by the previous line.
Continuation lines may contain
.q "until"
information, just as zone lines do, indicating that the next line is a further
continuation.
.PP
If a zone changes at the same instant that a rule would otherwise take
effect in the earlier zone or continuation line, the rule is ignored.
A zone or continuation line
.I L
with a named rule set starts with standard time by default:
that is, any of
.IR L 's
timestamps preceding
.IR L 's
earliest rule use the rule in effect after
.IR L 's
first transition into standard time.
In a single zone it is an error if two rules take effect at the same
instant, or if two zone changes take effect at the same instant.
.PP
If a continuation line subtracts
.I N
seconds from the UT offset after a transition that would be
interpreted to be later if using the continuation line's UT offset and
rules, the
.q "until"
time of the previous zone or continuation line is interpreted
according to the continuation line's UT offset and rules, and any rule
that would otherwise take effect in the next
.I N
seconds is instead assumed to take effect simultaneously.
For example:
.br
.ne 7
.nf
.in +2
.ta \w'# Rule\0\0'u +\w'NAME\0\0'u +\w'FROM\0\0'u +\w'2006\0\0'u +\w'\-\0\0'u +\w'Oct\0\0'u +\w'lastSun\0\0'u +\w'2:00\0\0'u +\w'SAVE\0\0'u
.sp
# Rule	NAME	FROM	TO	\-	IN	ON	AT	SAVE	LETTER/S
Rule	US	1967	2006	-	Oct	lastSun	2:00	0	S
Rule	US	1967	1973	-	Apr	lastSun	2:00	1:00	D
.ta \w'# Zone\0\0'u +\w'America/Menominee\0\0'u +\w'STDOFF\0\0'u +\w'RULES\0\0'u +\w'FORMAT\0\0'u
# Zone	NAME	STDOFF	RULES	FORMAT	[UNTIL]
Zone	America/Menominee	\-5:00	\-	EST	1973 Apr 29 2:00
		\-6:00	US	C%sT
.sp
.in
.fi
Here, an incorrect reading would be there were two clock changes on 1973-04-29,
the first from 02:00 EST (\-05) to 01:00 CST (\-06),
and the second an hour later from 02:00 CST (\-06) to 03:00 CDT (\-05).
However,
.B zic
interprets this more sensibly as a single transition from 02:00 CST (\-05) to
02:00 CDT (\-05).
.PP
A link line has the form
.sp
.nf
.ti +2
.ta \w'Link\0\0'u +\w'Europe/Istanbul\0\0'u
Link	TARGET	LINK-NAME
.sp
For example:
.sp
.ti +2
Link	Europe/Istanbul	Asia/Istanbul
.sp
.fi
The
.B TARGET
field should appear as the
.B NAME
field in some zone line or as the
.B LINK-NAME
field in some link line.
The
.B LINK-NAME
field is used as an alternative name for that zone;
it has the same syntax as a zone line's
.B NAME
field.
Links can chain together, although the behavior is unspecified if a
chain of one or more links does not terminate in a Zone name.
A link line can appear before the line that defines the link target.
For example:
.sp
.ne 3
.nf
.in +2
.ta \w'Zone\0\0'u +\w'Greenwich\0\0'u
Link	Greenwich	G_M_T
Link	Etc/GMT	Greenwich
Zone	Etc/GMT\0\00\0\0\-\0\0GMT
.sp
.in
.fi
The two links are chained together, and G_M_T, Greenwich, and Etc/GMT
all name the same zone.
.PP
Except for continuation lines,
lines may appear in any order in the input.
However, the behavior is unspecified if multiple zone or link lines
define the same name.
.PP
The file that describes leap seconds can have leap lines and an
expiration line.
Leap lines have the following form:
.nf
.ti +2
.ta \w'Leap\0\0'u +\w'YEAR\0\0'u +\w'MONTH\0\0'u +\w'DAY\0\0'u +\w'HH:MM:SS\0\0'u +\w'CORR\0\0'u
.sp
Leap	YEAR	MONTH	DAY	HH:MM:SS	CORR	R/S
.sp
For example:
.ti +2
.sp
Leap	2016	Dec	31	23:59:60	+	S
.sp
.fi
The
.BR YEAR ,
.BR MONTH ,
.BR DAY ,
and
.B HH:MM:SS
fields tell when the leap second happened.
The
.B CORR
field
should be
.q "+"
if a second was added
or
.q "\-"
if a second was skipped.
The
.B R/S
field
should be (an abbreviation of)
.q "Stationary"
if the leap second time given by the other fields should be interpreted as UTC
or
(an abbreviation of)
.q "Rolling"
if the leap second time given by the other fields should be interpreted as
local (wall clock) time.
.PP
Rolling leap seconds would let one see
Times Square ball drops where there'd be a
.q "3... 2... 1... leap... Happy New Year"
countdown, placing the leap second at
midnight New York time rather than midnight UTC.
Although stationary leap seconds are the common practice,
rolling leap seconds can be useful in specialized applications
like SMPTE timecodes that may prefer to put leap second
discontinuities at the end of a local broadcast day.
However, rolling leap seconds are not supported if the
.B \-r
option is used.
.PP
The expiration line, if present, has the form:
.nf
.ti +2
.ta \w'Expires\0\0'u +\w'YEAR\0\0'u +\w'MONTH\0\0'u +\w'DAY\0\0'u
.sp
Expires	YEAR	MONTH	DAY	HH:MM:SS
.sp
For example:
.ti +2
.sp
Expires	2020	Dec	28	00:00:00
.sp
.fi
The
.BR YEAR ,
.BR MONTH ,
.BR DAY ,
and
.B HH:MM:SS
fields give the expiration timestamp in UTC for the leap second table.
.br
.ne 22
.SH "EXTENDED EXAMPLE"
Here is an extended example of
.B zic
input, intended to illustrate many of its features.
.nf
.in +2
.ta \w'# Rule\0\0'u +\w'NAME\0\0'u +\w'FROM\0\0'u +\w'1973\0\0'u +\w'\-\0\0'u +\w'Apr\0\0'u +\w'lastSun\0\0'u +\w'2:00\0\0'u +\w'SAVE\0\0'u
.sp
# Rule	NAME	FROM	TO	\-	IN	ON	AT	SAVE	LETTER/S
Rule	Swiss	1941	1942	\-	May	Mon>=1	1:00	1:00	S
Rule	Swiss	1941	1942	\-	Oct	Mon>=1	2:00	0	\-
.sp .5
Rule	EU	1977	1980	\-	Apr	Sun>=1	1:00u	1:00	S
Rule	EU	1977	only	\-	Sep	lastSun	1:00u	0	\-
Rule	EU	1978	only	\-	Oct	 1	1:00u	0	\-
Rule	EU	1979	1995	\-	Sep	lastSun	1:00u	0	\-
Rule	EU	1981	max	\-	Mar	lastSun	1:00u	1:00	S
Rule	EU	1996	max	\-	Oct	lastSun	1:00u	0	\-
.sp
.ta \w'# Zone\0\0'u +\w'Europe/Zurich\0\0'u +\w'0:29:45.50\0\0'u +\w'RULES\0\0'u +\w'FORMAT\0\0'u
# Zone	NAME	STDOFF	RULES	FORMAT	[UNTIL]
Zone	Europe/Zurich	0:34:08	\-	LMT	1853 Jul 16
		0:29:45.50	\-	BMT	1894 Jun
		1:00	Swiss	CE%sT	1981
		1:00	EU	CE%sT
.sp
Link	Europe/Zurich	Europe/Vaduz
.sp
.in
.fi
In this example, the EU rules are for the European Union
and for its predecessor organization, the European Communities.
The timezone is named Europe/Zurich and it has the alias Europe/Vaduz.
This example says that Zurich was 34 minutes and 8
seconds east of UT until 1853-07-16 at 00:00, when the legal offset
was changed to
7\*d\*_26\*m\*_22.50\*s,
which works out to 0:29:45.50;
.B zic
treats this by rounding it to 0:29:46.
After 1894-06-01 at 00:00 the UT offset became one hour
and Swiss daylight saving rules (defined with lines beginning with
.q "Rule Swiss")
apply.  From 1981 to the present, EU daylight saving rules have
applied, and the UTC offset has remained at one hour.
.PP
In 1941 and 1942, daylight saving time applied from the first Monday
in May at 01:00 to the first Monday in October at 02:00.
The pre-1981 EU daylight-saving rules have no effect
here, but are included for completeness.  Since 1981, daylight
saving has begun on the last Sunday in March at 01:00 UTC.
Until 1995 it ended the last Sunday in September at 01:00 UTC,
but this changed to the last Sunday in October starting in 1996.
.PP
For purposes of display,
.q "LMT"
and
.q "BMT"
were initially used, respectively.  Since
Swiss rules and later EU rules were applied, the time zone abbreviation
has been CET for standard time and CEST for daylight saving
time.
.SH FILES
.TP
.I /etc/localtime
Default local timezone file.
.TP
.I /usr/share/zoneinfo
Default timezone information directory.
.SH NOTES
For areas with more than two types of local time,
you may need to use local standard time in the
.B AT
field of the earliest transition time's rule to ensure that
the earliest transition time recorded in the compiled file is correct.
.PP
If,
for a particular timezone,
a clock advance caused by the start of daylight saving
coincides with and is equal to
a clock retreat caused by a change in UT offset,
.B zic
produces a single transition to daylight saving at the new UT offset
without any change in local (wall clock) time.
To get separate transitions
use multiple zone continuation lines
specifying transition instants using universal time.
.SH SEE ALSO
.BR tzfile (5),
.BR zdump (8)

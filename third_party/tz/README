README for the tz distribution

"Where do I set the hands of the clock?" -- <PERSON> as The King
"Oh that--you can set them any place you want." -- <PERSON> as The Scientist
					(from the Bell System film "About Time")

The Time Zone Database (called tz, tzdb or zoneinfo) contains code and
data that represent the history of local time for many representative
locations around the globe.  It is updated periodically to reflect
changes made by political bodies to time zone boundaries, UTC offsets,
and daylight-saving rules.

See <https://www.iana.org/time-zones/repository/tz-link.html> or the
file tz-link.html for how to acquire the code and data.

Once acquired, read the leading comments in the file "Makefile"
and make any changes needed to make things right for your system,
especially when using a platform other than current GNU/Linux.

Then run the following commands, substituting your desired
installation directory for "$HOME/tzdir":

	make TOPDIR="$HOME/tzdir" install
	"$HOME/tzdir/usr/bin/zdump" -v America/Los_Angeles

See the file tz-how-to.html for examples of how to read the data files.

This database of historical local time information has several goals:

 * Provide a compendium of data about the history of civil time that
   is useful even if not 100% accurate.

 * Give an idea of the variety of local time rules that have existed
   in the past and thus may be expected in the future.

 * Test the generality of the local time rule description system.

The information in the time zone data files is by no means authoritative;
fixes and enhancements are welcome.  Please see the file CONTRIBUTING
for details.

Thanks to these Time Zone Caballeros who've made major contributions to the
time conversion package: Keith Bostic; Bob Devine; Paul Eggert; Robert Elz;
Guy Harris; Mark Horton; John Mackin; and Bradley White.  Thanks also to
Michael Bloom, <PERSON>, <PERSON> Prince, John Sovereign, and <PERSON> Wales
for testing work, and to Gwillim Law for checking local mean time data.
Thanks in particular to Arthur David Olson, the project's founder and first
maintainer, to whom the time zone community owes the greatest debt of all.
None of them are responsible for remaining errors.

-----

This file is in the public domain, so clarified as of 2009-05-17 by
Arthur David Olson.  The other files in this distribution are either
public domain or BSD licensed; see the file LICENSE for details.

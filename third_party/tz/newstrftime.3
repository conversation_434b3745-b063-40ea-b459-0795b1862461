.\" strftime man page
.\"
.\" Based on the UCB file whose corrected copyright information appears below.
.\" Copyright 1989, 1991 The Regents of the University of California.
.\" All rights reserved.
.\"
.\" This code is derived from software contributed to Berkeley by
.\" the American National Standards Committee X3, on Information
.\" Processing Systems.
.\"
.\" Redistribution and use in source and binary forms, with or without
.\" modification, are permitted provided that the following conditions
.\" are met:
.\" 1. Redistributions of source code must retain the above copyright
.\"    notice, this list of conditions and the following disclaimer.
.\" 2. Redistributions in binary form must reproduce the above copyright
.\"    notice, this list of conditions and the following disclaimer in the
.\"    documentation and/or other materials provided with the distribution.
.\" 3. Neither the name of the University nor the names of its contributors
.\"    may be used to endorse or promote products derived from this software
.\"    without specific prior written permission.
.\"
.\" THIS SOFTWARE IS PROVIDED BY THE REGENTS AND CONTRIBUTORS "AS IS" AND
.\" ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
.\" IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE
.\" ARE DISCLAIMED.  IN NO EVENT SHALL THE REGENTS OR CONTRIBUTORS BE LIABLE
.\" FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL
.\" DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS
.\" OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
.\" HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT
.\" LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY
.\" OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF
.\" SUCH DAMAGE.
.\"
.\"     from: @(#)strftime.3	5.12 (Berkeley) 6/29/91
.\"	$Id: strftime.3,v 1.4 1993/12/15 20:33:00 jtc Exp $
.\"
.TH newstrftime 3 "" "Time Zone Database"
.SH NAME
strftime \- format date and time
.SH SYNOPSIS
.nf
.B #include <time.h>
.PP
.B "size_t strftime(char *restrict buf, size_t maxsize,"
.B "    char const *restrict format, struct tm const *restrict timeptr);"
.PP
.B cc ... \-ltz
.fi
.SH DESCRIPTION
.ie '\(lq'' .ds lq \&"\"
.el .ds lq \(lq\"
.ie '\(rq'' .ds rq \&"\"
.el .ds rq \(rq\"
.de c
.ie \n(.g \f(CR\\$1\fP\\$2
.el \\$1\\$2
..
.de q
\\$3\*(lq\\$1\*(rq\\$2
..
The
.B strftime
function formats the information from
.BI * timeptr
into the array pointed to by
.I buf
according to the string pointed to by
.IR format .
.PP
The
.I format
string consists of zero or more conversion specifications and
ordinary characters.
All ordinary characters are copied directly into the array.
A conversion specification consists of a percent sign
.Ql %
and one other character.
.PP
No more than
.I maxsize
bytes are placed into the array.
.PP
Each conversion specification is replaced by the characters as
follows which are then copied into the array.
The characters depend on the values of zero or more members of
.BI * timeptr
as specified by brackets in the description.
If a bracketed member name is followed by
.q + ,
.B strftime
can use the named member even though POSIX.1-2024 does not list it;
if the name is followed by
.q \- ,
.B strftime
ignores the member even though POSIX.1-2024 lists it
which means portable code should set it.
For portability,
.BI * timeptr
should be initialized as if by a successful call to
.BR gmtime ,
.BR localtime ,
.BR mktime ,
.BR timegm ,
or similar functions.
.TP
%A
is replaced by the locale's full weekday name.
.RI [ tm_wday ]
.TP
%a
is replaced by the locale's abbreviated weekday name.
.RI [ tm_wday ]
.TP
%B
is replaced by the locale's full month name.
.RI [ tm_mon ]
.TP
%b or %h
is replaced by the locale's abbreviated month name.
.RI [ tm_mon ]
.TP
%C
is replaced by the century (a year divided by 100 and truncated to an integer)
as a decimal number, with at least two digits by default.
.RI [ tm_year ]
.TP
%c
is replaced by the locale's appropriate date and time representation.
.RI [ tm_year ,
.IR tm_yday ,
.IR tm_mon ,
.IR tm_mday ,
.IR tm_wday ,
.IR tm_hour ,
.IR tm_min ,
.IR tm_sec ,
.IR tm_gmtoff ,
.IR tm_zone ,
.IR tm_isdst \-].
.TP
%D
is equivalent to
.c %m/%d/%y .
Although used in the United States for current dates,
this format is ambiguous elsewhere
and for dates that might involve other centuries.
.RI [ tm_year ,
.IR tm_mon ,
.IR tm_mday ]
.TP
%d
is replaced by the day of the month as a decimal number [01,31].
.RI [ tm_mday ]
.TP
%e
is replaced by the day of month as a decimal number [1,31];
single digits are preceded by a blank.
.RI [ tm_mday ]
.TP
%F
is equivalent to
.c %Y-%m-%d
(the ISO 8601 date format).
.RI [ tm_year ,
.IR tm_mon ,
.IR tm_mday ]
.TP
%G
is replaced by the ISO 8601 year with century as a decimal number.
This is the year that includes the greater part of the week.
(Monday as the first day of a week).
See also the
.c %V
conversion specification.
.RI [ tm_year ,
.IR tm_yday ,
.IR tm_wday ]
.TP
%g
is replaced by the ISO 8601 year without century as a decimal number [00,99].
Since it omits the century, it is ambiguous for dates.
.RI [ tm_year ,
.IR tm_yday ,
.IR tm_wday ]
.TP
%H
is replaced by the hour (24-hour clock) as a decimal number [00,23].
.RI [ tm_hour ]
.TP
%I
is replaced by the hour (12-hour clock) as a decimal number [01,12].
.RI [ tm_hour ]
.TP
%j
is replaced by the day of the year as a decimal number [001,366].
.RI [ tm_yday ]
.TP
%k
is replaced by the hour (24-hour clock) as a decimal number [0,23];
single digits are preceded by a blank.
.RI [ tm_hour ]
.TP
%l
is replaced by the hour (12-hour clock) as a decimal number [1,12];
single digits are preceded by a blank.
.RI [ tm_hour ]
.TP
%M
is replaced by the minute as a decimal number [00,59].
.RI [ tm_min ]
.TP
%m
is replaced by the month as a decimal number [01,12].
.RI [ tm_mon ]
.TP
%n
is replaced by a newline.
.TP
%p
is replaced by the locale's equivalent of either
.q AM
or
.q PM .
.RI [ tm_hour ]
.TP
%R
is replaced by the time in the format
.c %H:%M .
.RI [ tm_hour ,
.IR tm_min ]
.TP
%r
is replaced by the locale's representation of 12-hour clock time
using AM/PM notation.
.RI [ tm_hour ,
.IR tm_min ,
.IR tm_sec ]
.TP
%S
is replaced by the second as a decimal number [00,60].
The range of
seconds is [00,60] instead of [00,59] to allow for the periodic occurrence
of leap seconds.
.RI [ tm_sec ]
.TP
%s
is replaced by the number of seconds since the Epoch (see
.BR ctime (3)).
Although %s is reliable in this implementation,
it can have glitches on other platforms
(notably obsolescent platforms lacking
.I tm_gmtoff
or where
.B time_t
is no wider than int), and POSIX allows
.B strftime
to set
.B errno
to
.B EINVAL
or
.B EOVERFLOW
and return 0 if the number of seconds would be negative or out of range for
.BR time_t .
Portable code should therefore format a
.B time_t
value directly via something like
.B sprintf
instead of via
.B localtime
followed by
.B strftime
with "%s".
.RI [ tm_year ,
.IR tm_mon ,
.IR tm_mday ,
.IR tm_hour ,
.IR tm_min ,
.IR tm_sec ,
.IR tm_gmtoff +,
.IR tm_isdst \-].
.TP
%T
is replaced by the time in the format
.c %H:%M:%S .
.RI [ tm_hour ,
.IR tm_min ,
.IR tm_sec ]
.TP
%t
is replaced by a tab.
.TP
%U
is replaced by the week number of the year (Sunday as the first day of
the week) as a decimal number [00,53].
.RI [ tm_wday ,
.IR tm_yday ,
.IR tm_year \-]
.TP
%u
is replaced by the weekday (Monday as the first day of the week)
as a decimal number [1,7].
.RI [ tm_wday ]
.TP
%V
is replaced by the week number of the year (Monday as the first day of
the week) as a decimal number [01,53].  If the week containing January
1 has four or more days in the new year, then it is week 1; otherwise
it is week 53 of the previous year, and the next week is week 1.
The year is given by the
.c %G
conversion specification.
.RI [ tm_year ,
.IR tm_yday ,
.IR tm_wday ]
.TP
%W
is replaced by the week number of the year (Monday as the first day of
the week) as a decimal number [00,53].
.RI [ tm_yday ,
.IR tm_wday ]
.TP
%w
is replaced by the weekday (Sunday as the first day of the week)
as a decimal number [0,6].
.RI [ tm_year ,
.IR tm_yday ,
.IR tm_wday ]
.TP
%X
is replaced by the locale's appropriate time representation.
.RI [ tm_year \-,
.IR tm_yday \-,
.IR tm_mon \-,
.IR tm_mday \-,
.IR tm_wday \-,
.IR tm_hour ,
.IR tm_min ,
.IR tm_sec ,
.IR tm_gmtoff ,
.IR tm_zone ,
.IR tm_isdst \-].
.TP
%x
is replaced by the locale's appropriate date representation.
This format can be ambiguous for dates, e.g.,
it can generate "01/02/03" in the C locale.
.RI [ tm_year ,
.IR tm_yday ,
.IR tm_mon ,
.IR tm_mday ,
.IR tm_wday ,
.IR tm_hour \-,
.IR tm_min \-,
.IR tm_sec \-,
.IR tm_gmtoff \-,
.IR tm_zone \-,
.IR tm_isdst \-].
.TP
%Y
is replaced by the year with century as a decimal number.
.RI [ tm_year ]
.TP
%y
is replaced by the year without century as a decimal number [00,99].
Since it omits the century, it is ambiguous for dates.
.RI [ tm_year ]
.TP
%Z
is replaced by the time zone abbreviation,
or by the empty string if this is not determinable.
.RI [ tm_zone ,
.IR tm_isdst \-]
.TP
%z
is replaced by the offset from the Prime Meridian
in the format +HHMM or \-HHMM (ISO 8601) as appropriate,
with positive values representing locations east of Greenwich,
or by the empty string if this is not determinable.
The numeric time zone abbreviation \-0000 is used when the time is
Universal Time
but local time is indeterminate; by convention this is used for
locations while uninhabited, and corresponds to a zero offset when the
time zone abbreviation begins with
.q "\-" .
.RI [ tm_gmtoff ,
.IR tm_zone +,
.IR tm_isdst \-]
.TP
%%
is replaced by a single %.
.TP
%+
is replaced by the locale's date and time in
.BR date (1)
format.
.RI [ tm_year ,
.IR tm_yday ,
.IR tm_mon ,
.IR tm_mday ,
.IR tm_wday ,
.IR tm_hour ,
.IR tm_min ,
.IR tm_sec ,
.IR tm_gmtoff ,
.IR tm_zone ]
.PP
As a side effect,
.B strftime
also behaves as if
.B tzset
were called.
This is for compatibility with older platforms, as required by POSIX;
it is not needed for
.BR strftime 's
own use.
.SH "RETURN VALUE"
If the conversion is successful,
.B strftime
returns the number of bytes placed into the array, not counting the
terminating NUL;
.B errno
is unchanged if the returned value is zero.
Otherwise,
.B errno
is set to indicate the error, zero is returned,
and the array contents are unspecified.
.SH ERRORS
This function fails if:
.TP
[ERANGE]
The total number of resulting bytes, including the terminating
NUL character, is more than
.IR maxsize .
.SH SEE ALSO
.BR date (1),
.BR getenv (3),
.BR newctime (3),
.BR newtzset (3),
.BR time (2),
.BR tzfile (5).
.SH BUGS
There is no conversion specification for the phase of the moon.

# This file is used to configure your project.
# Read more about the various options under:
# http://setuptools.readthedocs.io/en/latest/setuptools.html#configuring-setup-using-setup-cfg-files

[metadata]
name = fastcov
version = 1.14
description = A massively parallel gcov wrapper for generating intermediate coverage formats fast
author = <PERSON>
author-email = <EMAIL>
license = mit
url = https://github.com/RPGillespie6/fastcov
long-description = file: README.md
long-description-content-type = text/markdown
platforms = any
# Add here all kinds of additional classifiers as defined under
# https://pypi.python.org/pypi?%3Aaction=list_classifiers
classifiers =
    Development Status :: 5 - Production/Stable
    Topic :: Utilities
    Programming Language :: Python :: 3
    Programming Language :: Python :: 3 :: Only
    Programming Language :: Python :: 3.5
    Programming Language :: Python :: 3.6
    Programming Language :: Python :: 3.7
    Environment :: Console
    Intended Audience :: Developers
    License :: OSI Approved :: MIT License
    Operating System :: POSIX :: Linux
    Operating System :: Unix
    Operating System :: MacOS
    Operating System :: Microsoft :: Windows

[options]
setup_requires =
install_requires =
python_requires = >= 3.5
py_modules = fastcov
scripts =
    dist_scripts/fastcov
    dist_scripts/fastcov_summary
    dist_scripts/fastcov_to_sonarqube

[bdist_wheel]
universal = 1

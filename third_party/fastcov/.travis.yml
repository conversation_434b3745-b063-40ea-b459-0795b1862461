# Disable sudo to speed up the build
sudo: false

language: python
python: 3.6

branches:
  only:
    - master
    - /v\d+\.\d+/ # Match version tags i.e. 'v2.0'

services:
  - docker

before_install:
  # - docker build -t fastcov .
  - docker pull rpgillespie6/fastcov # This image shouldn't change much, so it's more efficient to pull a cached copy

script:
  - docker run -v "$TRAVIS_BUILD_DIR":/mnt/workspace -w /mnt/workspace rpgillespie6/fastcov bash -c "cd ./test && ./run_tests.sh"

after_success:
  - bash <(curl -s https://codecov.io/bash) -X gcov -X coveragepy -f test/functional/cmake_project/build/coverage.xml -f test/unit/coverage.xml # Upload coverage to codecov

deploy:
  provider: pypi
  user: rpgillespie
  password:
    secure: lEu1OhdfdNMa6qk7ujMLdz6Rrpk+JA88ON64Z3RdeimAPNvflpdJCp0IyJzh6c2r3D1G5FK/SU2OJAa5gkDOw55IuRUBXAuqqYz2x4vCCTb9gvG2zUwLlqrThb5tX8yjBMEbAYYLlwH/jWhEH0CIp71sThJCILfhRUKUn0lVI0vKbN72XOWBPJEOJUF25ZsqXfPkiCsFJjQa/z9zsEhH6uCRg/TdsuPSLRRwYhTzzvmtuHzjSS+RDE4LQTg17Nv74HPR7S1r+SBHCa99qa8tzNvmz/jMamO3/kEM1XT0QXLgelFpTAdIWJRd/ZNWxW6lJu0YVv8QURgOK8dvXnCdQ6cXeBX9TxEv3ExNWSkuWQqAhm8HY7YSBC73gmb4xR495vt2CESBOxRYPKBRexhMMhEuHk7VkhdUTCTOvcI7zucLcwVphi9tocFytiqEnruUS15k2b5S7UxLMu5kfe1GWZZukDLjSSfzrs86+MC33C2OpVt+jm8PnIcJFta+cA+f6gWq6kxMefyRpEK2DNxdPRA7rUvgHZ3R9vCOdnBZ6YAd7SkLx1R7SpnjSI+40VLHTSdYBGNdIfqJUHN3jokIjd4IVgo249dd+t5ewtc9x38l7bl8Mu/OCD2jKGtj2U1K09aDRfDrGFVLuUFvH3YLGUquzZPeBf1KXmfMOipe458=
  on:
    tags: true
    repo: RPGillespie6/fastcov
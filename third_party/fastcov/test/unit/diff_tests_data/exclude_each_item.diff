diff --git a/test/functional/cmake_project/src/source1.cpp b/test/functional/cmake_project/src/source1.cpp
index 0c5409a..17f860a 100644
--- a/test/functional/cmake_project/src/source1.cpp
+++ b/test/functional/cmake_project/src/source1.cpp
@@ -2,9 +2,9 @@
 
 int foo(bool take_branch)
 {
-    int x = 0;
+    int x = 0;//test
 
-    for (int i = 0; i < 1000; i++) {
+    for (int i = 0; i < 1000; i++) {//test
         x += 2*i;
 
         if (take_branch)

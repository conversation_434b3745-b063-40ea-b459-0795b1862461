diff --git a/test.txt b/test.txt
index 003b84f..e086bf8 100755
--- a/test.txt
+++ b/test.txt
@@ -1,12 +1,9 @@

-def test_DiffParser_parseDiffFile_empty():
-    result = fastcov.DiffParser().parseDiffFile('diff_files/empty.diff', '/base')
-    assert not result
-
 def test_DiffParser_parseDiffFile_no_files():
     result = fastcov.DiffParser().parseDiffFile('diff_files/no_files.diff', '/base')
-    assert not result
+    assert not result_test

 def test_DiffParser_parseDiffFile_deleted_file():
     result = fastcov.DiffParser().parseDiffFile('diff_files/deleted_file.diff', '/base')
+	newLine
     assert not result

diff --git a/test/functional/cmake_project/src/source1.cpp b/test/functional/cmake_project/src/source1.cpp
index 0c5409a..83c48bf 100644
--- a/test/functional/cmake_project/src/source1.cpp
+++ b/test/functional/cmake_project/src/source1.cpp
@@ -2,13 +2,13 @@
 
 int foo(bool take_branch)
 {
-    int x = 0;
-
+    int x = 0;//test
+//test
     for (int i = 0; i < 1000; i++) {
-        x += 2*i;
+        x += 2*i;//test
 
         if (take_branch)
-            x += i;
+            x += i;//test
     }
 
     return x;

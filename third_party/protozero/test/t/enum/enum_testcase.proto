syntax = "proto2";

option optimize_for = LITE_RUNTIME;

package TestEnum;

enum Color {
    BLACK = 0;
    RED   = 1;
    GREEN = 2;
    BLUE  = 3;
    MAX   = 2147483646;
    NEG   = -1;

    // Older versions (before 2.6.0) of the google protobuf compiler have a
    // bug and don't allow the real minimum of -2147483648, so we are testing
    // with this.
    MIN   = -2147483647;
}

message Test {

    required Color color = 1;

}


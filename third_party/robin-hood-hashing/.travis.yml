language: cpp
dist: xenial
cache: ccache

# see https://github.com/taocpp/sequences/blob/master/.travis.yml
matrix:
  include:

  # don't build on mac because I'm a poor OSS developer

  #- os: osx
  #  osx_image: xcode11.4
  #  compiler: clang
  #  env:
  #  - COMPILER=clang++
  #  - CMAKE_OPTIONS="-DRH_cxx_standard=17"

  # compile on ARM
  - os: linux
    arch: arm64
    compiler: gcc
    env:
    - COMPILER=g++-8
    - CMAKE_OPTIONS="-DRH_sanitizer=OFF -DRH_cxx_standard=11"
    addons:
      apt:
        sources: ['ubuntu-toolchain-r-test']
        packages: ['g++-8']

  # compile on S390x
  - os: linux
    arch: s390x
    compiler: gcc
    env:
    - COMPILER=g++
    - CMAKE_OPTIONS="-DRH_sanitizer=OFF -DRH_cxx_standard=11"

  # coverage
  - os: linux
    compiler: gcc
    env:
    - CMAKE_OPTIONS="-DRH_coverage=ON"
    - CODE_COVERAGE=true
    addons:
      apt:
        sources: ['ubuntu-toolchain-r-test']
        packages: ['lcov']

  # g++ 4.9
  - os: linux
    compiler: gcc
    env:
    - COMPILER=g++-4.9
    - CMAKE_OPTIONS="-DRH_cxx_standard=11"
    addons:
      apt:
        sources: ['ubuntu-toolchain-r-test']
        packages: ['g++-4.9']        

  # clang++-10
  - os: linux
    compiler: clang
    env:
    - COMPILER=clang++-10
    - CMAKE_OPTIONS="-DRH_sanitizer=ON -DRH_cxx_standard=11"
    addons:
      apt:
        sources:
        - ubuntu-toolchain-r-test
        - sourceline: 'deb https://apt.llvm.org/xenial/ llvm-toolchain-xenial-10 main'
          key_url: 'https://apt.llvm.org/llvm-snapshot.gpg.key'
        packages:
        - clang-10
        - g++-8

  - os: linux
    compiler: clang
    env:
    - COMPILER=clang++-10
    - CMAKE_OPTIONS="-DRH_sanitizer=ON -DRH_cxx_standard=14"
    addons:
      apt:
        sources:
        - ubuntu-toolchain-r-test
        - sourceline: 'deb https://apt.llvm.org/xenial/ llvm-toolchain-xenial-10 main'
          key_url: 'https://apt.llvm.org/llvm-snapshot.gpg.key'
        packages:
        - clang-10
        - g++-8

  - os: linux
    compiler: clang
    env:
    - COMPILER=clang++-10
    - CMAKE_OPTIONS="-DRH_sanitizer=ON -DRH_cxx_standard=17"
    addons:
      apt:
        sources:
        - ubuntu-toolchain-r-test
        - sourceline: 'deb https://apt.llvm.org/xenial/ llvm-toolchain-xenial-10 main'
          key_url: 'https://apt.llvm.org/llvm-snapshot.gpg.key'
        packages:
        - clang-10
        - g++-8
        
  # g++-8
  - os: linux
    compiler: gcc
    env:
    - COMPILER=g++-8
    - CMAKE_OPTIONS="-DRH_sanitizer=ON -DRH_cxx_standard=11"
    addons:
      apt:
        sources: ['ubuntu-toolchain-r-test']
        packages: ['g++-8']

  - os: linux
    compiler: gcc
    env:
    - COMPILER=g++-8
    - CMAKE_OPTIONS="-DRH_sanitizer=ON -DRH_cxx_standard=14"
    addons:
      apt:
        sources: ['ubuntu-toolchain-r-test']
        packages: ['g++-8']

  - os: linux
    compiler: gcc
    env:
    - COMPILER=g++-8
    - CMAKE_OPTIONS="-DRH_sanitizer=ON -DRH_cxx_standard=17"
    addons:
      apt:
        sources: ['ubuntu-toolchain-r-test']
        packages: ['g++-8']

  # g++-8 32bit
  - os: linux
    compiler: gcc
    env:
    - COMPILER=g++-8
    - CMAKE_OPTIONS="-DRH_sanitizer=ON -DRH_cxx_standard=11"
    - CXXFLAGS="-m32"
    - LDFLAGS="-m32"
    addons:
      apt:
        sources: ['ubuntu-toolchain-r-test']
        packages: ['g++-8-multilib', 'linux-libc-dev:i386']

  - os: linux
    compiler: gcc
    env:
    - COMPILER=g++-8
    - CMAKE_OPTIONS="-DRH_sanitizer=ON -DRH_cxx_standard=14"
    - CXXFLAGS="-m32"
    - LDFLAGS="-m32"
    addons:
      apt:
        sources: ['ubuntu-toolchain-r-test']
        packages: ['g++-8-multilib', 'linux-libc-dev:i386']

  - os: linux
    compiler: gcc
    env:
    - COMPILER=g++-8
    - CMAKE_OPTIONS="-DRH_sanitizer=ON -DRH_cxx_standard=17"
    - CXXFLAGS="-m32"
    - LDFLAGS="-m32"
    addons:
      apt:
        sources: ['ubuntu-toolchain-r-test']
        packages: ['g++-8-multilib', 'linux-libc-dev:i386']


install:
  - if [[ "${CODE_COVERAGE}" == "true" ]]; then gem install coveralls-lcov ; fi

script:
  # make sure CXX is correctly set
  - if [[ "${COMPILER}" != "" ]]; then export CXX=${COMPILER}; fi

  # show OS/compiler version
  - uname -a
  - $CXX --version

  # build
  - cd build
  - cmake .. -DCMAKE_BUILD_TYPE=Debug ${CMAKE_OPTIONS} && cmake --build . -- -j2

  # check file
  - file ./rh

  # run test
  - ./rh -ns -ts=show
  - ./rh -ns -ts=count
  - ./rh
  
  # coverage
  - |
    if [[ "${CODE_COVERAGE}" = "true" ]]; then
      cmake --build . --target lcov
    fi


notifications:
  webhooks:
    urls:
      - https://webhooks.gitter.im/e/2bc127001c1ba8740288
    on_success: change  # options: [always|never|change] default: always
    on_failure: always  # options: [always|never|change] default: always
    on_start: never     # options: [always|never|change] default: always

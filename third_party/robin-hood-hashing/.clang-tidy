---
Checks: "*,\
  -altera-*,\
  -bugprone-easily-swappable-parameters,\
  -cert-dcl21-cpp,\
  -cert-err58-cpp,\
  -clang-diagnostic-reserved-identifier,\
  -cppcoreguidelines-avoid-magic-numbers,\
  -cppcoreguidelines-avoid-non-const-global-variables,\
  -cppcoreguidelines-macro-usage,\
  -cppcoreguidelines-no-malloc,\
  -cppcoreguidelines-owning-memory,\
  -cppcoreguidelines-pro-bounds-array-to-pointer-decay,\
  -cppcoreguidelines-pro-bounds-constant-array-index,\
  -cppcoreguidelines-pro-bounds-pointer-arithmetic,\
  -cppcoreguidelines-pro-type-reinterpret-cast,\
  -cppcoreguidelines-pro-type-vararg,\
  -fuchsia-default-arguments-calls,\
  -fuchsia-default-arguments-declarations,\
  -fuchsia-default-arguments,\
  -fuchsia-multiple-inheritance,\
  -fuchsia-overloaded-operator,\
  -google-explicit-constructor,\
  -google-runtime-int,\
  -hicpp-invalid-access-moved,\
  -hicpp-no-array-decay,\
  -hicpp-no-malloc,\
  -hicpp-use-nullptr,\
  -hicpp-vararg,\
  -llvm-header-guard,\
  -llvm-qualified-auto,\
  -llvmlibc-callee-namespace,\
  -llvmlibc-implementation-in-namespace,\
  -llvmlibc-restrict-system-libc-headers,\
  -modernize-use-nullptr,\
  -modernize-use-trailing-return-type,\
  -readability-function-cognitive-complexity,\
  -readability-magic-numbers,\
  -readability-qualified-auto,\
  -readability-use-anyofallof,\
  "
WarningsAsErrors: "*"
HeaderFilterRegex: 'robin_hood\.h'
...


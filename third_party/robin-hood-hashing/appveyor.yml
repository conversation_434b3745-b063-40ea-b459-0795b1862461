image:
- Visual Studio 2017

version: '{build}'

init:
- cmake --version
- msbuild /version

platform:
- x86
- x64

before_build:
- cmake . -DCMAKE_CXX_FLAGS="%CXX_FLAGS%" -DCMAKE_EXE_LINKER_FLAGS="%LINKER_FLAGS%" -DCMAKE_IGNORE_PATH="C:/Program Files/Git/usr/bin"

build_script:
- cmake --build . --config Release

test_script:
- Release\rh.exe -ns -ts=show
- Release\rh.exe -ns -ts=count
- Release\rh.exe

name: C/C++ CI

on:
  push:
    branches: [ master, add-*, fix-* ]
  pull_request:
    branches: [ master ]

jobs:
  build:

    runs-on: ubuntu-latest

    steps:
    - uses: actions/checkout@v2
    - name: git_actions
      run: git submodule update --init --recursive
    - name: cmake
      run: |
        sudo apt update
        sudo apt install mm-common g++-9
        export CXX=g++-9
        cmake .
    - name: make
      run: |
       export CXX=g++-9
       make
    - name: run_tests
      run: |
        pwd
        ctest --output-on-failure

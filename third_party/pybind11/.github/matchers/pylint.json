{"problemMatcher": [{"severity": "warning", "pattern": [{"regexp": "^([^:]+):(\\d+):(\\d+): ([A-DF-Z]\\d+): \\033\\[[\\d;]+m([^\\033]+).*$", "file": 1, "line": 2, "column": 3, "code": 4, "message": 5}], "owner": "pylint-warning"}, {"severity": "error", "pattern": [{"regexp": "^([^:]+):(\\d+):(\\d+): (E\\d+): \\033\\[[\\d;]+m([^\\033]+).*$", "file": 1, "line": 2, "column": 3, "code": 4, "message": 5}], "owner": "pylint-error"}]}
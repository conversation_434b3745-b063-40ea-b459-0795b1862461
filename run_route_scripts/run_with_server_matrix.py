#!/usr/bin/env python3

import argparse
import multiprocessing
from pathlib import Path

import requests
import json
import time


# generator for post bodies from the file
def get_post_bodies(filename):
    with open(filename, 'r') as f:
        line_number = 0
        for line in f:
            line_number += 1
            line = line[line.find('{'):]
            line = line[0:line.rfind('}') + 1]
            post_body = json.loads(line)
            post_body['id'] = str(line_number)
            yield post_body


def initialize(args_, response_count_):
    # for persistent connections
    global session
    session = requests.Session()
    # so each process knows the options provided
    global args
    args = args_
    # so each process can signal completing a request
    global response_count
    response_count = response_count_


# post a request
def make_request(post_body):
    # make request
    try:
        start = time.time()
        response = session.post(args.url, json=post_body, headers=args.headers)
        stop = time.time()
        elapsed = stop - start
        response = response.json()
        with response_count.get_lock():
            response_count.value += 1
    except Exception as e:
        return {"error": e, "line": post_body["id"]}

    return {"response": response, "line": post_body["id"], "perf": elapsed, "sources": post_body["sources"], "targets": post_body["targets"]}


if __name__ == "__main__":
    # parse some program arguments
    parser = argparse.ArgumentParser(description="Runs payloads generated by gen_requests_matrix.py against a Valhalla server and writes the results to a single file for diffing purposes. Also prints out the total time.")
    parser.add_argument('--test-file', type=Path, help='The file with the test requests', required=True)
    parser.add_argument('--url', type=str, help='The url to which you want to POST the request bodies',
                        default='http://localhost:8002/route')
    parser.add_argument('--output-file', type=Path, help='The output file path')
    parser.add_argument('--concurrency', type=int, help='The number of processes to use to make requests',
                        default=multiprocessing.cpu_count())
    parser.add_argument('--headers', type=str,
                        help='Additional http headers to send with the requests. Follows the http header spec, eg. some-header-name: some-header-value',
                        action='append', nargs='*', default=[])
    args = parser.parse_args()

    # setup http headers
    args.headers = {k: v for k, v in [h.split(': ') for hs in args.headers for h in hs]}
    # track progress with a count of finished requests
    response_count = multiprocessing.Value('i', 0)
    # make a worker pool to work on the requests
    work = [body for body in get_post_bodies(args.test_file)]
    # Note: workers also call initialize for themselves
    results = list()
    with multiprocessing.Pool(initializer=initialize, initargs=(args, response_count),
                              processes=args.concurrency) as pool:
        for result in pool.imap_unordered(make_request, work):
            if result.get("error"):
                print(f"line {result}: {result['error']}")
                continue
            results.append(result)

    with args.output_file.open("w") as f:
        total_time = 0
        for result in sorted(results, key=lambda i: i["line"]):
            if not result["response"].get("sources_to_targets"):
                print(f"line {result['line']}: {result['response']}")
                continue
            for source in range(len(result["sources"])):
                for target in range(len(result["targets"])):
                    duration = result["response"]["sources_to_targets"]["durations"][source][target]
                    distance = result["response"]["sources_to_targets"]["distances"][source][target]
                    f.write(f"line {result['line']}, distance {distance}, [{json.dumps(result['sources'][source])}, {json.dumps(result['targets'][target])}]\n")
            total_time += result["perf"]

        print(f"Total time: {total_time}")
